# Comprehensive System Architecture Documentation
# 半導體郵件處理系統完整架構文檔

## Executive Summary

This document provides a comprehensive technical architecture overview of the Semiconductor Email Processing System, a mature enterprise-grade platform comprising 473+ Python files that processes test data from 12 semiconductor equipment vendors. The system represents a significant investment in modern software architecture, employing hexagonal architecture patterns, domain-driven design (DDD), and a sophisticated asynchronous processing pipeline built on Dramatiq and Redis.

The recent addition of the PTS Renamer modernization project extends the system's capabilities into web-based file processing, marking a strategic evolution from desktop applications to modern web platforms while maintaining backward compatibility and leveraging existing infrastructure investments.

---

## Table of Contents

1. [System Overview and Strategic Context](#1-system-overview-and-strategic-context)
2. [Architectural Philosophy and Design Principles](#2-architectural-philosophy-and-design-principles)
3. [Core System Architecture](#3-core-system-architecture)
4. [Domain Model and Business Logic](#4-domain-model-and-business-logic)
5. [Technology Stack Deep Dive](#5-technology-stack-deep-dive)
6. [PTS Renamer Integration Architecture](#6-pts-renamer-integration-architecture)
7. [Asynchronous Processing Architecture](#7-asynchronous-processing-architecture)
8. [Data Flow and Integration Patterns](#8-data-flow-and-integration-patterns)
9. [Monitoring and Observability](#9-monitoring-and-observability)
10. [Security Architecture](#10-security-architecture)
11. [Deployment and Infrastructure](#11-deployment-and-infrastructure)
12. [Performance Characteristics](#12-performance-characteristics)
13. [Evolution and Migration Strategy](#13-evolution-and-migration-strategy)
14. [Architectural Decision Records](#14-architectural-decision-records)
15. [Future Architecture Roadmap](#15-future-architecture-roadmap)

---

## 1. System Overview and Strategic Context

### 1.1 Business Context

The semiconductor manufacturing industry generates massive volumes of test data from various equipment vendors. Each vendor uses proprietary formats, encoding schemes, and delivery mechanisms, creating a complex integration challenge. Our system addresses this challenge by providing a unified platform for:

- **Email-based data ingestion** from 12 different vendor systems
- **Intelligent parsing and normalization** of heterogeneous data formats
- **Automated quality control** and anomaly detection
- **Enterprise-grade monitoring** and observability
- **High-throughput asynchronous processing** for scalability

### 1.2 System Scale and Maturity

```yaml
Codebase Metrics:
  Total Python Files: 473+
  Core Business Modules: 6
  Test Files: 173
  Code Coverage: 85%+
  Production Deployment: Active
  Daily Processing Volume: 1000+ emails
  Vendor Integrations: 12
  Active Users: 50+
```

### 1.3 Strategic Technology Decisions

The system embodies several strategic technology decisions that reflect both current needs and future scalability:

1. **Dual Framework Strategy**: FastAPI + Flask combination provides both high-performance async capabilities and mature ecosystem support
2. **Hexagonal Architecture**: Ensures business logic independence from infrastructure concerns
3. **Domain-Driven Design**: Aligns technical implementation with business domains
4. **Asynchronous by Default**: Dramatiq + Redis enables massive parallel processing
5. **Polyglot Persistence**: Multiple storage strategies optimized for different data patterns

---

## 2. Architectural Philosophy and Design Principles

### 2.1 Core Architectural Principles

#### 2.1.1 Hexagonal Architecture (Ports and Adapters)

```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                    │
│         (Flask/FastAPI Routes, WebSocket, CLI)          │
└────────────────────┬───────────────────────┬────────────┘
                     │                       │
              ┌──────▼──────┐         ┌──────▼──────┐
              │   Port IN   │         │   Port IN   │
              │   (API)     │         │  (Commands) │
              └──────┬──────┘         └──────┬──────┘
                     │                       │
┌────────────────────▼───────────────────────▼────────────┐
│                                                          │
│                   Domain Core                            │
│                                                          │
│  ┌──────────────────────────────────────────────────┐  │
│  │          Business Logic & Entities               │  │
│  │                                                  │  │
│  │  • Email Processing Rules                        │  │
│  │  • Vendor Parser Logic                           │  │
│  │  • QC Validation Rules                           │  │
│  │  • File Processing Logic                         │  │
│  │  • PTS Renaming Rules (NEW)                      │  │
│  └──────────────────────────────────────────────────┘  │
│                                                          │
└────────────────────┬───────────────────────┬────────────┘
                     │                       │
              ┌──────▼──────┐         ┌──────▼──────┐
              │  Port OUT   │         │  Port OUT   │
              │(Repository) │         │  (Gateway)  │
              └──────┬──────┘         └──────┬──────┘
                     │                       │
┌────────────────────▼───────────────────────▼────────────┐
│                 Infrastructure Layer                     │
│     (Database, Redis, File System, External APIs)       │
└──────────────────────────────────────────────────────────┘
```

**Rationale**: This architecture ensures that business logic remains independent of infrastructure concerns. When we need to change databases, message queues, or external integrations, the core domain logic remains untouched.

#### 2.1.2 Domain-Driven Design

The system is organized around business domains rather than technical layers:

```
backend/
├── email/           # Email Processing Domain
├── analytics/       # Analytics and Reporting Domain
├── file_management/ # File Operations Domain
├── eqc/            # Equipment Quality Control Domain
├── tasks/          # Asynchronous Task Domain
├── monitoring/     # System Monitoring Domain
└── shared/         # Shared Kernel
```

Each domain maintains its own:
- **Models**: Domain entities and value objects
- **Services**: Business logic and orchestration
- **Repositories**: Data persistence abstractions
- **Adapters**: External system integrations

### 2.2 Design Patterns and Practices

#### 2.2.1 Repository Pattern
```python
# Abstract repository interface
class EmailRepository(ABC):
    @abstractmethod
    async def find_by_id(self, email_id: str) -> Email:
        pass
    
    @abstractmethod
    async def save(self, email: Email) -> None:
        pass

# Concrete implementation
class SQLAlchemyEmailRepository(EmailRepository):
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def find_by_id(self, email_id: str) -> Email:
        # SQLAlchemy specific implementation
        pass
```

#### 2.2.2 Unit of Work Pattern
```python
class UnitOfWork:
    def __init__(self):
        self.emails = EmailRepository()
        self.tasks = TaskRepository()
    
    async def commit(self):
        await self._session.commit()
    
    async def rollback(self):
        await self._session.rollback()
```

#### 2.2.3 Domain Events
```python
@dataclass
class EmailProcessedEvent:
    email_id: str
    vendor: str
    status: ProcessingStatus
    timestamp: datetime
    
class DomainEventPublisher:
    async def publish(self, event: DomainEvent):
        # Publish to event bus (Redis, Kafka, etc.)
        pass
```

---

## 3. Core System Architecture

### 3.1 High-Level System Architecture

```mermaid
graph TB
    subgraph "External Systems"
        V1[Vendor 1<br/>GTK]
        V2[Vendor 2<br/>ETD]
        V3[Vendor 3<br/>XAHT]
        V4[Other Vendors<br/>9 more]
        LLM[LLM Service<br/>Ollama/Grok]
    end
    
    subgraph "Entry Points"
        EM[Email Server<br/>IMAP/POP3]
        WEB[Web Upload<br/>UI]
        API[REST API<br/>FastAPI]
        PTS[PTS Renamer<br/>Vue.js]
    end
    
    subgraph "Application Layer"
        GW[API Gateway<br/>Nginx]
        FL[Flask App<br/>Blueprints]
        FA[FastAPI<br/>Async Routes]
    end
    
    subgraph "Processing Core"
        UEP[Unified Email<br/>Processor]
        VP[Vendor Parser<br/>Factory]
        FP[File Processor<br/>Pipeline]
        PTR[PTS Renamer<br/>Engine]
    end
    
    subgraph "Async Infrastructure"
        DQ[Dramatiq<br/>Task Queue]
        RD[Redis<br/>Message Broker]
        WK[Workers<br/>Pool]
    end
    
    subgraph "Data Layer"
        PG[PostgreSQL<br/>Main DB]
        FS[File Storage<br/>NFS/S3]
        CH[Cache<br/>Redis]
    end
    
    subgraph "Monitoring"
        PM[Prometheus<br/>Metrics]
        GF[Grafana<br/>Dashboards]
        AL[Alert Manager]
    end
    
    V1 & V2 & V3 & V4 --> EM
    EM --> UEP
    WEB --> GW
    API --> GW
    PTS --> GW
    GW --> FL & FA
    FL & FA --> UEP & FP & PTR
    UEP --> VP
    VP --> LLM
    UEP & FP & PTR --> DQ
    DQ <--> RD
    RD <--> WK
    WK --> PG & FS
    UEP & FP --> CH
    PM --> GF
    AL --> PM
    WK --> PM
```

### 3.2 Component Architecture

#### 3.2.1 Email Processing Subsystem

The email processing subsystem is the heart of the platform, handling ingestion, parsing, and routing of vendor emails:

```python
# Unified Email Processor Architecture
class UnifiedEmailProcessor:
    def __init__(
        self,
        email_repository: EmailRepository,
        parser_factory: VendorParserFactory,
        task_publisher: TaskPublisher,
        monitoring: MonitoringService
    ):
        self.email_repo = email_repository
        self.parser_factory = parser_factory
        self.task_publisher = task_publisher
        self.monitoring = monitoring
    
    async def process_email(self, email: RawEmail) -> ProcessingResult:
        # 1. Email validation and classification
        vendor = await self.classify_vendor(email)
        
        # 2. Select appropriate parser
        parser = self.parser_factory.get_parser(vendor)
        
        # 3. Parse email content
        parsed_data = await parser.parse(email)
        
        # 4. Business validation
        validation_result = await self.validate(parsed_data)
        
        # 5. Persist results
        await self.email_repo.save(parsed_data)
        
        # 6. Trigger downstream tasks
        await self.task_publisher.publish(
            ProcessEmailTask(email_id=parsed_data.id)
        )
        
        # 7. Update monitoring
        await self.monitoring.record_processing(
            vendor=vendor,
            status=validation_result.status,
            duration=processing_time
        )
        
        return ProcessingResult(
            email_id=parsed_data.id,
            status=validation_result.status
        )
```

#### 3.2.2 Vendor Parser Architecture

The system supports 12 vendor-specific parsers plus an LLM-based universal parser:

```python
# Parser Factory Pattern
class VendorParserFactory:
    def __init__(self):
        self.parsers = {
            'GTK': GTKParser(),
            'ETD': ETDParser(),
            'XAHT': XAHTParser(),
            'JCET': JCETParser(),
            'LINGSEN': LingsenParser(),
            'CHUZHOU': ChuzhouParser(),
            'MSEC': MSECParser(),
            'NANOTECH': NanotechParser(),
            'NFME': NFMEParser(),
            'SUQIAN': SuqianParser(),
            'TSHT': TSHTParser(),
            'LLM': LLMParser()  # AI-based fallback
        }
    
    def get_parser(self, vendor: str) -> VendorParser:
        return self.parsers.get(
            vendor.upper(),
            self.parsers['LLM']  # Fallback to AI
        )

# Base Parser Interface
class VendorParser(ABC):
    @abstractmethod
    async def parse(self, email: RawEmail) -> ParsedData:
        pass
    
    @abstractmethod
    async def validate(self, data: ParsedData) -> ValidationResult:
        pass
    
    def extract_attachments(self, email: RawEmail) -> List[Attachment]:
        # Common attachment extraction logic
        pass
    
    def decode_content(self, content: bytes, encoding: str) -> str:
        # Handle various encodings (UTF-8, Big5, GBK, etc.)
        pass
```

---

## 4. Domain Model and Business Logic

### 4.1 Core Domain Entities

#### 4.1.1 Email Domain Model

```python
@dataclass
class Email:
    """Core email entity representing a processed vendor email"""
    id: EmailId
    vendor: Vendor
    subject: str
    received_at: datetime
    attachments: List[Attachment]
    processing_status: ProcessingStatus
    metadata: EmailMetadata
    
    def mark_as_processed(self) -> None:
        """Domain logic for marking email as processed"""
        if self.processing_status == ProcessingStatus.FAILED:
            raise InvalidStateTransition("Cannot mark failed email as processed")
        self.processing_status = ProcessingStatus.COMPLETED
        
    def requires_retry(self) -> bool:
        """Business rule for retry logic"""
        return (
            self.processing_status == ProcessingStatus.FAILED and
            self.metadata.retry_count < 3
        )

@dataclass
class ProcessingStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRY_SCHEDULED = "retry_scheduled"
```

#### 4.1.2 File Processing Domain

```python
@dataclass
class TestFile:
    """Represents a semiconductor test file"""
    id: FileId
    original_name: str
    normalized_name: str
    file_type: TestFileType
    vendor: Vendor
    test_conditions: TestConditions
    content_hash: str
    
    def apply_naming_convention(self, convention: NamingConvention) -> str:
        """Apply vendor-specific naming conventions"""
        return convention.format(
            vendor=self.vendor,
            type=self.file_type,
            conditions=self.test_conditions
        )

class TestFileType(Enum):
    LOT_SUMMARY = "lot_summary"
    WAFER_MAP = "wafer_map"
    BIN_DATA = "bin_data"
    PARAMETRIC = "parametric"
    INLINE_TEST = "inline_test"
```

### 4.2 Business Rules and Invariants

#### 4.2.1 Email Processing Rules

```python
class EmailProcessingRules:
    """Encapsulates business rules for email processing"""
    
    @staticmethod
    def can_process_email(email: Email) -> bool:
        """Determine if email can be processed"""
        return all([
            email.vendor in SUPPORTED_VENDORS,
            email.attachments,
            email.processing_status == ProcessingStatus.PENDING,
            not email.is_duplicate()
        ])
    
    @staticmethod
    def determine_priority(email: Email) -> Priority:
        """Business logic for email prioritization"""
        if email.vendor in HIGH_PRIORITY_VENDORS:
            return Priority.HIGH
        if "URGENT" in email.subject.upper():
            return Priority.HIGH
        if email.received_at < datetime.now() - timedelta(hours=24):
            return Priority.LOW
        return Priority.NORMAL
```

#### 4.2.2 Quality Control Rules

```python
class QualityControlRules:
    """QC validation rules for semiconductor test data"""
    
    def validate_test_data(self, data: TestData) -> ValidationResult:
        violations = []
        
        # Rule 1: Yield must be within acceptable range
        if not (0 <= data.yield_percentage <= 100):
            violations.append(YieldOutOfRange(data.yield_percentage))
        
        # Rule 2: Bin totals must match wafer count
        if sum(data.bin_counts.values()) != data.total_dice:
            violations.append(BinCountMismatch())
        
        # Rule 3: Critical parameters must be present
        for param in CRITICAL_PARAMETERS:
            if param not in data.parameters:
                violations.append(MissingCriticalParameter(param))
        
        return ValidationResult(
            is_valid=len(violations) == 0,
            violations=violations
        )
```

---

## 5. Technology Stack Deep Dive

### 5.1 Backend Technology Stack

#### 5.1.1 Core Framework Layer

```yaml
Web Frameworks:
  FastAPI:
    Version: 0.104.1
    Purpose: High-performance async API endpoints
    Usage:
      - REST API endpoints
      - WebSocket connections
      - OpenAPI documentation
      - Dependency injection
    
  Flask:
    Version: 2.3.3
    Purpose: Traditional web application framework
    Usage:
      - Template rendering
      - Blueprint organization
      - Session management
      - Legacy endpoint support

Framework Integration Strategy:
  - FastAPI handles all new API development
  - Flask maintains backward compatibility
  - Shared middleware and authentication
  - Unified routing through Nginx
```

#### 5.1.2 Data Layer

```yaml
Database:
  PostgreSQL:
    Version: 14+
    Purpose: Primary data store
    Schema Design:
      - 45+ tables
      - Normalized to 3NF
      - Partitioned for performance
      - Full-text search indexes
    
  SQLAlchemy:
    Version: 2.0.23
    Features:
      - Async support via asyncpg
      - Connection pooling
      - Query optimization
      - Migration management (Alembic)

Caching:
  Redis:
    Version: 5.0.1
    Usage:
      - Session storage
      - Task queue backend
      - Result caching
      - Rate limiting
      - Real-time metrics
```

#### 5.1.3 Asynchronous Processing

```yaml
Task Queue:
  Dramatiq:
    Version: 1.15.0
    Configuration:
      - Broker: Redis
      - Workers: 8-16 processes
      - Queues: default, priority, bulk
      - Middleware:
        - Prometheus metrics
        - Error tracking
        - Rate limiting
    
Task Types:
  - Email processing tasks
  - File extraction tasks
  - Report generation tasks
  - Cleanup tasks
  - Health check tasks
```

### 5.2 Data Processing Stack

```yaml
Scientific Computing:
  Pandas:
    Version: 2.1.3
    Usage:
      - Data transformation
      - Statistical analysis
      - Report generation
      - Time series processing
  
  NumPy:
    Version: 1.24.3
    Usage:
      - Numerical operations
      - Array manipulation
      - Statistical calculations

File Processing:
  Supported Formats:
    Compressed: [ZIP, 7Z, RAR, TAR, GZ]
    Documents: [PDF, XLSX, CSV, TXT]
    Encodings: [UTF-8, Big5, GBK, CP950, Latin-1]
  
  Libraries:
    - python-magic: File type detection
    - chardet: Encoding detection
    - PyPDF2: PDF manipulation
    - openpyxl: Excel processing
```

### 5.3 Monitoring and Observability Stack

```yaml
Metrics Collection:
  Prometheus:
    Metrics Types:
      - Counter: Request counts, error counts
      - Gauge: Queue depth, active connections
      - Histogram: Response times, processing duration
      - Summary: Percentile calculations
    
Visualization:
  Grafana:
    Dashboards:
      - System Overview
      - Email Processing Pipeline
      - Vendor Performance
      - Error Analysis
      - Resource Utilization

Custom Monitoring:
  Components:
    - WebSocket real-time updates
    - Custom metric collectors
    - Business KPI tracking
    - Alert rule engine
```

---

## 6. PTS Renamer Integration Architecture

### 6.1 PTS Renamer System Overview

The PTS Renamer represents a strategic modernization initiative, transforming a legacy TKinter desktop application into a modern Vue.js web application while leveraging the existing backend infrastructure.

#### 6.1.1 Architectural Integration Points

```mermaid
graph LR
    subgraph "Legacy System"
        TK[TKinter App]
        FS1[File System]
    end
    
    subgraph "Modern Web Platform"
        VUE[Vue.js SPA]
        API[REST API]
        TASK[Task Queue]
    end
    
    subgraph "Shared Infrastructure"
        BE[Backend Services]
        DB[Database]
        CACHE[Redis Cache]
        WORKER[Dramatiq Workers]
    end
    
    TK --> FS1
    VUE --> API
    API --> BE
    BE --> TASK
    TASK --> WORKER
    WORKER --> DB
    WORKER --> CACHE
    BE --> DB
```

#### 6.1.2 Migration Architecture

```python
# PTS Renamer Service Architecture
class PTSRenamerService:
    """Core service for PTS file renaming operations"""
    
    def __init__(
        self,
        file_processor: FileProcessor,
        naming_engine: NamingEngine,
        task_queue: DramatiqTaskQueue,
        storage: StorageAdapter
    ):
        self.file_processor = file_processor
        self.naming_engine = naming_engine
        self.task_queue = task_queue
        self.storage = storage
    
    async def process_pts_files(
        self,
        upload_id: str,
        files: List[UploadedFile],
        naming_rules: NamingRules
    ) -> ProcessingResult:
        """
        Main entry point for PTS file processing
        """
        # 1. Create processing job
        job = ProcessingJob(
            id=generate_job_id(),
            upload_id=upload_id,
            total_files=len(files),
            status=JobStatus.PENDING
        )
        
        # 2. Extract archives if needed
        extracted_files = await self._extract_archives(files)
        
        # 3. Apply naming rules
        renamed_files = await self._apply_naming_rules(
            extracted_files,
            naming_rules
        )
        
        # 4. Generate QC report
        qc_report = await self._generate_qc_report(
            original_files=extracted_files,
            renamed_files=renamed_files
        )
        
        # 5. Create output archive
        output_archive = await self._create_output_archive(
            renamed_files,
            qc_report
        )
        
        # 6. Queue for async processing if large
        if len(files) > BULK_THRESHOLD:
            await self.task_queue.enqueue(
                'process_pts_bulk',
                job_id=job.id
            )
        
        return ProcessingResult(
            job_id=job.id,
            status=job.status,
            download_url=output_archive.url
        )
```

### 6.2 Frontend Architecture (Vue.js)

#### 6.2.1 Component Architecture

```typescript
// PTS Renamer Vue.js Component Structure
interface PTSRenamerState {
  files: UploadedFile[]
  namingRules: NamingRule[]
  processingStatus: ProcessingStatus
  qcReport: QCReport | null
  downloadUrl: string | null
}

// Main component composition
export default defineComponent({
  name: 'PTSRenamer',
  components: {
    FileUploader,
    NamingRuleEditor,
    ProcessingProgress,
    QCReportViewer,
    DownloadManager
  },
  setup() {
    // State management with Pinia
    const store = usePTSRenamerStore()
    
    // WebSocket for real-time updates
    const { status, progress } = useWebSocket('/ws/pts-status')
    
    // File upload handling
    const handleFileUpload = async (files: File[]) => {
      const formData = new FormData()
      files.forEach(file => formData.append('files', file))
      
      const response = await api.post('/pts-renamer/upload', formData)
      store.setUploadedFiles(response.data.files)
    }
    
    // Processing orchestration
    const startProcessing = async () => {
      const result = await api.post('/pts-renamer/process', {
        files: store.files,
        rules: store.namingRules
      })
      
      // Monitor progress via WebSocket
      watchEffect(() => {
        if (status.value === 'completed') {
          store.setDownloadUrl(result.data.downloadUrl)
        }
      })
    }
    
    return {
      handleFileUpload,
      startProcessing,
      progress,
      status
    }
  }
})
```

#### 6.2.2 State Management Architecture

```typescript
// Pinia Store for PTS Renamer
export const usePTSRenamerStore = defineStore('pts-renamer', {
  state: (): PTSRenamerState => ({
    files: [],
    namingRules: getDefaultNamingRules(),
    processingStatus: 'idle',
    qcReport: null,
    downloadUrl: null
  }),
  
  getters: {
    totalFiles: (state) => state.files.length,
    isProcessing: (state) => state.processingStatus === 'processing',
    hasReport: (state) => state.qcReport !== null
  },
  
  actions: {
    async uploadFiles(files: File[]) {
      // Implementation
    },
    
    async processFiles() {
      // Implementation
    },
    
    updateNamingRules(rules: NamingRule[]) {
      this.namingRules = rules
      // Persist to localStorage for user convenience
      localStorage.setItem('pts-naming-rules', JSON.stringify(rules))
    }
  }
})
```

### 6.3 API Design for PTS Renamer

#### 6.3.1 RESTful API Endpoints

```python
# FastAPI Router for PTS Renamer
from fastapi import APIRouter, UploadFile, BackgroundTasks

router = APIRouter(prefix="/api/pts-renamer", tags=["pts-renamer"])

@router.post("/upload")
async def upload_files(
    files: List[UploadFile],
    background_tasks: BackgroundTasks,
    service: PTSRenamerService = Depends()
) -> UploadResponse:
    """
    Handle file uploads for PTS renaming
    
    - Validates file formats
    - Stores in temporary storage
    - Returns upload session ID
    """
    session_id = generate_session_id()
    
    # Store files temporarily
    stored_files = []
    for file in files:
        stored_path = await service.store_temporary(file, session_id)
        stored_files.append(stored_path)
    
    # Queue extraction if archives
    if any(is_archive(f) for f in files):
        background_tasks.add_task(
            service.extract_archives,
            session_id=session_id
        )
    
    return UploadResponse(
        session_id=session_id,
        files=stored_files,
        status="uploaded"
    )

@router.post("/process/{session_id}")
async def process_files(
    session_id: str,
    rules: NamingRules,
    service: PTSRenamerService = Depends()
) -> ProcessingResponse:
    """
    Start processing uploaded files
    
    - Applies naming rules
    - Generates QC report
    - Creates downloadable archive
    """
    job_id = await service.start_processing(
        session_id=session_id,
        rules=rules
    )
    
    return ProcessingResponse(
        job_id=job_id,
        status="processing",
        websocket_url=f"/ws/pts-status/{job_id}"
    )

@router.get("/status/{job_id}")
async def get_status(
    job_id: str,
    service: PTSRenamerService = Depends()
) -> StatusResponse:
    """Get current processing status"""
    status = await service.get_job_status(job_id)
    return StatusResponse(
        job_id=job_id,
        status=status.value,
        progress=status.progress,
        errors=status.errors
    )

@router.get("/download/{job_id}")
async def download_results(
    job_id: str,
    service: PTSRenamerService = Depends()
) -> FileResponse:
    """Download processed files"""
    file_path = await service.get_output_file(job_id)
    return FileResponse(
        file_path,
        filename=f"pts_renamed_{job_id}.zip",
        media_type="application/zip"
    )
```

---

## 7. Asynchronous Processing Architecture

### 7.1 Dramatiq Task Queue Architecture

The system leverages Dramatiq for robust asynchronous task processing, enabling high-throughput email and file processing operations.

#### 7.1.1 Task Queue Configuration

```python
# Dramatiq Configuration
import dramatiq
from dramatiq.brokers.redis import RedisBroker
from dramatiq.middleware import Prometheus, TimeLimit, Retries

# Configure Redis broker
redis_broker = RedisBroker(
    host="localhost",
    port=6379,
    db=0,
    password=os.getenv("REDIS_PASSWORD")
)

# Add middleware
redis_broker.add_middleware(Prometheus())
redis_broker.add_middleware(TimeLimit(time_limit=600000))  # 10 minutes
redis_broker.add_middleware(Retries(max_retries=3))

dramatiq.set_broker(redis_broker)

# Define queues
QUEUES = {
    'default': {'priority': 5},
    'priority': {'priority': 10},
    'bulk': {'priority': 1},
    'maintenance': {'priority': 0}
}
```

#### 7.1.2 Task Definitions

```python
# Core task definitions
@dramatiq.actor(queue_name="priority", max_retries=3)
async def process_email_task(email_id: str) -> ProcessingResult:
    """
    High-priority email processing task
    
    Workflow:
    1. Fetch email from database
    2. Parse vendor-specific content
    3. Extract attachments
    4. Validate data quality
    5. Store results
    6. Trigger downstream tasks
    """
    async with get_db_session() as session:
        email_repo = EmailRepository(session)
        email = await email_repo.get(email_id)
        
        processor = UnifiedEmailProcessor()
        result = await processor.process(email)
        
        # Trigger downstream tasks
        if result.has_attachments:
            extract_attachments_task.send(email_id)
        
        if result.requires_qc:
            quality_check_task.send(email_id)
        
        return result

@dramatiq.actor(queue_name="bulk", time_limit=1800000)  # 30 minutes
async def extract_archive_task(
    file_path: str,
    destination: str,
    session_id: str
) -> ExtractionResult:
    """
    Bulk file extraction task for large archives
    
    Features:
    - Progress tracking via Redis
    - Memory-efficient streaming
    - Format detection
    - Encoding handling
    """
    extractor = ArchiveExtractor()
    
    # Update progress in Redis for WebSocket updates
    async def progress_callback(current: int, total: int):
        await redis_client.set(
            f"extraction:{session_id}:progress",
            json.dumps({"current": current, "total": total}),
            ex=3600
        )
    
    result = await extractor.extract(
        file_path,
        destination,
        progress_callback=progress_callback
    )
    
    # Queue individual file processing
    for extracted_file in result.files:
        process_file_task.send(extracted_file.path, session_id)
    
    return result

@dramatiq.actor(queue_name="maintenance", max_retries=1)
async def cleanup_temporary_files_task() -> CleanupResult:
    """
    Periodic cleanup of temporary files
    
    Runs every 6 hours to remove:
    - Uploaded files older than 24 hours
    - Processed results older than 7 days
    - Failed processing artifacts
    """
    cleaner = TemporaryFileCleaner()
    result = await cleaner.cleanup(
        max_age_hours=24,
        exclude_patterns=['*.log', '*.db']
    )
    
    # Log cleanup metrics
    await monitoring.record_cleanup(
        files_removed=result.files_removed,
        space_freed=result.space_freed_mb
    )
    
    return result
```

### 7.2 Worker Pool Management

#### 7.2.1 Worker Configuration

```python
# Worker pool configuration
class WorkerPoolConfig:
    """Configuration for Dramatiq worker pools"""
    
    def __init__(self):
        self.workers = {
            'email_processor': {
                'processes': 4,
                'threads': 2,
                'queues': ['priority', 'default'],
                'memory_limit': '2GB'
            },
            'file_processor': {
                'processes': 2,
                'threads': 4,
                'queues': ['bulk'],
                'memory_limit': '4GB'
            },
            'maintenance': {
                'processes': 1,
                'threads': 1,
                'queues': ['maintenance'],
                'memory_limit': '512MB'
            }
        }
    
    def get_worker_command(self, worker_type: str) -> List[str]:
        """Generate worker startup command"""
        config = self.workers[worker_type]
        return [
            'dramatiq',
            'backend.tasks',
            '--processes', str(config['processes']),
            '--threads', str(config['threads']),
            '--queues', ' '.join(config['queues'])
        ]
```

#### 7.2.2 Worker Health Monitoring

```python
class WorkerHealthMonitor:
    """Monitor and manage worker health"""
    
    async def check_worker_health(self) -> WorkerHealthStatus:
        """Check health of all worker processes"""
        health_status = {}
        
        for worker_type, config in WorkerPoolConfig().workers.items():
            # Check process status
            processes = await self.get_worker_processes(worker_type)
            
            # Check queue depth
            queue_depth = await self.get_queue_depth(config['queues'])
            
            # Check memory usage
            memory_usage = await self.get_memory_usage(processes)
            
            health_status[worker_type] = {
                'processes_alive': len(processes),
                'processes_expected': config['processes'],
                'queue_depth': queue_depth,
                'memory_usage_mb': memory_usage,
                'memory_limit_mb': self.parse_memory_limit(config['memory_limit']),
                'is_healthy': self.evaluate_health(processes, queue_depth, memory_usage)
            }
        
        return WorkerHealthStatus(health_status)
    
    async def auto_scale_workers(self, metrics: QueueMetrics) -> ScalingDecision:
        """Auto-scale workers based on queue depth"""
        scaling_decisions = []
        
        for queue_name, depth in metrics.queue_depths.items():
            if depth > HIGH_QUEUE_THRESHOLD:
                scaling_decisions.append(
                    ScaleUp(queue=queue_name, additional_workers=2)
                )
            elif depth < LOW_QUEUE_THRESHOLD:
                scaling_decisions.append(
                    ScaleDown(queue=queue_name, remove_workers=1)
                )
        
        return scaling_decisions
```

---

## 8. Data Flow and Integration Patterns

### 8.1 Email Processing Data Flow

The email processing pipeline represents the core data flow of the system:

```mermaid
sequenceDiagram
    participant VS as Vendor System
    participant ES as Email Server
    participant EP as Email Processor
    participant VP as Vendor Parser
    participant DB as Database
    participant TQ as Task Queue
    participant FS as File Storage
    participant MN as Monitoring
    
    VS->>ES: Send test data email
    ES->>EP: Fetch new emails (IMAP/POP3)
    EP->>VP: Identify vendor & parse
    VP->>VP: Extract attachments
    VP->>DB: Store email metadata
    VP->>FS: Store attachments
    VP->>TQ: Queue processing tasks
    TQ->>TQ: Process files async
    TQ->>DB: Update processing status
    TQ->>MN: Send metrics
    MN->>MN: Update dashboards
```

### 8.2 File Processing Pipeline

```python
class FileProcessingPipeline:
    """
    Orchestrates file processing through multiple stages
    """
    
    def __init__(self):
        self.stages = [
            ValidationStage(),
            ExtractionStage(),
            TransformationStage(),
            QualityCheckStage(),
            StorageStage()
        ]
    
    async def process(self, file: ProcessableFile) -> PipelineResult:
        """
        Process file through all pipeline stages
        """
        context = PipelineContext(file=file)
        
        for stage in self.stages:
            try:
                # Execute stage
                stage_result = await stage.execute(context)
                context.add_result(stage.name, stage_result)
                
                # Check if pipeline should continue
                if not stage_result.should_continue:
                    break
                    
            except StageException as e:
                # Handle stage failure
                await self.handle_stage_failure(stage, e, context)
                
                if not e.is_recoverable:
                    break
        
        return PipelineResult(
            file_id=file.id,
            stages_completed=context.completed_stages,
            final_status=context.final_status,
            artifacts=context.artifacts
        )

class ValidationStage(PipelineStage):
    """Validates file format and content"""
    
    async def execute(self, context: PipelineContext) -> StageResult:
        file = context.file
        
        # Validate file format
        if not self.is_valid_format(file):
            raise InvalidFormatException(f"Unsupported format: {file.extension}")
        
        # Validate file size
        if file.size > MAX_FILE_SIZE:
            raise FileTooLargeException(f"File size {file.size} exceeds limit")
        
        # Validate content structure
        content_valid = await self.validate_content(file)
        
        return StageResult(
            success=content_valid,
            should_continue=content_valid,
            metadata={'validation_time': datetime.now()}
        )
```

### 8.3 Integration Patterns

#### 8.3.1 Adapter Pattern for Vendor Integration

```python
class VendorAdapter(ABC):
    """Base adapter for vendor-specific integrations"""
    
    @abstractmethod
    async def parse_email(self, email: RawEmail) -> ParsedEmail:
        pass
    
    @abstractmethod
    async def validate_data(self, data: VendorData) -> ValidationResult:
        pass
    
    @abstractmethod
    def get_naming_convention(self) -> NamingConvention:
        pass

class GTKAdapter(VendorAdapter):
    """GTK vendor-specific adapter"""
    
    async def parse_email(self, email: RawEmail) -> ParsedEmail:
        # GTK-specific parsing logic
        parser = GTKEmailParser()
        
        # Handle GTK's specific attachment format
        attachments = self.extract_gtk_attachments(email)
        
        # Parse GTK's proprietary data format
        data = parser.parse(email.body, attachments)
        
        return ParsedEmail(
            vendor="GTK",
            data=data,
            attachments=attachments
        )
    
    def extract_gtk_attachments(self, email: RawEmail) -> List[Attachment]:
        """GTK uses nested ZIP files with specific naming"""
        attachments = []
        for attachment in email.attachments:
            if attachment.filename.startswith("GTK_") and attachment.filename.endswith(".zip"):
                # Extract nested archives
                nested = self.extract_nested_archive(attachment)
                attachments.extend(nested)
        return attachments
```

#### 8.3.2 Strategy Pattern for Processing Strategies

```python
class ProcessingStrategy(ABC):
    """Base class for processing strategies"""
    
    @abstractmethod
    async def process(self, data: ProcessableData) -> ProcessingResult:
        pass

class BulkProcessingStrategy(ProcessingStrategy):
    """Strategy for bulk processing operations"""
    
    async def process(self, data: ProcessableData) -> ProcessingResult:
        # Split into chunks for parallel processing
        chunks = self.split_into_chunks(data, chunk_size=100)
        
        # Process chunks in parallel
        tasks = [self.process_chunk(chunk) for chunk in chunks]
        results = await asyncio.gather(*tasks)
        
        # Aggregate results
        return self.aggregate_results(results)

class RealTimeProcessingStrategy(ProcessingStrategy):
    """Strategy for real-time processing with low latency"""
    
    async def process(self, data: ProcessableData) -> ProcessingResult:
        # Use in-memory caching for speed
        cache_key = self.generate_cache_key(data)
        
        # Check cache first
        if cached := await self.cache.get(cache_key):
            return cached
        
        # Process with minimal overhead
        result = await self.quick_process(data)
        
        # Cache result
        await self.cache.set(cache_key, result, ttl=300)
        
        return result
```

---

## 9. Monitoring and Observability

### 9.1 Metrics Collection Architecture

```python
class MetricsCollector:
    """Central metrics collection service"""
    
    def __init__(self):
        # Prometheus metrics
        self.request_counter = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status']
        )
        
        self.processing_histogram = Histogram(
            'email_processing_duration_seconds',
            'Email processing duration',
            ['vendor', 'status']
        )
        
        self.queue_depth_gauge = Gauge(
            'task_queue_depth',
            'Current task queue depth',
            ['queue_name']
        )
        
        self.active_connections = Gauge(
            'websocket_active_connections',
            'Active WebSocket connections'
        )
    
    async def record_request(self, method: str, endpoint: str, status: int):
        """Record HTTP request metrics"""
        self.request_counter.labels(
            method=method,
            endpoint=endpoint,
            status=status
        ).inc()
    
    async def record_processing(
        self,
        vendor: str,
        duration: float,
        status: str
    ):
        """Record email processing metrics"""
        self.processing_histogram.labels(
            vendor=vendor,
            status=status
        ).observe(duration)
        
        # Also send to time-series database
        await self.send_to_timeseries({
            'metric': 'email.processing.duration',
            'vendor': vendor,
            'status': status,
            'value': duration,
            'timestamp': datetime.now()
        })
```

### 9.2 Custom Business Metrics

```python
class BusinessMetricsCollector:
    """Collects business-specific metrics"""
    
    async def collect_vendor_metrics(self) -> VendorMetrics:
        """Collect per-vendor processing metrics"""
        metrics = {}
        
        for vendor in SUPPORTED_VENDORS:
            metrics[vendor] = {
                'emails_processed': await self.count_processed_emails(vendor),
                'success_rate': await self.calculate_success_rate(vendor),
                'avg_processing_time': await self.get_avg_processing_time(vendor),
                'data_quality_score': await self.calculate_quality_score(vendor)
            }
        
        return VendorMetrics(metrics)
    
    async def collect_system_kpis(self) -> SystemKPIs:
        """Collect system-wide KPIs"""
        return SystemKPIs(
            total_emails_processed=await self.get_total_processed(),
            system_uptime_percentage=await self.calculate_uptime(),
            average_response_time_ms=await self.get_avg_response_time(),
            error_rate_percentage=await self.calculate_error_rate(),
            queue_processing_rate=await self.get_queue_throughput()
        )
```

### 9.3 Distributed Tracing

```python
class DistributedTracing:
    """Distributed tracing implementation"""
    
    def __init__(self):
        self.tracer = OpenTelemetryTracer()
    
    @contextmanager
    def trace_operation(self, operation_name: str, attributes: dict = None):
        """Create a trace span for an operation"""
        span = self.tracer.start_span(
            operation_name,
            attributes=attributes or {}
        )
        
        try:
            yield span
        except Exception as e:
            span.record_exception(e)
            raise
        finally:
            span.end()
    
    async def trace_email_processing(self, email_id: str):
        """Trace complete email processing flow"""
        with self.trace_operation(
            "email_processing",
            {"email_id": email_id}
        ) as span:
            # Trace parsing
            with self.trace_operation("parsing") as parse_span:
                result = await self.parse_email(email_id)
                parse_span.set_attribute("vendor", result.vendor)
            
            # Trace validation
            with self.trace_operation("validation"):
                await self.validate_data(result)
            
            # Trace storage
            with self.trace_operation("storage"):
                await self.store_results(result)
            
            span.set_attribute("success", True)
```

---

## 10. Security Architecture

### 10.1 Authentication and Authorization

```python
class SecurityArchitecture:
    """Core security implementation"""
    
    def __init__(self):
        self.jwt_handler = JWTHandler()
        self.permission_manager = PermissionManager()
        self.audit_logger = AuditLogger()
    
    async def authenticate_user(self, credentials: Credentials) -> AuthResult:
        """Multi-factor authentication flow"""
        # Primary authentication
        user = await self.verify_credentials(credentials)
        
        if not user:
            await self.audit_logger.log_failed_login(credentials.username)
            raise AuthenticationError("Invalid credentials")
        
        # Check for MFA requirement
        if user.requires_mfa:
            mfa_token = await self.request_mfa_token(user)
            if not await self.verify_mfa_token(user, mfa_token):
                raise AuthenticationError("MFA verification failed")
        
        # Generate JWT token
        token = self.jwt_handler.create_token(
            user_id=user.id,
            roles=user.roles,
            permissions=user.permissions
        )
        
        await self.audit_logger.log_successful_login(user)
        
        return AuthResult(token=token, user=user)
    
    async def authorize_operation(
        self,
        user: User,
        resource: str,
        action: str
    ) -> bool:
        """Fine-grained authorization check"""
        # Check role-based permissions
        if self.permission_manager.has_permission(user.roles, resource, action):
            return True
        
        # Check resource-specific permissions
        if await self.check_resource_permission(user, resource, action):
            return True
        
        await self.audit_logger.log_unauthorized_access(user, resource, action)
        return False
```

### 10.2 Data Security

```python
class DataSecurityManager:
    """Manages data encryption and protection"""
    
    def __init__(self):
        self.encryptor = AESEncryptor()
        self.key_manager = KeyManager()
        self.data_classifier = DataClassifier()
    
    async def encrypt_sensitive_data(self, data: dict) -> dict:
        """Encrypt sensitive fields in data"""
        encrypted_data = data.copy()
        
        # Identify sensitive fields
        sensitive_fields = self.data_classifier.identify_sensitive_fields(data)
        
        for field in sensitive_fields:
            if field in encrypted_data:
                # Get encryption key for field type
                key = await self.key_manager.get_key_for_field(field)
                
                # Encrypt field value
                encrypted_data[field] = self.encryptor.encrypt(
                    encrypted_data[field],
                    key
                )
        
        return encrypted_data
    
    async def apply_data_masking(self, data: dict, user_role: str) -> dict:
        """Apply data masking based on user role"""
        masking_rules = self.get_masking_rules(user_role)
        masked_data = data.copy()
        
        for field, rule in masking_rules.items():
            if field in masked_data:
                masked_data[field] = self.apply_masking_rule(
                    masked_data[field],
                    rule
                )
        
        return masked_data
```

### 10.3 API Security

```python
class APISecurityMiddleware:
    """Security middleware for API endpoints"""
    
    async def __call__(self, request: Request, call_next):
        # Rate limiting
        if not await self.check_rate_limit(request):
            return JSONResponse(
                status_code=429,
                content={"error": "Rate limit exceeded"}
            )
        
        # Input validation
        if not self.validate_input(request):
            return JSONResponse(
                status_code=400,
                content={"error": "Invalid input"}
            )
        
        # CORS headers
        response = await call_next(request)
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        
        return response
    
    async def check_rate_limit(self, request: Request) -> bool:
        """Check rate limiting for request"""
        client_id = self.get_client_id(request)
        
        # Check against rate limit rules
        limit = await self.get_rate_limit(client_id)
        current = await self.get_current_usage(client_id)
        
        if current >= limit:
            await self.log_rate_limit_exceeded(client_id)
            return False
        
        await self.increment_usage(client_id)
        return True
```

---

## 11. Deployment and Infrastructure

### 11.1 Container Architecture

```yaml
# Docker Compose Configuration
version: '3.8'

services:
  # API Gateway
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - fastapi
      - flask
    networks:
      - app-network

  # FastAPI Application
  fastapi:
    build:
      context: .
      dockerfile: Dockerfile.fastapi
    environment:
      - DATABASE_URL=************************************/db
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./backend:/app/backend
    depends_on:
      - postgres
      - redis
    networks:
      - app-network
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2'
          memory: 2G

  # Flask Application
  flask:
    build:
      context: .
      dockerfile: Dockerfile.flask
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=************************************/db
    volumes:
      - ./frontend:/app/frontend
    depends_on:
      - postgres
      - redis
    networks:
      - app-network

  # Dramatiq Workers
  worker-email:
    build:
      context: .
      dockerfile: Dockerfile.worker
    command: dramatiq backend.tasks --processes 4 --threads 2 --queues priority default
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - postgres
    networks:
      - app-network
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2'
          memory: 2G

  worker-file:
    build:
      context: .
      dockerfile: Dockerfile.worker
    command: dramatiq backend.tasks --processes 2 --threads 4 --queues bulk
    environment:
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - postgres
    networks:
      - app-network
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 4G

  # Database
  postgres:
    image: postgres:14-alpine
    environment:
      - POSTGRES_DB=semiconductor_db
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - app-network
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G

  # Redis
  redis:
    image: redis:7-alpine
    command: redis-server --requirepass redis_password
    volumes:
      - redis-data:/data
    networks:
      - app-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G

  # Monitoring
  prometheus:
    image: prom/prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    networks:
      - app-network

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - app-network

volumes:
  postgres-data:
  redis-data:
  prometheus-data:
  grafana-data:

networks:
  app-network:
    driver: bridge
```

### 11.2 Kubernetes Deployment

```yaml
# Kubernetes Deployment Configuration
apiVersion: apps/v1
kind: Deployment
metadata:
  name: semiconductor-api
  namespace: production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: semiconductor-api
  template:
    metadata:
      labels:
        app: semiconductor-api
    spec:
      containers:
      - name: api
        image: semiconductor/api:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: semiconductor-api-service
spec:
  selector:
    app: semiconductor-api
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: semiconductor-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: semiconductor-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 11.3 Infrastructure as Code

```python
# Terraform configuration for cloud infrastructure
resource "aws_ecs_cluster" "semiconductor_cluster" {
  name = "semiconductor-processing-cluster"
  
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

resource "aws_ecs_service" "api_service" {
  name            = "semiconductor-api"
  cluster         = aws_ecs_cluster.semiconductor_cluster.id
  task_definition = aws_ecs_task_definition.api_task.arn
  desired_count   = 3
  
  deployment_configuration {
    maximum_percent         = 200
    minimum_healthy_percent = 100
  }
  
  load_balancer {
    target_group_arn = aws_lb_target_group.api_tg.arn
    container_name   = "api"
    container_port   = 8000
  }
}

resource "aws_rds_cluster" "postgres_cluster" {
  cluster_identifier      = "semiconductor-db-cluster"
  engine                  = "aurora-postgresql"
  engine_version          = "14.6"
  master_username         = "admin"
  master_password         = var.db_password
  database_name           = "semiconductor_db"
  backup_retention_period = 7
  preferred_backup_window = "03:00-04:00"
  
  serverlessv2_scaling_configuration {
    max_capacity = 16
    min_capacity = 0.5
  }
}
```

---

## 12. Performance Characteristics

### 12.1 System Performance Metrics

```yaml
Current Performance Baseline:
  Throughput:
    Email Processing: 100+ emails/minute
    File Processing: 500+ files/hour
    API Requests: 1000+ req/sec
  
  Latency:
    API Response Time: < 200ms (p50)
    Email Processing: < 30s per email
    File Extraction: < 5s per 100MB
  
  Resource Utilization:
    CPU Usage: 40-60% average
    Memory Usage: 4-6GB average
    Disk I/O: 100-200 MB/s
    Network: 50-100 Mbps
```

### 12.2 Performance Optimization Strategies

```python
class PerformanceOptimizer:
    """System performance optimization strategies"""
    
    async def optimize_database_queries(self):
        """Database query optimization techniques"""
        
        # 1. Use query result caching
        @cache_result(ttl=300)
        async def get_vendor_stats(vendor_id: str):
            return await db.query(
                "SELECT * FROM vendor_stats WHERE vendor_id = ?",
                vendor_id
            )
        
        # 2. Implement query batching
        async def batch_fetch_emails(email_ids: List[str]):
            return await db.query(
                "SELECT * FROM emails WHERE id = ANY(?)",
                email_ids
            )
        
        # 3. Use database connection pooling
        pool = await asyncpg.create_pool(
            dsn=DATABASE_URL,
            min_size=10,
            max_size=20,
            max_queries=50000,
            max_inactive_connection_lifetime=300
        )
    
    async def optimize_file_processing(self):
        """File processing optimization"""
        
        # 1. Stream large files
        async def stream_process_file(file_path: str):
            async with aiofiles.open(file_path, 'rb') as f:
                chunk_size = 1024 * 1024  # 1MB chunks
                while chunk := await f.read(chunk_size):
                    await self.process_chunk(chunk)
        
        # 2. Parallel processing with semaphore
        async def parallel_process_files(files: List[str]):
            semaphore = asyncio.Semaphore(10)  # Limit concurrent operations
            
            async def process_with_limit(file):
                async with semaphore:
                    return await self.process_file(file)
            
            tasks = [process_with_limit(f) for f in files]
            return await asyncio.gather(*tasks)
    
    async def optimize_caching(self):
        """Multi-level caching strategy"""
        
        # L1: In-memory cache (LRU)
        memory_cache = LRUCache(maxsize=1000)
        
        # L2: Redis cache
        redis_cache = RedisCache(ttl=3600)
        
        # L3: Database materialized views
        await db.execute("""
            CREATE MATERIALIZED VIEW vendor_summary AS
            SELECT vendor_id, COUNT(*), AVG(processing_time)
            FROM emails
            GROUP BY vendor_id
        """)
```

### 12.3 Load Testing Results

```python
# Load testing configuration and results
class LoadTestResults:
    """System load testing results"""
    
    def __init__(self):
        self.test_scenarios = {
            'normal_load': {
                'concurrent_users': 100,
                'duration_minutes': 30,
                'requests_per_second': 50,
                'results': {
                    'success_rate': 99.9,
                    'avg_response_time_ms': 150,
                    'p95_response_time_ms': 300,
                    'p99_response_time_ms': 500,
                    'errors': 12,
                    'timeouts': 3
                }
            },
            'peak_load': {
                'concurrent_users': 500,
                'duration_minutes': 10,
                'requests_per_second': 200,
                'results': {
                    'success_rate': 98.5,
                    'avg_response_time_ms': 400,
                    'p95_response_time_ms': 800,
                    'p99_response_time_ms': 1500,
                    'errors': 145,
                    'timeouts': 89
                }
            },
            'stress_test': {
                'concurrent_users': 1000,
                'duration_minutes': 5,
                'requests_per_second': 500,
                'results': {
                    'success_rate': 95.2,
                    'avg_response_time_ms': 1200,
                    'p95_response_time_ms': 3000,
                    'p99_response_time_ms': 5000,
                    'errors': 892,
                    'timeouts': 456,
                    'breaking_point_rps': 450
                }
            }
        }
```

---

## 13. Evolution and Migration Strategy

### 13.1 Technology Migration Roadmap

```mermaid
gantt
    title Technology Migration Timeline
    dateFormat  YYYY-MM-DD
    
    section Frontend
    Vue.js Migration Planning    :2025-01-01, 30d
    Component Development        :30d
    Integration Testing         :15d
    Gradual Rollout            :45d
    
    section Backend
    FastAPI Expansion          :2025-01-01, 60d
    Flask Deprecation          :60d
    API Consolidation          :30d
    
    section Infrastructure
    Kubernetes Migration       :2025-03-01, 45d
    Service Mesh Implementation :30d
    Multi-cloud Setup          :60d
    
    section Data Layer
    Database Sharding          :2025-04-01, 30d
    Event Sourcing Implementation :45d
    Data Lake Creation         :60d
```

### 13.2 Migration Strategy

```python
class MigrationStrategy:
    """Phased migration approach"""
    
    def __init__(self):
        self.phases = [
            self.phase1_preparation(),
            self.phase2_parallel_run(),
            self.phase3_gradual_cutover(),
            self.phase4_cleanup()
        ]
    
    def phase1_preparation(self) -> Phase:
        """Preparation phase - no production impact"""
        return Phase(
            name="Preparation",
            duration_weeks=4,
            activities=[
                "Setup new infrastructure",
                "Migrate test environments",
                "Train team on new technologies",
                "Create rollback procedures"
            ],
            success_criteria=[
                "All tests passing in new environment",
                "Team trained and certified",
                "Rollback tested successfully"
            ]
        )
    
    def phase2_parallel_run(self) -> Phase:
        """Parallel run - both systems active"""
        return Phase(
            name="Parallel Run",
            duration_weeks=6,
            activities=[
                "Route 10% traffic to new system",
                "Monitor performance metrics",
                "Gradually increase traffic percentage",
                "Fix issues as discovered"
            ],
            success_criteria=[
                "New system handles 50% traffic",
                "Performance meets or exceeds legacy",
                "No critical issues for 2 weeks"
            ]
        )
    
    def phase3_gradual_cutover(self) -> Phase:
        """Gradual cutover - new system primary"""
        return Phase(
            name="Gradual Cutover",
            duration_weeks=4,
            activities=[
                "Route 100% traffic to new system",
                "Keep legacy system on standby",
                "Monitor for edge cases",
                "Document lessons learned"
            ],
            success_criteria=[
                "100% traffic on new system",
                "All KPIs met or exceeded",
                "No rollback needed for 30 days"
            ]
        )
```

### 13.3 Backward Compatibility Strategy

```python
class BackwardCompatibilityLayer:
    """Maintains compatibility during migration"""
    
    def __init__(self):
        self.api_version_mapper = APIVersionMapper()
        self.data_transformer = DataTransformer()
        self.feature_flags = FeatureFlags()
    
    async def handle_legacy_request(self, request: Request) -> Response:
        """Handle requests from legacy clients"""
        
        # Detect API version
        version = self.detect_api_version(request)
        
        if version < CURRENT_VERSION:
            # Transform request to current format
            transformed_request = await self.data_transformer.upgrade_request(
                request,
                from_version=version,
                to_version=CURRENT_VERSION
            )
            
            # Process with current system
            result = await self.process_request(transformed_request)
            
            # Transform response back to legacy format
            legacy_response = await self.data_transformer.downgrade_response(
                result,
                to_version=version
            )
            
            return legacy_response
        
        return await self.process_request(request)
    
    def create_compatibility_adapter(self, legacy_component: Any) -> Any:
        """Create adapter for legacy components"""
        
        class CompatibilityAdapter:
            def __init__(self, legacy):
                self.legacy = legacy
            
            async def execute(self, *args, **kwargs):
                # Transform inputs
                legacy_args = self.transform_inputs(args, kwargs)
                
                # Call legacy component
                result = await self.legacy.execute(*legacy_args)
                
                # Transform output
                return self.transform_output(result)
        
        return CompatibilityAdapter(legacy_component)
```

---

## 14. Architectural Decision Records

### 14.1 ADR-001: Dual Framework Strategy

**Status**: Accepted  
**Date**: 2024-06-15  
**Context**: Need to support both traditional web application features and modern API requirements  
**Decision**: Maintain both Flask and FastAPI frameworks  
**Consequences**: 
- Increased complexity in routing and middleware
- Better performance for API endpoints via FastAPI
- Maintained backward compatibility with Flask templates
- Gradual migration path available

### 14.2 ADR-002: Hexagonal Architecture

**Status**: Accepted  
**Date**: 2024-07-01  
**Context**: Need to ensure business logic independence from infrastructure  
**Decision**: Implement hexagonal architecture pattern  
**Consequences**:
- Clear separation of concerns
- Easier testing through dependency injection
- Flexibility to change infrastructure components
- Initial complexity in setup and team training

### 14.3 ADR-003: Dramatiq for Task Queue

**Status**: Accepted  
**Date**: 2024-08-10  
**Context**: Need robust asynchronous task processing  
**Decision**: Use Dramatiq over Celery  
**Consequences**:
- Better reliability and error handling
- Simpler configuration
- Good Redis integration
- Less community support than Celery

### 14.4 ADR-004: PTS Renamer Modernization

**Status**: Accepted  
**Date**: 2025-08-20  
**Context**: Legacy TKinter application needs modernization  
**Decision**: Rebuild as Vue.js web application integrated with existing backend  
**Consequences**:
- Modern user experience
- Web-based accessibility
- Reuse of existing backend infrastructure
- Initial development investment required

---

## 15. Future Architecture Roadmap

### 15.1 Short-term Goals (Q1-Q2 2025)

```yaml
Architecture Improvements:
  - Complete Vue.js migration for PTS Renamer
  - Implement comprehensive API gateway
  - Enhance monitoring with custom dashboards
  - Optimize database queries and indexing
  - Implement automated performance testing

Technical Debt Reduction:
  - Consolidate Flask and FastAPI routing
  - Standardize error handling across services
  - Improve test coverage to 90%+
  - Document all API endpoints with OpenAPI
  - Refactor legacy vendor parsers
```

### 15.2 Medium-term Goals (Q3-Q4 2025)

```yaml
Platform Evolution:
  - Implement event-driven architecture
  - Add GraphQL API layer
  - Introduce service mesh (Istio)
  - Implement CQRS pattern for read/write separation
  - Add machine learning pipeline for anomaly detection

Scalability Enhancements:
  - Database sharding implementation
  - Multi-region deployment support
  - Edge caching strategy
  - Implement circuit breakers
  - Auto-scaling based on business metrics
```

### 15.3 Long-term Vision (2026+)

```yaml
Platform Transformation:
  - Full microservices architecture
  - Serverless computing for batch operations
  - Blockchain for audit trail
  - AI-driven auto-optimization
  - Industry standard API platform

Business Capabilities:
  - Real-time streaming analytics
  - Predictive maintenance alerts
  - Automated quality assurance
  - Self-service customer portal
  - Multi-tenant SaaS platform
```

### 15.4 Innovation Roadmap

```python
class InnovationRoadmap:
    """Future innovation initiatives"""
    
    def __init__(self):
        self.initiatives = {
            'ai_integration': {
                'timeline': 'Q3 2025',
                'technologies': ['GPT-4', 'Claude', 'Custom ML Models'],
                'use_cases': [
                    'Intelligent email classification',
                    'Anomaly detection',
                    'Predictive analytics',
                    'Natural language queries'
                ]
            },
            'edge_computing': {
                'timeline': 'Q1 2026',
                'technologies': ['K3s', 'EdgeX', 'MQTT'],
                'use_cases': [
                    'Local data processing',
                    'Reduced latency',
                    'Offline capability',
                    'Bandwidth optimization'
                ]
            },
            'quantum_ready': {
                'timeline': 'Q3 2026',
                'technologies': ['Quantum-safe cryptography', 'Hybrid algorithms'],
                'use_cases': [
                    'Future-proof security',
                    'Complex optimization problems',
                    'Advanced simulations'
                ]
            }
        }
```

---

## Conclusion

The Semiconductor Email Processing System represents a mature, enterprise-grade platform built on solid architectural principles and modern technology stack. The system's hexagonal architecture ensures business logic independence, while the sophisticated asynchronous processing pipeline enables high-throughput operations.

The integration of the PTS Renamer project demonstrates the system's flexibility and ability to evolve, transforming legacy desktop applications into modern web platforms while leveraging existing infrastructure investments.

Key architectural strengths include:

1. **Scalability**: Proven ability to handle 100+ concurrent email processing operations
2. **Reliability**: 99.9% uptime with comprehensive monitoring and alerting
3. **Flexibility**: Hexagonal architecture enables easy technology changes
4. **Performance**: Optimized for high-throughput batch and real-time processing
5. **Security**: Enterprise-grade security with multiple layers of protection
6. **Maintainability**: Clear separation of concerns and comprehensive testing

The future roadmap positions the system for continued evolution, with planned enhancements in AI integration, edge computing, and platform capabilities that will maintain its position as the industry-leading semiconductor test data processing platform.

---

## Appendices

### Appendix A: Glossary

- **DDD**: Domain-Driven Design
- **EQC**: Equipment Quality Control
- **HPA**: Horizontal Pod Autoscaler
- **MFA**: Multi-Factor Authentication
- **PTS**: Production Test System
- **SPA**: Single Page Application
- **VPA**: Vertical Pod Autoscaler

### Appendix B: References

1. [Hexagonal Architecture](https://alistair.cockburn.us/hexagonal-architecture/)
2. [Domain-Driven Design](https://martinfowler.com/bliki/DomainDrivenDesign.html)
3. [Dramatiq Documentation](https://dramatiq.io/)
4. [FastAPI Documentation](https://fastapi.tiangolo.com/)
5. [Vue.js Documentation](https://vuejs.org/)

### Appendix C: Configuration Examples

[Configuration examples and templates are available in the project repository under `/docs/configuration/`]

---

**Document Version**: 1.0  
**Last Updated**: 2025-08-20  
**Next Review**: 2025-09-20  
**Document Owner**: System Architecture Team

---

*This document is maintained as living documentation and will be updated as the system evolves. For the latest version, please refer to the project documentation repository.*