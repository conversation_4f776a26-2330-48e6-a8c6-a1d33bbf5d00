#!/usr/bin/env python3
"""
Parser API Security Test Script
Tests all security fixes implemented in parser_api.py
"""

import requests
import json
import os
from typing import Dict, Any

# Test configuration
BASE_URL = "http://localhost:8000/api/parser"
VALID_API_KEY = os.environ.get('PARSER_API_KEY', 'dev-parser-key-12345')
INVALID_API_KEY = "invalid-key-12345"

def test_authentication():
    """Test API key authentication"""
    print("🔐 Testing Authentication...")
    
    # Test without API key
    response = requests.get(f"{BASE_URL}/statistics")
    assert response.status_code == 401, f"Expected 401, got {response.status_code}"
    data = response.json()
    assert data['code'] == 'MISSING_API_KEY', f"Expected MISSING_API_KEY, got {data.get('code')}"
    print("  ✅ Missing API key properly rejected")
    
    # Test with invalid API key
    headers = {'X-API-Key': INVALID_API_KEY}
    response = requests.get(f"{BASE_URL}/statistics", headers=headers)
    assert response.status_code == 401, f"Expected 401, got {response.status_code}"
    data = response.json()
    assert data['code'] == 'INVALID_API_KEY', f"Expected INVALID_API_KEY, got {data.get('code')}"
    print("  ✅ Invalid API key properly rejected")
    
    # Test with valid API key
    headers = {'X-API-Key': VALID_API_KEY}
    response = requests.get(f"{BASE_URL}/statistics", headers=headers)
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    print("  ✅ Valid API key accepted")

def test_input_validation():
    """Test input validation"""
    print("🔍 Testing Input Validation...")
    
    headers = {'X-API-Key': VALID_API_KEY, 'Content-Type': 'application/json'}
    
    # Test invalid email ID
    response = requests.post(f"{BASE_URL}/emails/0/reparse", headers=headers)
    assert response.status_code == 400, f"Expected 400, got {response.status_code}"
    data = response.json()
    assert data['code'] == 'INVALID_EMAIL_ID', f"Expected INVALID_EMAIL_ID, got {data.get('code')}"
    print("  ✅ Invalid email ID rejected")
    
    # Test invalid JSON payload
    response = requests.post(f"{BASE_URL}/test", headers=headers)
    assert response.status_code == 400, f"Expected 400, got {response.status_code}"
    print("  ✅ Missing JSON payload rejected")
    
    # Test invalid vendor code in manual input
    invalid_data = {
        'vendor_code': '<script>alert("xss")</script>',  # Should be sanitized
        'pd': 'TEST123',
        'lot': 'LOT001'
    }
    response = requests.post(f"{BASE_URL}/emails/1/manual-input", 
                           headers=headers, json=invalid_data)
    assert response.status_code == 400, f"Expected 400, got {response.status_code}"
    print("  ✅ Invalid vendor code format rejected")
    
    # Test invalid yield value
    invalid_data = {
        'vendor_code': 'TEST',
        'pd': 'TEST123',
        'lot': 'LOT001',
        'yield_value': 150  # Should be 0-100
    }
    response = requests.post(f"{BASE_URL}/emails/1/manual-input", 
                           headers=headers, json=invalid_data)
    assert response.status_code == 400, f"Expected 400, got {response.status_code}"
    print("  ✅ Invalid yield value rejected")

def test_security_headers():
    """Test security headers"""
    print("🛡️ Testing Security Headers...")
    
    headers = {'X-API-Key': VALID_API_KEY}
    response = requests.get(f"{BASE_URL}/statistics", headers=headers)
    
    # Check for security headers
    security_headers = [
        'X-Content-Type-Options',
        'X-Frame-Options',
        'X-XSS-Protection'
    ]
    
    for header in security_headers:
        assert header in response.headers, f"Missing security header: {header}"
    
    assert response.headers['X-Content-Type-Options'] == 'nosniff'
    assert response.headers['X-Frame-Options'] == 'DENY'
    assert response.headers['X-XSS-Protection'] == '1; mode=block'
    
    print("  ✅ All security headers present")

def test_cors_handling():
    """Test CORS headers"""
    print("🌐 Testing CORS Headers...")
    
    # Test OPTIONS request
    response = requests.options(f"{BASE_URL}/statistics")
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    
    # Check CORS headers
    cors_headers = [
        'Access-Control-Allow-Origin',
        'Access-Control-Allow-Headers',
        'Access-Control-Allow-Methods'
    ]
    
    for header in cors_headers:
        assert header in response.headers, f"Missing CORS header: {header}"
    
    print("  ✅ CORS headers properly configured")

def test_error_handling():
    """Test standardized error responses"""
    print("🚨 Testing Error Handling...")
    
    # Test 404 error
    headers = {'X-API-Key': VALID_API_KEY}
    response = requests.get(f"{BASE_URL}/emails/99999/llm-analysis", headers=headers)
    assert response.status_code in [404, 400], f"Expected 404 or 400, got {response.status_code}"
    
    data = response.json()
    assert 'success' in data and data['success'] == False
    assert 'error' in data
    assert 'code' in data
    
    print("  ✅ Error responses properly formatted")

def test_llm_mock_removal():
    """Test that MockLLMResult is no longer used"""
    print("🤖 Testing LLM Mock Removal...")
    
    headers = {'X-API-Key': VALID_API_KEY}
    response = requests.get(f"{BASE_URL}/emails/1/llm-analysis", headers=headers)
    
    # Should return proper error when LLM analysis not available
    if response.status_code == 404:
        data = response.json()
        assert data['code'] == 'LLM_ANALYSIS_UNAVAILABLE'
        print("  ✅ MockLLMResult removed - proper error handling implemented")
    else:
        print("  ℹ️  LLM analysis available for test email")

def run_security_tests():
    """Run all security tests"""
    print("🔒 Parser API Security Test Suite")
    print("=" * 50)
    
    try:
        test_authentication()
        test_input_validation()
        test_security_headers()
        test_cors_handling()
        test_error_handling()
        test_llm_mock_removal()
        
        print("\n" + "=" * 50)
        print("✅ All security tests passed!")
        print("🛡️ Parser API is now production-ready")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        print("🔧 Please check the API server and fix any issues")
        return False
    
    return True

if __name__ == "__main__":
    # Check if API server is running
    try:
        response = requests.get(f"{BASE_URL.replace('/api/parser', '')}/health", timeout=5)
    except requests.RequestException:
        print("❌ API server is not running")
        print("💡 Please start the server first: python start_integrated_services.py")
        exit(1)
    
    # Set development mode for testing if needed
    print(f"🔑 Using API key: {VALID_API_KEY[:10]}...")
    
    success = run_security_tests()
    exit(0 if success else 1)