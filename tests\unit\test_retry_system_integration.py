"""
Story 1.3: EmailDownloadRetryLogDB 系統整合測試
測試完整的重試系統，包括 Repository、Service 和性能驗證
"""

import pytest
import time
import statistics
from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from backend.shared.infrastructure.adapters.database.models import Base, EmailDB
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    EmailDownloadStatusDB, EmailDownloadRetryLogDB, RetryStrategy, RetryStatus, DownloadStatus
)
from backend.shared.infrastructure.adapters.database.retry_repository import RetryLogRepository
from backend.shared.services.retry_service import RetryService, RetryCalculator


class TestRetrySystemIntegration:
    """重試系統整合測試"""

    def setup_method(self):
        """設置測試環境"""
        self.engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(self.engine)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.session = self.SessionLocal()
        self.repository = RetryLogRepository(self.session)
        self.service = RetryService(self.session)
        
        # 創建測試數據
        self._create_test_data()

    def teardown_method(self):
        """清理測試環境"""
        self.session.close()
        Base.metadata.drop_all(self.engine)

    def _create_test_data(self):
        """創建測試數據"""
        # 創建測試郵件
        self.test_email = EmailDB(
            message_id="<EMAIL>",
            sender="<EMAIL>",
            subject="Integration Test Email",
            body="Test Body",
            received_time=datetime.utcnow()
        )
        self.session.add(self.test_email)
        self.session.commit()

        # 創建下載狀態
        self.download_status = EmailDownloadStatusDB(
            email_id=self.test_email.id,
            status=DownloadStatus.FAILED
        )
        self.session.add(self.download_status)
        self.session.commit()

    # =============== Repository 測試 ===============

    def test_repository_crud_operations(self):
        """測試 Repository 的完整 CRUD 操作"""
        # Create
        retry_data = {
            'download_status_id': self.download_status.id,
            'email_id': self.test_email.id,
            'retry_attempt': 1,
            'retry_strategy': RetryStrategy.EXPONENTIAL,
            'scheduled_at': datetime.utcnow(),
            'retry_delay_seconds': 60
        }
        
        created_log = self.repository.create_retry_log(retry_data)
        assert created_log.id is not None
        assert created_log.retry_attempt == 1
        assert created_log.status == RetryStatus.SCHEDULED

        # Read
        retrieved_log = self.repository.get_retry_log_by_id(created_log.id)
        assert retrieved_log is not None
        assert retrieved_log.email_id == self.test_email.id

        # Update
        success = self.repository.update_retry_status(
            created_log.id, 
            RetryStatus.SUCCESS,
            {'duration_ms': 5000}
        )
        assert success is True
        
        updated_log = self.repository.get_retry_log_by_id(created_log.id)
        assert updated_log.status == RetryStatus.SUCCESS
        assert updated_log.success is True
        assert updated_log.duration_ms == 5000

        # Delete
        delete_success = self.repository.delete_retry_log(created_log.id)
        assert delete_success is True
        
        deleted_log = self.repository.get_retry_log_by_id(created_log.id)
        assert deleted_log is None

    def test_repository_bulk_operations(self):
        """測試 Repository 的批量操作"""
        # 批量創建
        retry_logs_data = []
        for i in range(1, 6):  # 創建5個重試記錄
            retry_logs_data.append({
                'download_status_id': self.download_status.id,
                'email_id': self.test_email.id,
                'retry_attempt': i,
                'retry_strategy': RetryStrategy.LINEAR,
                'scheduled_at': datetime.utcnow() + timedelta(minutes=i),
                'retry_delay_seconds': 60 * i
            })
        
        created_logs = self.repository.bulk_create_retry_logs(retry_logs_data)
        assert len(created_logs) == 5
        
        # 批量更新
        retry_ids = [log.id for log in created_logs]
        updated_count = self.repository.bulk_update_status(retry_ids, RetryStatus.CANCELLED)
        assert updated_count == 5
        
        # 驗證批量更新
        for retry_id in retry_ids:
            log = self.repository.get_retry_log_by_id(retry_id)
            assert log.status == RetryStatus.CANCELLED

    def test_repository_advanced_queries(self):
        """測試 Repository 的高級查詢功能"""
        # 創建不同狀態的重試記錄
        statuses = [RetryStatus.SCHEDULED, RetryStatus.RUNNING, RetryStatus.SUCCESS, RetryStatus.FAILED]
        created_logs = []
        
        for i, status in enumerate(statuses, 1):
            retry_data = {
                'download_status_id': self.download_status.id,
                'email_id': self.test_email.id,
                'retry_attempt': i,
                'retry_strategy': RetryStrategy.EXPONENTIAL,
                'scheduled_at': datetime.utcnow(),
                'retry_delay_seconds': 60,
                'status': status,
                'success': (status == RetryStatus.SUCCESS)
            }
            log = self.repository.create_retry_log(retry_data)
            created_logs.append(log)

        # 測試按狀態查詢
        scheduled_logs = self.repository.get_retry_logs_by_status(RetryStatus.SCHEDULED)
        assert len(scheduled_logs) == 1
        assert scheduled_logs[0].status == RetryStatus.SCHEDULED

        # 測試獲取調度的重試
        scheduled_retries = self.repository.get_scheduled_retries()
        assert len(scheduled_retries) == 1

        # 測試統計信息
        stats = self.repository.get_retry_statistics(self.test_email.id)
        assert stats['total_retries'] == 4
        assert stats['success_rate'] == 0.25  # 1 success out of 4
        assert 'status_distribution' in stats
        assert stats['status_distribution'][RetryStatus.SUCCESS.value] == 1

    # =============== Service 測試 ===============

    def test_service_retry_scheduling(self):
        """測試 Service 的重試調度功能"""
        # 調度重試
        retry_log = self.service.schedule_retry(
            download_status_id=self.download_status.id,
            email_id=self.test_email.id,
            strategy=RetryStrategy.EXPONENTIAL,
            base_delay=60
        )
        
        assert retry_log is not None
        assert retry_log.retry_attempt == 1
        assert retry_log.status == RetryStatus.SCHEDULED
        assert retry_log.retry_strategy == RetryStrategy.EXPONENTIAL
        
        # 驗證下載狀態已更新
        self.session.refresh(self.download_status)
        assert self.download_status.status == DownloadStatus.RETRY_SCHEDULED
        assert self.download_status.next_retry_at is not None

    def test_service_retry_execution(self):
        """測試 Service 的重試執行功能"""
        # 調度重試
        retry_log = self.service.schedule_retry(
            download_status_id=self.download_status.id,
            email_id=self.test_email.id,
            strategy=RetryStrategy.FIXED_DELAY,
            base_delay=0  # 立即可執行
        )
        
        # 執行重試
        success, error = self.service.execute_retry(retry_log.id)
        
        # 由於是模擬執行，結果可能成功或失敗
        assert isinstance(success, bool)
        if not success:
            assert error is not None

    def test_service_retry_cancellation(self):
        """測試 Service 的重試取消功能"""
        # 調度多個重試
        for i in range(3):
            self.service.schedule_retry(
                download_status_id=self.download_status.id,
                email_id=self.test_email.id,
                strategy=RetryStrategy.LINEAR
            )
        
        # 取消重試
        cancelled_count = self.service.cancel_pending_retries(
            email_id=self.test_email.id,
            reason="測試取消"
        )
        
        assert cancelled_count == 3
        
        # 驗證重試狀態
        retry_logs = self.repository.get_retry_logs_by_email(self.test_email.id)
        for log in retry_logs:
            assert log.status == RetryStatus.CANCELLED

    def test_service_retry_recommendations(self):
        """測試 Service 的重試建議功能"""
        # 測試首次重試建議
        recommendations = self.service.get_retry_recommendations(self.test_email.id)
        assert recommendations['recommendation'] == 'FIRST_RETRY'
        assert recommendations['suggested_strategy'] == RetryStrategy.EXPONENTIAL

        # 創建一些歷史重試記錄
        for i in range(5):
            retry_data = {
                'download_status_id': self.download_status.id,
                'email_id': self.test_email.id,
                'retry_attempt': i + 1,
                'retry_strategy': RetryStrategy.EXPONENTIAL,
                'scheduled_at': datetime.utcnow(),
                'retry_delay_seconds': 60,
                'status': RetryStatus.FAILED,
                'success': False
            }
            self.repository.create_retry_log(retry_data)

        # 重新獲取建議
        recommendations = self.service.get_retry_recommendations(self.test_email.id)
        assert recommendations['recommendation'] in ['INCREASE_DELAY', 'REDUCE_FREQUENCY', 'CONTINUE_CURRENT']

    # =============== Calculator 測試 ===============

    def test_retry_calculator_strategies(self):
        """測試重試計算器的各種策略"""
        base_delay = 60
        
        # 測試線性策略
        linear_delays = []
        for attempt in range(1, 6):
            delay = RetryCalculator.calculate_delay(RetryStrategy.LINEAR, attempt, base_delay)
            linear_delays.append(delay)
        
        assert linear_delays == [60, 120, 180, 240, 300]
        
        # 測試指數策略
        exponential_delays = []
        for attempt in range(1, 6):
            delay = RetryCalculator.calculate_delay(RetryStrategy.EXPONENTIAL, attempt, base_delay)
            exponential_delays.append(delay)
        
        assert exponential_delays == [60, 120, 240, 480, 960]
        
        # 測試固定延遲
        fixed_delays = []
        for attempt in range(1, 6):
            delay = RetryCalculator.calculate_delay(RetryStrategy.FIXED_DELAY, attempt, base_delay)
            fixed_delays.append(delay)
        
        assert all(delay == 60 for delay in fixed_delays)
        
        # 測試自定義延遲
        custom_delays = [30, 90, 180, 300, 600]
        for attempt in range(1, 6):
            delay = RetryCalculator.calculate_delay(
                RetryStrategy.CUSTOM, attempt, base_delay, custom_delays=custom_delays
            )
            assert delay == custom_delays[attempt - 1]

    def test_retry_calculator_schedule_time(self):
        """測試重試計算器的調度時間計算"""
        current_time = datetime.utcnow()
        
        # 測試調度時間計算
        schedule_time = RetryCalculator.get_next_schedule_time(
            RetryStrategy.LINEAR, 2, 60, current_time
        )
        
        expected_time = current_time + timedelta(seconds=120)
        # 允許1秒的誤差
        assert abs((schedule_time - expected_time).total_seconds()) < 1

    # =============== 性能測試 ===============

    def test_performance_bulk_insert(self):
        """測試批量插入性能"""
        # 準備大量測試數據
        large_dataset = []
        for i in range(1000):
            large_dataset.append({
                'download_status_id': self.download_status.id,
                'email_id': self.test_email.id,
                'retry_attempt': (i % 10) + 1,
                'retry_strategy': RetryStrategy.EXPONENTIAL,
                'scheduled_at': datetime.utcnow() + timedelta(minutes=i),
                'retry_delay_seconds': 60
            })
        
        # 測量批量插入時間
        start_time = time.time()
        created_logs = self.repository.bulk_create_retry_logs(large_dataset)
        end_time = time.time()
        
        insert_duration = (end_time - start_time) * 1000  # 轉換為毫秒
        
        assert len(created_logs) == 1000
        assert insert_duration < 5000  # 應該在5秒內完成
        
        print(f"\n批量插入1000條記錄耗時: {insert_duration:.2f}ms")

    def test_performance_query_optimization(self):
        """測試查詢性能優化"""
        # 創建測試數據
        test_data = []
        for i in range(500):
            test_data.append({
                'download_status_id': self.download_status.id,
                'email_id': self.test_email.id,
                'retry_attempt': (i % 10) + 1,
                'retry_strategy': RetryStrategy.EXPONENTIAL,
                'scheduled_at': datetime.utcnow() + timedelta(minutes=i),
                'retry_delay_seconds': 60,
                'status': RetryStatus.SCHEDULED if i % 2 == 0 else RetryStatus.FAILED
            })
        
        self.repository.bulk_create_retry_logs(test_data)
        
        # 測試各種查詢的性能
        query_times = []
        
        # 測試1: 按郵件ID查詢
        start_time = time.time()
        email_logs = self.repository.get_retry_logs_by_email(self.test_email.id)
        query_times.append((time.time() - start_time) * 1000)
        
        # 測試2: 按狀態查詢
        start_time = time.time()
        scheduled_logs = self.repository.get_retry_logs_by_status(RetryStatus.SCHEDULED)
        query_times.append((time.time() - start_time) * 1000)
        
        # 測試3: 統計查詢
        start_time = time.time()
        stats = self.repository.get_retry_statistics(self.test_email.id)
        query_times.append((time.time() - start_time) * 1000)
        
        # 測試4: 性能指標查詢
        start_time = time.time()
        metrics = self.repository.get_performance_metrics()
        query_times.append((time.time() - start_time) * 1000)
        
        # 所有查詢應該在150ms內完成
        max_query_time = max(query_times)
        avg_query_time = statistics.mean(query_times)
        
        assert max_query_time < 150, f"查詢時間過長: {max_query_time:.2f}ms"
        assert avg_query_time < 100, f"平均查詢時間過長: {avg_query_time:.2f}ms"
        
        print(f"\n查詢性能測試結果:")
        print(f"  最大查詢時間: {max_query_time:.2f}ms")
        print(f"  平均查詢時間: {avg_query_time:.2f}ms")
        print(f"  性能評級: {'EXCELLENT' if max_query_time < 50 else 'GOOD' if max_query_time < 100 else 'ACCEPTABLE'}")

    def test_system_integration_workflow(self):
        """測試完整的系統集成工作流程"""
        # 1. 調度重試
        retry_log = self.service.schedule_retry(
            download_status_id=self.download_status.id,
            email_id=self.test_email.id,
            strategy=RetryStrategy.ADAPTIVE,
            base_delay=30
        )
        
        assert retry_log.status == RetryStatus.SCHEDULED
        
        # 2. 檢查隊列狀態
        queue_status = self.service.get_retry_queue_status()
        assert queue_status['scheduled_count'] >= 1
        assert queue_status['queue_health'] in ['HEALTHY', 'WARNING', 'CRITICAL']
        
        # 3. 處理調度的重試
        results = self.service.process_scheduled_retries(batch_size=5)
        assert results['processed'] >= 0
        
        # 4. 獲取統計信息
        stats = self.repository.get_retry_statistics(self.test_email.id)
        assert 'total_retries' in stats
        assert 'success_rate' in stats
        
        # 5. 測試清理功能
        # 首先創建一些舊記錄
        old_retry_data = {
            'download_status_id': self.download_status.id,
            'email_id': self.test_email.id,
            'retry_attempt': 1,
            'retry_strategy': RetryStrategy.LINEAR,
            'scheduled_at': datetime.utcnow() - timedelta(days=10),
            'retry_delay_seconds': 60,
            'status': RetryStatus.SUCCESS,
            'success': True
        }
        old_log = self.repository.create_retry_log(old_retry_data)
        
        # 執行清理
        cleaned_count = self.service.cleanup_completed_retries(days_old=7)
        assert cleaned_count >= 0

    def test_error_handling_and_recovery(self):
        """測試錯誤處理和恢復機制"""
        # 測試無效數據的處理
        with pytest.raises(ValueError):
            self.repository.create_retry_log({
                'download_status_id': self.download_status.id,
                'email_id': self.test_email.id,
                'retry_attempt': 15,  # 超出範圍
                'retry_strategy': RetryStrategy.LINEAR,
                'scheduled_at': datetime.utcnow(),
                'retry_delay_seconds': 60
            })
        
        # 測試不存在的記錄
        non_existent_log = self.repository.get_retry_log_by_id(99999)
        assert non_existent_log is None
        
        # 測試更新不存在的記錄
        update_success = self.repository.update_retry_status(99999, RetryStatus.SUCCESS)
        assert update_success is False
        
        # 測試刪除不存在的記錄
        delete_success = self.repository.delete_retry_log(99999)
        assert delete_success is False


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])