"""
PTS Renamer Flask Routes

This module contains Flask blueprint definitions and route handlers for the
PTS Renamer web interface. It provides the current implementation of the
view layer in the MVP architecture, accessible at /pts-renamer/.
"""

from flask import Blueprint, render_template, request, jsonify, send_file, current_app
from werkzeug.utils import secure_filename
from typing import List, Dict, Any
import asyncio
import logging
from pathlib import Path

from backend.pts_renamer.services.pts_rename_presenter import PTSRenamePresenter
from backend.pts_renamer.models.pts_rename_models import (
    PTSRenameJobRequest,
    PTSRenamePreviewRequest,
    PTSRenameOperation
)
from loguru import logger


# Create Flask blueprint
pts_renamer_bp = Blueprint(
    'pts_renamer', 
    __name__, 
    url_prefix='/pts-renamer',
    template_folder='../templates',
    static_folder='../static'
)


@pts_renamer_bp.route('/')
def index():
    """
    Main PTS Renamer interface
    
    Accessible at: http://localhost:5000/pts-renamer/
    
    Returns:
        Rendered HTML template for the main interface
    """
    try:
        logger.info("Serving PTS Renamer main interface")
        return render_template('pts_rename_main.html', 
                             title="PTS File Renamer",
                             module_name="PTS Renamer")
    except Exception as e:
        logger.error(f"Failed to serve main interface: {e}")
        return render_template('error.html', 
                             error_message="Failed to load PTS Renamer interface"), 500


@pts_renamer_bp.route('/api/upload', methods=['POST'])
def upload_files():
    """
    Handle file upload requests
    
    Accepts multipart/form-data with files and processes them for PTS operations.
    Supports both individual PTS files and compressed archives.
    
    Returns:
        JSON response with upload status and upload_id
    """
    try:
        # Check if files were provided
        if 'files' not in request.files:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'NO_FILES',
                    'message': 'No files provided in request'
                }
            }), 400
        
        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({
                'success': False,
                'error': {
                    'code': 'EMPTY_FILES',
                    'message': 'No files selected'
                }
            }), 400
        
        logger.info(f"Received upload request with {len(files)} files")
        
        # Get presenter instance (would be injected in production)
        presenter = _get_presenter_instance()
        
        # Process upload asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(presenter.handle_upload_request(files))
            return jsonify(result)
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Upload request failed: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'UPLOAD_ERROR',
                'message': 'Upload processing failed',
                'details': {'exception': str(e)}
            }
        }), 500


@pts_renamer_bp.route('/api/process', methods=['POST'])
def process_files():
    """
    Handle processing requests
    
    Accepts JSON payload with processing configuration and queues
    the processing job using Dramatiq infrastructure.
    
    Expected JSON format:
    {
        "upload_id": "string",
        "operations": ["rename", "qc_generation", "directory_creation"],
        "rename_config": {
            "old_pattern": "string",
            "new_pattern": "string"
        },
        "qc_enabled": boolean,
        "create_directories": boolean
    }
    
    Returns:
        JSON response with job_id for tracking progress
    """
    try:
        # Validate request content type
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_CONTENT_TYPE',
                    'message': 'Request must be JSON'
                }
            }), 400
        
        data = request.get_json()
        logger.info(f"Received processing request for upload {data.get('upload_id')}")
        
        # Create processing request model
        try:
            operations = [PTSRenameOperation(op) for op in data.get('operations', [])]
            job_request = PTSRenameJobRequest(
                upload_id=data['upload_id'],
                operations=operations,
                rename_config=data.get('rename_config'),
                qc_enabled=data.get('qc_enabled', False),
                create_directories=data.get('create_directories', False)
            )
        except (KeyError, ValueError) as e:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_REQUEST',
                    'message': f'Invalid request format: {e}'
                }
            }), 400
        
        # Get presenter instance
        presenter = _get_presenter_instance()
        
        # Process request asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(presenter.handle_processing_request(job_request))
            return jsonify(result)
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Processing request failed: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'PROCESSING_ERROR',
                'message': 'Processing request failed',
                'details': {'exception': str(e)}
            }
        }), 500


@pts_renamer_bp.route('/api/status/<job_id>')
def get_job_status(job_id: str):
    """
    Get job status and progress
    
    Args:
        job_id: Job identifier from processing request
        
    Returns:
        JSON response with current job status, progress, and download URL when completed
    """
    try:
        logger.info(f"Status request for job {job_id}")
        
        # Get presenter instance
        presenter = _get_presenter_instance()
        
        # Get status asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(presenter.get_job_status(job_id))
            return jsonify(result)
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Status request failed: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'STATUS_ERROR',
                'message': 'Failed to retrieve job status',
                'details': {'exception': str(e)}
            }
        }), 500


@pts_renamer_bp.route('/api/preview', methods=['POST'])
def get_preview():
    """
    Get processing preview
    
    Accepts JSON payload with preview configuration and returns
    a preview of what operations will be performed on each file.
    
    Expected JSON format:
    {
        "upload_id": "string",
        "operations": ["rename", "qc_generation", "directory_creation"],
        "rename_config": {
            "old_pattern": "string",
            "new_pattern": "string"
        }
    }
    
    Returns:
        JSON response with file-by-file preview information
    """
    try:
        # Validate request
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_CONTENT_TYPE',
                    'message': 'Request must be JSON'
                }
            }), 400
        
        data = request.get_json()
        logger.info(f"Preview request for upload {data.get('upload_id')}")
        
        # Create preview request model
        try:
            operations = [PTSRenameOperation(op) for op in data.get('operations', [])]
            preview_request = PTSRenamePreviewRequest(
                upload_id=data['upload_id'],
                operations=operations,
                rename_config=data.get('rename_config')
            )
        except (KeyError, ValueError) as e:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_REQUEST',
                    'message': f'Invalid preview request: {e}'
                }
            }), 400
        
        # Get presenter instance
        presenter = _get_presenter_instance()
        
        # Generate preview asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(presenter.get_preview(preview_request))
            return jsonify(result)
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Preview request failed: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'PREVIEW_ERROR',
                'message': 'Preview generation failed',
                'details': {'exception': str(e)}
            }
        }), 500


@pts_renamer_bp.route('/api/download/<job_id>')
def download_results(job_id: str):
    """
    Download compressed processing results
    
    Args:
        job_id: Job identifier
        
    Returns:
        File download response or JSON error
    """
    try:
        logger.info(f"Download request for job {job_id}")
        
        # Get presenter instance
        presenter = _get_presenter_instance()
        
        # Get download information asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(presenter.handle_download_request(job_id))
        finally:
            loop.close()
        
        if not result.get('success'):
            return jsonify(result), 400
        
        # In a real implementation, this would serve the actual file
        # For now, return download information
        return jsonify({
            'success': True,
            'message': 'Download ready',
            'download_info': result
        })
        
    except Exception as e:
        logger.error(f"Download request failed: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'DOWNLOAD_ERROR',
                'message': 'Download request failed',
                'details': {'exception': str(e)}
            }
        }), 500


@pts_renamer_bp.route('/api/download/<job_id>/direct')
def download_results_direct(job_id: str):
    """
    Direct download of compressed results file
    
    Args:
        job_id: Job identifier
        
    Returns:
        Direct file download or error response
    """
    try:
        # This would implement direct file serving in production
        # For now, redirect to the regular download endpoint
        return download_results(job_id)
        
    except Exception as e:
        logger.error(f"Direct download failed: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'DOWNLOAD_ERROR',
                'message': 'Direct download failed',
                'details': {'exception': str(e)}
            }
        }), 500


# Health check endpoint
@pts_renamer_bp.route('/health')
def health_check():
    """
    Health check endpoint for monitoring
    
    Returns:
        JSON response with service health status
    """
    return jsonify({
        'service': 'pts_renamer',
        'status': 'healthy',
        'timestamp': '2025-01-20T00:00:00Z',  # Would use actual timestamp
        'version': '1.0.0'
    })


# Error handlers
@pts_renamer_bp.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': {
            'code': 'NOT_FOUND',
            'message': 'Endpoint not found'
        }
    }), 404


@pts_renamer_bp.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}")
    return jsonify({
        'success': False,
        'error': {
            'code': 'INTERNAL_ERROR',
            'message': 'Internal server error'
        }
    }), 500


# Helper functions
def _get_presenter_instance() -> PTSRenamePresenter:
    """
    Get presenter instance
    
    Uses the service factory to create a properly configured presenter
    with all dependencies injected according to the MVP architecture.
    
    Returns:
        PTSRenamePresenter instance
    """
    try:
        # Import the service factory
        from backend.pts_renamer.services.pts_rename_service_factory import get_global_factory
        from backend.pts_renamer.models.pts_rename_models import PTSRenameConfig
        
        # Get the database path from Flask app config or use default
        database_path = current_app.config.get('DATABASE_PATH', 'outlook.db')
        
        # Create configuration with default settings
        config = PTSRenameConfig()
        
        # Get the global factory and create presenter
        factory = get_global_factory(config=config, database_path=database_path)
        presenter = factory.create_presenter()
        
        logger.debug("Successfully created presenter instance")
        return presenter
        
    except Exception as e:
        logger.error(f"Failed to create presenter instance: {e}")
        # Re-raise with more context for better error handling in routes
        raise Exception(f"Presenter initialization failed: {e}") from e


# Blueprint registration helper
def register_pts_renamer_routes(app):
    """
    Register PTS Renamer routes with Flask app
    
    Args:
        app: Flask application instance
    """
    app.register_blueprint(pts_renamer_bp)
    logger.info("PTS Renamer routes registered at /pts-renamer/")