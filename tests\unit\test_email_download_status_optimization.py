"""
EmailDownloadStatusDB 優化測試
Story 1.2: 測試優化後的 EmailDownloadStatusDB 表結構和功能

Red Phase: 這些測試目前都應該失敗，直到我們實現優化功能
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy import create_engine, inspect
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import sessionmaker

from backend.shared.infrastructure.adapters.database.models import Base
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    EmailDownloadStatusDB, 
    DownloadStatus,
    validate_status_transition,
    validate_download_status_data
)


class TestEmailDownloadStatusDBOptimization:
    """EmailDownloadStatusDB 優化測試類"""
    
    @pytest.fixture
    def setup_test_db(self):
        """設置測試資料庫"""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        
        yield session, engine
        
        session.close()
    
    def test_model_has_new_optimization_fields(self, setup_test_db):
        """測試模型包含新的優化欄位"""
        session, engine = setup_test_db
        
        # 檢查新增的優化欄位
        assert hasattr(EmailDownloadStatusDB, 'download_progress'), "缺少 download_progress 欄位"
        assert hasattr(EmailDownloadStatusDB, 'downloaded_bytes'), "缺少 downloaded_bytes 欄位"
        assert hasattr(EmailDownloadStatusDB, 'updated_at'), "缺少 updated_at 欄位"
        
        # 檢查欄位類型和預設值
        status_record = EmailDownloadStatusDB(email_id=1)
        assert status_record.download_progress == 0.0, "download_progress 預設值應為 0.0"
        assert status_record.downloaded_bytes == 0, "downloaded_bytes 預設值應為 0"
        assert status_record.updated_at is not None, "updated_at 應有預設值"
    
    def test_database_table_has_new_columns(self, setup_test_db):
        """測試資料庫表包含新欄位"""
        session, engine = setup_test_db
        
        inspector = inspect(engine)
        columns = {col['name']: col for col in inspector.get_columns('email_download_status')}
        
        # 驗證新欄位存在
        required_new_columns = ['download_progress', 'downloaded_bytes', 'updated_at']
        for col_name in required_new_columns:
            assert col_name in columns, f"資料庫表缺少欄位: {col_name}"
        
        # 驗證欄位屬性
        assert columns['download_progress']['type'] is not None
        assert columns['downloaded_bytes']['type'] is not None
        assert columns['updated_at']['nullable'] is False
    
    def test_indexes_created_for_performance(self, setup_test_db):
        """測試性能索引創建"""
        session, engine = setup_test_db
        
        inspector = inspect(engine)
        indexes = inspector.get_indexes('email_download_status')
        index_names = [idx['name'] for idx in indexes]
        
        # 檢查必要索引
        expected_indexes = [
            'idx_email_download_status_email_id',
            'idx_email_download_status_status',
            'idx_email_download_status_composite'
        ]
        
        for expected_idx in expected_indexes:
            assert expected_idx in index_names, f"缺少性能索引: {expected_idx}"
    
    def test_unique_constraint_on_email_id(self, setup_test_db):
        """測試 email_id 唯一性約束"""
        session, engine = setup_test_db
        
        # 創建第一個記錄
        status1 = EmailDownloadStatusDB(email_id=123, status=DownloadStatus.PENDING)
        session.add(status1)
        session.commit()
        
        # 嘗試創建重複記錄應該失敗
        status2 = EmailDownloadStatusDB(email_id=123, status=DownloadStatus.DOWNLOADING)
        session.add(status2)
        
        with pytest.raises(IntegrityError):
            session.commit()
    
    def test_download_progress_validation(self):
        """測試下載進度驗證"""
        # 有效進度
        valid_data = {'download_progress': 50.5}
        errors = validate_download_status_data(valid_data)
        assert len(errors) == 0, "有效進度數據不應有錯誤"
        
        # 無效進度 - 負數
        invalid_data_negative = {'download_progress': -10.0}
        errors = validate_download_status_data(invalid_data_negative)
        assert len(errors) > 0, "負數進度應該產生錯誤"
        assert "進度必須在 0-100 之間" in str(errors)
        
        # 無效進度 - 超過100
        invalid_data_over = {'download_progress': 150.0}
        errors = validate_download_status_data(invalid_data_over)
        assert len(errors) > 0, "超過100的進度應該產生錯誤"
        assert "進度必須在 0-100 之間" in str(errors)
    
    def test_downloaded_bytes_validation(self):
        """測試已下載位元組數驗證"""
        # 已下載位元組不能超過總檔案大小
        invalid_data = {
            'file_size_bytes': 1000,
            'downloaded_bytes': 1500
        }
        errors = validate_download_status_data(invalid_data)
        assert len(errors) > 0, "已下載位元組超過總檔案大小應該產生錯誤"
        assert "已下載位元組不能超過總檔案大小" in str(errors)
        
        # 有效情況
        valid_data = {
            'file_size_bytes': 1000,
            'downloaded_bytes': 500
        }
        errors = validate_download_status_data(valid_data)
        assert len(errors) == 0, "有效位元組數據不應有錯誤"
    
    def test_time_logic_validation(self):
        """測試時間邏輯驗證"""
        now = datetime.utcnow()
        past = now - timedelta(hours=1)
        
        # 完成時間早於開始時間 - 無效
        invalid_data = {
            'started_at': now,
            'completed_at': past
        }
        errors = validate_download_status_data(invalid_data)
        assert len(errors) > 0, "完成時間早於開始時間應該產生錯誤"
        assert "完成時間不能早於開始時間" in str(errors)
        
        # 正確的時間順序 - 有效
        valid_data = {
            'started_at': past,
            'completed_at': now
        }
        errors = validate_download_status_data(valid_data)
        assert len(errors) == 0, "正確的時間順序不應有錯誤"
    
    def test_status_transition_validation(self):
        """測試狀態轉換驗證"""
        # 有效轉換
        assert validate_status_transition(DownloadStatus.PENDING, DownloadStatus.DOWNLOADING), \
            "PENDING -> DOWNLOADING 應該是有效轉換"
        assert validate_status_transition(DownloadStatus.DOWNLOADING, DownloadStatus.COMPLETED), \
            "DOWNLOADING -> COMPLETED 應該是有效轉換"
        assert validate_status_transition(DownloadStatus.DOWNLOADING, DownloadStatus.FAILED), \
            "DOWNLOADING -> FAILED 應該是有效轉換"
        
        # 無效轉換
        assert not validate_status_transition(DownloadStatus.COMPLETED, DownloadStatus.PENDING), \
            "COMPLETED -> PENDING 應該是無效轉換"
        assert not validate_status_transition(DownloadStatus.PENDING, DownloadStatus.COMPLETED), \
            "PENDING -> COMPLETED 應該是無效轉換"
        assert not validate_status_transition(DownloadStatus.FAILED, DownloadStatus.DOWNLOADING), \
            "FAILED -> DOWNLOADING 應該是無效轉換(需要先轉到 RETRY_SCHEDULED)"
    
    def test_model_validation_methods(self, setup_test_db):
        """測試模型內建驗證方法"""
        session, engine = setup_test_db
        
        status_record = EmailDownloadStatusDB(email_id=1)
        
        # 測試進度驗證方法
        status_record.download_progress = 50.0
        try:
            status_record.validate_progress()  # 應該不拋出異常
        except ValueError:
            pytest.fail("50.0% 進度應該是有效的")
        
        # 測試無效進度
        status_record.download_progress = 150.0
        with pytest.raises(ValueError, match="下載進度必須在 0-100 之間"):
            status_record.validate_progress()
        
        # 測試狀態轉換驗證
        status_record.status = DownloadStatus.PENDING
        try:
            status_record.validate_status_transition(DownloadStatus.DOWNLOADING)  # 有效轉換
        except ValueError:
            pytest.fail("PENDING -> DOWNLOADING 應該是有效轉換")
        
        # 測試無效狀態轉換
        with pytest.raises(ValueError, match="無效的狀態轉換"):
            status_record.validate_status_transition(DownloadStatus.COMPLETED)  # 無效轉換
    
    def test_updated_at_auto_update(self, setup_test_db):
        """測試 updated_at 自動更新功能"""
        session, engine = setup_test_db
        
        # 創建記錄
        status_record = EmailDownloadStatusDB(email_id=1, status=DownloadStatus.PENDING)
        session.add(status_record)
        session.commit()
        
        original_updated_at = status_record.updated_at
        
        # 稍等一下確保時間差異
        import time
        time.sleep(0.001)
        
        # 更新記錄
        status_record.download_progress = 25.0
        session.commit()
        
        # 檢查 updated_at 是否自動更新
        assert status_record.updated_at > original_updated_at, \
            "updated_at 應該在記錄更新時自動更新"


# 功能測試類
class TestDownloadStatusValidationFunctions:
    """下載狀態驗證函數測試"""
    
    def test_validate_status_transition_function_exists(self):
        """測試狀態轉換驗證函數存在"""
        # 函數應該存在並可調用
        assert callable(validate_status_transition), "validate_status_transition 函數應該存在"
        
        # 測試基本功能
        result = validate_status_transition(DownloadStatus.PENDING, DownloadStatus.DOWNLOADING)
        assert isinstance(result, bool), "函數應該返回布林值"
    
    def test_validate_download_status_data_function_exists(self):
        """測試資料驗證函數存在"""
        # 函數應該存在並可調用
        assert callable(validate_download_status_data), "validate_download_status_data 函數應該存在"
        
        # 測試基本功能
        result = validate_download_status_data({})
        assert isinstance(result, list), "函數應該返回錯誤列表"


# 性能測試類
class TestDownloadStatusPerformance:
    """下載狀態性能測試"""
    
    def test_query_performance_with_indexes(self, setup_test_db):
        """測試索引優化後的查詢性能"""
        session, engine = setup_test_db
        
        # 創建測試資料
        test_records = []
        for i in range(100):
            record = EmailDownloadStatusDB(
                email_id=i,
                status=DownloadStatus.PENDING if i % 2 == 0 else DownloadStatus.DOWNLOADING,
                download_progress=i % 101
            )
            test_records.append(record)
        
        session.add_all(test_records)
        session.commit()
        
        import time
        
        # 測試按 email_id 查詢性能
        start_time = time.time()
        result = session.query(EmailDownloadStatusDB).filter(
            EmailDownloadStatusDB.email_id == 50
        ).first()
        query_time_1 = time.time() - start_time
        
        # 測試按 status 查詢性能
        start_time = time.time()
        results = session.query(EmailDownloadStatusDB).filter(
            EmailDownloadStatusDB.status == DownloadStatus.PENDING
        ).limit(10).all()
        query_time_2 = time.time() - start_time
        
        # 測試複合索引查詢性能
        start_time = time.time()
        results = session.query(EmailDownloadStatusDB).filter(
            EmailDownloadStatusDB.email_id.in_([1, 2, 3, 4, 5]),
            EmailDownloadStatusDB.status == DownloadStatus.DOWNLOADING
        ).all()
        query_time_3 = time.time() - start_time
        
        # 性能要求: 查詢時間 < 100ms (0.1秒)
        max_query_time = max(query_time_1, query_time_2, query_time_3)
        assert max_query_time < 0.1, f"查詢時間 {max_query_time:.3f}s 超過 100ms 限制"