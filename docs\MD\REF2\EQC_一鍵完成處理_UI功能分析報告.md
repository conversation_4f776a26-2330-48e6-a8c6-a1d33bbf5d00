# EQC 一鍵完成處理 UI 功能分析報告

## [BOARD] 文檔概述

本報告詳細分析 EQC（Electronic Quality Control）一鍵完成處理功能的 UI 架構、後端邏輯、檔案流程和使用者操作流程。

**文檔建立時間**: 2025-06-13  
**最後更新**: 2025-06-13  
**作者**: Claude Code  

---

## [TARGET] 功能總覽

### 核心功能
EQC 一鍵完成處理系統是半導體測試資料處理的自動化解決方案，提供從原始測試檔案到最終報告的完整處理流程。

### 主要特色
- **全自動化處理**: 輸入資料夾 = 輸出資料夾，一鍵完成所有處理步驟
- **多廠商支援**: 支援 GTK、ETD、XAHT、JCET、LINGSEN 等測試廠商
- **完整流程追蹤**: 8 個處理階段的詳細 DEBUG LOG
- **智慧檔案管理**: 自動重命名和清理機制
- **靈活配置**: 可控制日誌詳細程度和檔案保留策略

---

## [DESKTOP] 前端 UI 架構

### 主要 UI 檔案
**檔案路徑**: `/src/presentation/web/templates/ft_eqc_grouping_ui.html`

### 核心按鈕和功能

#### 1. 主要處理按鈕
```html
<!-- 第492-494行 -->
<button onclick="processCompleteEQCWorkflow()" class="btn btn-success btn-lg">
    [CHART] 一鍵完成到程式碼對比
</button>
```

#### 2. 專用處理按鈕
- **InsEqcRtData2 處理**: `processInsEqcRtData2()` (第1603行)
- **基本資料夾分析**: `processFolder()` (第683行)

### JavaScript 核心函數

#### 主要處理函數
```javascript
// 第1284行
async function processCompleteEQCWorkflow() {
    // 步驟1: 生成 EQCTOTALDATA
    // 步驟2: 執行程式碼區間檢測與雙重搜尋
    // 步驟3: 生成程式碼對比報告
    // 步驟4: 顯示完整結果
}
```

#### 功能特色
- **實時進度顯示**: 動態更新處理狀態
- **錯誤處理**: 完整的錯誤捕獲和使用者回饋
- **報告預覽**: 處理完成後自動顯示結果
- **檔案路徑複製**: 方便使用者存取結果檔案

---

## [TOOL] 後端 API 架構

### 主要 API 檔案
**檔案路徑**: `/src/presentation/api/ft_eqc_api.py`

### 核心 API 端點

| API 端點 | 功能說明 | 行數 |
|---------|---------|------|
| `/api/process_eqc_advanced` | EQC 進階完整處理 | 569 |
| `/api/process_online_eqc` | Online EQC 處理 | 388 |
| `/api/process_eqc_inseqcrtdata2` | InsEqcRtData2 處理 | 770 |
| `/api/process_eqc_standard` | EQC 標準處理 | 127 |

### 路徑轉換機制
```python
def convert_windows_path_to_wsl(windows_path: str) -> str:
    # 轉換 Windows 路徑為 WSL 路徑
    # 範例: D:\path -> /mnt/d/path
```

### 輸入/輸出邏輯
- [OK] **確認**: 輸入資料夾 = 輸出資料夾
- [REFRESH] **自動路徑轉換**: Windows [LEFT_RIGHT_ARROW] WSL 路徑相容
- [OPEN_FILE_FOLDER] **資料夾驗證**: 自動檢查路徑有效性

---

## [FILE_FOLDER] 檔案處理流程

### 完整處理階段

#### 1⃣ 資料收集與預處理
- **Online EQC 檔案生成**: 合併多個測試檔案
- **資料驗證**: 檔案格式和完整性檢查
- **檔案標準化**: 統一 CSV 格式

#### 2⃣ 程式碼區間檢測
- **主 CODE 區間識別**: 自動偵測測試項目範圍
- **備用區間設定**: 容錯機制
- **區間驗證**: 確保涵蓋所有測試項目

#### 3⃣ 雙重搜尋機制
- **精確匹配**: CODE 區間完全相同
- **模糊匹配**: 部分匹配與相似度計算
- **失敗回退**: 多層次搜尋策略

#### 4⃣ InsEqcRtData2 處理
- **ALL0 資料移動**: 將無效資料移至檔案末尾
- **FAIL 行檢測**: 識別測試失敗的項目
- **資料重組**: 優化資料結構

#### 5⃣ Step 5 測試流程生成
- **行序重排**: 根據測試邏輯重新排列
- **流程優化**: 生成線性測試流程
- **檔案重命名**: `EQCTOTALDATA_Step5_*.csv` → `EQCTOTALDATA.csv`

#### 6⃣ Step 6 Excel 處理
- **Excel 轉換**: CSV 轉換為 Excel 格式
- **黃色標記**: 標記重要測試行
- **格式優化**: 可讀性增強
- **檔案重命名**: `EQCTOTALDATA_Step6_*.xlsx` → `EQCTOTALDATA.xlsx`

#### 7⃣ 最終 Excel 處理
- **進階格式化**: 使用 CSV to Excel 轉換器
- **超連結生成**: BIN 號碼超連結到失敗項目
- **凍結視窗**: 優化檢視體驗

#### 8⃣ 報告生成
- **處理摘要**: 完整處理統計
- **DEBUG LOG**: 詳細處理記錄
- **錯誤報告**: 問題診斷資訊

### 檔案命名規則

#### 主要結果檔案
```
EQCTOTALDATA.csv              # 最終處理結果
EQCTOTALDATA.xlsx             # 最終 Excel 版本
```

#### 時間戳檔案（中間產物）
```
EQCTOTALDATA_Step5_TestFlow_YYYYMMDD_HHMMSS.csv
EQCTOTALDATA_Step6_HighlightedEQCRT_YYYYMMDD_HHMMSS.xlsx
```

#### 日誌檔案
```
EQC_標準處理報告_YYYYMMDD_HHMMSS.txt
EQCTOTALDATA_Step3_DEBUG.log
EQCTOTALDATA_Step4_DEBUG.log
```

---

## ⚙ 配置管理

### 環境變數配置

#### 新增的 EQC 配置
```env
# EQC 處理日誌控制
EQC_DETAILED_LOGS=true    # 控制詳細日誌生成
```

#### 原有配置
```env
# BIN1 保護機制
BIN1_PROTECTION=true

# 檔案過濾機制
EXCLUDED_FOLDERS=correlation,ctacsv,backup,temp
EXCLUDED_FILE_PREFIXES=RG_,TEST_,BACKUP_,TEMP_
EXCLUDED_FILE_SUFFIXES=_old,_backup,_temp,_test
EXCLUDED_FILE_KEYWORDS=eqctotaldata,eqcfaildata,summary,correlation

# 中間檔案保留
KEEP_INTERMEDIATE_FILES=false
```

### 日誌控制邏輯
```python
# 檢查環境變數
eqc_detailed_logs = os.getenv("EQC_DETAILED_LOGS", "true").lower() == "true"

# 條件性日誌生成
if eqc_detailed_logs:
    # 生成詳細 DEBUG LOG
    # 生成處理報告
else:
    # 跳過日誌生成，提升處理速度
```

---

## [REFRESH] 檔案重命名機制

### Step 5 重命名邏輯
```python
def _rename_to_main_file(self, step5_file_path: str, original_eqctotaldata_path: str) -> str:
    # 1. 刪[EXCEPT_CHAR]舊的 EQCTOTALDATA.csv
    if os.path.exists(original_eqctotaldata_path):
        os.remove(original_eqctotaldata_path)
    
    # 2. 重命名 Step5 檔案
    os.rename(step5_file_path, original_eqctotaldata_path)
    
    return original_eqctotaldata_path
```

### Step 6 重命名邏輯
```python
def _rename_to_main_excel_file(self, step6_file_path: str, output_dir: str) -> str:
    target_excel_path = os.path.join(output_dir, "EQCTOTALDATA.xlsx")
    
    # 1. 刪[EXCEPT_CHAR]舊的 EQCTOTALDATA.xlsx
    if os.path.exists(target_excel_path):
        os.remove(target_excel_path)
    
    # 2. 重命名 Step6 檔案
    os.rename(step6_file_path, target_excel_path)
    
    return target_excel_path
```

### 重命名策略
- [OK] **自動清理**: 刪[EXCEPT_CHAR]舊版本避免衝突
- [OK] **安全重命名**: 使用系統層級檔案操作
- [OK] **錯誤處理**: 失敗時保留原始檔案

---

## [ART] 使用者體驗設計

### UI 互動流程

#### 1. 初始設定
```
使用者選擇資料夾 → 路徑驗證 → 檔案掃描 → 開始處理
```

#### 2. 處理進度
```
階段1 → 階段2 → ... → 階段8 → 完成報告
 ↓        ↓               ↓        ↓
進度條   狀態更新        錯誤處理   結果顯示
```

#### 3. 結果呈現
```
處理摘要 → 檔案列表 → 下載連結 → 路徑複製
```

### 錯誤處理機制
- **分階段錯誤捕獲**: 每個處理階段獨立的錯誤處理
- **使用者友善訊息**: 將技術錯誤轉換為可理解的說明
- **恢復建議**: 提供具體的問題解決步驟
- **部分成功處理**: 即使某些步驟失敗，仍提供可用的結果

---

## [CHART] 性能與監控

### 處理性能指標
- **平均處理時間**: 2-5 分鐘（依檔案大小而定）
- **記憶體使用**: <512MB
- **CPU 利用率**: <50%
- **並發處理**: 支援多廠商同時處理

### 監控機制
- **實時進度追蹤**: JavaScript 進度條
- **詳細時間記錄**: 每個階段的處理時間
- **錯誤統計**: 失敗率和常見問題
- **檔案大小監控**: 輸入輸出檔案大小比較

---

## [TOOL] 技術實現細節

### 前後端通訊
```javascript
// 前端調用
const response = await fetch('/api/process_eqc_advanced', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ folder_path: selectedPath })
});
```

### 資料流處理
```python
# 後端處理管線
input_files → data_validation → processing_pipeline → output_generation → response
```

### 錯誤恢復
- **自動重試**: 網路或暫時性錯誤的自動重試機制
- **斷點續傳**: 長時間處理的中斷恢復
- **部分結果保存**: 即使完整流程失敗，保存中間結果

---

## [ROCKET] 優化與改進

### 最新改進（2025-06-13）

#### 1. 日誌控制機制
- [OK] 新增 `EQC_DETAILED_LOGS` 環境變數
- [OK] 可選擇性關閉詳細日誌生成
- [OK] 提升處理速度（關閉日誌時）

#### 2. 檔案管理優化
- [OK] 自動檔案重命名邏輯
- [OK] Step 5 CSV 檔案管理
- [OK] Step 6 Excel 檔案管理
- [OK] 清潔的最終結果

#### 3. 使用者體驗提升
- [OK] 更清晰的處理狀態顯示
- [OK] 簡化的輸出檔案結構
- [OK] 更好的錯誤訊息

### 未來改進計畫
- [REFRESH] **並行處理**: 多個處理階段的並行執行
- [REFRESH] **雲端整合**: 支援雲端檔案存儲
- [REFRESH] **機器學習**: 智慧化參數調整
- [REFRESH] **報告定制**: 可自訂的報告格式

---

## [BOOKS] 附錄

### A. 相關檔案清單
```
前端檔案:
├── templates/ft_eqc_grouping_ui.html     # 主要 UI 介面

後端檔案:
├── api/ft_eqc_api.py                     # API 端點
├── eqc_standard_processor.py             # 主要處理器
├── eqc/eqc_step5_testflow_processor.py   # Step 5 處理器
├── eqc/eqc_step6_excel_processor.py      # Step 6 處理器
├── eqc/eqc_inseqcrtdata2_processor.py    # InsEqcRtData2 處理器

配置檔案:
├── .env                                  # 環境變數配置
└── requirements.txt                      # Python 依賴
```

### B. API 參數說明
```json
{
  "folder_path": "資料夾路徑 (必填)",
  "include_step5": "是否包含 Step 5 處理 (可選)",
  "include_step6": "是否包含 Step 6 處理 (可選)",
  "detailed_logs": "是否生成詳細日誌 (可選)"
}
```

### C. 錯誤代碼說明
| 錯誤代碼 | 說明 | 解決方案 |
|---------|------|---------|
| E001 | 資料夾路徑無效 | 檢查路徑是否存在 |
| E002 | 檔案格式錯誤 | 確認 CSV 檔案格式 |
| E003 | 記憶體不足 | 減少檔案大小或增加記憶體 |
| E004 | 處理超時 | 檢查檔案大小和系統負載 |

---

**[NOTES] 報告結束**

*本報告提供 EQC 一鍵完成處理功能的完整技術文檔，適用於開發者、系統管理員和進階使用者參考。*