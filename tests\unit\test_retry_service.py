"""
Epic-04 Story 4.1: RetryService 核心引擎測試
TDD RED PHASE: 建立失敗測試，驅動實現

測試覆蓋：
- 完整 CRUD 操作
- 業務邏輯方法
- 錯誤處理和驗證
- 性能要求驗證
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, patch, MagicMock

from backend.shared.services.retry_service import RetryService
from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    EmailDownloadRetryLogDB, RetryStrategy, RetryStatus
)
from backend.shared.infrastructure.adapters.database.models import EmailDB


class MockTaskQueue:
    """模擬任務隊列"""
    
    def __init__(self):
        self.scheduled_tasks = []
        self.task_counter = 0
    
    def schedule_task(self, task_func, task_args: dict, delay: int = 0) -> int:
        """調度任務"""
        self.task_counter += 1
        task_id = self.task_counter
        
        self.scheduled_tasks.append({
            'id': task_id,
            'func': task_func,
            'args': task_args,
            'delay': delay,
            'scheduled_at': datetime.utcnow()
        })
        
        return task_id
    
    def get_scheduled_tasks(self) -> List[Dict]:
        """獲取已調度的任務"""
        return self.scheduled_tasks.copy()


class TestRetryServiceCRUD:
    """RetryService CRUD 操作測試 - RED PHASE"""
    
    @pytest.fixture
    def retry_service(self):
        """創建 RetryService 實例"""
        database = Mock(spec=EmailDatabase)
        task_queue = MockTaskQueue()
        return RetryService(database, task_queue)
    
    @pytest.fixture
    def sample_email_data(self):
        """樣本郵件資料"""
        return {
            'id': 1,
            'message_id': '<EMAIL>',
            'sender': '<EMAIL>',
            'subject': 'Test Email',
            'body': 'Test content',
            'received_time': datetime.utcnow()
        }
    
    def test_create_retry_task_basic(self, retry_service, sample_email_data):
        """測試創建重試任務 - GREEN PHASE"""
        # ARRANGE
        original_task_id = 1
        error_info = {
            'error_type': 'network_error',
            'error_message': 'Connection timeout',
            'retry_strategy': RetryStrategy.EXPONENTIAL
        }
        
        # ACT
        retry_id = retry_service.create_retry_task(original_task_id, error_info)
        
        # ASSERT - GREEN PHASE: 驗證基本功能
        assert retry_id is not None
        assert isinstance(retry_id, int)
        assert retry_id > 0
    
    def test_get_retry_status_basic(self, retry_service):
        """測試查詢重試狀態 - GREEN PHASE"""
        # ARRANGE
        task_id = 1
        
        # ACT
        status = retry_service.get_retry_status(task_id)
        
        # ASSERT - GREEN PHASE: 驗證基本功能
        assert status is not None
        assert hasattr(status, 'id')
        assert hasattr(status, 'original_task_id')
        assert status.original_task_id == task_id
    
    def test_update_retry_progress_basic(self, retry_service):
        """測試更新重試進度 - GREEN PHASE"""
        # ARRANGE
        retry_id = 1
        new_status = RetryStatus.RUNNING
        
        # ACT
        result = retry_service.update_retry_progress(retry_id, new_status)
        
        # ASSERT - GREEN PHASE: 驗證基本功能
        assert result is True
    
    def test_cancel_retry_task_basic(self, retry_service):
        """測試取消重試任務 - GREEN PHASE"""
        # ARRANGE
        retry_id = 1
        
        # ACT
        result = retry_service.cancel_retry_task(retry_id)
        
        # ASSERT - GREEN PHASE: 驗證基本功能
        assert result is True


class TestRetryServiceBusinessLogic:
    """RetryService 業務邏輯測試 - GREEN PHASE"""
    
    @pytest.fixture
    def retry_service(self):
        """創建 RetryService 實例"""
        database = Mock(spec=EmailDatabase)
        task_queue = MockTaskQueue()
        return RetryService(database, task_queue)
    
    def test_schedule_retry_basic(self, retry_service):
        """測試調度重試任務 - GREEN PHASE"""
        # ARRANGE
        task_id = 1
        error = Exception("Network timeout")
        
        # ACT
        retry_id = retry_service.schedule_retry(task_id, error)
        
        # ASSERT - GREEN PHASE: 驗證基本功能
        assert retry_id is not None
        assert isinstance(retry_id, int)
        assert retry_id > 0
    
    def test_execute_retry_basic(self, retry_service):
        """測試執行重試 - GREEN PHASE"""
        # ARRANGE
        retry_id = 1
        
        # ACT
        result = retry_service.execute_retry(retry_id)
        
        # ASSERT - GREEN PHASE: 驗證基本功能
        assert isinstance(result, bool)
        assert result is True  # GREEN PHASE 暫時總是成功
    
    def test_get_retry_statistics_basic(self, retry_service):
        """測試獲取重試統計 - GREEN PHASE"""
        # ARRANGE
        period = "24h"
        
        # ACT
        stats = retry_service.get_retry_statistics(period)
        
        # ASSERT - GREEN PHASE: 驗證基本功能
        assert isinstance(stats, dict)
        assert 'total_retries' in stats
        assert 'success_rate' in stats
        assert 'period' in stats
        assert stats['period'] == period


class TestRetryServicePerformance:
    """RetryService 性能要求測試 - GREEN PHASE"""
    
    @pytest.fixture
    def retry_service(self):
        """創建 RetryService 實例"""
        database = Mock(spec=EmailDatabase)
        task_queue = MockTaskQueue()
        return RetryService(database, task_queue)
    
    def test_retry_trigger_latency(self, retry_service):
        """測試重試觸發延遲 < 1 秒 - GREEN PHASE"""
        # ARRANGE
        task_id = 1
        error = Exception("Test error")
        
        # ACT
        start_time = time.time()
        retry_id = retry_service.schedule_retry(task_id, error)
        end_time = time.time()
        
        # ASSERT - GREEN PHASE: 驗證性能要求
        latency = end_time - start_time
        assert latency < 1.0, f"重試觸發延遲 {latency}s 超過 1 秒要求"
        assert retry_id is not None, "重試任務應該成功創建"
    
    def test_api_response_time(self, retry_service):
        """測試 API 響應時間 < 200ms - GREEN PHASE"""
        # ARRANGE
        task_id = 1
        
        # ACT
        start_time = time.time()
        status = retry_service.get_retry_status(task_id)
        end_time = time.time()
        
        # ASSERT - GREEN PHASE: 驗證性能要求
        response_time = (end_time - start_time) * 1000  # 轉換為毫秒
        assert response_time < 200, f"API 響應時間 {response_time}ms 超過 200ms 要求"
        assert status is not None, "API 應該返回有效狀態"


class TestRetryServiceErrorHandling:
    """RetryService 錯誤處理測試 - GREEN PHASE"""
    
    @pytest.fixture
    def retry_service(self):
        """創建 RetryService 實例"""
        database = Mock(spec=EmailDatabase)
        task_queue = MockTaskQueue()
        return RetryService(database, task_queue)
    
    def test_invalid_task_id_handling(self, retry_service):
        """測試無效任務ID處理 - GREEN PHASE"""
        # ARRANGE
        invalid_task_id = -1
        
        # ACT
        result = retry_service.get_retry_status(invalid_task_id)
        
        # ASSERT - GREEN PHASE: 驗證錯誤處理
        assert result is None  # 無效ID應該返回None
    
    def test_invalid_retry_id_update(self, retry_service):
        """測試無效重試ID更新處理 - GREEN PHASE"""
        # ARRANGE
        invalid_retry_id = -1
        
        # ACT & ASSERT
        with pytest.raises(ValueError, match="retry_id 必須大於 0"):
            retry_service.update_retry_progress(invalid_retry_id, RetryStatus.RUNNING)
    
    def test_invalid_error_info_type(self, retry_service):
        """測試無效錯誤信息類型處理 - GREEN PHASE"""
        # ARRANGE
        task_id = 1
        invalid_error_info = "string instead of dict"
        
        # ACT & ASSERT
        with pytest.raises(RuntimeError, match="創建重試任務失敗"):
            retry_service.create_retry_task(task_id, invalid_error_info)


class TestRetryServiceIntegration:
    """RetryService 整合測試 - GREEN PHASE"""
    
    @pytest.fixture
    def retry_service(self):
        """創建 RetryService 實例"""
        database = Mock(spec=EmailDatabase)
        task_queue = MockTaskQueue()
        return RetryService(database, task_queue)
    
    def test_complete_retry_workflow(self, retry_service):
        """測試完整重試工作流程 - GREEN PHASE"""
        # ARRANGE
        task_id = 1
        error = Exception("Network timeout")
        
        # ACT & ASSERT - GREEN PHASE: 驗證完整工作流程
        # 1. 調度重試
        retry_id = retry_service.schedule_retry(task_id, error)
        assert retry_id is not None
        assert isinstance(retry_id, int)
        
        # 2. 檢查重試狀態
        status = retry_service.get_retry_status(task_id)
        assert status is not None
        assert status.status == RetryStatus.SCHEDULED
        assert status.original_task_id == task_id
        
        # 3. 執行重試
        result = retry_service.execute_retry(retry_id)
        assert result is True
        
        # 4. 檢查操作指標
        metrics = retry_service.get_operation_metrics()
        assert metrics['create_operations'] > 0
        assert metrics['read_operations'] > 0
        assert metrics['update_operations'] > 0
    
    def test_task_queue_integration(self, retry_service):
        """測試任務隊列整合 - GREEN PHASE"""
        # ARRANGE
        task_id = 1
        error = Exception("Test error")
        
        # ACT
        retry_id = retry_service.schedule_retry(task_id, error)
        
        # ASSERT - GREEN PHASE: 驗證任務隊列調度
        assert retry_id is not None
        
        # 檢查任務隊列是否有調度任務
        scheduled_tasks = retry_service.task_queue.get_scheduled_tasks()
        assert len(scheduled_tasks) > 0
        
        # 檢查任務參數
        task = scheduled_tasks[0]
        assert 'retry_id' in task['args']
        assert task['delay'] == 60  # 1分鐘延遲


if __name__ == "__main__":
    # 運行測試以驗證 GREEN PHASE
    pytest.main([__file__, "-v", "--tb=short"])