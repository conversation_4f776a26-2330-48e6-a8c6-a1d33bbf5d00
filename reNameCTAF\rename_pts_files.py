import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import glob
from pathlib import Path
import re

class PTSFileRenamer:
    def __init__(self, root):
        self.root = root
        self.root.title("PTS檔案批量重命名工具")
        self.root.geometry("800x600")
        self.root.configure(bg='#2b2b2b')
        
        # 設定深色主題
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TFrame', background='#2b2b2b')
        style.configure('TLabel', background='#2b2b2b', foreground='white')
        style.configure('TButton', background='#404040', foreground='white')
        style.configure('TCheckbutton', background='#2b2b2b', foreground='white')
        
        # 變數
        self.selected_folders = []
        self.old_name_pattern = tk.StringVar()
        self.new_name_pattern = tk.StringVar()
        self.rename_enabled = tk.BooleanVar()
        self.qc_enabled = tk.BooleanVar()
        self.create_directories = tk.BooleanVar()
        
        self.create_widgets()
        
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 標題
        title_label = tk.Label(main_frame, text="PTS檔案批量重命名工具", 
                              font=("Arial", 16, "bold"), 
                              bg='#2b2b2b', fg='white')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 選擇文件夾區域
        folder_frame = ttk.LabelFrame(main_frame, text="選擇文件夾", padding="10")
        folder_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 文件夾列表
        self.folder_listbox = tk.Listbox(folder_frame, height=4, bg='#404040', fg='white', 
                                        selectmode=tk.MULTIPLE)
        self.folder_listbox.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 滾動條
        scrollbar = ttk.Scrollbar(folder_frame, orient=tk.VERTICAL, command=self.folder_listbox.yview)
        scrollbar.grid(row=0, column=2, sticky=(tk.N, tk.S))
        self.folder_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 按鈕框架
        button_frame = ttk.Frame(folder_frame)
        button_frame.grid(row=1, column=0, columnspan=3, pady=(10, 0))
        
        add_folder_btn = tk.Button(button_frame, text="新增文件夾", 
                                  command=self.add_folder, 
                                  bg='#404040', fg='white', relief=tk.FLAT)
        add_folder_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        remove_folder_btn = tk.Button(button_frame, text="移除選中的文件夾", 
                                     command=self.remove_folder, 
                                     bg='#404040', fg='white', relief=tk.FLAT)
        remove_folder_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_all_btn = tk.Button(button_frame, text="清空所有", 
                                 command=self.clear_folders, 
                                 bg='#404040', fg='white', relief=tk.FLAT)
        clear_all_btn.pack(side=tk.LEFT)
        
        # 重命名規則區域
        rename_frame = ttk.LabelFrame(main_frame, text="重命名規則", padding="10")
        rename_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 重新命名复选框
        rename_checkbox = tk.Checkbutton(rename_frame, text="重新命名", 
                                        variable=self.rename_enabled, 
                                        bg='#2b2b2b', fg='white', selectcolor='#404040',
                                        command=self.toggle_rename_fields)
        rename_checkbox.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        # 增加QC复选框
        qc_checkbox = tk.Checkbutton(rename_frame, text="增加QC", 
                                    variable=self.qc_enabled, 
                                    bg='#2b2b2b', fg='white', selectcolor='#404040')
        qc_checkbox.grid(row=0, column=1, sticky=tk.W, pady=(0, 10))
        
        # 創建目錄复选框
        dir_checkbox = tk.Checkbutton(rename_frame, text="創建目錄", 
                                     variable=self.create_directories, 
                                     bg='#2b2b2b', fg='white', selectcolor='#404040')
        dir_checkbox.grid(row=0, column=2, sticky=tk.W, pady=(0, 10))
        
        # 替換前檔案名稱
        self.old_name_label = tk.Label(rename_frame, text="替換前的檔案名稱模式:", 
                                      bg='#2b2b2b', fg='white')
        self.old_name_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        self.old_name_entry = tk.Entry(rename_frame, textvariable=self.old_name_pattern, 
                                      bg='#404040', fg='white', insertbackground='white')
        self.old_name_entry.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 替換後檔案名稱
        self.new_name_label = tk.Label(rename_frame, text="替換後的檔案名稱模式:", 
                                      bg='#2b2b2b', fg='white')
        self.new_name_label.grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        
        self.new_name_entry = tk.Entry(rename_frame, textvariable=self.new_name_pattern, 
                                      bg='#404040', fg='white', insertbackground='white')
        self.new_name_entry.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 初始狀態：隱藏重命名欄位
        self.toggle_rename_fields()
        

        
        # 輸出結果顯示區域
        preview_frame = ttk.LabelFrame(main_frame, text="輸出結果顯示", padding="10")
        preview_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 輸出結果列表
        self.preview_listbox = tk.Listbox(preview_frame, height=6, bg='#404040', fg='white')
        self.preview_listbox.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 輸出結果滾動條
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_listbox.yview)
        preview_scrollbar.grid(row=0, column=2, sticky=(tk.N, tk.S))
        self.preview_listbox.configure(yscrollcommand=preview_scrollbar.set)
        

        
        # 執行按鈕
        execute_btn = tk.Button(main_frame, text="執行", 
                               command=self.execute_rename, 
                               bg='#007acc', fg='white', relief=tk.FLAT, 
                               font=("Arial", 12, "bold"), width=20)
        execute_btn.grid(row=4, column=0, columnspan=3, pady=(20, 0))
        
        # 配置網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        folder_frame.columnconfigure(0, weight=1)
        rename_frame.columnconfigure(0, weight=1)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        
    def add_folder(self):
        folder = filedialog.askdirectory(title="選擇包含PTS檔案的文件夾")
        if folder:
            if folder not in self.selected_folders:
                self.selected_folders.append(folder)
                self.folder_listbox.insert(tk.END, folder)
                
    def remove_folder(self):
        selection = self.folder_listbox.curselection()
        if selection:
            # 從後往前刪除，避免索引變化
            for index in reversed(selection):
                folder = self.folder_listbox.get(index)
                self.selected_folders.remove(folder)
                self.folder_listbox.delete(index)
                
    def clear_folders(self):
        self.selected_folders.clear()
        self.folder_listbox.delete(0, tk.END)
        self.preview_listbox.delete(0, tk.END)
        
    def toggle_rename_fields(self):
        """切換重命名欄位的顯示/隱藏"""
        if self.rename_enabled.get():
            self.old_name_label.grid()
            self.old_name_entry.grid()
            self.new_name_label.grid()
            self.new_name_entry.grid()
        else:
            self.old_name_label.grid_remove()
            self.old_name_entry.grid_remove()
            self.new_name_label.grid_remove()
            self.new_name_entry.grid_remove()
        
    def get_pts_files(self):
        pts_files = []
        for folder in self.selected_folders:
            pts_pattern = os.path.join(folder, "*.pts")
            pts_files.extend(glob.glob(pts_pattern))
        return pts_files
        
    def generate_new_name(self, old_path, old_pattern, new_pattern):
        old_filename = os.path.basename(old_path)
        name_without_ext = os.path.splitext(old_filename)[0]
        ext = os.path.splitext(old_filename)[1]
        
        # 如果沒有設置替換模式，返回原始檔案名稱
        if not old_pattern or not new_pattern:
            return old_filename
        
        # 嘗試提取數字部分
        num_match = re.search(r'\d+', name_without_ext)
        num = num_match.group() if num_match else ""
        
        # 替換模式中的佔位符
        new_name = new_pattern
        new_name = new_name.replace("{old}", name_without_ext)
        new_name = new_name.replace("{ext}", ext)
        new_name = new_name.replace("{num}", num)
        
        # 如果沒有使用佔位符，嘗試正則表達式替換
        if "{old}" not in new_pattern and "{ext}" not in new_pattern and "{num}" not in new_pattern:
            try:
                # 檢查是否真的能找到匹配的模式
                if re.search(old_pattern, old_filename):
                    new_name = re.sub(old_pattern, new_pattern, old_filename)
                else:
                    # 如果找不到匹配的模式，返回原始檔案名稱
                    new_name = old_filename
            except re.error:
                new_name = old_filename
                
        return new_name
        
    def create_qc_file(self, pts_file):
        """創建QC檔案"""
        try:
            # 讀取原始PTS檔案內容
            with open(pts_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 分割內容為行
            lines = content.split('\n')
            
            # 找到 "Parameter," 行的索引
            parameter_index = -1
            qa_index = -1
            
            for i, line in enumerate(lines):
                if line.strip().startswith("Parameter,"):
                    parameter_index = i
                elif line.strip().startswith("QA,"):
                    qa_index = i
                    break
            
            if parameter_index == -1:
                return False, "找不到 'Parameter,' 行"
            
            if qa_index == -1:
                return False, "找不到 'QA,' 行"
            
            # 保留到 "Parameter," 行，然後跳到 "QA," 行之後（不包含"QA,"行）
            new_lines = lines[:parameter_index + 1] + lines[qa_index + 1:]
            
            # 生成QC檔案名稱
            file_path = Path(pts_file)
            qc_filename = file_path.stem + "_QC" + file_path.suffix
            qc_file_path = file_path.parent / qc_filename
            
            # 如果QC檔案已存在，先刪除
            if qc_file_path.exists():
                qc_file_path.unlink()
            
            # 寫入QC檔案
            with open(qc_file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_lines))
            
            # 執行額外的QC修改
            self.modify_qc_file_content(qc_file_path)
            
            return True, qc_filename
            
        except Exception as e:
            return False, str(e)
            
    def modify_qc_file_content(self, qc_file_path):
        """修改QC檔案內容"""
        try:
            # 讀取QC檔案內容
            with open(qc_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            modified_lines = []
            
            # 1. 修改QCOnlySBinAlter行
            for line in lines:
                if line.strip().startswith("QCOnlySBinAlter="):
                    modified_lines.append("QCOnlySBinAlter=1,0")
                else:
                    modified_lines.append(line)
            
            # 2. 計算ParamCnt並更新
            param_count = 0
            parameter_start = -1
            end_index = -1
            
            for i, line in enumerate(modified_lines):
                if line.strip().startswith("Parameter,"):
                    parameter_start = i
                elif line.strip() == "END":
                    end_index = i
                    break
            
            if parameter_start != -1 and end_index != -1:
                # 計算從Parameter行到END行之間的非空行數
                for i in range(parameter_start + 1, end_index):
                    if modified_lines[i].strip():  # 非空行
                        param_count += 1
            
            # 更新ParamCnt行
            for i, line in enumerate(modified_lines):
                if line.strip().startswith("ParamCnt"):
                    modified_lines[i] = f"ParamCnt={param_count}"
                    break
            
            # 3. 處理[Bin Definition]部分
            bin_def_start = -1
            for i, line in enumerate(modified_lines):
                if line.strip() == "[Bin Definition]":
                    bin_def_start = i
                    break
            
            if bin_def_start != -1:
                # 找到[Bin Definition]後的第一個數字行
                bin_lines_start = -1
                for i in range(bin_def_start + 1, len(modified_lines)):
                    if modified_lines[i].strip() and modified_lines[i].strip()[0].isdigit():
                        bin_lines_start = i
                        break
                
                if bin_lines_start != -1:
                    # 保留[Bin Definition]之前的所有內容
                    new_lines = modified_lines[:bin_def_start + 1]
                    
                    # 處理[Bin Definition]之後的內容
                    for i in range(bin_lines_start, len(modified_lines)):
                        line = modified_lines[i]
                        if line.strip():
                            if line.strip()[0].isdigit():
                                # 檢查是否以1或31開頭
                                parts = line.strip().split(',')
                                if parts and parts[0].strip() in ['1', '31']:
                                    new_lines.append(line)
                            else:
                                # 非數字開頭的行，保留
                                new_lines.append(line)
                        else:
                            # 空行，保留
                            new_lines.append(line)
                    
                    modified_lines = new_lines
            
            # 寫回修改後的內容
            with open(qc_file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(modified_lines))
                
        except Exception as e:
            print(f"修改QC檔案內容時發生錯誤: {str(e)}")
            
    def create_pts_directory(self, pts_file, original_folder):
        """為PTS檔案創建目錄並複製整個文件夾內容，只保留對應的PTS檔案"""
        try:
            import shutil
            file_path = Path(pts_file)
            # 使用檔案名（不含擴展名）作為目錄名
            dir_name = file_path.stem
            

            
            # 檢查PTS檔案名稱是否與原始文件夾名稱相同
            original_folder_name = Path(original_folder).name
            if dir_name == original_folder_name:
                return False, f"PTS檔案名稱 '{dir_name}' 與文件夾名稱 '{original_folder_name}' 相同，無需創建目錄"
            
            # 目標目錄在原始文件夾的父目錄中
            parent_dir = Path(original_folder).parent
            dir_path = parent_dir / dir_name
            
            # 檢查原始文件夾是否存在
            if not Path(original_folder).exists():
                return False, f"原始文件夾不存在: {original_folder}"
            
            # 如果目錄已存在，先刪除
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                except Exception as e:
                    return False, f"刪除現有目錄失敗: {str(e)}"
            
            # 複製整個原始文件夾到父目錄
            try:
                shutil.copytree(original_folder, dir_path)
            except Exception as e:
                return False, f"複製文件夾失敗: {str(e)}"
            
            # 刪除除了當前PTS檔案之外的所有其他PTS檔案
            try:
                pts_files_to_delete = []
                for pts_file_in_dir in dir_path.glob("*.pts"):
                    if pts_file_in_dir.name != file_path.name:
                        pts_files_to_delete.append(pts_file_in_dir)
                
                for pts_file_to_delete in pts_files_to_delete:
                    pts_file_to_delete.unlink()
                    
            except Exception as e:
                return False, f"刪除其他PTS檔案失敗: {str(e)}"
            
            # 刪除所有*.ini檔案
            try:
                ini_files_to_delete = []
                for ini_file_in_dir in dir_path.glob("*.ini"):
                    ini_files_to_delete.append(ini_file_in_dir)
                
                for ini_file_to_delete in ini_files_to_delete:
                    ini_file_to_delete.unlink()
                    
            except Exception as e:
                return False, f"刪除INI檔案失敗: {str(e)}"
            
            # 如果當前PTS檔案不在創建的目錄中，複製它
            target_pts_file = dir_path / file_path.name
            if not target_pts_file.exists():
                try:
                    shutil.copy2(pts_file, target_pts_file)
                except Exception as e:
                    return False, f"複製當前PTS檔案失敗: {str(e)}"
            
            return True, dir_name
            
        except Exception as e:
            return False, f"創建目錄時發生未知錯誤: {str(e)}"
        
    def preview_rename(self):
        self.preview_listbox.delete(0, tk.END)
        pts_files = self.get_pts_files()
        
        if not pts_files:
            messagebox.showwarning("警告", "沒有找到PTS檔案！")
            return
            
        # 如果沒有設置替換模式，顯示原始檔案名稱
        if not self.old_name_pattern.get() or not self.new_name_pattern.get():
            self.preview_listbox.insert(tk.END, "=== 顯示原始檔案名稱 ===")
            for pts_file in pts_files:
                old_name = os.path.basename(pts_file)
                self.preview_listbox.insert(tk.END, f"原始檔案：{old_name}")
            return
            
        for pts_file in pts_files:
            old_name = os.path.basename(pts_file)
            new_name = self.generate_new_name(pts_file, self.old_name_pattern.get(), self.new_name_pattern.get())
            
            # 檢查是否真的發生了替換
            if new_name == old_name:
                preview_text = f"無替換：{old_name} (找不到匹配的模式)"
            else:
                preview_text = f"{old_name} → {new_name}"
            self.preview_listbox.insert(tk.END, preview_text)
            
    def execute_rename(self):
        pts_files = self.get_pts_files()
        
        if not pts_files:
            messagebox.showwarning("警告", "沒有找到PTS檔案！")
            return
            
        # 檢查是否需要重命名
        if self.rename_enabled.get():
            if not self.old_name_pattern.get() or not self.new_name_pattern.get():
                messagebox.showwarning("警告", "請輸入替換前和替換後的檔案名稱模式才能執行重命名！")
                return
        
        # 檢查是否需要執行任何操作
        if not self.rename_enabled.get() and not self.qc_enabled.get() and not self.create_directories.get():
            messagebox.showwarning("警告", "請至少選擇一個操作（重新命名、增加QC或創建目錄）！")
            return
            
        success_count = 0
        error_count = 0
        rename_results = []  # 儲存重命名結果
        qc_results = []  # 儲存QC結果
        dir_results = []  # 儲存目錄創建結果
        
        # 按文件夾分組處理PTS檔案
        folder_pts_map = {}
        for pts_file in pts_files:
            folder = os.path.dirname(pts_file)
            if folder not in folder_pts_map:
                folder_pts_map[folder] = []
            folder_pts_map[folder].append(pts_file)
        
        # 顯示處理進度
        total_files = len(pts_files)
        processed_files = 0
        
        for pts_file in pts_files:
            current_file = pts_file
            old_name = os.path.basename(pts_file)
            
            # 執行重命名（如果啟用）
            if self.rename_enabled.get():
                try:
                    new_name = self.generate_new_name(current_file, self.old_name_pattern.get(), self.new_name_pattern.get())
                    
                    # 檢查是否真的發生了替換
                    if new_name == old_name:
                        # 沒有發生替換，記錄為失敗
                        error_count += 1
                        rename_results.append((old_name, old_name, False, "找不到匹配的模式"))
                        continue
                    
                    new_path = os.path.join(os.path.dirname(current_file), new_name)
                    
                    # 檢查新檔案名稱是否已存在
                    if os.path.exists(new_path) and current_file != new_path:
                        messagebox.showerror("錯誤", f"檔案 {new_name} 已存在！")
                        error_count += 1
                        rename_results.append((old_name, new_name, False, f"檔案 {new_name} 已存在"))
                        continue
                        
                    os.rename(current_file, new_path)
                    current_file = new_path
                    success_count += 1
                    rename_results.append((old_name, new_name, True, ""))
                    
                except Exception as e:
                    error_msg = f"重命名 {old_name} 時發生錯誤：{str(e)}"
                    messagebox.showerror("錯誤", error_msg)
                    error_count += 1
                    rename_results.append((old_name, "", False, str(e)))
                    continue
            
            # 執行QC處理（如果啟用）
            qc_file_path = None
            if self.qc_enabled.get():
                try:
                    # 檢查PTS檔案名稱是否已經以"_QC"結尾
                    current_filename = os.path.basename(current_file)
                    if current_filename.endswith("_QC.pts"):
                        qc_results.append((current_filename, current_filename, False, "檔案名稱已包含'_QC'，無需執行QC處理"))
                        continue
                    
                    qc_success, qc_result = self.create_qc_file(current_file)
                    if qc_success:
                        success_count += 1
                        qc_results.append((os.path.basename(current_file), qc_result, True, ""))
                        # 保存QC文件路徑，但不更新current_file
                        qc_file_path = os.path.join(os.path.dirname(current_file), qc_result)
                    else:
                        error_count += 1
                        qc_results.append((os.path.basename(current_file), "", False, qc_result))
                except Exception as e:
                    error_count += 1
                    qc_results.append((os.path.basename(current_file), "", False, str(e)))
            
            # 執行目錄創建（如果啟用）
            if self.create_directories.get():
                try:
                    original_folder = os.path.dirname(pts_file)
                    
                    # 為原始/重命名的PTS文件創建目錄
                    dir_success, dir_result = self.create_pts_directory(current_file, original_folder)
                    if dir_success:
                        success_count += 1
                        dir_results.append((os.path.basename(current_file), dir_result, True, ""))
                    else:
                        error_count += 1
                        dir_results.append((os.path.basename(current_file), "", False, dir_result))
                    
                    # 如果QC處理成功，也為QC文件創建目錄
                    if qc_file_path and os.path.exists(qc_file_path):
                        qc_dir_success, qc_dir_result = self.create_pts_directory(qc_file_path, original_folder)
                        if qc_dir_success:
                            success_count += 1
                            dir_results.append((os.path.basename(qc_file_path), qc_dir_result, True, ""))
                        else:
                            error_count += 1
                            dir_results.append((os.path.basename(qc_file_path), "", False, qc_dir_result))
                            
                except Exception as e:
                    error_count += 1
                    dir_results.append((os.path.basename(current_file), "", False, str(e)))
            
            # 更新進度
            processed_files += 1
            if processed_files % 5 == 0 or processed_files == total_files:  # 每處理5個文件或最後一個文件時更新
                self.preview_listbox.delete(0, tk.END)
                self.preview_listbox.insert(tk.END, f"正在處理... {processed_files}/{total_files}")
                self.root.update()  # 強制更新界面
                
        # 顯示結果
        operation_text = []
        if self.rename_enabled.get():
            operation_text.append("重命名")
        if self.qc_enabled.get():
            operation_text.append("QC處理")
        if self.create_directories.get():
            operation_text.append("創建目錄")
        
        message = f"{' + '.join(operation_text)}完成！\n成功：{success_count} 個檔案\n失敗：{error_count} 個檔案"
        messagebox.showinfo("完成", message)
        
        # 在輸出結果顯示區域顯示結果
        self.preview_listbox.delete(0, tk.END)
        self.preview_listbox.insert(tk.END, f"=== 操作完成 ===")
        self.preview_listbox.insert(tk.END, f"成功：{success_count} 個檔案")
        self.preview_listbox.insert(tk.END, f"失敗：{error_count} 個檔案")
        self.preview_listbox.insert(tk.END, "")
        
        # 顯示重命名結果
        if self.rename_enabled.get() and rename_results:
            self.preview_listbox.insert(tk.END, "=== 重命名結果 ===")
            for old_name, new_name, success, error_msg in rename_results:
                if success:
                    self.preview_listbox.insert(tk.END, f"✓ {old_name} → {new_name}")
                else:
                    if new_name:
                        self.preview_listbox.insert(tk.END, f"✗ {old_name} → {new_name} ({error_msg})")
                    else:
                        self.preview_listbox.insert(tk.END, f"✗ {old_name} → 重命名失敗 ({error_msg})")
        
        # 顯示QC結果
        if self.qc_enabled.get() and qc_results:
            self.preview_listbox.insert(tk.END, "")
            self.preview_listbox.insert(tk.END, "=== QC處理結果 ===")
            for original_name, qc_name, success, error_msg in qc_results:
                if success:
                    self.preview_listbox.insert(tk.END, f"✓ {original_name} → {qc_name}")
                else:
                    self.preview_listbox.insert(tk.END, f"✗ {original_name} → QC處理失敗 ({error_msg})")
        
        # 顯示目錄創建結果
        if self.create_directories.get() and dir_results:
            self.preview_listbox.insert(tk.END, "")
            self.preview_listbox.insert(tk.END, "=== 目錄創建結果 ===")
            for original_name, dir_name, success, error_msg in dir_results:
                if success:
                    self.preview_listbox.insert(tk.END, f"✓ {original_name} → 目錄: {dir_name}")
                else:
                    self.preview_listbox.insert(tk.END, f"✗ {original_name} → 目錄創建失敗 ({error_msg})")

def main():
    root = tk.Tk()
    app = PTSFileRenamer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
