# Story 5: 前端界面整合 - Vue.js 可視化管理界面

## 📋 Story 概要

**Story ID**: EDTS-005  
**Story 名稱**: 前端界面整合  
**Epic**: 郵件下載狀態追蹤  
**優先級**: P1 (Important)  
**估計工作量**: 20 Story Points (3-4 工作日)  
**依賴**: Story 1, Story 2, Story 3, Story 4

### User Story
**As a** 系統管理員和監控人員  
**I want** 直觀的 Web 界面來管理和監控郵件下載狀態  
**So that** 可以實時查看下載狀態、觸發重試操作、分析統計數據並快速處理問題

---

## 🎯 驗收標準

### 必須完成的功能 (Must Have)
- [ ] ✅ 下載狀態儀表板組件
- [ ] ✅ 下載狀態詳情表格組件
- [ ] ✅ 手動重試操作界面
- [ ] ✅ 統計圖表和分析組件
- [ ] ✅ 整合到現有 monitoring 系統
- [ ] ✅ 響應式設計支援多種設備
- [ ] ✅ 實時數據更新機制

### 應該有的功能 (Should Have)
- [ ] ✅ 高級過濾和搜索功能
- [ ] ✅ 數據導出功能
- [ ] ✅ 自定義儀表板配置
- [ ] ✅ 告警通知顯示

### 期望的功能 (Could Have)
- [ ] ✅ WebSocket 實時推送
- [ ] ✅ 移動端優化
- [ ] ✅ 用戶偏好設置
- [ ] ✅ 鍵盤快捷鍵支援

---

## 🏗️ 技術實現詳情

### 前端架構設計

```
Frontend Architecture
├── Components/
│   ├── EmailDownloadDashboard/        # 主儀表板
│   ├── EmailDownloadDetailTable/      # 詳情表格
│   ├── EmailDownloadCharts/           # 統計圖表
│   ├── EmailRetryControls/            # 重試控制
│   └── EmailDownloadFilters/          # 過濾器
├── Services/
│   ├── downloadTrackingApi.js         # API 封裝
│   ├── realTimeUpdates.js             # 實時更新
│   └── dataFormatters.js              # 數據格式化
├── Store/
│   └── downloadTracking.js            # Vuex 狀態管理
└── Utils/
    ├── chartConfigs.js                # 圖表配置
    └── notifications.js               # 通知工具
```

### 實現文件結構

```
frontend/
├── email/
│   ├── static/
│   │   ├── css/
│   │   │   └── download-tracking.css
│   │   └── js/
│   │       ├── components/
│   │       │   ├── EmailDownloadDashboard.js
│   │       │   ├── EmailDownloadDetailTable.js
│   │       │   ├── EmailDownloadCharts.js
│   │       │   └── EmailRetryControls.js
│   │       ├── services/
│   │       │   ├── downloadTrackingApi.js
│   │       │   └── realTimeUpdates.js
│   │       └── download-tracking-app.js
│   ├── templates/
│   │   ├── download_tracking.html
│   │   └── components/
│   │       └── download-tracking-widgets.html
│   └── routes/
│       └── email_routes.py              # 新增路由
└── monitoring/
    ├── templates/
    │   └── database_manager.html         # 整合修改
    └── static/
        └── js/
            └── download-tracking-integration.js
```

---

## 🔧 詳細實現步驟

### Step 1: 主儀表板組件 (1.5天)

**檔案位置**: `frontend/email/static/js/components/EmailDownloadDashboard.js`

```javascript
/**
 * 郵件下載追蹤主儀表板組件
 * 提供下載狀態概覽、統計圖表和快速操作
 */

const EmailDownloadDashboard = {
  name: 'EmailDownloadDashboard',
  
  template: `
    <div class="email-download-dashboard">
      <!-- 統計卡片區域 -->
      <div class="statistics-section">
        <el-row :gutter="20">
          <el-col :span="6" v-for="(stat, index) in statisticsCards" :key="index">
            <el-card class="stat-card" :class="stat.type">
              <div class="stat-content">
                <div class="stat-number">{{ formatNumber(stat.value) }}</div>
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-change" :class="stat.changeType">
                  <i :class="stat.changeIcon"></i>
                  {{ stat.change }}
                </div>
              </div>
              <div class="stat-icon">
                <i :class="stat.icon"></i>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      
      <!-- 成功率和趨勢圖表 -->
      <div class="charts-section">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card>
              <div slot="header">
                <span>下載成功率</span>
                <el-select v-model="successRateTimeRange" size="small" style="float: right;">
                  <el-option label="最近1小時" value="1h"></el-option>
                  <el-option label="最近6小時" value="6h"></el-option>
                  <el-option label="最近24小時" value="24h"></el-option>
                  <el-option label="最近7天" value="7d"></el-option>
                </el-select>
              </div>
              
              <div class="success-rate-display">
                <div class="success-rate-circle">
                  <el-progress 
                    type="circle" 
                    :percentage="statistics.success_rate"
                    :width="120"
                    :color="getSuccessRateColor(statistics.success_rate)">
                    <span class="success-rate-text">{{ statistics.success_rate.toFixed(1) }}%</span>
                  </el-progress>
                </div>
                
                <div class="success-rate-details">
                  <div class="success-breakdown">
                    <div class="success-item success">
                      <span class="indicator"></span>
                      成功: {{ statistics.successful_downloads }}
                    </div>
                    <div class="success-item failed">
                      <span class="indicator"></span>
                      失敗: {{ statistics.failed_downloads }}
                    </div>
                    <div class="success-item pending">
                      <span class="indicator"></span>
                      待重試: {{ statistics.pending_retries }}
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card>
              <div slot="header">
                <span>下載趨勢</span>
                <el-button-group size="small" style="float: right;">
                  <el-button 
                    v-for="range in trendTimeRanges" 
                    :key="range.value"
                    :type="trendTimeRange === range.value ? 'primary' : ''"
                    @click="trendTimeRange = range.value; loadTrendData()">
                    {{ range.label }}
                  </el-button>
                </el-button-group>
              </div>
              
              <div ref="trendChart" class="trend-chart" style="height: 200px;"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      
      <!-- 錯誤分析 -->
      <div class="error-analysis-section">
        <el-card>
          <div slot="header">
            <span>錯誤類型分析</span>
            <div style="float: right;">
              <el-tooltip content="刷新數據" placement="top">
                <el-button icon="el-icon-refresh" size="small" @click="refreshErrorAnalysis"></el-button>
              </el-tooltip>
              <el-tooltip content="查看詳細分析" placement="top">
                <el-button icon="el-icon-data-analysis" size="small" @click="showDetailedAnalysis"></el-button>
              </el-tooltip>
            </div>
          </div>
          
          <div class="error-types-chart">
            <div v-if="statistics.error_types.length === 0" class="no-errors">
              <i class="el-icon-success"></i>
              <p>沒有錯誤記錄</p>
            </div>
            
            <div v-else class="error-types-list">
              <div 
                v-for="errorType in statistics.error_types.slice(0, 5)" 
                :key="errorType.type" 
                class="error-type-item">
                <div class="error-type-header">
                  <span class="error-type-name">{{ getErrorTypeLabel(errorType.type) }}</span>
                  <span class="error-type-count">{{ errorType.count }}</span>
                </div>
                
                <el-progress 
                  :percentage="(errorType.count / statistics.failed_downloads * 100)" 
                  :show-text="false"
                  :color="getErrorTypeColor(errorType.type)">
                </el-progress>
                
                <div class="error-type-actions">
                  <el-button 
                    size="mini" 
                    type="text" 
                    @click="showErrorDetails(errorType.type)">
                    查看詳情
                  </el-button>
                  <el-button 
                    size="mini" 
                    type="text" 
                    @click="retryErrorType(errorType.type)">
                    批量重試
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 快速操作區域 -->
      <div class="quick-actions-section">
        <el-card>
          <div slot="header">
            <span>快速操作</span>
          </div>
          
          <div class="action-buttons">
            <el-button 
              type="primary" 
              icon="el-icon-refresh" 
              @click="refreshAllData"
              :loading="loading.refresh">
              刷新數據
            </el-button>
            
            <el-button 
              type="warning" 
              icon="el-icon-refresh-right" 
              @click="processAllRetries"
              :loading="loading.processRetries"
              :disabled="statistics.pending_retries === 0">
              處理所有重試 ({{ statistics.pending_retries }})
            </el-button>
            
            <el-button 
              type="info" 
              icon="el-icon-view" 
              @click="showDetailTable">
              查看詳細列表
            </el-button>
            
            <el-button 
              type="success" 
              icon="el-icon-download" 
              @click="exportData">
              導出報告
            </el-button>
          </div>
        </el-card>
      </div>
      
      <!-- 詳細分析對話框 -->
      <el-dialog 
        title="詳細錯誤分析" 
        :visible.sync="showAnalysisDialog"
        width="80%">
        <email-error-analysis 
          v-if="showAnalysisDialog" 
          :error-data="detailedErrorData">
        </email-error-analysis>
      </el-dialog>
    </div>
  `,
  
  data() {
    return {
      // 統計數據
      statistics: {
        total_downloads: 0,
        successful_downloads: 0,
        failed_downloads: 0,
        pending_retries: 0,
        success_rate: 0,
        error_types: []
      },
      
      // 時間範圍選擇
      successRateTimeRange: '24h',
      trendTimeRange: '24h',
      trendTimeRanges: [
        { label: '1小時', value: '1h' },
        { label: '6小時', value: '6h' },
        { label: '24小時', value: '24h' },
        { label: '7天', value: '7d' }
      ],
      
      // 載入狀態
      loading: {
        refresh: false,
        processRetries: false
      },
      
      // 對話框狀態
      showAnalysisDialog: false,
      detailedErrorData: null,
      
      // 圖表實例
      trendChart: null,
      
      // 自動刷新
      refreshInterval: null,
      autoRefreshEnabled: true
    }
  },
  
  computed: {
    statisticsCards() {
      return [
        {
          label: '總下載數',
          value: this.statistics.total_downloads,
          icon: 'el-icon-download',
          type: 'primary',
          change: '+12%',
          changeType: 'positive',
          changeIcon: 'el-icon-arrow-up'
        },
        {
          label: '成功下載',
          value: this.statistics.successful_downloads,
          icon: 'el-icon-check',
          type: 'success',
          change: '+8%',
          changeType: 'positive',
          changeIcon: 'el-icon-arrow-up'
        },
        {
          label: '待重試',
          value: this.statistics.pending_retries,
          icon: 'el-icon-refresh',
          type: 'warning',
          change: '-15%',
          changeType: 'negative',
          changeIcon: 'el-icon-arrow-down'
        },
        {
          label: '失敗下載',
          value: this.statistics.failed_downloads,
          icon: 'el-icon-close',
          type: 'danger',
          change: '-5%',
          changeType: 'negative',
          changeIcon: 'el-icon-arrow-down'
        }
      ]
    }
  },
  
  mounted() {
    this.initializeComponent()
    this.loadInitialData()
    this.setupAutoRefresh()
  },
  
  beforeDestroy() {
    this.cleanupComponent()
  },
  
  methods: {
    // ==================== 初始化和清理 ====================
    
    async initializeComponent() {
      try {
        // 初始化圖表
        await this.$nextTick()
        this.initializeTrendChart()
        
        // 監聽窗口大小變化
        window.addEventListener('resize', this.handleResize)
        
        this.$message.success('儀表板初始化完成')
      } catch (error) {
        console.error('儀表板初始化失敗:', error)
        this.$message.error('儀表板初始化失敗')
      }
    },
    
    cleanupComponent() {
      // 清理定時器
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
      }
      
      // 清理事件監聽
      window.removeEventListener('resize', this.handleResize)
      
      // 清理圖表
      if (this.trendChart) {
        this.trendChart.dispose()
      }
    },
    
    // ==================== 數據載入 ====================
    
    async loadInitialData() {
      try {
        await Promise.all([
          this.loadStatistics(),
          this.loadTrendData()
        ])
      } catch (error) {
        console.error('初始數據載入失敗:', error)
        this.$message.error('數據載入失敗，請檢查網絡連接')
      }
    },
    
    async loadStatistics() {
      try {
        const timeRange = this.successRateTimeRange
        const hours = this.parseTimeRange(timeRange)
        
        const response = await downloadTrackingApi.getStatistics({ hours })
        
        if (response.success) {
          this.statistics = response.data
          this.$emit('statistics-updated', this.statistics)
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        console.error('載入統計數據失敗:', error)
        this.$message.error('載入統計數據失敗')
      }
    },
    
    async loadTrendData() {
      try {
        const response = await downloadTrackingApi.getTrends({
          period: this.trendTimeRange,
          granularity: this.getTrendGranularity(this.trendTimeRange)
        })
        
        if (response.success) {
          this.updateTrendChart(response.data)
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        console.error('載入趨勢數據失敗:', error)
        this.$message.error('載入趨勢數據失敗')
      }
    },
    
    // ==================== 圖表管理 ====================
    
    initializeTrendChart() {
      if (!this.$refs.trendChart) return
      
      // 使用 ECharts 或其他圖表庫
      this.trendChart = echarts.init(this.$refs.trendChart)
      
      const option = {
        title: { text: '' },
        tooltip: { trigger: 'axis' },
        legend: { data: ['成功下載', '失敗下載'] },
        xAxis: { type: 'category', data: [] },
        yAxis: { type: 'value' },
        series: [
          {
            name: '成功下載',
            type: 'line',
            data: [],
            smooth: true,
            itemStyle: { color: '#67c23a' }
          },
          {
            name: '失敗下載',
            type: 'line',
            data: [],
            smooth: true,
            itemStyle: { color: '#f56c6c' }
          }
        ]
      }
      
      this.trendChart.setOption(option)
    },
    
    updateTrendChart(trendData) {
      if (!this.trendChart || !trendData.data_points) return
      
      const timeLabels = trendData.data_points.map(point => point.time)
      const successData = trendData.data_points.map(point => point.successful_downloads)
      const failedData = trendData.data_points.map(point => point.failed_downloads)
      
      this.trendChart.setOption({
        xAxis: { data: timeLabels },
        series: [
          { data: successData },
          { data: failedData }
        ]
      })
    },
    
    handleResize() {
      if (this.trendChart) {
        this.trendChart.resize()
      }
    },
    
    // ==================== 用戶操作 ====================
    
    async refreshAllData() {
      this.loading.refresh = true
      
      try {
        await this.loadInitialData()
        this.$message.success('數據已刷新')
      } catch (error) {
        this.$message.error('刷新數據失敗')
      } finally {
        this.loading.refresh = false
      }
    },
    
    async processAllRetries() {
      if (this.statistics.pending_retries === 0) {
        this.$message.info('沒有待重試的任務')
        return
      }
      
      try {
        await this.$confirm(
          `確定要處理所有 ${this.statistics.pending_retries} 個待重試任務嗎？`,
          '確認操作',
          { type: 'warning' }
        )
        
        this.loading.processRetries = true
        
        const response = await downloadTrackingApi.processRetries({
          batch_size: Math.min(this.statistics.pending_retries, 50)
        })
        
        if (response.success) {
          this.$message.success(
            `重試處理完成: 成功 ${response.data.success_count}, 失敗 ${response.data.failure_count}`
          )
          
          // 刷新數據
          await this.loadStatistics()
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('處理重試失敗:', error)
          this.$message.error('處理重試失敗')
        }
      } finally {
        this.loading.processRetries = false
      }
    },
    
    showDetailTable() {
      this.$emit('show-detail-table')
    },
    
    async exportData() {
      try {
        const response = await downloadTrackingApi.exportReport({
          format: 'excel',
          period: '24h'
        })
        
        if (response.success) {
          // 觸發文件下載
          const link = document.createElement('a')
          link.href = response.data.download_url
          link.download = response.data.filename
          link.click()
          
          this.$message.success('報告導出成功')
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        console.error('導出報告失敗:', error)
        this.$message.error('導出報告失敗')
      }
    },
    
    async refreshErrorAnalysis() {
      try {
        await this.loadStatistics()
        this.$message.success('錯誤分析已刷新')
      } catch (error) {
        this.$message.error('刷新錯誤分析失敗')
      }
    },
    
    async showDetailedAnalysis() {
      try {
        const response = await downloadTrackingApi.getErrorAnalysis({
          hours: 24,
          top_n: 20
        })
        
        if (response.success) {
          this.detailedErrorData = response.data
          this.showAnalysisDialog = true
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        console.error('載入詳細分析失敗:', error)
        this.$message.error('載入詳細分析失敗')
      }
    },
    
    async showErrorDetails(errorType) {
      // 顯示特定錯誤類型的詳細信息
      this.$emit('show-error-details', errorType)
    },
    
    async retryErrorType(errorType) {
      try {
        await this.$confirm(
          `確定要重試所有 "${this.getErrorTypeLabel(errorType)}" 類型的失敗郵件嗎？`,
          '批量重試確認',
          { type: 'warning' }
        )
        
        const response = await downloadTrackingApi.retryByErrorType({
          error_type: errorType,
          priority: 'HIGH'
        })
        
        if (response.success) {
          this.$message.success(`已安排 ${response.data.scheduled_count} 個郵件重試`)
          await this.loadStatistics()
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量重試失敗:', error)
          this.$message.error('批量重試失敗')
        }
      }
    },
    
    // ==================== 自動刷新 ====================
    
    setupAutoRefresh() {
      if (this.autoRefreshEnabled) {
        this.refreshInterval = setInterval(() => {
          this.loadStatistics()
        }, 30000) // 30秒刷新一次
      }
    },
    
    toggleAutoRefresh() {
      this.autoRefreshEnabled = !this.autoRefreshEnabled
      
      if (this.autoRefreshEnabled) {
        this.setupAutoRefresh()
        this.$message.success('自動刷新已開啟')
      } else {
        if (this.refreshInterval) {
          clearInterval(this.refreshInterval)
          this.refreshInterval = null
        }
        this.$message.info('自動刷新已關閉')
      }
    },
    
    // ==================== 工具方法 ====================
    
    formatNumber(number) {
      if (number >= 1000000) {
        return (number / 1000000).toFixed(1) + 'M'
      } else if (number >= 1000) {
        return (number / 1000).toFixed(1) + 'K'
      }
      return number.toString()
    },
    
    getSuccessRateColor(rate) {
      if (rate >= 95) return '#67c23a'
      if (rate >= 85) return '#e6a23c'
      if (rate >= 70) return '#f56c6c'
      return '#909399'
    },
    
    getErrorTypeLabel(type) {
      const labels = {
        'connection_error': '連接錯誤',
        'timeout': '超時',
        'authentication_error': '認證失敗',
        'server_error': '服務器錯誤',
        'rate_limit': '速率限制',
        'unknown': '未知錯誤'
      }
      return labels[type] || type
    },
    
    getErrorTypeColor(type) {
      const colors = {
        'connection_error': '#e6a23c',
        'timeout': '#409eff',
        'authentication_error': '#f56c6c',
        'server_error': '#f56c6c',
        'rate_limit': '#909399',
        'unknown': '#606266'
      }
      return colors[type] || '#606266'
    },
    
    parseTimeRange(range) {
      const mapping = {
        '1h': 1,
        '6h': 6,
        '24h': 24,
        '7d': 168
      }
      return mapping[range] || 24
    },
    
    getTrendGranularity(range) {
      const mapping = {
        '1h': '5m',
        '6h': '30m',
        '24h': '1h',
        '7d': '6h'
      }
      return mapping[range] || '1h'
    }
  },
  
  watch: {
    successRateTimeRange() {
      this.loadStatistics()
    },
    
    trendTimeRange() {
      this.loadTrendData()
    }
  }
}

// 註冊組件
if (typeof Vue !== 'undefined') {
  Vue.component('email-download-dashboard', EmailDownloadDashboard)
}
```

### Step 2: 詳情表格組件 (1天)

**檔案位置**: `frontend/email/static/js/components/EmailDownloadDetailTable.js`

```javascript
/**
 * 郵件下載狀態詳情表格組件
 * 提供詳細的下載狀態列表，支援過濾、排序、分頁和批量操作
 */

const EmailDownloadDetailTable = {
  name: 'EmailDownloadDetailTable',
  
  template: `
    <div class="email-download-detail-table">
      <!-- 過濾器區域 -->
      <div class="filter-section">
        <el-card>
          <div class="filter-form">
            <el-form :inline="true" size="small">
              <el-form-item label="狀態:">
                <el-select v-model="filters.status" placeholder="選擇狀態" clearable @change="applyFilters">
                  <el-option label="全部" value=""></el-option>
                  <el-option label="待處理" value="pending"></el-option>
                  <el-option label="下載中" value="downloading"></el-option>
                  <el-option label="已完成" value="completed"></el-option>
                  <el-option label="失敗" value="failed"></el-option>
                  <el-option label="待重試" value="retry_scheduled"></el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="錯誤類型:">
                <el-select v-model="filters.errorType" placeholder="選擇錯誤類型" clearable @change="applyFilters">
                  <el-option label="全部" value=""></el-option>
                  <el-option 
                    v-for="errorType in availableErrorTypes" 
                    :key="errorType.value"
                    :label="errorType.label" 
                    :value="errorType.value">
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="郵件ID:">
                <el-input 
                  v-model="filters.emailId" 
                  placeholder="輸入郵件ID" 
                  @keyup.enter="applyFilters"
                  clearable>
                </el-input>
              </el-form-item>
              
              <el-form-item label="時間範圍:">
                <el-date-picker
                  v-model="filters.dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="開始時間"
                  end-placeholder="結束時間"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="applyFilters">
                </el-date-picker>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="applyFilters">查詢</el-button>
                <el-button icon="el-icon-refresh" @click="resetFilters">重置</el-button>
                <el-button icon="el-icon-download" @click="exportFiltered">導出</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </div>
      
      <!-- 批量操作區域 -->
      <div class="batch-actions-section" v-if="selectedRows.length > 0">
        <el-card>
          <div class="batch-actions">
            <span class="selection-info">已選擇 {{ selectedRows.length }} 項</span>
            
            <div class="action-buttons">
              <el-button 
                type="success" 
                icon="el-icon-refresh" 
                size="small"
                @click="batchRetry"
                :loading="batchLoading.retry">
                批量重試
              </el-button>
              
              <el-button 
                type="warning" 
                icon="el-icon-delete" 
                size="small"
                @click="batchReset"
                :loading="batchLoading.reset">
                批量重置
              </el-button>
              
              <el-button 
                type="info" 
                icon="el-icon-download" 
                size="small"
                @click="exportSelected">
                導出選中
              </el-button>
              
              <el-button 
                size="small"
                @click="clearSelection">
                清除選擇
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 數據表格 -->
      <div class="table-section">
        <el-card>
          <el-table 
            :data="tableData" 
            v-loading="loading"
            @selection-change="handleSelectionChange"
            @sort-change="handleSortChange"
            stripe
            border
            style="width: 100%"
            :default-sort="{prop: 'created_at', order: 'descending'}">
            
            <el-table-column type="selection" width="55" fixed="left"></el-table-column>
            
            <el-table-column prop="id" label="追蹤ID" width="80" sortable="custom"></el-table-column>
            
            <el-table-column prop="email_id" label="郵件ID" width="100" sortable="custom">
              <template slot-scope="scope">
                <el-button type="text" @click="showEmailDetails(scope.row.email_id)">
                  {{ scope.row.email_id }}
                </el-button>
              </template>
            </el-table-column>
            
            <el-table-column label="狀態" width="120" prop="status" sortable="custom">
              <template slot-scope="scope">
                <el-tag :type="getStatusTagType(scope.row.status)" size="small">
                  {{ getStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="進度" width="120">
              <template slot-scope="scope">
                <div class="progress-info">
                  <span class="progress-text">
                    {{ scope.row.download_attempt }} / {{ scope.row.max_retry_count }}
                  </span>
                  <el-progress 
                    :percentage="(scope.row.download_attempt / scope.row.max_retry_count * 100)"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getProgressColor(scope.row.status)">
                  </el-progress>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="創建時間" width="160" prop="created_at" sortable="custom">
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.created_at) }}
              </template>
            </el-table-column>
            
            <el-table-column label="開始時間" width="160" prop="started_at" sortable="custom">
              <template slot-scope="scope">
                {{ scope.row.started_at ? formatDateTime(scope.row.started_at) : '-' }}
              </template>
            </el-table-column>
            
            <el-table-column label="完成時間" width="160" prop="completed_at" sortable="custom">
              <template slot-scope="scope">
                {{ scope.row.completed_at ? formatDateTime(scope.row.completed_at) : '-' }}
              </template>
            </el-table-column>
            
            <el-table-column label="下次重試" width="160" prop="next_retry_at" sortable="custom">
              <template slot-scope="scope">
                <span v-if="scope.row.next_retry_at" :class="getRetryTimeClass(scope.row.next_retry_at)">
                  {{ formatDateTime(scope.row.next_retry_at) }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column label="下載信息" width="150">
              <template slot-scope="scope">
                <div v-if="scope.row.download_size_bytes || scope.row.download_duration_seconds">
                  <div v-if="scope.row.download_size_bytes">
                    大小: {{ formatFileSize(scope.row.download_size_bytes) }}
                  </div>
                  <div v-if="scope.row.download_duration_seconds">
                    耗時: {{ scope.row.download_duration_seconds.toFixed(2) }}s
                  </div>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column label="錯誤信息" min-width="200">
              <template slot-scope="scope">
                <div v-if="scope.row.error_message">
                  <el-tooltip :content="scope.row.error_message" placement="top" :disabled="scope.row.error_message.length <= 50">
                    <span class="error-message">
                      {{ scope.row.error_message.length > 50 ? scope.row.error_message.substring(0, 50) + '...' : scope.row.error_message }}
                    </span>
                  </el-tooltip>
                  
                  <el-tag v-if="scope.row.error_type" size="mini" type="warning" style="margin-left: 5px;">
                    {{ getErrorTypeLabel(scope.row.error_type) }}
                  </el-tag>
                </div>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="180" fixed="right">
              <template slot-scope="scope">
                <el-button-group size="mini">
                  <el-button 
                    type="primary" 
                    icon="el-icon-view"
                    @click="showRowDetails(scope.row)"
                    title="查看詳情">
                  </el-button>
                  
                  <el-button 
                    type="warning" 
                    icon="el-icon-refresh"
                    @click="retryEmail(scope.row)"
                    :disabled="scope.row.status === 'downloading'"
                    title="重試下載">
                  </el-button>
                  
                  <el-button 
                    type="danger" 
                    icon="el-icon-delete"
                    @click="resetEmail(scope.row)"
                    title="重置狀態">
                  </el-button>
                  
                  <el-button 
                    type="info" 
                    icon="el-icon-time"
                    @click="showRetryHistory(scope.row)"
                    title="重試歷史">
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
      
      <!-- 分頁 -->
      <div class="pagination-section">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.current"
          :page-sizes="[20, 50, 100, 200]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
      
      <!-- 詳情對話框 -->
      <el-dialog 
        title="下載狀態詳情" 
        :visible.sync="showDetailDialog"
        width="70%">
        <email-download-details 
          v-if="showDetailDialog && selectedRowData"
          :download-data="selectedRowData"
          @retry="handleDetailRetry"
          @reset="handleDetailReset">
        </email-download-details>
      </el-dialog>
      
      <!-- 重試歷史對話框 -->
      <el-dialog 
        title="重試歷史" 
        :visible.sync="showHistoryDialog"
        width="80%">
        <email-retry-history 
          v-if="showHistoryDialog && selectedRowData"
          :email-id="selectedRowData.email_id">
        </email-retry-history>
      </el-dialog>
    </div>
  `,
  
  data() {
    return {
      // 表格數據
      tableData: [],
      selectedRows: [],
      
      // 載入狀態
      loading: false,
      batchLoading: {
        retry: false,
        reset: false
      },
      
      // 過濾器
      filters: {
        status: '',
        errorType: '',
        emailId: '',
        dateRange: null
      },
      
      // 排序
      currentSort: {
        prop: 'created_at',
        order: 'descending'
      },
      
      // 分頁
      pagination: {
        current: 1,
        size: 50,
        total: 0
      },
      
      // 對話框狀態
      showDetailDialog: false,
      showHistoryDialog: false,
      selectedRowData: null,
      
      // 可用的錯誤類型
      availableErrorTypes: [
        { label: '連接錯誤', value: 'connection_error' },
        { label: '超時', value: 'timeout' },
        { label: '認證失敗', value: 'authentication_error' },
        { label: '服務器錯誤', value: 'server_error' },
        { label: '速率限制', value: 'rate_limit' },
        { label: '未知錯誤', value: 'unknown' }
      ]
    }
  },
  
  mounted() {
    this.loadData()
  },
  
  methods: {
    // ==================== 數據載入 ====================
    
    async loadData() {
      this.loading = true
      
      try {
        const params = this.buildQueryParams()
        const response = await downloadTrackingApi.getDownloadStatusList(params)
        
        if (response.success) {
          this.tableData = response.data.items || response.data
          this.pagination.total = response.data.total || this.tableData.length
          
          // 如果返回的數據少於頁面大小，說明是最後一頁
          if (this.tableData.length < this.pagination.size && this.pagination.current > 1) {
            this.pagination.total = (this.pagination.current - 1) * this.pagination.size + this.tableData.length
          }
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        console.error('載入數據失敗:', error)
        this.$message.error('載入數據失敗')
        this.tableData = []
      } finally {
        this.loading = false
      }
    },
    
    buildQueryParams() {
      const params = {
        limit: this.pagination.size,
        offset: (this.pagination.current - 1) * this.pagination.size
      }
      
      // 添加過濾條件
      if (this.filters.status) {
        params.status = this.filters.status
      }
      
      if (this.filters.errorType) {
        params.error_type = this.filters.errorType
      }
      
      if (this.filters.emailId) {
        params.email_id = this.filters.emailId
      }
      
      if (this.filters.dateRange && this.filters.dateRange.length === 2) {
        params.start_time = this.filters.dateRange[0]
        params.end_time = this.filters.dateRange[1]
      }
      
      // 添加排序
      if (this.currentSort.prop) {
        params.order_by = this.currentSort.prop
        params.order_direction = this.currentSort.order === 'ascending' ? 'ASC' : 'DESC'
      }
      
      return params
    },
    
    // ==================== 過濾和排序 ====================
    
    applyFilters() {
      this.pagination.current = 1 // 重置到第一頁
      this.loadData()
    },
    
    resetFilters() {
      this.filters = {
        status: '',
        errorType: '',
        emailId: '',
        dateRange: null
      }
      this.applyFilters()
    },
    
    handleSortChange({ prop, order }) {
      this.currentSort = { prop, order }
      this.loadData()
    },
    
    // ==================== 分頁處理 ====================
    
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadData()
    },
    
    handleCurrentChange(page) {
      this.pagination.current = page
      this.loadData()
    },
    
    // ==================== 選擇處理 ====================
    
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    clearSelection() {
      this.$refs.table && this.$refs.table.clearSelection()
      this.selectedRows = []
    },
    
    // ==================== 單行操作 ====================
    
    showRowDetails(row) {
      this.selectedRowData = row
      this.showDetailDialog = true
    },
    
    async retryEmail(row) {
      try {
        await this.$confirm(
          `確定要重試郵件 ${row.email_id} 的下載嗎？`,
          '確認重試',
          { type: 'warning' }
        )
        
        const response = await downloadTrackingApi.manualRetry({
          email_ids: [row.email_id],
          reason: 'manual_single_retry'
        })
        
        if (response.success) {
          this.$message.success('重試已安排')
          await this.loadData()
          this.$emit('retry-triggered', [row.email_id])
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重試失敗:', error)
          this.$message.error('重試失敗')
        }
      }
    },
    
    async resetEmail(row) {
      try {
        await this.$confirm(
          `確定要重置郵件 ${row.email_id} 的下載狀態嗎？此操作不可恢復。`,
          '確認重置',
          { type: 'warning' }
        )
        
        const response = await downloadTrackingApi.resetDownloadStatus(row.email_id)
        
        if (response.success) {
          this.$message.success('狀態已重置')
          await this.loadData()
          this.$emit('status-reset', row.email_id)
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重置失敗:', error)
          this.$message.error('重置失敗')
        }
      }
    },
    
    showRetryHistory(row) {
      this.selectedRowData = row
      this.showHistoryDialog = true
    },
    
    showEmailDetails(emailId) {
      this.$emit('show-email-details', emailId)
    },
    
    // ==================== 批量操作 ====================
    
    async batchRetry() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('請選擇要重試的項目')
        return
      }
      
      try {
        await this.$confirm(
          `確定要重試選中的 ${this.selectedRows.length} 個郵件嗎？`,
          '批量重試確認',
          { type: 'warning' }
        )
        
        this.batchLoading.retry = true
        
        const emailIds = this.selectedRows.map(row => row.email_id)
        const response = await downloadTrackingApi.manualRetry({
          email_ids: emailIds,
          reason: 'manual_batch_retry'
        })
        
        if (response.success) {
          const successCount = response.data.retry_results.filter(r => r.status === 'scheduled').length
          this.$message.success(`已安排 ${successCount}/${emailIds.length} 個郵件重試`)
          
          await this.loadData()
          this.clearSelection()
          this.$emit('batch-retry-triggered', emailIds)
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量重試失敗:', error)
          this.$message.error('批量重試失敗')
        }
      } finally {
        this.batchLoading.retry = false
      }
    },
    
    async batchReset() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('請選擇要重置的項目')
        return
      }
      
      try {
        await this.$confirm(
          `確定要重置選中的 ${this.selectedRows.length} 個郵件的下載狀態嗎？此操作不可恢復。`,
          '批量重置確認',
          { type: 'warning' }
        )
        
        this.batchLoading.reset = true
        
        const promises = this.selectedRows.map(row => 
          downloadTrackingApi.resetDownloadStatus(row.email_id)
        )
        
        const results = await Promise.allSettled(promises)
        const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length
        
        this.$message.success(`已重置 ${successCount}/${this.selectedRows.length} 個郵件狀態`)
        
        await this.loadData()
        this.clearSelection()
        this.$emit('batch-reset-completed', successCount)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量重置失敗:', error)
          this.$message.error('批量重置失敗')
        }
      } finally {
        this.batchLoading.reset = false
      }
    },
    
    // ==================== 導出功能 ====================
    
    async exportFiltered() {
      try {
        const params = this.buildQueryParams()
        delete params.limit // 導出全部數據
        delete params.offset
        
        const response = await downloadTrackingApi.exportDownloadStatus({
          ...params,
          format: 'excel'
        })
        
        if (response.success) {
          this.downloadFile(response.data.download_url, response.data.filename)
          this.$message.success('導出完成')
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        console.error('導出失敗:', error)
        this.$message.error('導出失敗')
      }
    },
    
    async exportSelected() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('請選擇要導出的項目')
        return
      }
      
      try {
        const emailIds = this.selectedRows.map(row => row.email_id)
        const response = await downloadTrackingApi.exportDownloadStatus({
          email_ids: emailIds,
          format: 'excel'
        })
        
        if (response.success) {
          this.downloadFile(response.data.download_url, response.data.filename)
          this.$message.success('導出完成')
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        console.error('導出選中項失敗:', error)
        this.$message.error('導出失敗')
      }
    },
    
    downloadFile(url, filename) {
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    
    // ==================== 對話框處理 ====================
    
    handleDetailRetry(emailId) {
      this.showDetailDialog = false
      this.retryEmail({ email_id: emailId })
    },
    
    handleDetailReset(emailId) {
      this.showDetailDialog = false
      this.resetEmail({ email_id: emailId })
    },
    
    // ==================== 工具方法 ====================
    
    getStatusTagType(status) {
      const types = {
        'pending': 'info',
        'downloading': 'primary',
        'completed': 'success',
        'failed': 'danger',
        'retry_scheduled': 'warning'
      }
      return types[status] || 'info'
    },
    
    getStatusLabel(status) {
      const labels = {
        'pending': '待處理',
        'downloading': '下載中',
        'completed': '已完成',
        'failed': '失敗',
        'retry_scheduled': '待重試'
      }
      return labels[status] || status
    },
    
    getProgressColor(status) {
      const colors = {
        'pending': '#909399',
        'downloading': '#409eff',
        'completed': '#67c23a',
        'failed': '#f56c6c',
        'retry_scheduled': '#e6a23c'
      }
      return colors[status] || '#909399'
    },
    
    getErrorTypeLabel(type) {
      const labels = {
        'connection_error': '連接錯誤',
        'timeout': '超時',
        'authentication_error': '認證失敗',
        'server_error': '服務器錯誤',
        'rate_limit': '速率限制',
        'unknown': '未知錯誤'
      }
      return labels[type] || type
    },
    
    getRetryTimeClass(retryTime) {
      const now = new Date()
      const retry = new Date(retryTime)
      
      if (retry <= now) {
        return 'retry-time-due'
      } else if (retry <= new Date(now.getTime() + 5 * 60 * 1000)) {
        return 'retry-time-soon'
      }
      return 'retry-time-later'
    },
    
    formatDateTime(dateStr) {
      if (!dateStr) return '-'
      
      const date = new Date(dateStr)
      return date.toLocaleString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}

// 註冊組件
if (typeof Vue !== 'undefined') {
  Vue.component('email-download-detail-table', EmailDownloadDetailTable)
}
```

### Step 3: API 服務封裝 (0.5天)

**檔案位置**: `frontend/email/static/js/services/downloadTrackingApi.js`

```javascript
/**
 * 郵件下載追蹤 API 服務
 * 封裝所有與下載追蹤相關的 API 調用
 */

class DownloadTrackingApi {
  constructor(baseUrl = '/api/v1/email/download') {
    this.baseUrl = baseUrl
    this.timeout = 10000 // 10秒超時
  }
  
  // ==================== 基礎請求方法 ====================
  
  async request(method, url, options = {}) {
    const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`
    
    const config = {
      method: method.toUpperCase(),
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    }
    
    if (config.method !== 'GET' && options.data) {
      config.body = JSON.stringify(options.data)
    }
    
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.timeout)
      
      config.signal = controller.signal
      
      const response = await fetch(fullUrl, config)
      clearTimeout(timeoutId)
      
      const result = await response.json()
      
      if (response.ok) {
        return {
          success: true,
          data: result,
          status: response.status
        }
      } else {
        return {
          success: false,
          message: result.detail?.message || result.message || '請求失敗',
          error: result.detail?.error || 'API_ERROR',
          status: response.status
        }
      }
    } catch (error) {
      console.error('API 請求失敗:', error)
      
      return {
        success: false,
        message: error.name === 'AbortError' ? '請求超時' : error.message || '網絡錯誤',
        error: error.name || 'NETWORK_ERROR',
        status: 0
      }
    }
  }
  
  async get(url, params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const fullUrl = queryString ? `${url}?${queryString}` : url
    
    return this.request('GET', fullUrl)
  }
  
  async post(url, data = {}, options = {}) {
    return this.request('POST', url, { data, ...options })
  }
  
  async put(url, data = {}, options = {}) {
    return this.request('PUT', url, { data, ...options })
  }
  
  async delete(url, options = {}) {
    return this.request('DELETE', url, options)
  }
  
  // ==================== 狀態查詢 API ====================
  
  /**
   * 獲取特定郵件的下載狀態
   */
  async getEmailDownloadStatus(emailId) {
    return this.get(`/status/${emailId}`)
  }
  
  /**
   * 獲取下載狀態列表
   */
  async getDownloadStatusList(params = {}) {
    return this.get('/status', params)
  }
  
  /**
   * 獲取待重試任務列表
   */
  async getPendingRetries(params = {}) {
    const defaultParams = {
      limit: 50,
      include_metadata: false
    }
    return this.get('/pending-retries', { ...defaultParams, ...params })
  }
  
  /**
   * 獲取郵件重試歷史
   */
  async getRetryHistory(emailId) {
    return this.get(`/status/${emailId}/retry-history`)
  }
  
  // ==================== 重試控制 API ====================
  
  /**
   * 手動觸發重試
   */
  async manualRetry(data) {
    const defaultData = {
      reason: 'manual_retry'
    }
    return this.post('/retry/manual', { ...defaultData, ...data })
  }
  
  /**
   * 批量重試
   */
  async batchRetry(data) {
    const defaultData = {
      priority: 'HIGH',
      reason: 'manual_batch_retry',
      force: false
    }
    return this.post('/retry/batch', { ...defaultData, ...data })
  }
  
  /**
   * 處理待重試任務
   */
  async processRetries(data = {}) {
    const defaultData = {
      batch_size: 10,
      timeout_seconds: 300
    }
    return this.post('/retry/process', { ...defaultData, ...data })
  }
  
  /**
   * 獲取重試服務狀態
   */
  async getRetryServiceStatus() {
    return this.get('/retry/status')
  }
  
  /**
   * 重置斷路器
   */
  async resetCircuitBreaker() {
    return this.post('/retry/circuit-breaker/reset')
  }
  
  // ==================== 統計信息 API ====================
  
  /**
   * 獲取下載統計摘要
   */
  async getStatistics(params = {}) {
    const defaultParams = {
      hours: 24
    }
    return this.get('/statistics/summary', { ...defaultParams, ...params })
  }
  
  /**
   * 獲取趨勢分析
   */
  async getTrends(params = {}) {
    const defaultParams = {
      period: '24h',
      granularity: '1h'
    }
    return this.get('/statistics/trends', { ...defaultParams, ...params })
  }
  
  /**
   * 獲取錯誤分析
   */
  async getErrorAnalysis(params = {}) {
    const defaultParams = {
      hours: 24,
      top_n: 10
    }
    return this.get('/statistics/errors', { ...defaultParams, ...params })
  }
  
  /**
   * 獲取性能指標
   */
  async getPerformanceMetrics(params = {}) {
    const defaultParams = {
      hours: 24
    }
    return this.get('/statistics/performance', { ...defaultParams, ...params })
  }
  
  // ==================== 管理功能 API ====================
  
  /**
   * 重置下載狀態
   */
  async resetDownloadStatus(emailId) {
    return this.delete(`/status/${emailId}`)
  }
  
  /**
   * 清理舊記錄
   */
  async cleanupOldRecords(days = 30) {
    return this.post('/management/cleanup', { days })
  }
  
  /**
   * 更新服務配置
   */
  async updateServiceConfig(config) {
    return this.put('/management/config', config)
  }
  
  // ==================== 數據導出 API ====================
  
  /**
   * 導出下載狀態報告
   */
  async exportDownloadStatus(params = {}) {
    const defaultParams = {
      format: 'excel'
    }
    return this.post('/export/status', { ...defaultParams, ...params })
  }
  
  /**
   * 導出統計報告
   */
  async exportStatisticsReport(params = {}) {
    const defaultParams = {
      format: 'excel',
      period: '24h'
    }
    return this.post('/export/statistics', { ...defaultParams, ...params })
  }
  
  // ==================== 實時更新 API ====================
  
  /**
   * 訂閱實時更新
   */
  subscribeToUpdates(callback, options = {}) {
    const defaultOptions = {
      reconnect: true,
      reconnectInterval: 5000
    }
    
    const config = { ...defaultOptions, ...options }
    
    if (typeof WebSocket === 'undefined') {
      console.warn('WebSocket 不支援，無法訂閱實時更新')
      return null
    }
    
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws/download/updates`
    
    let ws = null
    let reconnectTimer = null
    
    const connect = () => {
      try {
        ws = new WebSocket(wsUrl)
        
        ws.onopen = () => {
          console.log('WebSocket 連接已建立')
          if (reconnectTimer) {
            clearTimeout(reconnectTimer)
            reconnectTimer = null
          }
        }
        
        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            callback(data)
          } catch (error) {
            console.error('解析 WebSocket 消息失敗:', error)
          }
        }
        
        ws.onclose = (event) => {
          console.log('WebSocket 連接已關閉:', event.code, event.reason)
          
          if (config.reconnect && !reconnectTimer) {
            reconnectTimer = setTimeout(() => {
              console.log('嘗試重新連接 WebSocket...')
              connect()
            }, config.reconnectInterval)
          }
        }
        
        ws.onerror = (error) => {
          console.error('WebSocket 錯誤:', error)
        }
      } catch (error) {
        console.error('創建 WebSocket 連接失敗:', error)
      }
    }
    
    connect()
    
    // 返回取消訂閱函數
    return () => {
      config.reconnect = false
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
        reconnectTimer = null
      }
      if (ws) {
        ws.close()
        ws = null
      }
    }
  }
  
  // ==================== 批量操作 API ====================
  
  /**
   * 根據錯誤類型重試
   */
  async retryByErrorType(data) {
    return this.post('/retry/by-error-type', data)
  }
  
  /**
   * 根據狀態批量操作
   */
  async batchOperationByStatus(operation, status, options = {}) {
    return this.post(`/batch/${operation}`, { status, ...options })
  }
  
  // ==================== 健康檢查 API ====================
  
  /**
   * 檢查 API 健康狀態
   */
  async healthCheck() {
    return this.get('/health')
  }
  
  /**
   * 獲取 API 版本信息
   */
  async getApiVersion() {
    return this.get('/version')
  }
}

// 創建全局實例
const downloadTrackingApi = new DownloadTrackingApi()

// 如果在瀏覽器環境中，將實例掛載到 window 對象
if (typeof window !== 'undefined') {
  window.downloadTrackingApi = downloadTrackingApi
}

// Node.js 環境導出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DownloadTrackingApi
}
```

### Step 4: 整合到現有系統 (1天)

**檔案位置**: `frontend/email/routes/email_routes.py`

```python
# 在現有的 email_routes.py 中添加新路由

@email_bp.route('/download-tracking')
def download_tracking():
    """郵件下載追蹤頁面"""
    return render_template(
        'email/download_tracking.html',
        title='郵件下載追蹤',
        page_title='郵件下載狀態監控',
        api_base_url='/api/v1/email/download'
    )

@email_bp.route('/download-tracking/embedded')
def download_tracking_embedded():
    """嵌入式下載追蹤組件"""
    return render_template(
        'email/components/download-tracking-widgets.html',
        api_base_url='/api/v1/email/download'
    )
```

**檔案位置**: `frontend/email/templates/download_tracking.html`

```html
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - 郵件處理系統</title>
    
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts/dist/echarts.min.js"></script>
    <!-- 自定義樣式 -->
    <link rel="stylesheet" href="{{ url_for('email.static', filename='css/download-tracking.css') }}">
    
    <style>
        body {
            margin: 0;
            font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
            background-color: #f5f5f5;
        }
        
        .page-container {
            min-height: 100vh;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        }
        
        .page-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
            min-height: calc(100vh - 140px);
        }
        
        .breadcrumb {
            margin-bottom: 10px;
        }
        
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="page-container">
            <!-- 頁面標題 -->
            <div class="page-header">
                <el-breadcrumb separator="/" class="breadcrumb">
                    <el-breadcrumb-item><a href="/">首頁</a></el-breadcrumb-item>
                    <el-breadcrumb-item><a href="/email/">郵件管理</a></el-breadcrumb-item>
                    <el-breadcrumb-item>下載追蹤</el-breadcrumb-item>
                </el-breadcrumb>
                
                <h1>{{ page_title }}</h1>
                <p>監控郵件下載狀態，管理重試機制和查看詳細統計信息</p>
                
                <!-- 快速導航 -->
                <div class="quick-nav">
                    <el-button-group size="small">
                        <el-button 
                            :type="currentView === 'dashboard' ? 'primary' : ''"
                            @click="currentView = 'dashboard'">
                            儀表板
                        </el-button>
                        <el-button 
                            :type="currentView === 'details' ? 'primary' : ''"
                            @click="currentView = 'details'">
                            詳細列表
                        </el-button>
                        <el-button 
                            :type="currentView === 'analytics' ? 'primary' : ''"
                            @click="currentView = 'analytics'">
                            分析報告
                        </el-button>
                    </el-button-group>
                </div>
            </div>
            
            <!-- 主要內容 -->
            <div class="page-content">
                <div v-loading="pageLoading" class="loading-container" v-if="pageLoading">
                    <div>
                        <i class="el-icon-loading"></i>
                        <p>載入中...</p>
                    </div>
                </div>
                
                <div v-else>
                    <!-- 儀表板視圖 -->
                    <div v-show="currentView === 'dashboard'">
                        <email-download-dashboard 
                            @show-detail-table="currentView = 'details'"
                            @statistics-updated="handleStatisticsUpdate">
                        </email-download-dashboard>
                    </div>
                    
                    <!-- 詳細列表視圖 -->
                    <div v-show="currentView === 'details'">
                        <email-download-detail-table 
                            @retry-triggered="handleRetryTriggered"
                            @batch-retry-triggered="handleBatchRetryTriggered"
                            @status-reset="handleStatusReset"
                            @show-email-details="handleShowEmailDetails">
                        </email-download-detail-table>
                    </div>
                    
                    <!-- 分析報告視圖 -->
                    <div v-show="currentView === 'analytics'">
                        <email-download-analytics></email-download-analytics>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 通知區域 -->
        <div class="notification-area">
            <el-alert
                v-for="notification in notifications"
                :key="notification.id"
                :title="notification.title"
                :type="notification.type"
                :description="notification.description"
                :closable="true"
                @close="removeNotification(notification.id)"
                style="margin-bottom: 10px;">
            </el-alert>
        </div>
    </div>

    <!-- Vue.js 和相關庫 -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <!-- 下載追蹤相關腳本 -->
    <script src="{{ url_for('email.static', filename='js/services/downloadTrackingApi.js') }}"></script>
    <script src="{{ url_for('email.static', filename='js/components/EmailDownloadDashboard.js') }}"></script>
    <script src="{{ url_for('email.static', filename='js/components/EmailDownloadDetailTable.js') }}"></script>
    <script src="{{ url_for('email.static', filename='js/components/EmailDownloadCharts.js') }}"></script>
    <script src="{{ url_for('email.static', filename='js/components/EmailDownloadAnalytics.js') }}"></script>

    <script>
        // 主應用實例
        new Vue({
            el: '#app',
            data: {
                currentView: 'dashboard',
                pageLoading: true,
                notifications: [],
                realTimeUpdates: null,
                apiBaseUrl: '{{ api_base_url }}'
            },
            
            created() {
                this.initializeApp()
            },
            
            beforeDestroy() {
                this.cleanupApp()
            },
            
            methods: {
                async initializeApp() {
                    try {
                        // 設置 axios 基礎配置
                        axios.defaults.baseURL = this.apiBaseUrl
                        axios.defaults.timeout = 10000
                        
                        // 檢查 API 健康狀態
                        await this.checkApiHealth()
                        
                        // 設置實時更新
                        this.setupRealTimeUpdates()
                        
                        this.pageLoading = false
                        
                        this.showNotification('系統初始化完成', 'success', '郵件下載追蹤系統已就緒')
                        
                    } catch (error) {
                        console.error('應用初始化失敗:', error)
                        this.pageLoading = false
                        this.showNotification('初始化失敗', 'error', '請檢查網絡連接並刷新頁面')
                    }
                },
                
                async checkApiHealth() {
                    try {
                        const response = await downloadTrackingApi.healthCheck()
                        if (!response.success) {
                            throw new Error('API 健康檢查失敗')
                        }
                    } catch (error) {
                        console.warn('API 健康檢查失敗，但繼續初始化:', error)
                    }
                },
                
                setupRealTimeUpdates() {
                    try {
                        this.realTimeUpdates = downloadTrackingApi.subscribeToUpdates((data) => {
                            this.handleRealTimeUpdate(data)
                        })
                    } catch (error) {
                        console.warn('設置實時更新失敗:', error)
                    }
                },
                
                cleanupApp() {
                    if (this.realTimeUpdates) {
                        this.realTimeUpdates() // 取消訂閱
                    }
                },
                
                handleRealTimeUpdate(data) {
                    // 處理實時更新數據
                    if (data.type === 'status_update') {
                        this.$emit('realtime-status-update', data.payload)
                    } else if (data.type === 'retry_completed') {
                        this.showNotification('重試完成', 'success', `郵件 ${data.payload.email_id} 重試完成`)
                    }
                },
                
                handleStatisticsUpdate(statistics) {
                    // 統計數據更新處理
                    this.$emit('statistics-updated', statistics)
                },
                
                handleRetryTriggered(emailIds) {
                    this.showNotification('重試已觸發', 'info', `已安排 ${emailIds.length} 個郵件重試`)
                },
                
                handleBatchRetryTriggered(emailIds) {
                    this.showNotification('批量重試已觸發', 'info', `已安排 ${emailIds.length} 個郵件批量重試`)
                },
                
                handleStatusReset(emailId) {
                    this.showNotification('狀態已重置', 'success', `郵件 ${emailId} 的下載狀態已重置`)
                },
                
                handleShowEmailDetails(emailId) {
                    // 可以跳轉到郵件詳情頁面或彈出對話框
                    window.open(`/email/details/${emailId}`, '_blank')
                },
                
                showNotification(title, type = 'info', description = '') {
                    const notification = {
                        id: Date.now(),
                        title,
                        type,
                        description
                    }
                    
                    this.notifications.push(notification)
                    
                    // 自動移除通知（除了錯誤通知）
                    if (type !== 'error') {
                        setTimeout(() => {
                            this.removeNotification(notification.id)
                        }, 5000)
                    }
                },
                
                removeNotification(id) {
                    const index = this.notifications.findIndex(n => n.id === id)
                    if (index > -1) {
                        this.notifications.splice(index, 1)
                    }
                }
            }
        })
    </script>
</body>
</html>
```

**修改檔案**: `frontend/monitoring/templates/database_manager.html`

在現有的資料庫管理界面中添加下載追蹤集成：

```html
<!-- 在適當位置添加下載追蹤卡片 -->
<div class="monitoring-card">
    <el-card>
        <div slot="header">
            <span>郵件下載追蹤</span>
            <el-button 
                style="float: right; padding: 3px 0" 
                type="text"
                @click="openDownloadTracking">
                查看詳情
            </el-button>
        </div>
        
        <div id="download-tracking-widget">
            <!-- 嵌入簡化的下載追蹤組件 -->
            <div class="widget-stats">
                <div class="stat-item">
                    <span class="stat-label">總下載數</span>
                    <span class="stat-value">{{ downloadStats.total || '-' }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">成功率</span>
                    <span class="stat-value success">{{ downloadStats.success_rate || '-' }}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">待重試</span>
                    <span class="stat-value warning">{{ downloadStats.pending_retries || '-' }}</span>
                </div>
            </div>
            
            <div class="widget-actions">
                <el-button size="mini" @click="refreshDownloadStats">刷新</el-button>
                <el-button size="mini" type="warning" @click="processRetries" :disabled="!downloadStats.pending_retries">
                    處理重試
                </el-button>
            </div>
        </div>
    </el-card>
</div>

<script>
// 在現有的 Vue 實例中添加下載追蹤相關方法
// ... 現有代碼 ...

data: {
    // ... 現有數據 ...
    downloadStats: {
        total: 0,
        success_rate: 0,
        pending_retries: 0
    }
},

methods: {
    // ... 現有方法 ...
    
    async loadDownloadStats() {
        try {
            const response = await downloadTrackingApi.getStatistics({ hours: 24 })
            if (response.success) {
                this.downloadStats = {
                    total: response.data.total_downloads,
                    success_rate: response.data.success_rate.toFixed(1),
                    pending_retries: response.data.pending_retries
                }
            }
        } catch (error) {
            console.error('載入下載統計失敗:', error)
        }
    },
    
    async refreshDownloadStats() {
        await this.loadDownloadStats()
        this.$message.success('下載統計已刷新')
    },
    
    async processRetries() {
        try {
            const response = await downloadTrackingApi.processRetries()
            if (response.success) {
                this.$message.success('重試處理已啟動')
                await this.loadDownloadStats()
            } else {
                this.$message.error('重試處理失敗')
            }
        } catch (error) {
            this.$message.error('重試處理失敗')
        }
    },
    
    openDownloadTracking() {
        window.open('/email/download-tracking', '_blank')
    }
}

// 在 mounted 中載入下載統計
mounted() {
    // ... 現有代碼 ...
    this.loadDownloadStats()
}
</script>
```

---

## 🧪 測試策略

### 前端單元測試

**檔案位置**: `tests/frontend/email/test_download_tracking_components.js`

```javascript
/**
 * 下載追蹤組件測試
 */

describe('EmailDownloadDashboard', () => {
  it('should load statistics on mount', async () => {
    // 測試組件掛載時載入統計數據
  })
  
  it('should handle refresh action', async () => {
    // 測試刷新功能
  })
  
  it('should process retries correctly', async () => {
    // 測試重試處理
  })
})

describe('EmailDownloadDetailTable', () => {
  it('should filter data correctly', async () => {
    // 測試過濾功能
  })
  
  it('should handle batch operations', async () => {
    // 測試批量操作
  })
  
  it('should paginate correctly', async () => {
    // 測試分頁功能
  })
})
```

### 整合測試

**檔案位置**: `tests/integration/test_frontend_api_integration.py`

```python
"""
前端 API 整合測試
"""

def test_frontend_page_loads():
    """測試前端頁面載入"""
    response = client.get('/email/download-tracking')
    assert response.status_code == 200
    assert 'download-tracking' in response.data.decode()

def test_api_endpoints_accessible():
    """測試 API 端點可訪問性"""
    endpoints = [
        '/api/v1/email/download/status',
        '/api/v1/email/download/statistics/summary',
        '/api/v1/email/download/pending-retries'
    ]
    
    for endpoint in endpoints:
        response = client.get(endpoint)
        assert response.status_code in [200, 401]  # 200 或需要認證
```

---

## 🎯 成功標準

### 功能成功標準
- ✅ 所有前端組件正常工作
- ✅ 與後端 API 完全整合
- ✅ 實時數據更新功能正常
- ✅ 響應式設計支援多種設備
- ✅ 用戶體驗流暢直觀

### 性能成功標準
- ✅ 頁面初始載入時間 < 3秒
- ✅ 數據刷新響應時間 < 1秒
- ✅ 支援 1000+ 記錄的流暢操作
- ✅ 記憶體使用穩定無洩漏

### 兼容性成功標準
- ✅ 支援 Chrome/Firefox/Safari/Edge
- ✅ 移動端瀏覽器基本功能可用
- ✅ 與現有監控系統無衝突
- ✅ 向後兼容性良好

---

## 📋 交付清單

### 代碼文件
- [ ] 所有 Vue.js 組件
- [ ] API 服務封裝
- [ ] 樣式表和資源文件
- [ ] HTML 模板

### 整合文件
- [ ] 路由配置更新
- [ ] 現有系統集成修改
- [ ] 配置文件更新

### 測試和文檔
- [ ] 前端單元測試
- [ ] 整合測試用例
- [ ] 用戶使用指南
- [ ] 組件文檔

---

**Story 完成標準**: 所有驗收標準通過 ✅ 且交付清單項目完成 📋

**系列完成**: 所有 5 個 Story 完成，郵件下載追蹤系統完整實現 🎉