"""
郵件下載追蹤表遷移腳本
"""

from sqlalchemy import text, create_engine
from sqlalchemy.orm import sessionmaker
import logging
from datetime import datetime
import os

logger = logging.getLogger(__name__)

# 創建資料庫引擎
DATABASE_URL = "sqlite:///email_inbox.db"
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def migrate_add_download_tracking():
    """添加郵件下載追蹤表"""
    session = SessionLocal()
    try:
        # 檢查表是否已存在
        result = session.execute(text("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='email_download_status'
        """))
        
        if result.fetchone():
            logger.info("email_download_status 表已存在，跳過遷移")
            return True
        
        logger.info("開始創建郵件下載追蹤表...")
        
        # 創建 email_download_status 表
        session.execute(text("""
            CREATE TABLE email_download_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email_id INTEGER NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                download_attempt INTEGER DEFAULT 1,
                max_retry_count INTEGER DEFAULT 3,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                started_at DATETIME,
                completed_at DATETIME,
                last_retry_at DATETIME,
                next_retry_at DATETIME,
                error_type VARCHAR(50),
                error_message TEXT,
                error_details TEXT,
                download_size_bytes INTEGER,
                download_duration_seconds REAL,
                server_response_code VARCHAR(10),
                retry_strategy VARCHAR(20) DEFAULT 'exponential',
                retry_interval_seconds INTEGER DEFAULT 60,
                FOREIGN KEY (email_id) REFERENCES emails (id) ON DELETE CASCADE
            )
        """))
        
        # 創建索引
        session.execute(text("CREATE INDEX idx_email_download_status_email_id ON email_download_status (email_id)"))
        session.execute(text("CREATE INDEX idx_email_download_status_status ON email_download_status (status)"))
        session.execute(text("CREATE INDEX idx_email_download_status_next_retry ON email_download_status (next_retry_at)"))
        session.execute(text("CREATE INDEX idx_email_download_status_created ON email_download_status (created_at)"))
        
        # 創建 email_download_retry_log 表
        session.execute(text("""
            CREATE TABLE email_download_retry_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                download_status_id INTEGER NOT NULL,
                retry_attempt INTEGER NOT NULL,
                retry_reason VARCHAR(100),
                attempted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                result VARCHAR(20),
                error_message TEXT,
                duration_seconds REAL,
                retry_strategy_used VARCHAR(20),
                retry_interval_used INTEGER,
                FOREIGN KEY (download_status_id) REFERENCES email_download_status (id) ON DELETE CASCADE
            )
        """))
        
        # 創建索引
        session.execute(text("CREATE INDEX idx_retry_log_download_status ON email_download_retry_log (download_status_id)"))
        session.execute(text("CREATE INDEX idx_retry_log_attempted_at ON email_download_retry_log (attempted_at)"))
        
        session.commit()
        logger.info("郵件下載追蹤表創建成功")
        
        # 為現有郵件創建初始狀態記錄
        create_initial_download_status(session)
        
        return True
        
    except Exception as e:
        logger.error(f"遷移失敗: {e}")
        session.rollback()
        return False
    finally:
        session.close()

def create_initial_download_status(session):
    """為現有郵件創建初始下載狀態"""
    try:
        # 為所有現有郵件創建 'completed' 狀態記錄
        result = session.execute(text("""
            INSERT INTO email_download_status (email_id, status, download_attempt, completed_at)
            SELECT id, 'completed', 1, created_at 
            FROM emails 
            WHERE id NOT IN (SELECT email_id FROM email_download_status)
        """))
        
        affected_rows = result.rowcount
        session.commit()
        logger.info(f"為 {affected_rows} 封現有郵件創建初始下載狀態完成")
        
    except Exception as e:
        logger.error(f"創建初始下載狀態失敗: {e}")
        session.rollback()
        raise

def rollback_download_tracking_migration():
    """回滾遷移"""
    session = SessionLocal()
    try:
        logger.info("開始回滾郵件下載追蹤表...")
        
        # 刪除表（順序很重要，先刪除有外鍵的表）
        session.execute(text("DROP TABLE IF EXISTS email_download_retry_log"))
        session.execute(text("DROP TABLE IF EXISTS email_download_status"))
        
        session.commit()
        logger.info("郵件下載追蹤表回滾完成")
        
    except Exception as e:
        logger.error(f"回滾失敗: {e}")
        session.rollback()
        raise
    finally:
        session.close()

def verify_migration():
    """驗證遷移結果"""
    session = SessionLocal()
    try:
        # 檢查表是否存在
        tables_result = session.execute(text("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('email_download_status', 'email_download_retry_log')
            ORDER BY name
        """))
        
        tables = [row[0] for row in tables_result.fetchall()]
        expected_tables = ['email_download_retry_log', 'email_download_status']
        
        if set(tables) != set(expected_tables):
            raise Exception(f"表創建不完整: 期望 {expected_tables}, 實際 {tables}")
        
        # 檢查索引是否存在
        indexes_result = session.execute(text("""
            SELECT name FROM sqlite_master 
            WHERE type='index' AND name LIKE 'idx_%download%'
            ORDER BY name
        """))
        
        indexes = [row[0] for row in indexes_result.fetchall()]
        logger.info(f"創建的索引: {indexes}")
        
        # 檢查外鍵約束
        pragma_result = session.execute(text("PRAGMA foreign_key_check"))
        fk_errors = pragma_result.fetchall()
        
        if fk_errors:
            raise Exception(f"外鍵約束檢查失敗: {fk_errors}")
        
        # 檢查初始數據
        count_result = session.execute(text("SELECT COUNT(*) FROM email_download_status"))
        initial_count = count_result.fetchone()[0]
        
        email_count_result = session.execute(text("SELECT COUNT(*) FROM emails"))
        email_count = email_count_result.fetchone()[0]
        
        logger.info(f"遷移驗證完成: 郵件總數={email_count}, 初始追蹤記錄={initial_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"遷移驗證失敗: {e}")
        return False
    finally:
        session.close()

if __name__ == "__main__":
    # 設置日誌
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 執行遷移
    if migrate_add_download_tracking():
        print("✅ 遷移成功")
        
        # 驗證遷移
        if verify_migration():
            print("✅ 遷移驗證通過")
        else:
            print("❌ 遷移驗證失敗")
    else:
        print("❌ 遷移失敗")