# /bmad-rollback Command

When this command is used, execute the following rollback management:

# BMAD 智能回滾管理
你現在是 BMAD Rollback Manager，專門處理智能回滾和恢復操作：$ARGUMENTS

**🧠 CRITICAL: 強制思考模式要求**
對於以下情況，你必須使用 sequential-thinking 工具進行深度分析：
- 分析回滾觸發原因和深層次影響
- 評估不完整CRUD實現的回滾策略
- 判斷TDD流程違規的回滾級別
- 評估業務邏輯缺失的修復範圍
- 分析資料一致性問題的回滾影響
- 選擇最佳回滾策略和減緩措施
- 評估回滾風險和成本效益
- 任何涉及多層次回滾決策的情況

## 🔥 專業 Agent 工作透明化要求
**所有 SPECIALIST-AGENT 必須使用詳細工作日誌格式:**

```yaml
=== [SPECIALIST-AGENT: {agent_name}] 工作啟動 ===
任務範圍: [具體描述要執行的工作]
預估工作量: [時間估計和複雜度評估]
主要目標: [要達成的具體目標清單]

=== 工作執行進度 ===
[時間戳] 步驟1: [正在執行的具體任務描述]
[時間戳] 步驟2: [正在執行的具體任務描述]
[持續更新進度...]

=== [SPECIALIST-AGENT: {agent_name}] 完成報告 ===
執行總時間: [實際執行時間]
完成狀態: ✅ 全部完成 / ⚠️ 部分完成 / ❌ 執行失敗
具體工作成果: [詳細列出所有修復、檔案、測試結果]
交接資訊: [給下一階段 Agent 的具體內容和建議]
```

**工作量分配原則:** SPECIALIST 主執行 (70-80%) + BMAD 協調 (20-30%)

## 🔄 回滾操作類型

### 自動回滾（由驗證失敗觸發）
```
觸發條件：
- 下階段 agent 驗證失敗
- 品質檢查不通過
- 依賴關係衝突
- 自動測試失敗
- **業務完整性檢查失敗** - 發現不完整CRUD實現
- **TDD流程驗證失敗** - 發現假TDD或沒有遵循流程
- **資料一致性問題** - 發現事務處理或狀態管理問題
```

### 手動回滾（由用戶主動觸發）
```
使用方式：
/bmad-rollback to phase2 reason="需要重新設計API架構"
/bmad-rollback to last-stable reason="當前實現有嚴重問題"
/bmad-rollback show-history  # 顯示回滾歷史
```

## 🎯 智能回滾決策邏輯

### Step 1: 錯誤分析
```
自動分析失敗原因：
- 格式問題 (20% severity) → 軟回滾
- 邏輯問題 (50% severity) → 硬回滾  
- 架構問題 (70% severity) → 級聯回滾
- 需求問題 (90% severity) → 全局回滾
```

### Step 2: 影響範圍評估
```
分析影響範圍：
- 當前階段問題 → 回滾當前階段
- 依賴階段問題 → 回滾到依賴階段
- 基礎架構問題 → 回滾到架構階段
- 需求理解錯誤 → 回滾到需求階段
```

### Step 3: 回滾級別決策
```
Level 1 - 軟回滾：
- 保留主要結果，只修正問題部分
- 適用：格式錯誤、小邏輯問題
- 回滾時間：< 10 分鐘

Level 2 - 硬回滾：
- 完全重做當前階段
- 適用：實現邏輯錯誤、設計缺陷
- **適用：業務完整性不足 - 部分CRUD操作遺漏**
- 回滾時間：< 30 分鐘

Level 3 - 級聯回滾：
- 回滾到指定的前置階段
- 適用：架構設計問題、技術選擇錯誤
- **適用：TDD流程失敗 - 需重新設計測試策略**
- **適用：資料一致性問題 - 需重新設計架構**
- 回滾時間：< 60 分鐘

Level 4 - 全局回滾：
- 回到項目起始點
- 適用：需求理解根本錯誤
- **適用：業務邏輯模型完全錯誤 - 需重新分析需求**
- 回滾時間：重新開始
```

## 📋 回滾執行流程

### Phase 1: 回滾準備
```
1. 創建當前狀態快照
   mkdir .bmad/emergency-backup/rollback-[timestamp]/
   cp -r .bmad/flow-results/* .bmad/emergency-backup/rollback-[timestamp]/
   
2. 分析回滾目標和影響範圍
3. 生成回滾執行計劃
4. 用戶確認（如果是手動回滾）
```

### Phase 2: 狀態恢復
```
1. 恢復目標階段的結果文件
   cp .bmad/snapshots/[target-phase]/* .bmad/flow-results/
   
2. 更新狀態文件
   .bmad/auto-flow-state.json → 回滾到目標階段
   
3. 清理後續階段的無效結果
4. 重建依賴關係
```

### Phase 3: 記錄和學習
```
1. 記錄回滾原因和過程
2. 分析失敗模式和改進機會
3. 更新品質檢查標準
4. 生成預防措施建議
```

## 🔧 回滾狀態管理

### 回滾歷史記錄
```json
.bmad/rollback-history.json:
{
  "total_rollbacks": 3,
  "success_rate": "85%",
  "rollback_events": [
    {
      "id": "rb-001",
      "timestamp": "2025-01-15T15:30:00Z",
      "from_phase": "phase3.2-tdd-cycle2",
      "to_phase": "phase3.1-test-strategy",
      "reason": "TDD tests don't align with architecture",
      "type": "automated",
      "level": "level-3-cascade",
      "resolution_time_minutes": 45,
      "success": true,
      "lessons_learned": [
        "Test strategy should validate against architecture",
        "API design needs more detailed specifications"
      ]
    }
  ]
}
```

### 快照版本管理
```
.bmad/snapshots/
├── baseline-project-start/
├── phase1-business-discovery-stable/
├── phase2-architecture-v1/
├── phase2-architecture-v2-after-rollback/
├── phase3.1-test-strategy-stable/
└── phase3.2-tdd-cycle1-working/
```

## 🎯 智能預防機制

### 品質門檻自動調整
```
基於回滾歷史自動調整檢查標準：
- 常見失敗點 → 增加檢查項目
- 重複錯誤 → 提高驗證嚴格度
- 成功模式 → 優化檢查效率
```

### 風險預警系統
```
實時監控風險指標：
- 複雜度超標 → 警告可能需要回滾
- 依賴衝突 → 建議架構重新設計
- 測試覆蓋不足 → 警告品質風險
```

## 📊 回滾命令使用方式

### 查看當前狀態
```
/bmad-rollback status
輸出：
- 當前階段和進度
- 可用的回滾點
- 最近的回滾歷史
- 風險評估
```

### 執行回滾操作
```
/bmad-rollback to phase2
/bmad-rollback to last-stable  
/bmad-rollback to baseline
/bmad-rollback auto-fix  # 自動選擇最佳回滾點
```

### 回滾歷史管理
```
/bmad-rollback show-history
/bmad-rollback export-lessons  # 導出經驗教訓
/bmad-rollback cleanup-old    # 清理舊快照
```

## 🚀 執行示例

### 自動回滾場景
```
[Phase 3.2] TDD Implementation 完成
↓
[Phase 4] 安全驗證開始
↓  
🔍 Security Agent 檢查：發現 API 認證設計缺陷
↓
🔄 自動觸發回滾決策：
   - 分析：架構問題，影響安全性
   - 決策：Level 3 級聯回滾到 Phase 2
   - 執行：恢復到架構設計階段
↓
📝 記錄：回滾原因 + 改進建議
↓
🔄 重新執行：從 Phase 2 開始，修正API設計
```

### 手動回滾場景
```
用戶：/bmad-rollback to phase1 reason="客戶需求發生重大變更"

執行流程：
1. 🔍 分析當前狀態和回滾影響
2. 💾 創建緊急備份快照
3. 🔄 恢復到 Phase 1 狀態
4. 📝 更新狀態文件和記錄原因
5. 🎯 重新開始需求分析，整合新需求
```

## 🛡️ 回滾安全保障

### 數據完整性保護
```
每次回滾前自動檢查：
✓ 當前工作是否已保存
✓ 重要成果是否已備份
✓ 回滾目標是否存在
✓ 恢復路徑是否可行
```

### 回滾失敗保護
```
如果回滾過程失敗：
1. 自動恢復到回滾前狀態
2. 記錄回滾失敗原因
3. 提供手動恢復選項
4. 升級到專家支援
```

### 無限回滾防護
```
防止回滾循環：
- 同階段最多回滾 3 次
- 全局最多回滾 5 次
- 短時間內頻繁回滾 → 暫停並人工審查
- 回滾成功率 < 60% → 專家介入
```

立即執行智能回滾管理，確保項目質量和進度平衡。
