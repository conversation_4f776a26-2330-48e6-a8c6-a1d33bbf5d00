# PTS文件處理路徑追蹤和日誌增強需求

## 問題描述
用戶上傳PTS文件並重命名後，按下"開始處理"時出現錯誤：
- 錯誤信息：No PTS files found for upload pts_upload_417e891db513
- 用戶需要在日誌中明確看到系統在哪裡尋找PTS檔案
- 需要增強路徑追蹤和除錯資訊

## 錯誤堆疊分析
1. 前端錯誤 (pts_renamer.js:638): PTSRenamerUI.startProcessing
2. 後端錯誤 (pts_rename_service:125): process_pts_files - No PTS files found
3. 展示器錯誤 (pts_rename_presenter:155): Processing service error

## 需要改進的地方
1. 在日誌中顯示具體搜尋路徑
2. 顯示預期的檔案位置
3. 列出實際找到的檔案
4. 增強除錯資訊以幫助診斷路徑問題

## 技術要求
- 增強後端服務的路徑日誌記錄
- 改進錯誤訊息包含具體路徑資訊
- 前端也需要顯示更詳細的錯誤資訊