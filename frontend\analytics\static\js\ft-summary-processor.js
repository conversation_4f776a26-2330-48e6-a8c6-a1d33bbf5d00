/**
 * FT Summary Processor Module
 * FT 摘要處理器模組 JavaScript
 */

class FTSummaryProcessor {
    constructor() {
        this.isProcessing = false;
        this.processedData = null;
        this.charts = {};
        this.currentView = 'summary';
        
        this.init();
    }
    
    async init() {
        console.log('FT Summary Processor: Initializing...');
        this.bindEvents();
        this.loadExistingSummaries();

        // 初始化檔案上傳組件
        await this.initFileUpload();
    }

    /**
     * 初始化檔案上傳組件
     */
    async initFileUpload() {
        try {
            if (typeof fileUpload !== 'undefined' && fileUpload.init) {
                await fileUpload.init();
                console.log('FT Summary Processor: 檔案上傳組件初始化完成');
            } else {
                console.warn('FT Summary Processor: 檔案上傳組件不可用');
            }
        } catch (error) {
            console.error('FT Summary Processor: 檔案上傳組件初始化失敗:', error);
        }
    }
    
    bindEvents() {
        // 處理按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.process-summary-btn')) {
                this.handleProcessSummary(e);
            }
        });
        
        // 檢視切換
        document.addEventListener('click', (e) => {
            if (e.target.matches('.view-toggle-btn')) {
                this.handleViewToggle(e);
            }
        });
        
        // 匯出按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-summary-btn')) {
                this.handleExportSummary(e);
            }
        });
        
        // 重新處理按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.reprocess-btn')) {
                this.handleReprocess(e);
            }
        });
        
        // 篩選器
        document.addEventListener('change', (e) => {
            if (e.target.matches('.summary-filter')) {
                this.handleFilterChange(e);
            }
        });
        
        // 摘要項目點擊
        document.addEventListener('click', (e) => {
            if (e.target.closest('.summary-item')) {
                this.handleSummaryItemClick(e);
            }
        });
    }
    
    async loadExistingSummaries() {
        try {
            const response = await fetch('/analytics/api/summaries');
            
            if (response.ok) {
                const summaries = await response.json();
                this.displaySummariesList(summaries);
            }
        } catch (error) {
            console.error('Failed to load summaries:', error);
        }
    }
    
    displaySummariesList(summaries) {
        const container = document.querySelector('.summaries-list');
        if (!container) return;
        
        if (summaries.length === 0) {
            container.innerHTML = `
                <div class="no-summaries">
                    <p>尚未有處理過的摘要資料</p>
                    <p class="text-muted">點擊「開始處理」來生成第一個摘要</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = '';
        summaries.forEach(summary => {
            const item = this.createSummaryItem(summary);
            container.appendChild(item);
        });
    }
    
    createSummaryItem(summary) {
        const item = document.createElement('div');
        item.className = 'summary-item';
        item.dataset.summaryId = summary.id;
        
        const statusClass = this.getStatusClass(summary.status);
        const progressWidth = summary.progress || 0;
        
        item.innerHTML = `
            <div class="summary-header">
                <h5 class="summary-title">${summary.title}</h5>
                <span class="summary-status status-${statusClass}">${this.getStatusText(summary.status)}</span>
            </div>
            <div class="summary-info">
                <span class="summary-date">${this.formatDateTime(summary.created_at)}</span>
                <span class="summary-type">${summary.type}</span>
                <span class="summary-size">${summary.total_items || 0} 項目</span>
            </div>
            <div class="summary-progress">
                <div class="progress">
                    <div class="progress-bar" style="width: ${progressWidth}%"></div>
                </div>
                <span class="progress-text">${progressWidth}%</span>
            </div>
            <div class="summary-actions">
                <button class="btn btn-sm btn-primary view-summary-btn" data-summary-id="${summary.id}">檢視</button>
                <button class="btn btn-sm btn-secondary export-summary-btn" data-summary-id="${summary.id}">匯出</button>
                ${summary.status === 'failed' ? `<button class="btn btn-sm btn-warning reprocess-btn" data-summary-id="${summary.id}">重新處理</button>` : ''}
            </div>
        `;
        
        return item;
    }
    
    async handleProcessSummary(e) {
        if (this.isProcessing) {
            this.showWarning('目前正在處理中，請稍候...');
            return;
        }
        
        const processBtn = e.target;
        const originalText = processBtn.textContent;
        
        this.isProcessing = true;
        processBtn.textContent = '處理中...';
        processBtn.disabled = true;
        
        try {
            // 獲取處理選項
            const options = this.getProcessingOptions();
            
            const response = await fetch('/analytics/api/summaries/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(options)
            });
            
            if (response.ok) {
                const result = await response.json();
                
                if (result.async) {
                    // 異步處理，開始監控進度
                    this.monitorProgress(result.task_id, result.auto_download_enabled);

                    if (result.auto_download_enabled) {
                        this.showSuccess('摘要處理已開始，完成後將自動提供下載連結...');
                    } else {
                        this.showSuccess('摘要處理已開始，正在背景執行...');
                    }
                } else {
                    // 同步處理完成
                    this.processedData = result;
                    this.displayProcessingResults();
                    this.showSuccess('摘要處理完成！');
                }
                
                // 重新載入摘要列表
                this.loadExistingSummaries();
                
            } else {
                const error = await response.json();
                throw new Error(error.message || '處理失敗');
            }
            
        } catch (error) {
            console.error('Process summary error:', error);
            this.showError('處理失敗：' + error.message);
        } finally {
            this.isProcessing = false;
            processBtn.textContent = originalText;
            processBtn.disabled = false;
        }
    }
    
    getProcessingOptions() {
        // 獲取資料夾路徑
        const folderPath = document.querySelector('#folderPath')?.value;
        if (!folderPath) {
            throw new Error('請輸入資料夾路徑');
        }

        // 獲取處理模式
        const processingMode = document.querySelector('input[name="processing_mode"]:checked')?.value || 'summary_only';

        // 獲取 7zip 路徑
        const sevenZipPath = document.querySelector('#sevenZipPath')?.value || 'C:\\Program Files\\7-Zip';

        return {
            'folderPath': folderPath,
            'folder_path': folderPath,  // 兼容性
            'processing_mode': processingMode,
            'seven_zip_path': sevenZipPath,
            'source_type': 'folder',
            'include_attachments': true,
            'extract_keywords': true,
            'generate_insights': true
        };
    }
    
    async monitorProgress(taskId, autoDownloadEnabled = false) {
        const progressInterval = setInterval(async () => {
            try {
                // 使用新的任務狀態 API
                const response = await fetch(`/analytics/api/summaries/task-status/${taskId}`);

                if (response.ok) {
                    const progress = await response.json();
                    this.updateProgress(progress);

                    if (progress.completed) {
                        clearInterval(progressInterval);

                        if (progress.status === 'completed') {
                            // 檢查是否有自動下載
                            if (autoDownloadEnabled) {
                                if (progress.download_available) {
                                    this.showDownloadLink(progress.download_url, progress.download_filename);
                                    this.showSuccess('摘要處理完成！下載連結已準備好。');
                                } else if (progress.archive_status === 'processing') {
                                    this.showSuccess('摘要處理完成！正在準備下載檔案...');
                                    // 繼續監控壓縮任務
                                    setTimeout(() => {
                                        this.monitorProgress(taskId, autoDownloadEnabled);
                                    }, 3000);
                                    return; // 不清除 interval，繼續監控
                                } else if (progress.archive_status === 'failed') {
                                    this.showWarning('摘要處理完成，但下載檔案準備失敗：' + (progress.archive_error || '未知錯誤'));
                                } else {
                                    this.showSuccess('摘要處理完成！');
                                }
                            } else {
                                this.showSuccess('摘要處理完成！');
                            }
                            this.loadExistingSummaries();
                        } else {
                            this.showError('處理失敗：' + (progress.result?.error || '未知錯誤'));
                        }
                    }
                }
            } catch (error) {
                console.error('Progress monitoring error:', error);
                clearInterval(progressInterval);
            }
        }, 3000); // 每3秒檢查一次
    }
    
    updateProgress(progress) {
        const progressBars = document.querySelectorAll('.progress-bar');
        const progressTexts = document.querySelectorAll('.progress-text');

        // 更新進度條（如果有進度資訊）
        const percentage = progress.percentage || (progress.completed ? 100 : 50);

        progressBars.forEach(bar => {
            bar.style.width = `${percentage}%`;
        });

        progressTexts.forEach(text => {
            text.textContent = `${percentage}%`;
        });

        // 更新狀態消息
        const statusText = progress.current_step || progress.message || '處理中...';
        const statusElement = document.querySelector('.processing-status');
        if (statusElement) {
            statusElement.textContent = statusText;
        }
    }

    showDownloadLink(downloadUrl, filename) {
        // 創建下載區域
        const downloadArea = document.createElement('div');
        downloadArea.className = 'download-area alert alert-success';
        downloadArea.innerHTML = `
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <i class="fas fa-download me-2"></i>
                    <strong>處理完成！</strong> 結果已準備好下載
                </div>
                <div>
                    <a href="${downloadUrl}" class="btn btn-success btn-sm" download="${filename}">
                        <i class="fas fa-download me-1"></i>
                        下載結果 (${filename})
                    </a>
                </div>
            </div>
        `;

        // 找到合適的位置插入下載區域
        const container = document.querySelector('.processing-results') ||
                         document.querySelector('.summary-processor') ||
                         document.querySelector('.main-content');

        if (container) {
            // 移除舊的下載區域
            const oldDownloadArea = container.querySelector('.download-area');
            if (oldDownloadArea) {
                oldDownloadArea.remove();
            }

            // 插入新的下載區域
            container.insertBefore(downloadArea, container.firstChild);

            // 自動滾動到下載區域
            downloadArea.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
    
    displayProcessingResults() {
        if (!this.processedData) return;
        
        // 顯示結果區域
        const resultsContainer = document.querySelector('.processing-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'block';
        }
        
        // 更新摘要統計
        this.updateSummaryStatistics();
        
        // 顯示關鍵詞雲
        this.displayKeywordCloud();
        
        // 生成圖表
        this.generateSummaryCharts();
        
        // 顯示摘要內容
        this.displaySummaryContent();
    }
    
    updateSummaryStatistics() {
        const stats = this.processedData.statistics;
        if (!stats) return;
        
        this.updateElement('.total-processed', stats.total_processed);
        this.updateElement('.key-insights', stats.key_insights);
        this.updateElement('.top-keywords', stats.top_keywords?.join(', ') || '');
        this.updateElement('.processing-time', this.formatDuration(stats.processing_time));
    }
    
    displayKeywordCloud() {
        const keywords = this.processedData.keywords;
        if (!keywords) return;
        
        const container = document.querySelector('.keyword-cloud');
        if (!container) return;
        
        container.innerHTML = '';
        
        keywords.forEach(keyword => {
            const tag = document.createElement('span');
            tag.className = 'keyword-tag';
            tag.textContent = keyword.word;
            tag.style.fontSize = `${Math.min(keyword.frequency / 10 + 12, 24)}px`;
            tag.title = `出現 ${keyword.frequency} 次`;
            
            container.appendChild(tag);
        });
    }
    
    generateSummaryCharts() {
        const chartData = this.processedData.charts;
        if (!chartData) return;
        
        // 時間趨勢圖
        if (chartData.timeline) {
            this.createTimelineChart(chartData.timeline);
        }
        
        // 分類分佈圖
        if (chartData.categories) {
            this.createCategoriesChart(chartData.categories);
        }
        
        // 重要性評分圖
        if (chartData.importance) {
            this.createImportanceChart(chartData.importance);
        }
    }
    
    createTimelineChart(data) {
        const ctx = document.getElementById('timelineChart');
        if (!ctx) return;
        
        this.charts.timeline = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: '項目數量',
                    data: data.values,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    createCategoriesChart(data) {
        const ctx = document.getElementById('categoriesChart');
        if (!ctx) return;
        
        this.charts.categories = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#007bff', '#28a745', '#ffc107', 
                        '#dc3545', '#17a2b8', '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    displaySummaryContent() {
        const summaryContent = this.processedData.summary;
        if (!summaryContent) return;
        
        const container = document.querySelector('.summary-content');
        if (!container) return;
        
        container.innerHTML = `
            <div class="summary-sections">
                ${summaryContent.sections.map(section => `
                    <div class="summary-section">
                        <h4>${section.title}</h4>
                        <p>${section.content}</p>
                        ${section.items ? `
                            <ul>
                                ${section.items.map(item => `<li>${item}</li>`).join('')}
                            </ul>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    handleViewToggle(e) {
        const viewType = e.target.dataset.view;
        this.currentView = viewType;
        
        // 更新按鈕狀態
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        e.target.classList.add('active');
        
        // 切換檢視
        this.switchView(viewType);
    }
    
    switchView(viewType) {
        const containers = document.querySelectorAll('.view-container');
        containers.forEach(container => {
            container.style.display = 'none';
        });
        
        const targetContainer = document.querySelector(`.${viewType}-view`);
        if (targetContainer) {
            targetContainer.style.display = 'block';
        }
    }
    
    handleSummaryItemClick(e) {
        const summaryId = e.target.closest('.summary-item').dataset.summaryId;
        this.loadSummaryDetail(summaryId);
    }
    
    async loadSummaryDetail(summaryId) {
        try {
            const response = await fetch(`/analytics/api/summaries/${summaryId}`);
            
            if (response.ok) {
                const summary = await response.json();
                this.displaySummaryDetail(summary);
            }
        } catch (error) {
            console.error('Failed to load summary detail:', error);
            this.showError('載入摘要詳情失敗');
        }
    }
    
    displaySummaryDetail(summary) {
        // 在這裡顯示摘要詳情
        this.processedData = summary;
        this.displayProcessingResults();
        
        // 切換到詳情檢視
        this.switchView('detail');
    }
    
    handleExportSummary(e) {
        const summaryId = e.target.dataset.summaryId;
        const format = e.target.dataset.format || 'pdf';
        
        const exportUrl = `/analytics/api/summaries/${summaryId}/export?format=${format}`;
        window.open(exportUrl, '_blank');
    }
    
    getStatusClass(status) {
        const statusMap = {
            'pending': 'pending',
            'processing': 'processing',
            'completed': 'success',
            'failed': 'danger'
        };
        
        return statusMap[status] || 'secondary';
    }
    
    getStatusText(status) {
        const statusMap = {
            'pending': '待處理',
            'processing': '處理中',
            'completed': '已完成',
            'failed': '失敗'
        };
        
        return statusMap[status] || status;
    }
    
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW');
    }
    
    formatDuration(seconds) {
        if (seconds < 60) {
            return `${seconds}秒`;
        } else if (seconds < 3600) {
            return `${Math.floor(seconds / 60)}分${seconds % 60}秒`;
        } else {
            return `${Math.floor(seconds / 3600)}小時${Math.floor((seconds % 3600) / 60)}分`;
        }
    }
    
    updateElement(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            element.textContent = value;
        }
    }
    
    showSuccess(message) {
        this.showAlert(message, 'success');
    }
    
    showError(message) {
        this.showAlert(message, 'danger');
    }
    
    showWarning(message) {
        this.showAlert(message, 'warning');
    }
    
    showAlert(message, type) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.ft-summary-container');
        if (container) {
            container.insertBefore(alert, container.firstChild);
        }
        
        // 自動移除警告
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.ft-summary-container')) {
        new FTSummaryProcessor();
    }
});