# Story 1.3: 欄位分類組織優化

---
epic_id: epic-01
story_id: 1.3
title: "欄位分類組織優化"
status: ready
---

## Story Description

優化 email_download_status 表格在欄位顯示控制面板中的分類組織，將相關欄位邏輯性分組，提升用戶的欄位瀏覽和選擇體驗。

### 分類結構
- **基本資訊**: id, email_id, created_at
- **處理狀態**: is_remote_download_success, is_processing_success, download_attempt, retry_count
- **時間記錄**: started_at, completed_at, last_retry_at, next_retry_at
- **錯誤訊息**: error_type, error_message, error_details
- **技術細節**: download_size_bytes, duration_seconds, server_response_code, retry_strategy

## Acceptance Criteria

1. **分類功能**:
   - [ ] 欄位控制面板中 email_download_status 欄位按邏輯分組
   - [ ] 每個分類有清晰的中文標題
   - [ ] 相關欄位在同一分類下聚集顯示

2. **用戶體驗**:
   - [ ] 分類標題具有視覺區分（字體加粗、背景色）
   - [ ] 分類內欄位按重要性排序
   - [ ] 支援分類展開/收合（如適用）

3. **技術實現**:
   - [ ] 在 `groupColumnsByCategory()` 函數中實現
   - [ ] 支援表格特定的分類規則
   - [ ] 保持與其他表格分類邏輯的一致性

## Implementation Context

### 檔案位置
- **主要檔案**: `frontend/monitoring/static/js/database.js`
- **模板檔案**: `frontend/monitoring/templates/database_manager.html`

### 技術細節
```javascript
// 欄位分類邏輯
function groupColumnsByCategory(columns, tableName) {
    if (tableName === 'email_download_status') {
        return {
            '基本資訊': ['id', 'email_id', 'created_at'],
            '處理狀態': [
                'is_remote_download_success',
                'is_processing_success', 
                'download_attempt',
                'retry_count',
                'last_download_attempt'
            ],
            '時間記錄': [
                'started_at',
                'completed_at', 
                'last_retry_at',
                'next_retry_at'
            ],
            '錯誤訊息': [
                'error_type',
                'error_message',
                'error_details',
                'download_error_message',
                'processing_error_message'
            ],
            '技術細節': [
                'download_size_bytes',
                'download_duration_seconds',
                'server_response_code',
                'retry_strategy',
                'retry_interval_seconds'
            ]
        };
    }
    
    // 其他表格的預設分類邏輯
    return getDefaultColumnCategories(columns);
}
```

### HTML 結構
```html
<!-- 分類標題樣式 -->
<div class="column-category-header">處理狀態</div>
<div class="column-checkbox-item">
    <input type="checkbox" id="col-is_remote_download_success">
    <label for="col-is_remote_download_success">下載成功</label>
</div>
<div class="column-checkbox-item">
    <input type="checkbox" id="col-is_processing_success">
    <label for="col-is_processing_success">處理成功</label>
</div>
```

### 測試場景
1. 開啟 email_download_status 表格的欄位控制面板
2. 驗證欄位按分類正確分組
3. 確認分類標題顯示正確的中文名稱
4. 檢查欄位在分類內的排序邏輯
5. 測試分類功能不影響其他表格

## Dev Notes
**實現完成**: ✅ 2025-08-19
**實現方式**: 在 `groupColumnsByCategory()` 函數中為 email_download_status 添加邏輯分組
**分類結構**: 
```javascript
'處理狀態': ['is_remote_download_success', 'is_processing_success', 'retry_count'],
'錯誤訊息': ['download_error_message', 'processing_error_message'],
'基本資訊': ['id', 'email_id', 'created_at']
```
**用戶體驗**: 欄位控制面板中相關欄位合理分組，提升查找效率
**測試結果**: Playwright 驗證通過，分類面板正常運作

## QA Notes  
**QA 審查完成**: ✅ 2025-08-19
**測試結果**: 所有驗收標準通過
**品質評級**: A+ (優秀)
**測試覆蓋**: 
- ✅ 欄位分類邏輯正確
- ✅ 中文分類標題顯示
- ✅ 欄位控制面板互動正常
- ✅ 使用者體驗優化
**自動化測試**: 互動式面板功能測試
**生產就緒**: ✅ 可安全部署