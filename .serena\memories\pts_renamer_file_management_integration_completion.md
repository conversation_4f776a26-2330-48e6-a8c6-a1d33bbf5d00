# PTS Renamer 與 File Management 系統整合完成

## 整合目標
將 PTS Renamer 系統與現有 backend/file_management 系統整合，使其使用 .env 檔案中定義的路徑配置，而不是硬編碼路徑。

## 完成的修改

### 1. PTSRenameConfig 配置模型整合
**檔案**: `backend/pts_renamer/models/pts_rename_models.py`

**修改內容**:
- 添加 `_upload_config` 私有屬性來緩存 UploadConfig 實例
- 添加 `upload_config` 屬性方法，動態載入 file_management 的 UploadConfig
- 添加 `temp_storage_path` 屬性，返回 .env 的 UPLOAD_TEMP_DIR 路徑
- 添加 `result_storage_path` 屬性，返回 .env 的 EXTRACT_TEMP_DIR + /results 路徑

**關鍵代碼**:
```python
@property
def upload_config(self) -> 'UploadConfig':
    """Get upload configuration from file_management system"""
    if self._upload_config is None:
        from backend.file_management.adapters.file_upload.upload_config import load_upload_config
        self._upload_config = load_upload_config()
    return self._upload_config

@property 
def temp_storage_path(self) -> str:
    """Get temporary storage path from UploadConfig (.env UPLOAD_TEMP_DIR)"""
    return self.upload_config.upload_temp_dir
    
@property
def result_storage_path(self) -> str:
    """Get result storage path from UploadConfig (.env EXTRACT_TEMP_DIR + /results)"""
    extract_dir = self.upload_config.extract_temp_dir
    return f"{extract_dir}/results"
```

### 2. PTSRenameUploadService 服務整合
**檔案**: `backend/pts_renamer/services/pts_rename_upload_service.py`

**修改內容**:
- 更新 `__init__` 方法，優先使用 config.temp_storage_path 屬性
- 添加更清晰的優先級邏輯和註釋
- 確保服務正確使用來自 .env 的路徑配置

**關鍵代碼**:
```python
# Use configuration from file_management system (.env variables)
# Priority: explicit override > config property > fallback default
if temp_storage_path:
    temp_path = temp_storage_path
elif config and hasattr(config, 'temp_storage_path'):
    temp_path = config.temp_storage_path  # Uses UploadConfig from file_management
else:
    temp_path = "d:/temp/uploads"  # Fallback default
```

### 3. PTSRenameDownloadService 服務整合
**檔案**: `backend/pts_renamer/services/pts_rename_download_service.py`

**修改內容**:
- 更新 `__init__` 方法，優先使用 config.result_storage_path 屬性
- 添加相同的優先級邏輯以保持一致性
- 確保下載服務使用來自 .env 的結果存儲路徑

**關鍵代碼**:
```python
# Use configuration from file_management system (.env variables)
# Priority: explicit override > config property > fallback default
if result_storage_path:
    storage_path = result_storage_path
elif config and hasattr(config, 'result_storage_path'):
    storage_path = config.result_storage_path  # Uses UploadConfig from file_management
else:
    storage_path = "d:/temp/results"  # Fallback default
```

## 環境變數配置
確保 .env 檔案中包含以下配置：
```
UPLOAD_TEMP_DIR=d:/temp/uploads
EXTRACT_TEMP_DIR=d:/temp
```

## 整合效果

### 之前（硬編碼路徑）:
- 上傳暫存: `/tmp/pts_renamer` (Linux 路徑，在 Windows 不存在)
- 結果存儲: `/tmp/pts_renamer/results` (硬編碼)

### 之後（.env 配置）:
- 上傳暫存: `d:/temp/uploads` (從 UPLOAD_TEMP_DIR)
- 結果存儲: `d:/temp/results` (從 EXTRACT_TEMP_DIR + /results)

## 技術優勢

1. **配置統一**: PTS Renamer 現在使用與其他系統組件相同的配置機制
2. **環境適配**: 路徑配置可以根據部署環境調整，不再硬編碼
3. **維護性**: 所有路徑配置集中在 .env 檔案中，易於管理
4. **向後兼容**: 保留覆寫參數，不影響現有呼叫方式
5. **錯誤處理**: 提供回退預設值，確保系統穩定性

## 驗證步驟

1. 確認 PTSRenameConfig 正確載入 UploadConfig
2. 驗證服務初始化時使用正確路徑
3. 測試檔案上傳和下載功能
4. 檢查路徑創建和清理功能

## 相關檔案

- `backend/pts_renamer/models/pts_rename_models.py` - 配置模型
- `backend/pts_renamer/services/pts_rename_upload_service.py` - 上傳服務
- `backend/pts_renamer/services/pts_rename_download_service.py` - 下載服務
- `backend/file_management/adapters/file_upload/upload_config.py` - 檔案管理配置
- `.env` - 環境變數配置檔案

整合完成，PTS Renamer 系統現在與 file_management 系統使用統一的配置機制。