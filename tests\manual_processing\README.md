# 手動處理功能驗證測試套件

## 概述

這是一套完整的手動處理功能驗證測試，確保修復後的功能能正常運作且與自動處理流程保持一致。

## 測試架構

### 測試文件結構
```
tests/manual_processing/
├── README.md                    # 本說明文件
├── run_all_tests.py            # 主要執行腳本
├── test_api_connection.py      # API 連接測試
├── test_data_integrity.py      # 資料完整性測試
├── test_error_handling.py      # 錯誤處理測試
└── test_ui_feedback.py         # UI 回饋測試
```

### 測試階段

#### 階段 1: API 連接測試 (`test_api_connection.py`)
- **目標**: 確認前端能正確調用後端 API
- **測試項目**:
  - API 端點可用性檢查
  - API Key 認證機制
  - CORS 標頭配置
  - 響應格式驗證
  - 手動輸入端點結構驗證

#### 階段 2: 資料完整性測試 (`test_data_integrity.py`)
- **目標**: 確認郵件資料正確傳遞和處理
- **測試項目**:
  - 必填欄位驗證 (vendor_code, pd, lot)
  - 選填欄位處理 (mo, yield_value)
  - 資料格式驗證
  - 資料庫更新準確性
  - 時間戳記錄

#### 階段 3: 錯誤處理測試 (`test_error_handling.py`)
- **目標**: 測試各種錯誤情況的處理
- **測試項目**:
  - 無效的 emailId
  - 缺少必填欄位
  - 格式驗證錯誤
  - API Key 錯誤
  - 網路連接問題
  - 格式錯誤的 JSON

#### 階段 4: UI 回饋測試 (`test_ui_feedback.py`)
- **目標**: 確認成功/失敗訊息正確顯示
- **測試項目**:
  - 手動輸入對話框顯示
  - 成功儲存後的回饋
  - 驗證錯誤回饋
  - 郵件列表更新
  - JavaScript 錯誤檢查

#### 階段 5: 整合測試
- **目標**: 驗證與系統其他部分的整合
- **測試項目**:
  - 與自動處理流程一致性
  - 無回歸問題檢查
  - 系統效能影響評估

## 使用方法

### 前置條件

1. **安裝依賴**:
```bash
pip install requests sqlite3 pytest
pip install playwright  # 用於 UI 測試
playwright install      # 安裝瀏覽器
```

2. **環境設定**:
```bash
# 設定環境變數
export API_BASE_URL="http://localhost:5000"
export UI_BASE_URL="http://localhost:5000"
export PARSER_API_KEY="dev-parser-key-12345"
```

3. **確保系統運行**:
- 確保後端服務正在運行
- 確保資料庫可以訪問
- 確保前端頁面可以正常載入

### 執行完整測試套件

```bash
# 執行所有測試
python tests/manual_processing/run_all_tests.py
```

### 執行個別測試

```bash
# API 連接測試
python tests/manual_processing/test_api_connection.py

# 資料完整性測試
python tests/manual_processing/test_data_integrity.py

# 錯誤處理測試
python tests/manual_processing/test_error_handling.py

# UI 回饋測試
python tests/manual_processing/test_ui_feedback.py
```

## 測試結果解讀

### 通過條件
- 所有測試項目 100% 通過
- 無關鍵錯誤或警告
- 使用者體驗流暢自然
- 與現有功能完全相容

### 失敗處理
如果測試失敗，請檢查：

1. **API 連接問題**:
   - 檢查服務是否正在運行
   - 驗證 API Key 設定
   - 確認網路連接正常

2. **資料完整性問題**:
   - 檢查資料庫連接
   - 驗證資料格式
   - 確認必填欄位邏輯

3. **錯誤處理問題**:
   - 檢查錯誤響應格式
   - 驗證狀態碼返回
   - 確認錯誤訊息清晰

4. **UI 回饋問題**:
   - 檢查 JavaScript 控制台錯誤
   - 驗證 DOM 元素存在
   - 確認事件綁定正確

## 報告格式

### 個別測試報告
每個測試會生成 JSON 格式的詳細報告：
```json
{
  "test_type": "API Connection Test",
  "timestamp": "2025-08-17T...",
  "total_tests": 10,
  "passed_tests": 9,
  "failed_tests": 1,
  "test_results": [...]
}
```

### 綜合報告
執行完整測試套件後會生成綜合報告：
```json
{
  "test_suite": "Manual Processing Verification",
  "overall_success": true,
  "summary": {
    "total_tests": 45,
    "total_passed": 43,
    "total_failed": 2
  },
  "test_results": {...}
}
```

## 關鍵驗證點

### ✅ 處理按鈕調用正確的 API 端點
- 按鈕點擊觸發 `showManualInputDialog(emailId)`
- API 路徑: `/email/api/parser/emails/${emailId}/manual-input`
- HTTP 方法: POST

### ✅ 使用正確的 emailId 參數
- emailId 從郵件列表正確獲取
- 參數類型驗證 (必須為正整數)
- 資料庫存在性檢查

### ✅ 與自動處理流程一致性
- 資料欄位結構相同
- 處理結果格式統一
- 後續處理流程一致
- 通知機制相同

### ✅ 無回歸問題
- 不影響現有自動解析功能
- 不影響批次處理功能
- 不影響郵件同步功能
- 系統效能無明顯下降

## 故障排除

### 常見問題

1. **Playwright 安裝問題**:
```bash
pip install playwright
playwright install chromium
```

2. **資料庫路徑問題**:
確認資料庫檔案路徑正確，測試會自動搜尋以下位置：
- `D:/project/python/outlook_summary/data/email_inbox.db`
- `./data/email_inbox.db`
- `./email_inbox.db`

3. **API Key 認證問題**:
檢查環境變數或使用預設值：
```bash
export PARSER_API_KEY="dev-parser-key-12345"
```

4. **網路超時問題**:
調整測試中的超時設定或確認服務響應速度。

### 調試建議

1. **啟用詳細日誌**:
修改測試腳本中的日誌級別以獲得更多資訊。

2. **單步執行**:
可以單獨執行每個測試階段來精確定位問題。

3. **檢查測試資料**:
確認測試過程中建立的資料是否正確清理。

## 維護指南

### 測試更新
- 當 API 端點變更時，更新相應的測試
- 當 UI 結構變更時，更新選擇器
- 當資料庫結構變更時，更新資料驗證邏輯

### 效能監控
- 定期執行測試以確保功能穩定
- 監控測試執行時間變化
- 收集並分析失敗模式

### 擴展測試
- 根據新需求增加測試案例
- 增加邊界條件測試
- 添加更多錯誤情境測試

---

## 總結

這套測試系統提供了對手動處理功能的全面驗證，確保：

1. **功能正確性**: 所有基本功能都能正常運作
2. **資料完整性**: 資料傳遞和儲存準確無誤
3. **錯誤處理**: 各種異常情況都能妥善處理
4. **使用者體驗**: UI 回饋清晰及時
5. **系統整合**: 與現有功能無衝突

通過這套測試，可以確信手動處理功能修復後的品質和穩定性。