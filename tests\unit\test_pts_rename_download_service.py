"""
Unit tests for PTS Rename Download Service

Tests the download service functionality including result compression,
download URL generation, and file cleanup.
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from backend.pts_renamer.services.pts_rename_download_service import PTSRenameDownloadService
from backend.pts_renamer.models.pts_rename_models import PTSRenameConfig
from backend.pts_renamer.repositories.pts_rename_repository import IPTSRenameRepository


class TestPTSRenameDownloadService:
    """Test cases for PTS Rename Download Service"""
    
    @pytest.fixture
    def mock_repository(self):
        """Mock repository for testing"""
        repo = Mock(spec=IPTSRenameRepository)
        repo.update_job_compression_info = AsyncMock(return_value=True)
        return repo
    
    @pytest.fixture
    def test_config(self):
        """Test configuration"""
        return PTSRenameConfig(
            cleanup_retention_hours=24,
            compression_level=6,
            enable_compression=True,
            result_storage_path="/tmp/pts_results"
        )
    
    @pytest.fixture
    def download_service(self, mock_repository, test_config):
        """Download service instance for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = test_config
            config.result_storage_path = temp_dir
            
            service = PTSRenameDownloadService(
                repository=mock_repository,
                config=config,
                result_storage_path=temp_dir,
                base_download_url="http://localhost:5000/pts-renamer/api/download"
            )
            return service
    
    @pytest.mark.asyncio
    async def test_create_processing_report(self, download_service):
        """Test creation of processing report"""
        with tempfile.TemporaryDirectory() as temp_dir:
            target_dir = Path(temp_dir)
            
            # Create some test files
            test_files = [
                target_dir / "test1.pts",
                target_dir / "test2_QC.pts",
                target_dir / "test_dir"
            ]
            
            test_files[0].write_text("test content 1")
            test_files[1].write_text("test content 2")
            test_files[2].mkdir()
            (test_files[2] / "subfile.txt").write_text("sub content")
            
            report_path = await download_service._create_processing_report(
                target_dir, test_files
            )
            
            assert report_path is not None
            assert report_path.exists()
            assert report_path.name == "processing_report.txt"
            
            # Check report content
            report_content = report_path.read_text()
            assert "PTS File Renamer - Processing Report" in report_content
            assert "test1.pts" in report_content
            assert "test2_QC.pts" in report_content
            assert "test_dir" in report_content
            assert "Total files processed: 3" in report_content
    
    @pytest.mark.asyncio
    async def test_create_local_archive(self, download_service):
        """Test creation of local ZIP archive"""
        with tempfile.TemporaryDirectory() as temp_dir:
            source_dir = Path(temp_dir) / "source"
            result_dir = Path(temp_dir) / "result"
            source_dir.mkdir()
            result_dir.mkdir()
            
            # Create test files in source directory
            (source_dir / "test1.pts").write_text("test content 1")
            (source_dir / "test2.pts").write_text("test content 2")
            
            subdir = source_dir / "subdir"
            subdir.mkdir()
            (subdir / "subfile.txt").write_text("sub content")
            
            archive_path = await download_service._create_local_archive(
                source_dir, result_dir, "test_archive", compression_level=1
            )
            
            assert archive_path.exists()
            assert archive_path.suffix == ".zip"
            assert archive_path.name == "test_archive.zip"
            assert archive_path.stat().st_size > 0
            
            # Verify archive contents
            import zipfile
            with zipfile.ZipFile(archive_path, 'r') as zipf:
                file_list = zipf.namelist()
                assert "test1.pts" in file_list
                assert "test2.pts" in file_list
                assert "subdir/subfile.txt" in file_list
    
    @pytest.mark.asyncio
    async def test_prepare_files_for_compression(self, download_service):
        """Test preparation of files for compression"""
        with tempfile.TemporaryDirectory() as temp_dir:
            source_dir = Path(temp_dir) / "source"
            target_dir = Path(temp_dir) / "target"
            source_dir.mkdir()
            target_dir.mkdir()
            
            # Create test files
            test_files = [
                source_dir / "test1.pts",
                source_dir / "test2.pts"
            ]
            
            test_files[0].write_text("test content 1")
            test_files[1].write_text("test content 2")
            
            # Create test directory
            test_dir = source_dir / "test_directory"
            test_dir.mkdir()
            (test_dir / "subfile.txt").write_text("sub content")
            test_files.append(test_dir)
            
            copied_files = await download_service._prepare_files_for_compression(
                test_files, target_dir, include_original=False
            )
            
            assert len(copied_files) >= 3  # 2 files + 1 directory + report
            
            # Check that files were copied
            assert (target_dir / "test1.pts").exists()
            assert (target_dir / "test2.pts").exists()
            assert (target_dir / "test_directory").exists()
            assert (target_dir / "test_directory" / "subfile.txt").exists()
            
            # Check that processing report was created
            report_files = list(target_dir.glob("processing_report.txt"))
            assert len(report_files) == 1
    
    def test_generate_download_url(self, download_service):
        """Test generation of download URL"""
        job_id = "test_job_123"
        compressed_file_path = "/path/to/compressed/file.zip"
        
        download_url, expiration_time = download_service.generate_download_url(
            job_id, compressed_file_path, expires_in_hours=2
        )
        
        assert download_url.startswith("http://localhost:5000/pts-renamer/api/download/")
        assert job_id in download_url
        
        # Check expiration time
        expected_expiration = datetime.now() + timedelta(hours=2)
        time_diff = abs((expiration_time - expected_expiration).total_seconds())
        assert time_diff < 60  # Within 1 minute tolerance
    
    def test_store_and_get_download_mapping(self, download_service):
        """Test storing and retrieving download mapping"""
        download_token = "test_token_123"
        file_path = "/path/to/file.zip"
        expiration_time = datetime.now() + timedelta(hours=1)
        job_id = "test_job_123"
        
        # Store mapping
        download_service._store_download_mapping(
            download_token, file_path, expiration_time, job_id
        )
        
        # Retrieve mapping
        mapping_info = download_service.get_download_info(download_token)
        
        assert mapping_info is not None
        assert mapping_info['download_token'] == download_token
        assert mapping_info['file_path'] == file_path
        assert mapping_info['job_id'] == job_id
        assert mapping_info['downloaded'] is False
        assert mapping_info['download_count'] == 0
    
    def test_get_download_info_expired(self, download_service):
        """Test getting download info for expired token"""
        download_token = "expired_token_123"
        file_path = "/path/to/file.zip"
        expiration_time = datetime.now() - timedelta(hours=1)  # Expired
        job_id = "test_job_123"
        
        # Store expired mapping
        download_service._store_download_mapping(
            download_token, file_path, expiration_time, job_id
        )
        
        # Try to retrieve expired mapping
        mapping_info = download_service.get_download_info(download_token)
        
        assert mapping_info is None
    
    def test_get_download_info_nonexistent(self, download_service):
        """Test getting download info for non-existent token"""
        mapping_info = download_service.get_download_info("nonexistent_token")
        assert mapping_info is None
    
    def test_mark_download_accessed(self, download_service):
        """Test marking download as accessed"""
        download_token = "access_token_123"
        file_path = "/path/to/file.zip"
        expiration_time = datetime.now() + timedelta(hours=1)
        job_id = "test_job_123"
        
        # Store mapping
        download_service._store_download_mapping(
            download_token, file_path, expiration_time, job_id
        )
        
        # Mark as accessed
        success = download_service.mark_download_accessed(download_token)
        assert success is True
        
        # Verify access was recorded
        mapping_info = download_service.get_download_info(download_token)
        assert mapping_info['downloaded'] is True
        assert mapping_info['download_count'] == 1
        assert 'last_downloaded_at' in mapping_info
        
        # Mark as accessed again
        success = download_service.mark_download_accessed(download_token)
        assert success is True
        
        # Verify count increased
        mapping_info = download_service.get_download_info(download_token)
        assert mapping_info['download_count'] == 2
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_downloads(self, download_service):
        """Test cleanup of expired downloads"""
        # Create expired mapping
        expired_token = "expired_token_123"
        expired_expiration = datetime.now() - timedelta(hours=1)
        download_service._store_download_mapping(
            expired_token, "/path/to/expired.zip", expired_expiration, "job1"
        )
        
        # Create valid mapping
        valid_token = "valid_token_456"
        valid_expiration = datetime.now() + timedelta(hours=1)
        download_service._store_download_mapping(
            valid_token, "/path/to/valid.zip", valid_expiration, "job2"
        )
        
        # Run cleanup
        cleaned_count = await download_service.cleanup_expired_downloads()
        
        assert cleaned_count >= 1
        
        # Verify expired mapping was removed
        assert download_service.get_download_info(expired_token) is None
        
        # Verify valid mapping still exists
        assert download_service.get_download_info(valid_token) is not None
    
    def test_get_service_statistics(self, download_service):
        """Test getting service statistics"""
        # Create some test data
        job_id = "test_job_123"
        job_result_dir = download_service.result_storage_path / job_id
        job_result_dir.mkdir(parents=True, exist_ok=True)
        (job_result_dir / "test_file.zip").write_text("test content")
        
        # Create download mapping
        download_token = "stats_token_123"
        expiration_time = datetime.now() + timedelta(hours=1)
        download_service._store_download_mapping(
            download_token, "/path/to/file.zip", expiration_time, job_id
        )
        
        try:
            stats = download_service.get_service_statistics()
            
            assert 'result_directories' in stats
            assert 'active_downloads' in stats
            assert 'expired_downloads' in stats
            assert 'total_storage_mb' in stats
            assert 'storage_path' in stats
            assert 'retention_hours' in stats
            assert 'compression_enabled' in stats
            
            assert stats['result_directories'] >= 1
            assert stats['active_downloads'] >= 1
            assert isinstance(stats['total_storage_mb'], (int, float))
            
        finally:
            # Cleanup
            import shutil
            if job_result_dir.exists():
                shutil.rmtree(job_result_dir)
    
    @pytest.mark.asyncio
    async def test_create_download_package(self, download_service):
        """Test creation of complete download package"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test result files
            result_files = []
            
            # Renamed files
            renamed_file = Path(temp_dir) / "renamed_test.pts"
            renamed_file.write_text("renamed content")
            result_files.append(renamed_file)
            
            # QC files
            qc_file = Path(temp_dir) / "test_QC.pts"
            qc_file.write_text("qc content")
            result_files.append(qc_file)
            
            # Directory
            test_dir = Path(temp_dir) / "test_directory"
            test_dir.mkdir()
            (test_dir / "dir_file.txt").write_text("directory content")
            result_files.append(test_dir)
            
            # Mock results dictionary
            results = {
                'renamed_files': [{'new_path': str(renamed_file)}],
                'qc_files': [{'qc_path': str(qc_file)}],
                'created_directories': [{'directory_path': str(test_dir)}]
            }
            
            job_id = "package_test_job"
            
            # Mock the compress_processed_files method to return success
            with patch.object(download_service, 'compress_processed_files') as mock_compress:
                mock_compress.return_value = (True, "/path/to/package.zip")
                
                package_path = await download_service.create_download_package(job_id, results)
                
                assert package_path == "/path/to/package.zip"
                mock_compress.assert_called_once()
                
                # Verify the files passed to compression
                call_args = mock_compress.call_args
                compressed_files = call_args[0][1]  # Second argument (processed_files)
                
                assert len(compressed_files) == 3
                assert any(str(f) == str(renamed_file) for f in compressed_files)
                assert any(str(f) == str(qc_file) for f in compressed_files)
                assert any(str(f) == str(test_dir) for f in compressed_files)


if __name__ == "__main__":
    pytest.main([__file__])