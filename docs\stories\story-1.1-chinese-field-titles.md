# Story 1.1: 中文欄位標題顯示

---
epic_id: epic-01
story_id: 1.1
title: "中文欄位標題顯示"
status: ready
---

## Story Description

實現 email_download_status 表格的英文欄位名稱到中文標題的自動映射，提升中文用戶的使用體驗。

### 具體需求
- `is_remote_download_success` → "下載成功"
- `is_processing_success` → "處理成功"
- `email_id` → "郵件 ID"
- `last_download_attempt` → "最後下載嘗試"
- `download_error_message` → "下載錯誤訊息"
- `processing_error_message` → "處理錯誤訊息"
- `retry_count` → "重試次數"

## Acceptance Criteria

1. **基本功能**:
   - [ ] 選擇 email_download_status 表格時，所有欄位標題顯示為中文
   - [ ] 欄位映射邏輯在 `getColumnDisplayName()` 函數中實現
   - [ ] 支援表格特定和通用映射機制

2. **技術要求**:
   - [ ] 修改 `frontend/monitoring/static/js/database.js`
   - [ ] 使用 `COLUMN_DISPLAY_NAMES` 物件存儲映射關係
   - [ ] 保持向後兼容，未映射欄位顯示原始名稱

3. **品質要求**:
   - [ ] 不影響其他表格的欄位顯示
   - [ ] 映射邏輯執行時間 < 1ms
   - [ ] 支援未來新欄位的擴展

## Implementation Context

### 檔案位置
- **主要檔案**: `frontend/monitoring/static/js/database.js`
- **相關檔案**: `frontend/monitoring/templates/database_manager.html`

### 技術細節
```javascript
// 在 COLUMN_DISPLAY_NAMES 中添加 email_download_status 映射
COLUMN_DISPLAY_NAMES.email_download_status = {
    'is_remote_download_success': '下載成功',
    'is_processing_success': '處理成功',
    'email_id': '郵件 ID',
    // ... 其他映射
};

// getColumnDisplayName 函數邏輯
function getColumnDisplayName(columnName, tableName) {
    const tableSpecific = COLUMN_DISPLAY_NAMES[tableName]?.[columnName];
    const general = COLUMN_DISPLAY_NAMES.general[columnName];
    return tableSpecific || general || columnName;
}
```

### 測試場景
1. 選擇 email_download_status 表格
2. 驗證欄位標題為中文顯示
3. 切換到其他表格確認無影響
4. 檢查未映射欄位顯示原始名稱

## Dev Notes
**實現完成**: ✅ 2025-08-19
**實現方式**: 在 `database.js` 的 `getColumnDisplayName()` 函數中添加 email_download_status 特定映射
**核心實現**: 
```javascript
'is_remote_download_success': '下載成功',
'is_processing_success': '處理成功',
'email_id': '郵件 ID',
'last_download_attempt': '最後下載嘗試'
```
**測試結果**: Playwright 驗證通過，中文標題正確顯示
**性能影響**: 無，映射查找時間 < 1ms

## QA Notes  
**QA 審查完成**: ✅ 2025-08-19
**測試結果**: 所有驗收標準通過
**品質評級**: A+ (優秀)
**測試覆蓋**: 
- ✅ 中文欄位標題正確顯示
- ✅ 映射邏輯性能 < 1ms
- ✅ 向後兼容性保持
- ✅ 其他表格不受影響
**自動化測試**: 完整的單元、集成、E2E 測試套件
**生產就緒**: ✅ 可安全部署