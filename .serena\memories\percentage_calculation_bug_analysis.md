# EQCTOTALDATA.xlsx Summary Sheet 百分比計算錯誤分析

## 問題現象
用戶報告在 code_comparison.py 生成的 EQCTOTALDATA.xlsx 中 summary sheet 的百分比計算錯誤：
- 原本應該顯示 6% 的數據變成了 6000%
- 原本應該顯示 4% 的數據變成了 4000%
- 看起來百分比被乘以了1000倍（或者10倍再乘以100倍）

## 具體錯誤示例
```
Yield    6000%                    
                    Total    5
Bin    Count    %    Definition    Note    Site 1    %
1    3    6000%    All Pass        3    6000%
394    2    4000%    VIO_Gm        2    4000%
```

## 已檢查的文件
1. **ft_summary_generator.py** (line 448, 467, 95, 272)
   - 百分比計算：`(count / total * 100)` - 正確
   
2. **summary_generator.py** (line 107, 114, 208, 270)  
   - 百分比計算：`count / total * 100` - 正確
   
3. **batch_csv_to_excel_processor.py** (line 514, 534)
   - 百分比計算：`(count / total * 100)` - 正確

## 問題分析
所有找到的百分比計算代碼看起來都是正確的（乘以100），但用戶看到的結果是乘以1000倍。

## 可能原因
1. **雙重百分比轉換**：某個地方已經是百分比格式，又被乘以100
2. **格式化問題**：數據在display時被錯誤格式化
3. **Excel生成問題**：在生成Excel時百分比格式有問題
4. **特定路徑問題**：某個特定的代碼路徑有額外的乘法運算

## 需要進一步檢查的地方
1. Excel輸出相關的格式化代碼
2. Summary sheet生成的具體實現
3. 可能存在的百分比格式轉換邏輯
4. 實際測試重現問題

## 用戶提示
- 這個問題"之前有修改過"，需要找到歷史修改記錄
- 問題出現在Summary sheet中