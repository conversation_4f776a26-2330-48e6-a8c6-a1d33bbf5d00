# Data 目錄 - JSON 資料檔案

此目錄包含專案的各種 JSON 資料檔案，按類型分類存放。

## 目錄結構

- **[config/](./config/)** - 專案配置和統計檔案 (2 個檔案)
- **[reports/](./reports/)** - 測試和驗證報告 (13 個檔案)
- **[archive/](./archive/)** - 歷史存檔檔案 (0 個檔案)


## 使用說明

### 配置檔案 (config/)
包含專案配置和統計資料，如：
- 專案基本資訊
- 效能指標
- 系統配置

### 報告檔案 (reports/)  
包含各種測試和驗證報告，如：
- 功能測試結果
- 架構驗證報告
- 效能監控資料

### 存檔檔案 (archive/)
包含歷史資料和備份檔案，如：
- 舊版本測試結果
- 歷史配置備份
- 過時的報告資料

## 檔案管理

- **新增檔案**: 根據用途放入適當的子目錄
- **命名規範**: 使用描述性檔名，包含日期或版本
- **定期清理**: 定期檢查並移除過時的檔案

---
*最後更新: 2025-08-16 23:25:44*
*目錄建立: JSON 檔案整理工具*
