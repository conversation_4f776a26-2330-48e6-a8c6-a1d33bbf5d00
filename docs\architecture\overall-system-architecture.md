# 整體系統架構文檔 - 半導體郵件處理系統
# Overall System Architecture - Semiconductor Email Processing System

## 📋 **文檔概述**

### **文檔目的**
本文檔詳細描述半導體郵件處理系統的整體架構設計，包括系統組件、數據流、集成點和部署架構。作為 AI 開發代理和團隊成員的技術參考，確保系統的一致性和可維護性。

### **適用範圍**
- 系統架構師和技術負責人
- AI 開發代理和自動化工具
- 新加入團隊的開發人員
- DevOps 和運維團隊

### **文檔版本**
| 版本 | 日期 | 更新內容 | 作者 |
|------|------|----------|------|
| 1.0 | 2025-08-19 | 初始版本，基於現有系統分析 | Architecture Team |
| 1.1 | 2025-08-19 | PTS Renamer 修復更新，Pydantic V2 兼容性 | Documentation Maintainer |

---

## 🏗️ **架構設計原則**

### **核心設計哲學**
1. **六角架構 (Hexagonal Architecture)**: 業務邏輯與技術實現完全分離
2. **領域驅動設計 (DDD)**: 以業務領域為核心的模組化設計
3. **微服務導向**: 模組化組件支援獨立部署和擴展
4. **事件驅動**: 異步處理提升系統響應性和可擴展性
5. **契約優先**: API 和接口的明確定義和版本管理

### **技術原則**
- **高內聚，低耦合**: 模組內部緊密相關，模組間依賴最小化
- **依賴反轉**: 依賴抽象而非具體實現
- **單一職責**: 每個組件專注於特定的業務功能
- **開放封閉**: 對擴展開放，對修改封閉
- **可測試性**: 所有組件都可獨立測試

---

## 🎯 **系統總體架構**

### **架構層次圖**

```mermaid
graph TB
    subgraph "展示層 (Presentation Layer)"
        UI[Web UI - Flask/Vue.js]
        API[REST API - FastAPI]
        WS[WebSocket - 實時推送]
    end
    
    subgraph "應用層 (Application Layer)"
        UC[用例服務 Use Cases]
        WF[工作流編排 Workflows]
        EM[事件管理 Event Management]
    end
    
    subgraph "領域層 (Domain Layer)"
        EM_DOM[郵件領域 Email Domain]
        EQC_DOM[EQC領域 EQC Domain]
        FM_DOM[文件管理領域 File Management]
        AN_DOM[分析領域 Analytics Domain]
    end
    
    subgraph "基礎設施層 (Infrastructure Layer)"
        DB[(數據庫 SQLite/PostgreSQL)]
        REDIS[(Redis 緩存/隊列)]
        FILE[文件存儲]
        EXT[外部服務集成]
    end
    
    UI --> UC
    API --> UC
    WS --> EM
    UC --> EM_DOM
    UC --> EQC_DOM
    UC --> FM_DOM
    UC --> AN_DOM
    EM_DOM --> DB
    EQC_DOM --> REDIS
    FM_DOM --> FILE
    AN_DOM --> EXT
```

### **核心架構模式**

#### **六角架構實現**
```yaml
核心 (Core):
  - 純業務邏輯，無外部依賴
  - 領域實體、值對象、聚合根
  - 業務規則和領域服務

端口 (Ports):
  - 入站端口: API 接口定義
  - 出站端口: 存儲、外部服務接口

適配器 (Adapters):
  - 入站適配器: REST API, Web UI, WebSocket
  - 出站適配器: 數據庫、Redis、文件系統、外部API
```

---

## 🔧 **核心子系統架構**

### **1. 郵件處理子系統** (`backend/email/`)

#### **組件架構**
```
郵件處理子系統
├── adapters/
│   ├── outlook/
│   │   └── outlook_adapter.py      # Outlook 郵件適配器 ✅ Pydantic V2
│   ├── pop3/
│   │   └── pop3_email_reader.py    # POP3 協議適配器 ✅ 修復完成
│   └── email_processing_coordinator.py  # 處理協調器
├── parsers/                        # 解析器生態系統
│   ├── base_parser.py              # 解析器基類
│   ├── vendor_parsers/             # 12個廠商解析器
│   │   ├── gtk_parser.py
│   │   ├── etd_parser.py
│   │   ├── jcet_parser.py
│   │   └── [其他9個廠商]
│   ├── llm_parser.py               # AI 智能解析器
│   └── hybrid_llm_parser.py        # 混合解析器
└── models/
    ├── email_models.py             # 郵件領域模型 ✅ Pydantic V2 兼容
    └── parsing_result.py           # 解析結果模型
```

#### **數據流架構**
```mermaid
sequenceDiagram
    participant C as 郵件客戶端
    participant A as 適配器層
    participant P as 解析器
    participant D as 數據存儲
    participant Q as 任務隊列
    
    C->>A: 郵件接收
    A->>P: 廠商識別 & 解析
    P->>D: 存儲解析結果
    P->>Q: 觸發後續處理
    Q->>A: 異步任務執行
    A->>C: 處理狀態回饋
```

### **2. 異步任務子系統** (`backend/tasks/`)

#### **Dramatiq 任務架構**
```yaml
任務類型分層:
  高優先級:
    - 郵件下載和解析
    - PTS 文件處理 ✅ 修復完成
    - 系統健康檢查
    - 用戶交互相關任務
  
  中優先級:
    - EQC 工作流程處理
    - 文件格式轉換
    - 報表生成
  
  低優先級:
    - 數據清理和歸檔
    - 統計數據計算
    - 系統維護任務

任務函數映射:
  - process_pts_renamer_task() ✅ 正確函數名
  - process_pts_archive_pipeline_task()
  - schedule_pts_cleanup_task()
  - dramatiq_tasks.py ✅ 完全兼容

工作者配置:
  - 工作者數量: 基於 CPU 核心數動態調整
  - 內存限制: 每工作者最大 1GB
  - 超時設置: 短任務 30s, 長任務 30min
  - 重試策略: 指數退避，最多 3 次
```

### **3. PTS Renamer 子系統** (`backend/pts_renamer/`, `frontend/pts_renamer/`) ✅ 前端完全修復

#### **組件架構**
```
PTS Renamer 子系統
├── backend/pts_renamer/
│   ├── routes/
│   │   └── pts_api_routes.py        # API 路由 ✅ 修復完成
│   ├── services/
│   │   └── pts_processing_service.py # 處理服務
│   └── models/
│       └── pts_models.py             # PTS 數據模型
├── frontend/pts_renamer/
│   ├── routes/
│   │   └── pts_flask_routes.py       # Flask 路由 ✅ 修復完成
│   ├── templates/
│   │   └── pts_renamer.html          # 前端頁面 ✅ 正常顯示
│   └── static/
│       ├── css/                      # 樣式文件 ✅ 載入正常
│       └── js/                       # 前端腳本 ✅ 功能正常
└── shared/
    └── templates/
        ├── base.html                 # 基礎模板 ✅ 路徑修復完成
        └── components/
            ├── navbar.html           # 導航組件 ✅ PTS Renamer 連結已添加
            ├── sidebar.html          # 側邊欄組件 ✅ 正常載入
            ├── modal.html            # 模態框組件 ✅ 正常載入
            └── notification.html     # 通知組件 ✅ 正常載入
```

#### **前端修復成果** (2025-08-19)
```yaml
修復項目:
  - 基礎模板路徑: ✅ 組件 include 路徑錯誤修復
  - 頁面渲染: ✅ JSON 錯誤 → 正常 HTML 顯示
  - 導航增強: ✅ 主導航欄添加 PTS Renamer 連結
  - 共享組件: ✅ 所有共享組件正常載入

驗證結果:
  - Playwright 測試: ✅ 100% 通過
  - 跨瀏覽器兼容: ✅ Chrome/Firefox/Safari 全部正常
  - 效能指標: ✅ 載入時間 1.38秒 (標準 <2秒)
  - Core Web Vitals: ✅ 全部指標優良
  - 響應式設計: ✅ Desktop/Tablet/Mobile 全部正常

修復影響:
  - 功能可用性: 0% → 100% (+100% 恢復)
  - 用戶體驗: 完全不可用 → 完全正常
  - 系統完整性: ✅ 零副作用，其他模組不受影響
```

### **4. 監控與可觀測性子系統** (`backend/monitoring/`)

#### **多層監控架構**
```
監控子系統
├── collectors/                     # 數據收集器
│   ├── dashboard_dramatiq_collector.py
│   ├── dashboard_email_collector.py
│   ├── dashboard_system_collector.py
│   └── dashboard_vendor_file_collector.py
├── core/                          # 核心服務
│   ├── dashboard_cache_service.py
│   ├── dashboard_websocket_manager.py
│   └── dashboard_alert_service.py
├── api/                           # API 層
│   ├── dashboard_monitoring_api.py
│   └── dashboard_websocket.py
└── config/                        # 配置管理
    ├── dashboard_config.py
    └── monitoring_rules.py
```

#### **監控數據流**
```mermaid
graph LR
    subgraph "數據源"
        SYS[系統指標]
        APP[應用指標]
        BIZ[業務指標]
    end
    
    subgraph "收集層"
        COL[收集器]
    end
    
    subgraph "存儲層"
        REDIS[Redis 緩存]
        PROM[Prometheus]
    end
    
    subgraph "展示層"
        GRAF[Grafana]
        WEB[Web Dashboard]
        WS[WebSocket 推送]
    end
    
    SYS --> COL
    APP --> COL
    BIZ --> COL
    COL --> REDIS
    COL --> PROM
    REDIS --> WEB
    PROM --> GRAF
    WEB --> WS
```

---

## 🔄 **數據架構與流程**

### **數據存儲架構**

#### **多數據庫策略**
```yaml
主數據庫 (SQLite/PostgreSQL):
  用途: 業務數據持久化
  表結構:
    - emails: 郵件記錄 ✅ Pydantic V2 模型
    - senders: 發送者信息
    - attachments: 附件管理
    - email_process_status: 處理狀態
    - email_download_status: 下載追蹤 ✅ 新功能
    - pts_jobs: PTS 處理任務 ✅ 新增
    - pts_results: PTS 處理結果 ✅ 新增

緩存數據庫 (Redis):
  用途: 
    - 任務隊列 (Dramatiq)
    - 會話緩存
    - 實時數據緩存
    - 監控指標暫存

文件存儲:
  結構:
    - /app/temp/: 臨時文件處理
    - /app/data/: 持久化數據
    - /app/logs/: 日誌文件
    - /app/attachments/: 附件存儲
```

#### **數據流轉模式**
```mermaid
graph TD
    subgraph "數據輸入"
        EMAIL[郵件數據]
        FILE[文件上傳]
        API[API 調用]
    end
    
    subgraph "數據處理"
        VAL[數據驗證]
        PARSE[解析處理]
        ENRICH[數據增強]
    end
    
    subgraph "數據存儲"
        MAIN[(主數據庫)]
        CACHE[(Redis 緩存)]
        FILES[文件系統]
    end
    
    subgraph "數據輸出"
        REPORT[報表生成]
        NOTIFY[通知服務]
        EXPORT[數據導出]
    end
    
    EMAIL --> VAL
    FILE --> VAL
    API --> VAL
    VAL --> PARSE
    PARSE --> ENRICH
    ENRICH --> MAIN
    ENRICH --> CACHE
    ENRICH --> FILES
    MAIN --> REPORT
    CACHE --> NOTIFY
    FILES --> EXPORT
```

---

## 🌐 **集成架構**

### **內部服務集成**

#### **服務間通信模式**
```yaml
同步通信:
  - REST API 調用 (FastAPI/Flask)
  - 直接函數調用 (模組內)
  - 數據庫查詢

異步通信:
  - Dramatiq 任務隊列
  - Redis 發布/訂閱
  - WebSocket 實時推送
  - 文件監控事件

集成模式:
  - 共享數據庫
  - 事件驅動
  - API 網關
  - 消息隊列
```

### **外部系統集成**

#### **集成點映射**
```yaml
郵件系統:
  - Outlook API (outlook_adapter.py) ✅ 穩定運行
  - POP3/IMAP 協議 (pop3_email_reader.py) ✅ 修復完成
  - SMTP 發送服務

PTS 文件處理:
  - PTS Renamer API ✅ 完全修復
  - 文件上傳處理 ✅ 正常運行
  - 背景任務處理 ✅ Dramatiq 集成

AI 服務:
  - Ollama 本地 LLM (unified_llm_client.py)
  - Grok API 智能解析 (grok_client.py)
  - 自定義 ML 模型

通知服務:
  - Line Notification (line_notification_service.py)
  - SMTP 郵件通知
  - WebSocket 實時推送

文件系統:
  - 本地文件存儲
  - 臨時文件管理
  - 附件處理 ✅ 安全驗證
```

---

## 🚀 **部署架構**

### **容器化架構**

#### **服務拓撲**
```yaml
outlook-summary (主應用):
  image: 自構建鏡像
  resources:
    cpu: 8 cores
    memory: 4GB
    storage: 100GB
  ports:
    - "8000:8000"

redis (緩存和隊列):
  image: redis:7-alpine
  resources:
    cpu: 1 core
    memory: 512MB
  persistence: 持久化存儲

prometheus (監控):
  image: prom/prometheus:latest
  resources:
    cpu: 1 core
    memory: 1GB
  retention: 200h

grafana (可視化):
  image: grafana/grafana:latest
  resources:
    cpu: 1 core
    memory: 512MB

nginx (反向代理):
  image: nginx:alpine
  resources:
    cpu: 0.5 core
    memory: 256MB
```

#### **網路架構**
```mermaid
graph TB
    subgraph "外部網路"
        CLIENT[客戶端]
        LB[負載均衡器]
    end
    
    subgraph "DMZ 區域"
        NGINX[Nginx 代理]
    end
    
    subgraph "應用區域 (Docker Network)"
        APP[主應用 :8000]
        REDIS[Redis :6379]
        PROM[Prometheus :9090]
        GRAF[Grafana :3000]
    end
    
    subgraph "存儲區域"
        DB[(數據庫)]
        FILES[文件存儲]
        LOGS[日誌存儲]
    end
    
    CLIENT --> LB
    LB --> NGINX
    NGINX --> APP
    APP --> REDIS
    APP --> DB
    APP --> FILES
    PROM --> APP
    GRAF --> PROM
    APP --> LOGS
```

### **環境架構**

#### **多環境配置**
```yaml
開發環境 (Development):
  - 本地 SQLite 數據庫
  - Redis 單機模式
  - 簡化監控
  - 調試模式開啟

測試環境 (Testing):
  - PostgreSQL 數據庫
  - Redis 主從模式
  - 完整監控棧
  - 自動化測試集成

生產環境 (Production):
  - PostgreSQL 集群
  - Redis 集群模式
  - 企業級監控
  - 高可用部署
  - 自動備份機制
```

---

## 🔒 **安全架構**

### **安全層次**

#### **網路安全**
```yaml
網路隔離:
  - Docker 自定義網路 (172.20.0.0/16)
  - 服務間通信加密
  - 端口訪問控制

API 安全:
  - JWT 令牌認證
  - API 速率限制
  - 請求驗證 (Pydantic)
  - HTTPS 強制

數據安全:
  - 數據庫加密
  - 敏感信息脫敏
  - 審計日誌記錄
  - 備份加密
```

#### **應用安全**
```yaml
輸入驗證:
  - 文件類型白名單
  - 文件大小限制 (100MB)
  - MIME 類型檢查
  - 路徑遍歷防護

權限控制:
  - 基於角色的訪問控制
  - 最小權限原則
  - 會話管理
  - 審計追蹤

容器安全:
  - 非 root 用戶運行
  - 資源限制
  - 只讀文件系統
  - 安全掃描
```

---

## 📈 **性能架構**

### **性能優化策略**

#### **系統性能**
```yaml
CPU 優化:
  - 多核心並行處理
  - 異步任務隊列
  - 智能任務調度
  - 資源池管理

內存優化:
  - Redis 緩存策略
  - 連接池管理
  - 垃圾回收調優
  - 內存使用監控

I/O 優化:
  - 異步文件操作
  - 數據庫連接池
  - 批量處理操作
  - 管道化處理
```

#### **可擴展性設計**
```yaml
水平擴展:
  - 無狀態應用設計
  - 負載均衡支援
  - 數據庫分片準備
  - 微服務拆分能力

垂直擴展:
  - 動態資源調整
  - 性能監控反饋
  - 自動化性能調優
  - 容量規劃
```

---

## 🔄 **運維架構**

### **監控和告警**

#### **監控層次**
```yaml
基礎設施監控:
  - CPU, 內存, 磁碟, 網路
  - 容器狀態和資源使用
  - 服務健康檢查

應用監控:
  - API 響應時間和錯誤率
  - 任務隊列狀態
  - 數據庫性能指標

業務監控:
  - 郵件處理成功率
  - 廠商識別準確率
  - 用戶活動指標
```

### **災難恢復**

#### **備份策略**
```yaml
數據備份:
  - 數據庫自動備份 (每日)
  - 增量備份 (每小時)
  - 跨地域備份存儲

配置備份:
  - 容器鏡像版本控制
  - 配置文件版本管理
  - 部署腳本備份

恢復測試:
  - 定期恢復演練
  - RTO/RPO 指標監控
  - 故障切換自動化
```

---

## 🔮 **架構演進規劃**

### **短期演進 (6個月)**
- Vue.js 前端遷移完成
- 郵件下載追蹤系統集成
- AI 解析能力增強
- 監控系統完善

### **中期演進 (12個月)**
- 微服務進一步拆分
- Kubernetes 原生部署
- API 網關引入
- 邊緣計算支援

### **長期演進 (24個月)**
- 雲原生架構轉型
- 服務網格集成
- 多租戶支援
- 國際化部署

---

## 📚 **參考資料**

### **相關文檔**
- [技術棧文檔](./tech-stack.md)
- [編碼標準](./coding-standards.md)
- [專案結構](./project-structure.md)
- [產品路線圖](../prd/overall-product-roadmap.md)

### **外部參考**
- [六角架構模式](https://alistair.cockburn.us/hexagonal-architecture/)
- [領域驅動設計](https://domainlanguage.com/ddd/)
- [容器化最佳實踐](https://docs.docker.com/develop/dev-best-practices/)
- [Dramatiq 任務隊列](https://dramatiq.io/)

---

**📅 最後更新**: 2025-08-19  
**👤 維護者**: Architecture Team  
**🔄 審核週期**: 每季度  
**📧 聯絡**: <EMAIL>