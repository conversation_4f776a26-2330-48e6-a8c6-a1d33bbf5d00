# 3.EQC一鍵完成流程架構

*最後更新: 2025-06-14 - 兩階段EQC處理流程架構完整實施與重命名功能修復*

## [BOARD] 概覽

本文檔詳細記錄「一鍵完成到程式碼對比」兩階段處理流程的完整架構、實際使用的模組、CODE 區間設定機制，重命名功能修復記錄，以及問題排查指南。

## [FIRE] 重大架構更新：兩階段處理流程

### [TARGET] 架構變更摘要
- **舊版本**：單階段處理，檔案選擇錯誤（選取小檔案）
- **新版本**：兩階段處理，智能整合所有可用檔案
- **核心改善**：動態適應檔案數量，確保完整資料處理
- **功能保持**：所有原有功能完全保留，符合CLAUDE.md功能替換原則
- **智能處理**：根據實際檔案結構自動調整整合策略

## [REFRESH] 兩階段處理流程圖

```mermaid
graph TD
    A[前端 UI 表單] --> B[API: /api/process_eqc_advanced]
    B --> C[兩階段流程控制器]
    
    C --> D1[[FIRE] 第一階段: EQCBin1FinalProcessor]
    D1 --> D1A[[REFRESH] 步驟0: SPD檔案自動轉換為CSV]
    D1A --> D1B[[LINK] 步驟1: FT-EQC智能配對處理]  [STAR]
    D1B --> D2[自動發現並整合所有CSV檔案]
    D2 --> D3[生成完整EQCTOTALDATA.csv]
    D3 --> D4[[LINK] 步驟7: 添加FT-EQC配對失敗資料] [STAR]
    
    D3 --> E1[[ROCKET] 第二階段: StandardEQCProcessor.process_from_stage2_only]
    E1 --> E2[步驟2: 程式碼區間檢測]
    E2 --> E3[步驟3: 雙重搜尋機制]
    E3 --> E4[步驟4: InsEqcRtData2 Step 1-2-3-4]
    E4 --> E5[步驟5: 測試流程生成]
    E5 --> E6[步驟6: Excel 生成與標記]
    E6 --> E7[[NOTES] 檔案重命名: EQCTOTALDATA.xlsx]
    E7 --> E8[步驟7: 最終 CSV 到 Excel 轉換]
    E8 --> E9[步驟8: 完整報告生成]

    A1[CODE 區間設定] --> E2
    A1 --> E3
    A1 --> E4
    
    style D1 fill:#ff9999
    style E1 fill:#99ccff
    style E7 fill:#99ff99
```

## [TARGET] 兩階段核心模組清單（實際使用）

### [FIRE] 第一階段模組
- **`src/infrastructure/adapters/excel/eqc/eqc_bin1_final_processor.py`**
  - 類別：`EQCBin1FinalProcessor`
  - 功能：自動發現並整合目錄下所有CSV檔案，生成完整EQCTOTALDATA.csv
  - 關鍵方法：`process_complete_eqc_integration()`
  - 動態處理：根據實際檔案數量和內容自動調整整合邏輯
  - **新功能**：`convert_spd_files_to_csv()` - SPD檔案自動轉換為CSV格式（不區分大小寫）

### [ROCKET] 第二階段主要控制器
- **`eqc_standard_processor.py`** - 完整流程控制器
  - 作用：統合所有步驟的執行順序和參數傳遞
  - 關鍵方法：`process_from_stage2_only()` (新增)
  - 原有方法：`process_standard_eqc_pipeline()` (保留)

### API 接口層（已更新為兩階段）
- **`src/presentation/api/ft_eqc_api.py`**
  - 端點：`/api/process_eqc_advanced`
  - 功能：兩階段流程協調、CODE 區間參數提取、路徑轉換
  - **更新內容**：實施兩階段處理邏輯，先執行第一階段再執行第二階段

### 核心處理模組（按執行順序）

#### 步驟1-2：程式碼區間檢測
- **`src/infrastructure/adapters/excel/eqc/eqc_simple_detector.py`**
  - 類別：`EQCSimpleDetector`
  - 功能：檢測主要區間（預設298-335）和備用區間（預設1565-1600）
  - **支援前端 CODE 區間設定** [OK]

#### 步驟3：雙重搜尋機制
- **`src/infrastructure/adapters/excel/eqc/eqc_dual_search_corrected.py`**
  - 類別：`EQCDualSearchCorrected`
  - 功能：主要區間100%匹配 + 備用區間映射匹配
  - **支援前端 CODE 區間設定** [OK]

#### 步驟4：InsEqcRtData2 資料重組
- **`src/infrastructure/adapters/excel/eqc/eqc_inseqcrtdata2_processor.py`**
  - 類別：`EQCInsEqcRtData2Processor`
  - 功能：Step 1-2（ALL0移動）、Step 3（FAIL檢測）、Step 4（匹配搜尋）
  - **支援前端 CODE 區間設定** [OK]

#### 步驟5：測試流程生成
- **`src/infrastructure/adapters/excel/eqc/eqc_step5_testflow_processor.py`**
  - 類別：`EQCStep5TestFlowProcessor`
  - 功能：解析 Step 4 DEBUG LOG，生成線性測試流程

#### 步驟6：Excel 生成與標記（已修復重命名功能）
- **`src/infrastructure/adapters/excel/csv_to_excel_converter.py`** (使用增強版)
  - 類別：`CsvToExcelConverter`
  - 功能：生成 Excel 檔案，標記 Online EQC RT 行為黃色
  - **重命名功能**：`EQCTOTALDATA_Step6_HighlightedEQCRT_*.xlsx` → `EQCTOTALDATA.xlsx`
  - **修復記錄**：恢復兩階段流程中遺失的重命名邏輯（2025-06-14修復）
- **輔助模組**：`src/infrastructure/adapters/excel/eqc/eqc_step6_excel_processor.py`
  - 狀態：保留作為參考實作

#### 步驟7：最終轉換
- **`src/infrastructure/adapters/excel/csv_to_excel_converter.py`**
  - 類別：`CsvToExcelConverter`
  - 功能：完整的 CSV 到 Excel 轉換，包含 Summary 工作表

### 輔助處理器
- **`src/infrastructure/adapters/excel/eqc/eqc_bin1_final_processor.py`**
  - 用途：EQC BIN1 最終處理（僅用於 `/api/process_online_eqc` 端點）

## [ERROR] 已清理的未使用檔案

### 已刪[EXCEPT_CHAR]的檔案 [OK]
- **`src/infrastructure/adapters/excel/eqc/eqc_code_difference_detector.py`** 
  - 狀態：已刪[EXCEPT_CHAR]（完全未被使用，功能已被其他模組取代）
- **`debug.txt`**
  - 狀態：已刪[EXCEPT_CHAR]（臨時分析檔案，InsEqcRtData2 分析已完成）

### 保留的重要檔案 [OK]
- **`eqc_standard_processor.py`** (根目錄)
  - 狀態：**核心依賴**，被 `ft_eqc_api.py` 直接導入
  - 作用：API 核心控制器，整合所有 EQC 子模組
- **`eqc_dual_search_corrected.py`**
  - 狀態：**搜尋功能核心**，雙重搜尋機制的主要實作
  - 作用：主要區間100%匹配 + 備用區間映射匹配

### 範例檔案（保留）
- **`src/infrastructure/adapters/excel/eqc/examples/eqc_dual_search_example.py`**
  - 狀態：範例和文檔用途
  - 建議：保留作為參考

### 其他專案模組（非 EQC 流程）
- **`src/infrastructure/adapters/excel/ft_eqc_grouping_processor.py`**
  - 用途：其他 API 端點的檔案分組功能

## [TOOL] CODE 區間設定機制

### 參數傳遞路徑

```
前端 UI 表單 (main_start: 340, main_end: 350)
    ↓
ft_eqc_api.process_eqc_advanced() 
    ↓ (code_regions dict)
StandardEQCProcessor.process_standard_eqc_pipeline()
    ↓ (code_regions)
EQCSimpleDetector.find_code_region()
    ↓ (覆蓋自動檢測結果)
EQCDualSearchCorrected.perform_corrected_dual_search()
    ↓ (使用前端設定的區間)
EQCInsEqcRtData2Processor.perform_complete_inseqcrtdata2_workflow()
    ↓ (實際執行使用前端區間)
日誌輸出：「主要 CODE 區間: 第340-350欄」
```

### 優先級順序
1. **最高優先權**：前端 UI 表單設定
2. **次要優先權**：自動檢測結果
3. **預設值**：第298-335欄（主要）、第1565-1600欄（備用）

### 參數格式
- **前端傳送**：`main_start`, `main_end`, `backup_start`, `backup_end`
- **內部處理**：自動轉換為0-based索引
- **日誌顯示**：用戶友好的1-based欄位編號

## [BUILD] 問題排查指南

### CODE 區間設定未生效

#### 檢查清單
1. **前端參數傳遞**
   ```javascript
   // 檢查 Console 日誌
   console.log('CODE區間設定:', codeRegionSettings);
   ```

2. **後端參數接收**
   ```python
   # 查看 API 日誌
   logger.info(f"[TARGET] 收到前端 CODE 區間設定: {code_regions}")
   ```

3. **區間檢測器使用**
   ```python
   # 查看 EQCSimpleDetector 日誌
   logger.info(f"[TARGET] 使用前端 CODE 區間設定: start={start}, end={end}")
   ```

4. **最終執行驗證**
   ```python
   # 查看 InsEqcRtData2 日誌
   logger.info(f"主要 CODE 區間: 第{start+1}-{end+1}欄")
   ```

#### 常見問題和解決方案

**問題1：前端設定340-350，但日誌仍顯示298-335**
- **原因**：參數傳遞鏈中斷
- **解決**：確認 `EQCSimpleDetector.find_code_region()` 接收到 `code_regions` 參數

**問題2：參數為 null 或 undefined**
- **原因**：前端欄位為空
- **解決**：填入完整的區間數值，或留空使用自動檢測

**問題3：區間驗證失敗**
- **原因**：設定的區間不符合條件要求
- **解決**：檢查區間是否在有效範圍內

## [CHART] 統計資料

- **EQC 相關檔案總數**：7個（已清理後）
- **實際使用檔案**：7個
- **使用率**：100% [OK]
- **已刪[EXCEPT_CHAR]未使用檔案**：2個
- **CODE 區間設定支援**：完全支援 [OK]

## [REFRESH] 依賴關係

```python
# 主要 import 關係
eqc_standard_processor.py
├── eqc_simple_detector.py (區間檢測)
├── eqc_dual_search_corrected.py (雙重搜尋)
├── eqc_inseqcrtdata2_processor.py (資料重組)
├── eqc_step5_testflow_processor.py (測試流程)
├── eqc_step6_excel_processor.py (Excel 標記)
└── csv_to_excel_converter.py (最終轉換)
```

## [TARGET] 關鍵成就

### [FIRE] 兩階段架構成就
- [OK] **兩階段流程**：第一階段檔案整合 + 第二階段完整處理
- [OK] **智能檔案整合**：自動發現並整合目錄下所有CSV檔案
- [OK] **動態適應性**：根據實際檔案數量和結構自動調整
- [OK] **重命名功能修復**：EQCTOTALDATA_Step6_HighlightedEQCRT_*.xlsx → EQCTOTALDATA.xlsx

### [ROCKET] 技術架構成就
- [OK] **完整流程整合**：8個步驟無縫銜接（增加檔案重命名步驟）
- [OK] **CODE 區間設定**：前端到後端完整支援
- [OK] **模組化設計**：單一責任，職責分明
- [OK] **錯誤處理**：驗證失敗時自動退回機制
- [OK] **日誌追蹤**：每個步驟都有詳細日誌
- [OK] **程式碼清理**：移[EXCEPT_CHAR]未使用檔案，使用率達100%

## [TOOL] 重大更新記錄

### [CALENDAR] 2025-06-14 SPD檔案自動轉換功能新增
**更新時間**：2025-06-14 22:40
**主要變更**：
- [OK] **SPD檔案轉換**：新增 `convert_spd_files_to_csv()` 方法
- [OK] **大小寫不敏感**：自動偵測 .spd、.SPD、.Spd 等各種大小寫組合
- [OK] **統一格式化**：轉換為標準 .csv 小寫副檔名
- [OK] **前置步驟整合**：在檔案整合前自動執行檔案標準化
- [OK] **錯誤處理**：完整的異常處理和日誌輸出

**流程優化**：
- 步驟0：SPD檔案自動轉換為CSV（新增）
- 步驟1：檔案發現和整合（原有）
- 符合CLAUDE.md功能替換原則，無向下相容負擔

### [CALENDAR] 2025-06-14 兩階段流程實施
**更新時間**：2025-06-14 21:30-22:30
**主要變更**：
- [OK] **架構重構**：從單階段改為兩階段處理流程
- [OK] **第一階段實施**：整合 `EQCBin1FinalProcessor` 進行檔案整合
- [OK] **第二階段優化**：新增 `process_from_stage2_only()` 方法
- [OK] **API端點更新**：修改 `/api/process_eqc_advanced` 實施兩階段邏輯
- [OK] **重命名功能修復**：恢復 Step6 檔案自動重命名為 EQCTOTALDATA.xlsx
- [OK] **環境變數控制**：統一使用 `EQC_DETAILED_LOGS` 控制報告生成

**效能提升**：
- 檔案處理能力：自動適應各種檔案數量和大小
- 處理準確度：完整檔案整合，避免選取錯誤檔案
- 功能完整性：所有原有功能保持不變

### [CALENDAR] 2025-06-14 清理記錄
**清理時間**：2025-06-14 10:30
**清理檔案**：
- `src/infrastructure/adapters/excel/eqc/eqc_code_difference_detector.py` (已刪[EXCEPT_CHAR])
- `debug.txt` (已刪[EXCEPT_CHAR])

**保留重要檔案**：
- `eqc_standard_processor.py` - API 核心依賴，不可刪[EXCEPT_CHAR]
- `eqc_dual_search_corrected.py` - 搜尋功能核心，正常使用中

**清理效果**：檔案使用率從87.5%提升至100%，確保「一鍵完成到程式碼對比」功能完全不受影響。

---

**[PARTY] 本文檔完整記錄了 EQC 一鍵完成流程的技術架構，是系統維護和問題排查的重要參考！**