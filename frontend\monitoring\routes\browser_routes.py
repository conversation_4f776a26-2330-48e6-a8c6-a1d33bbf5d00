"""網路瀏覽器 API - 瀏覽相關路由
處理網路共享資料夾的瀏覽、下載和連接功能
"""

import os
import subprocess
from pathlib import Path
from fastapi import APIRouter, Query, HTTPException
from fastapi.responses import FileResponse, JSONResponse

from loguru import logger

from frontend.shared.models.network_models import (
    NetworkFileListResponse, NetworkConnectRequest, NetworkConnectResponse,
    NetworkPathValidateRequest, NetworkPathValidateResponse, NetworkCredentials
)
from frontend.shared.utils.network_utils import (
    list_smb_files, test_smb_connection, convert_unc_to_linux_path,
    validate_network_path, get_file_info
)

# 建立路由器
router = APIRouter(prefix="/api", tags=["Network Browser"])

# 全域變數儲存活動連接
active_connections = {}


def try_mount_network_share(server: str, share: str, mount_point: str, credentials: NetworkCredentials = None) -> bool:
    """嘗試掛載網路共享到指定掛載點，支援認證"""
    try:
        os.makedirs(mount_point, exist_ok=True)
        
        if credentials:
            success, message = test_smb_connection(server, share, credentials)
            if not success:
                logger.warning(f"[ERROR] SMB連接測試失敗，跳過掛載: {message}")
                return False
            
            creds_file = f"/tmp/.smbcreds_{os.getpid()}"
            try:
                with open(creds_file, 'w') as f:
                    f.write(f"username={credentials.username}\n")
                    f.write(f"password={credentials.password}\n")
                    if credentials.domain:
                        f.write(f"domain={credentials.domain}\n")
                os.chmod(creds_file, 0o600)
                
                mount_cmd1 = [
                    "mount", "-t", "cifs", f"//{server}/{share}", mount_point,
                    "-o", f"credentials={creds_file},uid=1000,gid=1000,iocharset=utf8"
                ]
                result = subprocess.run(mount_cmd1, capture_output=True, text=True, timeout=15)
                
                if result.returncode != 0:
                    mount_cmd2 = [
                        "sudo", "-n", "mount", "-t", "cifs", f"//{server}/{share}", mount_point,
                        "-o", f"credentials={creds_file},uid=1000,gid=1000,iocharset=utf8"
                    ]
                    result = subprocess.run(mount_cmd2, capture_output=True, text=True, timeout=15)
                
                if os.path.exists(creds_file):
                    os.remove(creds_file)
                
                if result.returncode == 0:
                    logger.info(f"[OK] 成功掛載網路共享(認證): //{server}/{share} -> {mount_point}")
                    return True
                else:
                    logger.warning(f"[ERROR] 認證掛載失敗: {result.stderr}")
                    return False
            finally:
                if os.path.exists(creds_file):
                    os.remove(creds_file)
        else:
            mount_cmd = [
                "sudo", "mount", "-t", "cifs", f"//{server}/{share}", mount_point,
                "-o", "guest,uid=1000,gid=1000,iocharset=utf8"
            ]
            result = subprocess.run(mount_cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                logger.info(f"[OK] 成功掛載網路共享(Guest): //{server}/{share} -> {mount_point}")
                return True
            else:
                logger.debug(f"Guest掛載失敗: {result.stderr}")
                return False
            
    except subprocess.TimeoutExpired:
        logger.warning(f"[ALARM_CLOCK] 掛載超時: //{server}/{share}")
        return False
    except Exception as e:
        logger.debug(f"掛載失敗: {str(e)}")
        return False


@router.get("/list", response_model=NetworkFileListResponse)
async def list_network_files(path: str = Query(..., description="網路資料夾路徑")):
    """列出網路共享資料夾中的檔案"""
    try:
        logger.info(f"[FILE_FOLDER] 取得網路檔案列表請求: {path}")
        
        if path.startswith("\\\\"):
            path_parts = path[2:].split("\\")
            if len(path_parts) >= 2:
                server = path_parts[0]
                share = path_parts[1]
                subpath = "/".join(path_parts[2:]) if len(path_parts) > 2 else ""
            else:
                return JSONResponse(status_code=400, content={"error": "無效的UNC路徑格式"})
        else:
            return JSONResponse(status_code=400, content={"error": "請使用UNC路徑格式"})
        
        # 嘗試列出檔案
        files = list_smb_files(server, share, subpath)
        
        if files is None:
            return JSONResponse(status_code=404, content={"error": "無法存取指定路徑"})
        
        return NetworkFileListResponse(
            path=path,
            files=files,
            total_count=len(files)
        )
        
    except Exception as e:
        logger.error(f"[ERROR] 列出網路檔案失敗: {str(e)}")
        return JSONResponse(status_code=500, content={"error": f"列出檔案時發生錯誤: {str(e)}"})


@router.get("/download")
async def download_network_file(path: str = Query(...), filename: str = Query(...)):
    """下載網路共享檔案"""
    try:
        linux_base_path = convert_unc_to_linux_path(path)
        linux_file_path = os.path.join(linux_base_path, filename)
        
        if not os.path.exists(linux_file_path):
            raise HTTPException(status_code=404, detail="檔案不存在")
        
        return FileResponse(linux_file_path, filename=filename)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"檔案下載失敗: {str(e)}")


@router.get("/info")
async def get_network_file_info(path: str = Query(...), filename: str = Query(...)):
    """取得網路共享檔案的詳細資訊"""
    try:
        is_valid, linux_base_path = validate_network_path(path)
        if not is_valid:
            raise HTTPException(status_code=400, detail="無效的網路路徑")
        
        linux_file_path = os.path.join(linux_base_path, filename)
        
        if not os.path.exists(linux_file_path):
            raise HTTPException(status_code=404, detail="檔案不存在")
        
        file_info = get_file_info(linux_file_path)
        
        return {
            "path": path,
            "filename": filename,
            "info": file_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取得檔案資訊失敗: {str(e)}")


@router.get("/credentials")
async def get_network_credentials():
    """獲取網路連接認證資訊"""
    try:
        # 從環境變數讀取認證資訊
        username = os.getenv('EMAIL_ADDRESS', 'telowyield1')
        password = os.getenv('EMAIL_PASSWORD', 'Telo@2024')
        domain = os.getenv('DOMAIN', '')
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "credentials": {
                    "username": username,
                    "password": password,
                    "domain": domain
                }
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"獲取認證資訊失敗: {str(e)}"}
        )


@router.get("/current-user")
async def get_current_user():
    """獲取當前 Windows 用戶資訊"""
    try:
        import platform
        import getpass

        # 獲取系統資訊
        system_info = {
            "system": platform.system(),
            "node": platform.node(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor()
        }

        # 獲取當前用戶
        current_user = getpass.getuser()

        # 嘗試獲取更多用戶資訊（Windows 特定）
        user_info = {"username": current_user}

        if platform.system() == "Windows":
            try:
                import subprocess
                # 獲取用戶的完整名稱
                result = subprocess.run(['whoami', '/upn'], capture_output=True, text=True)
                if result.returncode == 0:
                    user_info["upn"] = result.stdout.strip()
            except:
                pass

        return JSONResponse(
            status_code=200,
            content={
                "status": "success",
                "user": user_info,
                "system": system_info
            }
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"獲取當前用戶資訊失敗: {str(e)}"}
        )
