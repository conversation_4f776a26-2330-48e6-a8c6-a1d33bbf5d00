# Frontend 部署與故障排除指南

**文檔版本**: 1.0  
**最後更新**: 2025-08-19  
**適用範圍**: 前端模組部署、監控與故障排除  
**維護者**: Frontend Team & Documentation Maintainer  

---

## 🎯 **文檔概述**

本指南專為半導體郵件處理系統的前端部署和故障排除提供完整的技術支援，特別針對 PTS Renamer 前端修復後的部署流程和常見問題解決方案。

### **適用場景**
- 新環境前端部署
- 前端模組故障排除
- 性能問題診斷
- 跨瀏覽器兼容性問題
- PTS Renamer 特定問題

---

## 🚀 **前端部署流程**

### **標準部署流程**

#### **1. 環境準備**
```bash
# 1. 檢查系統要求
python --version  # >= 3.9
node --version    # >= 16.0 (如有需要)

# 2. 克隆專案
git clone <repository_url>
cd outlook_summary

# 3. 設置虛擬環境
python -m venv venv_win_3_11_12
venv_win_3_11_12\Scripts\activate  # Windows
source venv/bin/activate            # Linux/Mac

# 4. 安裝依賴
pip install -r requirements.txt
```

#### **2. 前端模組配置**
```bash
# 驗證前端結構
ls -la frontend/
# 應顯示：
# email/
# analytics/
# file_management/
# eqc/
# tasks/
# monitoring/
# pts_renamer/  ✅ PTS Renamer 模組
# shared/       ✅ 共享資源

# 檢查共享模板
ls -la frontend/shared/templates/
# 應顯示：
# base.html           ✅ 基礎模板
# components/         ✅ 組件目錄
```

#### **3. 前端模組驗證**
```python
# 執行前端模組檢查腳本
python << 'EOF'
import os
import sys

def check_frontend_modules():
    frontend_path = 'frontend'
    required_modules = [
        'email', 'analytics', 'file_management', 
        'eqc', 'tasks', 'monitoring', 'pts_renamer'
    ]
    
    print("🔍 檢查前端模組結構...")
    for module in required_modules:
        module_path = os.path.join(frontend_path, module)
        if os.path.exists(module_path):
            print(f"✅ {module} 模組存在")
            
            # 檢查路由文件
            routes_path = os.path.join(module_path, 'routes')
            if os.path.exists(routes_path):
                print(f"  ✅ {module} 路由目錄存在")
            else:
                print(f"  ❌ {module} 路由目錄缺失")
        else:
            print(f"❌ {module} 模組缺失")
    
    # 檢查共享資源
    shared_path = os.path.join(frontend_path, 'shared')
    if os.path.exists(shared_path):
        print("✅ shared 共享模組存在")
        
        base_template = os.path.join(shared_path, 'templates', 'base.html')
        if os.path.exists(base_template):
            print("  ✅ base.html 基礎模板存在")
        else:
            print("  ❌ base.html 基礎模板缺失")
    else:
        print("❌ shared 共享模組缺失")

if __name__ == "__main__":
    check_frontend_modules()
EOF
```

#### **4. 服務啟動**
```bash
# 方法 1: 統一啟動 (推薦)
python start_integrated_services.py

# 方法 2: 僅前端開發模式
cd frontend
python app.py --debug

# 方法 3: 使用 UTF-8 編碼啟動 (解決編碼問題)
chcp 65001
set PYTHONIOENCODING=utf-8
python start_integrated_services.py
```

---

## 🔧 **PTS Renamer 專屬部署**

### **PTS Renamer 前端修復後部署**

#### **修復內容確認**
```bash
# 1. 檢查基礎模板修復
cat frontend/shared/templates/base.html | grep -A 5 -B 5 "components/"

# 應顯示正確的組件 include 路徑：
# {% include 'components/navbar.html' %}
# {% include 'components/sidebar.html' %}
# {% include 'components/modal.html' %}
# {% include 'components/notification.html' %}

# 2. 檢查主要模組路由
python -c "
from frontend.email.routes.email_routes import email_bp
print(f'Email 藍圖已註冊: {email_bp.name}')
print(f'可用路由: {[rule.rule for rule in email_bp.url_map.iter_rules()]}')"

# 3. 檢查導航連結
curl -s http://localhost:8000 | grep -i "email\|analytics"
```

#### **功能驗證測試**
```python
# PTS Renamer 功能測試腳本
python << 'EOF'
import requests
import time
from urllib.parse import urljoin

BASE_URL = "http://localhost:8000"

def test_pts_renamer_frontend():
    print("🧪 測試 PTS Renamer 前端功能...")
    
    try:
        # 1. 測試主頁面訪問
        print("1. 測試主頁面訪問...")
        response = requests.get(BASE_URL, timeout=10)
        if response.status_code == 200:
            if "pts" in response.text.lower() or "renamer" in response.text.lower():
                print("  ✅ 主頁面包含 PTS Renamer 相關內容")
            else:
                print("  ⚠️  主頁面未找到 PTS Renamer 相關內容")
        else:
            print(f"  ❌ 主頁面訪問失敗: {response.status_code}")
        
        # 2. 測試 PTS Renamer 頁面直接訪問
        print("2. 測試 PTS Renamer 頁面直接訪問...")
        pts_url = urljoin(BASE_URL, "/pts_renamer/")
        response = requests.get(pts_url, timeout=10)
        
        if response.status_code == 200:
            if "html" in response.headers.get('content-type', '').lower():
                print("  ✅ PTS Renamer 頁面返回 HTML 內容")
                if "json" not in response.text[:100].lower():
                    print("  ✅ 頁面內容正常，非 JSON 錯誤")
                else:
                    print("  ❌ 頁面仍顯示 JSON 錯誤")
            else:
                print(f"  ❌ PTS Renamer 頁面返回異常內容類型: {response.headers.get('content-type')}")
        else:
            print(f"  ❌ PTS Renamer 頁面訪問失敗: {response.status_code}")
        
        # 3. 測試導航連結
        print("3. 測試導航連結...")
        if "pts" in response.text.lower() and "renamer" in response.text.lower():
            print("  ✅ 導航包含 PTS Renamer 連結")
        else:
            print("  ⚠️  導航可能缺少 PTS Renamer 連結")
            
    except requests.exceptions.ConnectionError:
        print("❌ 無法連接到服務器，請確保服務已啟動")
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    test_pts_renamer_frontend()
EOF
```

---

## 🚨 **常見問題與故障排除**

### **1. PTS Renamer 頁面顯示 JSON 錯誤**

#### **問題症狀**
- 訪問 PTS Renamer 頁面時顯示 JSON 格式錯誤而非 HTML 頁面
- 瀏覽器顯示類似 `{"error": "template not found"}` 的錯誤

#### **解決方案**
```bash
# 1. 檢查基礎模板路徑
echo "檢查基礎模板中的組件引用路徑..."
grep -n "include.*components" frontend/shared/templates/base.html

# 正確的路徑應該是：
# {% include 'components/navbar.html' %}
# 錯誤的路徑可能是：
# {% include 'shared/components/navbar.html' %}

# 2. 修復組件路徑 (如果需要)
sed -i "s|include 'shared/components/|include 'components/|g" frontend/shared/templates/base.html

# 3. 檢查組件文件是否存在
ls -la frontend/shared/templates/components/
# 應該包含：navbar.html, sidebar.html, modal.html, notification.html

# 4. 重新啟動服務
pkill -f python  # 停止現有服務
python start_integrated_services.py  # 重新啟動
```

### **2. 共享組件載入失敗**

#### **問題症狀**
- 頁面部分顯示不正常
- 導航欄或側邊欄缺失
- 控制台出現組件載入錯誤

#### **解決方案**
```bash
# 1. 檢查組件文件完整性
for component in navbar sidebar modal notification confirm-dialog; do
    file="frontend/shared/templates/components/${component}.html"
    if [ -f "$file" ]; then
        echo "✅ $component.html 存在"
    else
        echo "❌ $component.html 缺失"
    fi
done

# 2. 檢查模板語法
python << 'EOF'
import os
import re

def check_template_syntax():
    components_dir = "frontend/shared/templates/components"
    if not os.path.exists(components_dir):
        print(f"❌ 組件目錄不存在: {components_dir}")
        return
    
    for filename in os.listdir(components_dir):
        if filename.endswith('.html'):
            filepath = os.path.join(components_dir, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 檢查基本的 Jinja2 語法
                if '{{' in content or '{%' in content:
                    print(f"✅ {filename} 包含 Jinja2 語法")
                else:
                    print(f"⚠️  {filename} 可能是純 HTML (無動態內容)")

if __name__ == "__main__":
    check_template_syntax()
EOF

# 3. 檢查 Flask 藍圖註冊
python -c "
from frontend.app import create_app
app = create_app()
print('已註冊的藍圖:')
for blueprint_name, blueprint in app.blueprints.items():
    print(f'  - {blueprint_name}: {blueprint.url_prefix}')
"
```

### **3. 靜態資源載入問題**

#### **問題症狀**
- CSS 樣式不生效
- JavaScript 功能異常
- 圖片或圖標無法顯示

#### **解決方案**
```bash
# 1. 檢查靜態資源目錄
ls -la frontend/shared/static/
# 應包含：css/, js/, images/

# 2. 驗證靜態資源路由
curl -I http://localhost:8000/static/css/global.css
# 應返回 HTTP 200

# 3. 檢查瀏覽器控制台
# 在瀏覽器中打開開發者工具，查看是否有 404 錯誤

# 4. 清除瀏覽器快取
echo "請清除瀏覽器快取並重新載入頁面"

# 5. 檢查靜態文件權限 (Linux/Mac)
find frontend/shared/static/ -type f -exec chmod 644 {} \;
find frontend/shared/static/ -type d -exec chmod 755 {} \;
```

### **4. 跨瀏覽器兼容性問題**

#### **問題症狀**
- 在某些瀏覽器中顯示異常
- JavaScript 功能在特定瀏覽器中失效
- CSS 樣式在不同瀏覽器中表現不一致

#### **解決方案**
```bash
# 1. 使用 Playwright 進行跨瀏覽器測試
python << 'EOF'
from playwright.sync_api import sync_playwright
import time

def test_cross_browser_compatibility():
    with sync_playwright() as p:
        browsers = {
            'chromium': p.chromium,
            'firefox': p.firefox,
            'webkit': p.webkit
        }
        
        for browser_name, browser_type in browsers.items():
            print(f"🧪 測試 {browser_name}...")
            try:
                browser = browser_type.launch(headless=True)
                page = browser.new_page()
                
                # 訪問 PTS Renamer 頁面
                response = page.goto("http://localhost:8000/pts_renamer/", timeout=10000)
                
                if response.status == 200:
                    print(f"  ✅ {browser_name}: 頁面載入成功")
                    
                    # 檢查頁面內容
                    if page.locator("html").count() > 0:
                        print(f"  ✅ {browser_name}: HTML 內容正常")
                    else:
                        print(f"  ❌ {browser_name}: HTML 內容異常")
                        
                else:
                    print(f"  ❌ {browser_name}: 頁面載入失敗 ({response.status})")
                    
                browser.close()
                
            except Exception as e:
                print(f"  ❌ {browser_name}: 測試失敗 - {str(e)}")

if __name__ == "__main__":
    test_cross_browser_compatibility()
EOF

# 2. 檢查 JavaScript 兼容性
echo "檢查 JavaScript 是否使用了不兼容的特性..."
grep -r "arrow function\|const\|let\|async\|await" frontend/shared/static/js/ || echo "未發現現代 JavaScript 特性"

# 3. 檢查 CSS 兼容性
echo "檢查 CSS 是否使用了不兼容的屬性..."
grep -r "grid\|flexbox\|transform\|transition" frontend/shared/static/css/ || echo "未發現現代 CSS 特性"
```

### **5. 性能問題**

#### **問題症狀**
- 頁面載入緩慢
- JavaScript 執行卡頓
- 大文件處理超時

#### **解決方案**
```bash
# 1. 頁面載入性能測試
python << 'EOF'
import requests
import time

def test_page_performance():
    url = "http://localhost:8000/pts_renamer/"
    
    print("⏱️  測試頁面載入性能...")
    
    # 進行多次測試
    times = []
    for i in range(5):
        start_time = time.time()
        response = requests.get(url)
        end_time = time.time()
        
        load_time = end_time - start_time
        times.append(load_time)
        
        print(f"  第 {i+1} 次: {load_time:.2f}秒")
    
    avg_time = sum(times) / len(times)
    print(f"\n📊 平均載入時間: {avg_time:.2f}秒")
    
    if avg_time < 2.0:
        print("✅ 性能良好 (< 2秒)")
    elif avg_time < 5.0:
        print("⚠️  性能一般 (2-5秒)")
    else:
        print("❌ 性能較差 (> 5秒)")

if __name__ == "__main__":
    test_page_performance()
EOF

# 2. 檢查系統資源使用
echo "📊 系統資源使用情況:"
python << 'EOF'
import psutil
import os

# CPU 使用率
cpu_percent = psutil.cpu_percent(interval=1)
print(f"CPU 使用率: {cpu_percent}%")

# 內存使用
memory = psutil.virtual_memory()
print(f"內存使用: {memory.percent}% ({memory.used // (1024**3)}GB / {memory.total // (1024**3)}GB)")

# 磁碟使用
disk = psutil.disk_usage('.')
print(f"磁碟使用: {disk.percent}% ({disk.used // (1024**3)}GB / {disk.total // (1024**3)}GB)")

# 檢查 Python 進程
python_processes = []
for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
    try:
        if 'python' in proc.info['name'].lower():
            python_processes.append(proc.info)
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        pass

print(f"\n🐍 Python 進程數量: {len(python_processes)}")
for proc in python_processes:
    print(f"  PID {proc['pid']}: 內存 {proc['memory_info'].rss // (1024**2)}MB")
EOF

# 3. 優化建議
echo "
🚀 性能優化建議:

1. 靜態資源優化:
   - 壓縮 CSS 和 JavaScript 文件
   - 優化圖片大小和格式
   - 啟用瀏覽器快取

2. 服務器優化:
   - 使用 Redis 快取頻繁查詢
   - 實施連接池管理
   - 啟用 gzip 壓縮

3. 數據庫優化:
   - 添加適當的索引
   - 優化查詢語句
   - 實施分頁載入

4. 前端優化:
   - 實施懶加載
   - 減少 DOM 操作
   - 使用 CDN 加速
"
```

---

## 📊 **監控與維護**

### **健康檢查腳本**

```bash
# 創建前端健康檢查腳本
cat << 'EOF' > frontend_health_check.py
#!/usr/bin/env python3
"""
前端健康檢查腳本
定期檢查前端服務狀態和關鍵功能
"""

import requests
import time
import json
from datetime import datetime
from urllib.parse import urljoin

class FrontendHealthChecker:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.checks = []
        
    def check_main_page(self):
        """檢查主頁面"""
        try:
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                self.checks.append({"check": "main_page", "status": "OK", "response_time": response.elapsed.total_seconds()})
                return True
            else:
                self.checks.append({"check": "main_page", "status": "FAIL", "error": f"HTTP {response.status_code}"})
                return False
        except Exception as e:
            self.checks.append({"check": "main_page", "status": "ERROR", "error": str(e)})
            return False
    
    def check_pts_renamer(self):
        """檢查 PTS Renamer 功能"""
        try:
            url = urljoin(self.base_url, "/pts_renamer/")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                # 檢查是否返回 HTML 而不是 JSON 錯誤
                is_html = "html" in response.headers.get("content-type", "").lower()
                has_json_error = "error" in response.text[:200].lower() and "{" in response.text[:50]
                
                if is_html and not has_json_error:
                    self.checks.append({"check": "pts_renamer", "status": "OK", "response_time": response.elapsed.total_seconds()})
                    return True
                else:
                    self.checks.append({"check": "pts_renamer", "status": "FAIL", "error": "JSON error displayed"})
                    return False
            else:
                self.checks.append({"check": "pts_renamer", "status": "FAIL", "error": f"HTTP {response.status_code}"})
                return False
        except Exception as e:
            self.checks.append({"check": "pts_renamer", "status": "ERROR", "error": str(e)})
            return False
    
    def check_static_resources(self):
        """檢查靜態資源"""
        static_resources = [
            "/static/css/global.css",
            "/static/js/ui-components.js",
            "/static/images/favicon.ico"
        ]
        
        all_ok = True
        for resource in static_resources:
            try:
                url = urljoin(self.base_url, resource)
                response = requests.head(url, timeout=5)
                if response.status_code == 200:
                    continue
                else:
                    self.checks.append({"check": f"static_{resource}", "status": "FAIL", "error": f"HTTP {response.status_code}"})
                    all_ok = False
            except Exception as e:
                self.checks.append({"check": f"static_{resource}", "status": "ERROR", "error": str(e)})
                all_ok = False
        
        if all_ok:
            self.checks.append({"check": "static_resources", "status": "OK"})
        
        return all_ok
    
    def run_all_checks(self):
        """執行所有檢查"""
        print(f"🏥 開始前端健康檢查... ({datetime.now()})")
        
        self.checks = []
        results = {
            "main_page": self.check_main_page(),
            "pts_renamer": self.check_pts_renamer(),
            "static_resources": self.check_static_resources()
        }
        
        # 顯示結果
        print("\n📋 檢查結果:")
        for check in self.checks:
            status_icon = "✅" if check["status"] == "OK" else "❌" if check["status"] == "FAIL" else "⚠️"
            print(f"{status_icon} {check['check']}: {check['status']}")
            if "error" in check:
                print(f"   錯誤: {check['error']}")
            if "response_time" in check:
                print(f"   響應時間: {check['response_time']:.3f}秒")
        
        # 總體狀態
        all_ok = all(results.values())
        print(f"\n🎯 總體狀態: {'健康' if all_ok else '存在問題'}")
        
        return all_ok, self.checks

if __name__ == "__main__":
    checker = FrontendHealthChecker()
    is_healthy, checks = checker.run_all_checks()
    
    # 保存結果到文件
    result = {
        "timestamp": datetime.now().isoformat(),
        "healthy": is_healthy,
        "checks": checks
    }
    
    with open("frontend_health_report.json", "w", encoding="utf-8") as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 詳細報告已保存至: frontend_health_report.json")
EOF

# 使健康檢查腳本可執行
chmod +x frontend_health_check.py

# 執行健康檢查
python frontend_health_check.py
```

### **自動化監控設置**

```bash
# 創建監控 cron 任務 (Linux/Mac)
cat << 'EOF' > setup_monitoring_cron.sh
#!/bin/bash

# 添加每 5 分鐘執行一次前端健康檢查
CRON_JOB="*/5 * * * * cd $(pwd) && python frontend_health_check.py >> frontend_health.log 2>&1"

# 檢查是否已經存在該任務
if crontab -l 2>/dev/null | grep -q "frontend_health_check.py"; then
    echo "⚠️  前端健康檢查任務已存在"
else
    # 添加新的 cron 任務
    (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
    echo "✅ 前端健康檢查任務已添加到 cron"
    echo "   執行頻率: 每 5 分鐘"
    echo "   日誌文件: frontend_health.log"
fi

# 顯示當前 cron 任務
echo ""
echo "📋 當前 cron 任務:"
crontab -l | grep -E "(frontend|health)" || echo "未找到相關任務"
EOF

chmod +x setup_monitoring_cron.sh

# Windows 任務計劃器設置
cat << 'EOF' > setup_monitoring_windows.bat
@echo off
echo 設置 Windows 任務計劃器監控...

schtasks /create /tn "前端健康檢查" /tr "python %CD%\frontend_health_check.py" /sc minute /mo 5 /f

if %errorlevel% == 0 (
    echo ✅ 前端健康檢查任務已成功添加到任務計劃器
    echo    執行頻率: 每 5 分鐘
    echo    任務名稱: 前端健康檢查
) else (
    echo ❌ 任務添加失敗，請檢查權限
)

pause
EOF
```

---

## 📚 **故障排除速查表**

### **快速診斷命令**

```bash
# 1. 檢查服務狀態
netstat -tlnp | grep :8000  # 檢查端口是否被占用
ps aux | grep python         # 檢查 Python 進程

# 2. 檢查日誌
tail -f logs/app.log         # 查看應用日誌
tail -f logs/error.log       # 查看錯誤日誌

# 3. 檢查前端文件結構
find frontend/ -name "*.html" | head -10  # 檢查模板文件
find frontend/ -name "*.py" | head -10    # 檢查 Python 文件

# 4. 測試網路連接
curl -I http://localhost:8000/            # 測試主頁面
curl -I http://localhost:8000/pts_renamer/ # 測試 PTS Renamer

# 5. 檢查依賴
pip list | grep -i flask                   # 檢查 Flask 版本
pip list | grep -i jinja                   # 檢查 Jinja2 版本
```

### **常見錯誤代碼**

| 錯誤代碼 | 問題描述 | 解決方案 |
|---------|----------|----------|
| 404 | 頁面或資源未找到 | 檢查路由配置和文件路徑 |
| 500 | 服務器內部錯誤 | 檢查應用日誌和代碼錯誤 |
| 502 | 網關錯誤 | 檢查上游服務是否正常運行 |
| 503 | 服務不可用 | 檢查服務器負載和資源 |
| Template Not Found | 模板文件未找到 | 檢查模板路徑和文件存在性 |
| Static File 404 | 靜態文件未找到 | 檢查靜態資源路徑配置 |

### **緊急恢復流程**

```bash
# 1. 停止所有服務
pkill -f python
pkill -f flask

# 2. 備份當前配置
cp -r frontend/ frontend_backup_$(date +%Y%m%d_%H%M%S)/

# 3. 重置到最後已知正常狀態
git status
git stash  # 如果有未提交的更改
git checkout HEAD~1  # 回到上一個提交

# 4. 重新啟動服務
python start_integrated_services.py

# 5. 驗證功能
python frontend_health_check.py

# 6. 如果問題仍存在，查看備份
ls -la frontend_backup_*
```

---

## 📞 **技術支援**

### **聯絡資訊**
- **前端團隊**: <EMAIL>
- **技術支援**: <EMAIL>
- **緊急聯絡**: <EMAIL>

### **文檔更新**
本文檔將根據系統更新和問題回饋定期更新。如發現新問題或有改進建議，請聯絡文檔維護團隊。

### **相關文檔**
- [整體系統架構](../architecture/overall-system-architecture.md)
- [技術棧文檔](../architecture/tech-stack.md)
- [開發指南](../development/README.md)
- [PTS Renamer 修復報告](../.bmad/flow-results/frontend-fix-complete-report-20250819.md)

---

**📅 最後更新**: 2025-08-19  
**👤 維護者**: Documentation Maintainer  
**🔄 審核週期**: 每月  
**📧 回饋**: <EMAIL>