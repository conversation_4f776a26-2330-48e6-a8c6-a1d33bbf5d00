"""
PTS Renamer Database Models

This module contains simple database models for PTS Renamer data persistence.
It extends the existing outlook.db database with PTS-specific tables
following the established patterns used in TaskStatusDB.
"""

import sqlite3
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path


class PTSRenameJobModel:
    """
    Simple model for PTS processing jobs
    
    Uses direct SQLite operations following TaskStatusDB patterns
    """
    
    def __init__(self, id=None, upload_id=None, status='pending', operations=None,
                 rename_config=None, qc_enabled=False, create_directories=False,
                 progress=0, files_processed=0, total_files=0, error_message=None,
                 result_download_url=None, compressed_file_size=None,
                 compressed_file_name=None, download_expires_at=None,
                 created_at=None, updated_at=None, started_at=None, completed_at=None):
        self.id = id or f"job_{uuid.uuid4().hex[:12]}"
        self.upload_id = upload_id
        self.status = status
        self.operations = operations if isinstance(operations, str) else json.dumps(operations or [])
        self.rename_config = rename_config if isinstance(rename_config, str) else json.dumps(rename_config or {})
        self.qc_enabled = qc_enabled
        self.create_directories = create_directories
        self.progress = progress
        self.files_processed = files_processed
        self.total_files = total_files
        self.error_message = error_message
        self.result_download_url = result_download_url
        self.compressed_file_size = compressed_file_size
        self.compressed_file_name = compressed_file_name
        self.download_expires_at = download_expires_at
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        self.started_at = started_at
        self.completed_at = completed_at
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'upload_id': self.upload_id,
            'status': self.status,
            'operations': json.loads(self.operations) if isinstance(self.operations, str) else self.operations,
            'rename_config': json.loads(self.rename_config) if isinstance(self.rename_config, str) else self.rename_config,
            'qc_enabled': self.qc_enabled,
            'create_directories': self.create_directories,
            'progress': self.progress,
            'files_processed': self.files_processed,
            'total_files': self.total_files,
            'error_message': self.error_message,
            'result_download_url': self.result_download_url,
            'compressed_file_size': self.compressed_file_size,
            'compressed_file_name': self.compressed_file_name,
            'download_expires_at': self.download_expires_at.isoformat() if self.download_expires_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
    
    def save(self, connection: sqlite3.Connection):
        """Save job to database"""
        connection.execute("""
            INSERT OR REPLACE INTO pts_rename_jobs
            (id, upload_id, status, operations, rename_config, qc_enabled, create_directories,
             progress, files_processed, total_files, error_message, result_download_url,
             compressed_file_size, compressed_file_name, download_expires_at,
             created_at, updated_at, started_at, completed_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            self.id, self.upload_id, self.status, self.operations, self.rename_config,
            self.qc_enabled, self.create_directories, self.progress, self.files_processed,
            self.total_files, self.error_message, self.result_download_url,
            self.compressed_file_size, self.compressed_file_name,
            self.download_expires_at.isoformat() if self.download_expires_at else None,
            self.created_at.isoformat(), self.updated_at.isoformat(),
            self.started_at.isoformat() if self.started_at else None,
            self.completed_at.isoformat() if self.completed_at else None
        ))
    
    def delete(self, connection: sqlite3.Connection):
        """Delete job from database"""
        connection.execute("DELETE FROM pts_rename_jobs WHERE id = ?", (self.id,))
    
    @classmethod
    def query(cls, connection: sqlite3.Connection, **filters) -> List['PTSRenameJobModel']:
        """Execute query with filters (simplified implementation)"""
        # For now, return empty list - this would need proper implementation
        # based on the specific filter types used in the repository
        return []
    
    @classmethod
    def get_by_id(cls, connection: sqlite3.Connection, job_id: str) -> Optional['PTSRenameJobModel']:
        """Get job by ID"""
        cursor = connection.execute("""
            SELECT * FROM pts_rename_jobs WHERE id = ?
        """, (job_id,))
        
        row = cursor.fetchone()
        if row:
            return cls._from_row(row)
        return None
    
    @classmethod
    def get_by_status(cls, connection: sqlite3.Connection, status: str, limit: Optional[int] = None) -> List['PTSRenameJobModel']:
        """Get jobs by status"""
        query = "SELECT * FROM pts_rename_jobs WHERE status = ?"
        params = [status]
        
        if limit:
            query += " LIMIT ?"
            params.append(limit)
        
        cursor = connection.execute(query, params)
        return [cls._from_row(row) for row in cursor.fetchall()]
    
    @classmethod
    def _from_row(cls, row) -> 'PTSRenameJobModel':
        """Create model instance from database row"""
        return cls(
            id=row['id'],
            upload_id=row['upload_id'],
            status=row['status'],
            operations=row['operations'],
            rename_config=row['rename_config'],
            qc_enabled=bool(row['qc_enabled']),
            create_directories=bool(row['create_directories']),
            progress=row['progress'],
            files_processed=row['files_processed'],
            total_files=row['total_files'],
            error_message=row['error_message'],
            result_download_url=row['result_download_url'],
            compressed_file_size=row['compressed_file_size'],
            compressed_file_name=row['compressed_file_name'],
            download_expires_at=datetime.fromisoformat(row['download_expires_at']) if row['download_expires_at'] else None,
            created_at=datetime.fromisoformat(row['created_at']),
            updated_at=datetime.fromisoformat(row['updated_at']),
            started_at=datetime.fromisoformat(row['started_at']) if row['started_at'] else None,
            completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None
        )


class PTSRenameFileModel:
    """
    Simple model for PTS files
    
    Tracks individual PTS files within processing jobs
    """
    
    def __init__(self, id=None, job_id=None, original_path=None, filename=None,
                 size=0, checksum=None, processed=False, created_at=None):
        self.id = id or f"file_{uuid.uuid4().hex[:12]}"
        self.job_id = job_id
        self.original_path = original_path
        self.filename = filename
        self.size = size
        self.checksum = checksum
        self.processed = processed
        self.created_at = created_at or datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'job_id': self.job_id,
            'original_path': self.original_path,
            'filename': self.filename,
            'size': self.size,
            'checksum': self.checksum,
            'processed': self.processed,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def save(self, connection: sqlite3.Connection):
        """Save file to database"""
        connection.execute("""
            INSERT OR REPLACE INTO pts_rename_files
            (id, job_id, original_path, filename, size, checksum, processed, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            self.id, self.job_id, self.original_path, self.filename,
            self.size, self.checksum, self.processed,
            self.created_at.isoformat()
        ))


class PTSRenameResultModel:
    """
    Simple model for processing results
    
    Tracks the results of operations performed on PTS files
    """
    
    def __init__(self, id=None, job_id=None, file_id=None, operation=None,
                 original_name=None, new_name=None, success=False, 
                 error_message=None, created_at=None):
        self.id = id or f"result_{uuid.uuid4().hex[:12]}"
        self.job_id = job_id
        self.file_id = file_id
        self.operation = operation
        self.original_name = original_name
        self.new_name = new_name
        self.success = success
        self.error_message = error_message
        self.created_at = created_at or datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'job_id': self.job_id,
            'file_id': self.file_id,
            'operation': self.operation,
            'original_name': self.original_name,
            'new_name': self.new_name,
            'success': self.success,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def save(self, connection: sqlite3.Connection):
        """Save result to database"""
        connection.execute("""
            INSERT OR REPLACE INTO pts_rename_results
            (id, job_id, file_id, operation, original_name, new_name, success, error_message, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            self.id, self.job_id, self.file_id, self.operation,
            self.original_name, self.new_name, self.success,
            self.error_message, self.created_at.isoformat()
        ))


# Database initialization and utility functions

def init_pts_renamer_tables(db_path: str = "outlook.db"):
    """
    Initialize PTS Renamer tables in the PTS Renamer database
    
    Args:
        db_path: Path to the SQLite database file
    """
    try:
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # Create pts_rename_jobs table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS pts_rename_jobs (
                    id TEXT PRIMARY KEY,
                    upload_id TEXT,
                    status TEXT NOT NULL DEFAULT 'pending',
                    operations TEXT,
                    rename_config TEXT,
                    qc_enabled INTEGER DEFAULT 0,
                    create_directories INTEGER DEFAULT 0,
                    progress INTEGER DEFAULT 0,
                    files_processed INTEGER DEFAULT 0,
                    total_files INTEGER DEFAULT 0,
                    error_message TEXT,
                    result_download_url TEXT,
                    compressed_file_size INTEGER,
                    compressed_file_name TEXT,
                    download_expires_at TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    started_at TEXT,
                    completed_at TEXT
                )
            """)
            
            # Create pts_rename_files table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS pts_rename_files (
                    id TEXT PRIMARY KEY,
                    job_id TEXT NOT NULL,
                    original_path TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    size INTEGER NOT NULL,
                    checksum TEXT,
                    extracted_from TEXT,
                    processed INTEGER DEFAULT 0,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (job_id) REFERENCES pts_rename_jobs (id)
                )
            """)
            
            # Create pts_rename_results table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS pts_rename_results (
                    id TEXT PRIMARY KEY,
                    job_id TEXT NOT NULL,
                    file_id TEXT,
                    operation TEXT NOT NULL,
                    original_name TEXT NOT NULL,
                    new_name TEXT,
                    success INTEGER NOT NULL,
                    error_message TEXT,
                    processing_time_seconds INTEGER,
                    file_size_bytes INTEGER,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (job_id) REFERENCES pts_rename_jobs (id),
                    FOREIGN KEY (file_id) REFERENCES pts_rename_files (id)
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_pts_jobs_status ON pts_rename_jobs(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_pts_jobs_upload ON pts_rename_jobs(upload_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_pts_jobs_created ON pts_rename_jobs(created_at)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_pts_files_job ON pts_rename_files(job_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_pts_results_job ON pts_rename_results(job_id)")
            
            conn.commit()
            
    except Exception as e:
        raise Exception(f"Failed to initialize PTS Renamer tables: {str(e)}")


def get_job_statistics(db_path: str = "outlook.db", days: int = 7) -> Dict[str, Any]:
    """
    Get job processing statistics
    
    Args:
        db_path: Path to the SQLite database file
        days: Number of days to include in statistics
        
    Returns:
        Dictionary containing statistics
    """
    try:
        with sqlite3.connect(db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # Calculate date threshold
            from datetime import timedelta
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # Get basic counts
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_jobs,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_jobs,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_jobs,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_jobs,
                    SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_jobs,
                    SUM(files_processed) as total_files_processed
                FROM pts_rename_jobs 
                WHERE created_at >= ?
            """, (cutoff_date,))
            
            stats = dict(cursor.fetchone())
            
            # Calculate success rate
            total_jobs = stats['total_jobs']
            completed_jobs = stats['completed_jobs']
            stats['success_rate'] = (completed_jobs / total_jobs * 100) if total_jobs > 0 else 0
            
            # Get average processing time for completed jobs
            cursor = conn.execute("""
                SELECT AVG(
                    CASE 
                        WHEN completed_at IS NOT NULL AND started_at IS NOT NULL 
                        THEN (julianday(completed_at) - julianday(started_at)) * 24 * 60 * 60
                        ELSE NULL 
                    END
                ) as avg_processing_time_seconds
                FROM pts_rename_jobs 
                WHERE status = 'completed' AND created_at >= ?
            """, (cutoff_date,))
            
            result = cursor.fetchone()
            stats['avg_processing_time_seconds'] = result['avg_processing_time_seconds'] or 0
            
            return stats
            
    except Exception as e:
        return {'error': str(e)}


def cleanup_expired_jobs(db_path: str = "outlook.db", retention_hours: int = 24) -> int:
    """
    Clean up expired jobs and their associated data
    
    Args:
        db_path: Path to the SQLite database file
        retention_hours: Hours after which completed jobs are considered expired
        
    Returns:
        Number of jobs cleaned up
    """
    try:
        with sqlite3.connect(db_path) as conn:
            # Calculate cutoff time
            from datetime import timedelta
            cutoff_time = (datetime.now() - timedelta(hours=retention_hours)).isoformat()
            
            # Get jobs to be deleted
            cursor = conn.execute("""
                SELECT id FROM pts_rename_jobs 
                WHERE status IN ('completed', 'failed') AND completed_at < ?
            """, (cutoff_time,))
            
            job_ids = [row['id'] for row in cursor.fetchall()]
            
            if not job_ids:
                return 0
            
            # Delete associated results
            placeholders = ','.join('?' * len(job_ids))
            conn.execute(f"""
                DELETE FROM pts_rename_results 
                WHERE job_id IN ({placeholders})
            """, job_ids)
            
            # Delete associated files
            conn.execute(f"""
                DELETE FROM pts_rename_files 
                WHERE job_id IN ({placeholders})
            """, job_ids)
            
            # Delete jobs
            conn.execute(f"""
                DELETE FROM pts_rename_jobs 
                WHERE id IN ({placeholders})
            """, job_ids)
            
            conn.commit()
            return len(job_ids)
            
    except Exception as e:
        raise Exception(f"Failed to cleanup expired jobs: {str(e)}")