/**
 * PTS Renamer JavaScript 模組
 * 提供 PTS 檔案重命名工具的前端邏輯
 * 與簡化版界面完全兼容
 */

class PTSRenamerAPI {
    constructor() {
        this.baseUrl = '/pts-renamer/api';
        this.timeout = 30000; // 30 秒超時
    }

    async uploadFiles(files) {
        const formData = new FormData();
        files.forEach(file => formData.append('files', file));

        const response = await fetch(`${this.baseUrl}/upload`, {
            method: 'POST',
            body: formData,
            timeout: this.timeout
        });

        if (!response.ok) {
            throw new Error(`上傳失敗: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    async processFiles(config) {
        console.log('[API] 发送处理请求:', {
            url: `${this.baseUrl}/process`,
            config: config
        });
        
        const response = await fetch(`${this.baseUrl}/process`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config),
            timeout: this.timeout
        });

        console.log('[API] 处理请求响应状态:', response.status, response.statusText);

        if (!response.ok) {
            let errorMessage = `處理失敗: ${response.status} ${response.statusText}`;
            
            // 尝试读取详细错误信息
            try {
                const errorData = await response.json();
                console.error('[API] 服务器错误详情:', errorData);
                
                if (errorData.error) {
                    errorMessage += '\n服务器错误: ' + (errorData.error.message || errorData.error);
                    
                    if (errorData.error.details) {
                        errorMessage += '\n詳細資訊: ' + JSON.stringify(errorData.error.details, null, 2);
                    }
                }
            } catch (jsonError) {
                console.error('[API] 无法解析错误响应:', jsonError);
                
                // 尝试读取原始文本
                try {
                    const errorText = await response.text();
                    console.error('[API] 原始错误响应:', errorText);
                    if (errorText && errorText.length < 200) {
                        errorMessage += '\n原始响应: ' + errorText;
                    }
                } catch (textError) {
                    console.error('[API] 无法读取原始错误响应:', textError);
                }
            }
            
            throw new Error(errorMessage);
        }

        const result = await response.json();
        console.log('[API] 处理请求响应数据:', result);
        return result;
    }

    async getJobStatus(jobId) {
        const response = await fetch(`${this.baseUrl}/status/${jobId}`, {
            timeout: this.timeout
        });

        if (!response.ok) {
            throw new Error(`狀態查詢失敗: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    async getPreview(config) {
        const response = await fetch(`${this.baseUrl}/preview`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config),
            timeout: this.timeout
        });

        if (!response.ok) {
            throw new Error(`預覽生成失敗: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    async downloadResults(jobId) {
        const response = await fetch(`${this.baseUrl}/download/${jobId}`, {
            timeout: this.timeout
        });

        if (!response.ok) {
            throw new Error(`下載失敗: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }
}

class PTSRenamerUI {
    constructor() {
        this.api = new PTSRenamerAPI();
        this.uploadedFiles = [];
        this.isProcessing = false;
        this.isUploading = false;  // 添加上傳狀態鎖
        this.currentJobId = null;
        this.currentUploadId = null;
        this.progressInterval = null;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateUI();
        console.log('PTS Renamer UI initialized for simplified interface');
    }

    bindEvents() {
        // 檔案上傳事件
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }
        
        if (uploadArea) {
            // 拖放事件已在模板中定義，這裡只需要增強
            this.enhanceDragDropEvents();
        }
        
        // 重命名選項切換事件 (已在模板中定義為 toggleRenameFields)
        const renameCheckbox = document.getElementById('renameEnabled');
        if (renameCheckbox) {
            renameCheckbox.addEventListener('change', () => {
                toggleRenameFields(); // 調用模板中的函數
                this.updateUI();
            });
        }
        
        // 其他選項切換事件
        ['qcEnabled', 'createDirectories'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.updateUI());
            }
        });
    }

    enhanceDragDropEvents() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        if (!uploadArea || !fileInput) return;
        
        // 增強已有的拖放功能
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            // 只有離開uploadArea時才移除樣式
            if (!uploadArea.contains(e.relatedTarget)) {
                uploadArea.classList.remove('dragover');
            }
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files);
            this.addFiles(files);
            
            // 清除檔案輸入框的值，避免狀態不一致
            if (fileInput) {
                fileInput.value = '';
            }
        });
    }

    handleFileSelect(event) {
        // 檢查是否正在上傳
        if (this.isUploading) {
            this.showNotification('檔案正在處理中，請稍等...', 'warning');
            return;
        }
        
        const files = Array.from(event.target.files);
        if (files.length > 0) {
            this.addFiles(files);
        }
        
        // 立即清除檔案輸入框的值，避免重複選擇
        if (event.target) {
            event.target.value = '';
        }
    }

    async addFiles(files) {
        // 設定上傳狀態鎖
        this.isUploading = true;
        
        try {
            const validExtensions = ['.zip', '.7z', '.rar'];
            const maxFileSize = 100 * 1024 * 1024; // 100MB
            let addedCount = 0;
            const validFiles = [];
            
            files.forEach(file => {
                const extension = file.name.toLowerCase().slice(file.name.lastIndexOf('.'));
                
                // 檢查檔案格式
                if (!validExtensions.includes(extension)) {
                    this.showNotification(`檔案 "${file.name}" 格式不支援`, 'error');
                    return;
                }
                
                // 檢查檔案大小
                if (file.size > maxFileSize) {
                    this.showNotification(`檔案 "${file.name}" 太大（超過 100MB）`, 'error');
                    return;
                }
                
                // 檢查是否已存在
                if (!this.uploadedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    validFiles.push(file);
                    addedCount++;
                }
            });
            
            if (addedCount > 0) {
                // 立即上傳檔案到後端
                this.showNotification('正在上傳檔案...', 'info');
                const uploadResult = await this.api.uploadFiles(validFiles);
                
                if (uploadResult.success) {
                    // 上傳成功後添加到檔案列表
                    validFiles.forEach(file => {
                        this.uploadedFiles.push({
                            file: file,
                            name: file.name,
                            size: file.size,
                            type: this.getFileType(file.name),
                            uploadTime: new Date(),
                            id: this.generateFileId()
                        });
                    });
                    
                    // 保存上傳ID供後續使用
                    this.currentUploadId = uploadResult.upload_id;
                    
                    this.updateUploadedFilesList();
                    this.updateUI();
                    this.showNotification(`成功上傳 ${addedCount} 個檔案`, 'success');
                } else {
                    throw new Error(uploadResult.error?.message || '檔案上傳失敗');
                }
            } else {
                this.showNotification('沒有新檔案被添加', 'info');
            }
        } catch (error) {
            console.error('檔案上傳錯誤:', error);
            this.showNotification('檔案上傳失敗: ' + error.message, 'error');
        } finally {
            // 釋放上傳狀態鎖
            setTimeout(() => {
                this.isUploading = false;
            }, 500); // 500ms 延遲避免連續點擊
        }
    }

    generateFileId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    getFileType(fileName) {
        const extension = fileName.toLowerCase().slice(fileName.lastIndexOf('.'));
        switch (extension) {
            case '.zip': return 'ZIP';
            case '.7z': return '7Z';
            case '.rar': return 'RAR';
            default: return 'UNKNOWN';
        }
    }

    getFileIcon(type) {
        switch (type) {
            case 'ZIP': return 'fas fa-file-archive';
            case '7Z': return 'fas fa-compress-arrows-alt';
            case 'RAR': return 'fas fa-archive';
            default: return 'fas fa-file';
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    updateUploadedFilesList() {
        const container = document.getElementById('uploadedFiles');
        
        if (!container) return;
        
        if (this.uploadedFiles.length === 0) {
            container.innerHTML = '';
        } else {
            container.innerHTML = this.uploadedFiles.map((fileData, index) => `
                <div class="file-item" data-file-id="${fileData.id}">
                    <div class="file-info">
                        <div class="file-icon">📦</div>
                        <div class="file-details">
                            <div class="file-name">${this.escapeHtml(fileData.name)}</div>
                            <div class="file-size">
                                ${this.formatFileSize(fileData.size)} • ${fileData.type}
                            </div>
                        </div>
                    </div>
                    <button class="remove-file-btn" onclick="ptsRenamerUI.removeUploadedFile(${index})">
                        移除
                    </button>
                </div>
            `).join('');
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    removeUploadedFile(index) {
        if (index >= 0 && index < this.uploadedFiles.length) {
            const fileItem = document.querySelector(`[data-file-id="${this.uploadedFiles[index].id}"]`);
            
            if (fileItem) {
                fileItem.classList.add('pts-fade-out');
                setTimeout(() => {
                    this.uploadedFiles.splice(index, 1);
                    this.updateUploadedFilesList();
                    this.clearPreview();
                    this.updateUI();
                    this.showNotification('檔案已移除', 'info');
                }, 300);
            } else {
                this.uploadedFiles.splice(index, 1);
                this.updateUploadedFilesList();
                this.clearPreview();
                this.updateUI();
            }
        }
    }

    clearUploadedFiles() {
        this.uploadedFiles = [];
        this.updateUploadedFilesList();
        this.clearPreview();
        this.hideDownloadSection();
        this.updateUI();
        this.showNotification('所有檔案已清除', 'info');
    }

    toggleOptions() {
        const renameEnabled = document.getElementById('renameEnabled')?.checked || false;
        const qcEnabled = document.getElementById('qcEnabled')?.checked || false;
        const createDirectories = document.getElementById('createDirectories')?.checked || false;

        const optionElements = {
            'renameOptions': renameEnabled,
            'qcOptions': qcEnabled,
            'directoryOptions': createDirectories
        };

        Object.entries(optionElements).forEach(([id, show]) => {
            const element = document.getElementById(id);
            if (element) {
                element.classList.toggle('hidden', !show);
                if (show) {
                    element.classList.add('pts-fade-in');
                }
            }
        });
        
        this.updateUI();
        this.clearPreview(); // 選項變更時清除預覽
    }

    updateUI() {
        const executeBtn = document.getElementById('executeBtn');
        if (!executeBtn) return;
        
        const hasFiles = this.uploadedFiles.length > 0;
        const hasOptions = ['renameEnabled', 'qcEnabled', 'createDirectories']
            .some(id => document.getElementById(id)?.checked);
        
        const canExecute = hasFiles && hasOptions && !this.isProcessing;
        executeBtn.disabled = !canExecute;
        
        // 更新按鈕樣式
        if (canExecute) {
            executeBtn.classList.remove('disabled');
            executeBtn.title = '';
        } else {
            executeBtn.classList.add('disabled');
            if (!hasFiles) {
                executeBtn.title = '請先上傳檔案';
            } else if (!hasOptions) {
                executeBtn.title = '請選擇至少一個處理選項';
            } else if (this.isProcessing) {
                executeBtn.title = '處理中，請稍候';
            }
        }
    }

    animateFileAddition() {
        const fileItems = document.querySelectorAll('.file-item:last-child');
        fileItems.forEach(item => {
            item.classList.add('pts-bounce');
            setTimeout(() => item.classList.remove('pts-bounce'), 600);
        });
    }

    async previewProcessing() {
        if (this.uploadedFiles.length === 0) {
            this.showNotification('請先上傳至少一個檔案', 'error');
            return;
        }

        const operations = this.getSelectedOperations();
        if (operations.length === 0) {
            this.showNotification('請至少選擇一個處理選項', 'error');
            return;
        }

        try {
            this.updatePreviewDisplay('處理中...');
            
            // 檢查是否有上傳ID（檔案應該在 addFiles 中已經上傳）
            if (!this.currentUploadId) {
                throw new Error('找不到上傳ID，請重新上傳檔案');
            }
            
            // 獲取預覽
            const result = await this.api.getPreview({
                upload_id: this.currentUploadId,
                operations: operations,
                rename_config: this.getRenameConfig()
            });
            
            if (result.success) {
                this.displayPreviewResults(result.preview_data || result.data);
                this.showNotification('預覽生成完成', 'success');
            } else {
                throw new Error(result.error?.message || '預覽生成失敗');
            }
        } catch (error) {
            console.error('預覽生成失敗:', error);
            this.showNotification('預覽生成失敗: ' + error.message, 'error');
            this.updatePreviewDisplay('預覽生成失敗，請重試');
        }
    }

    showProcessingOverlay(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const overlay = document.createElement('div');
        overlay.className = 'processing-overlay';
        overlay.innerHTML = `
            <div class="processing-spinner"></div>
            <div class="processing-text">處理中...</div>
        `;
        
        container.style.position = 'relative';
        container.appendChild(overlay);
    }

    hideProcessingOverlay(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const overlay = container.querySelector('.processing-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    generatePreview(operations) {
        const previewList = document.getElementById('previewList');
        if (!previewList) return;
        
        let previewHTML = '<div class="preview-item info"><span class="status-icon"><i class="fas fa-info-circle"></i></span>=== 處理預覽 ===</div>';

        this.uploadedFiles.forEach((fileData, index) => {
            previewHTML += `<div class="preview-item info file-added"><span class="status-icon"><i class="fas fa-file-archive"></i></span>處理壓縮檔：${this.escapeHtml(fileData.name)}</div>`;

            // 模擬檔案內容
            const sampleFiles = [
                `test_${index + 1}_001.pts`,
                `device_data_${index + 1}_002.pts`,
                `quality_check_${index + 1}_003.pts`
            ];

            sampleFiles.forEach(file => {
                if (operations.includes('rename')) {
                    const oldPattern = document.getElementById('oldPattern')?.value || '';
                    const newPattern = document.getElementById('newPattern')?.value || '';

                    if (oldPattern && newPattern) {
                        const newName = this.applyRenamePattern(file, oldPattern, newPattern);
                        previewHTML += `<div class="preview-item success file-renamed"><span class="status-icon"><i class="fas fa-check"></i></span>　重命名：${file} → ${newName}</div>`;
                    } else {
                        previewHTML += `<div class="preview-item warning"><span class="status-icon"><i class="fas fa-exclamation-triangle"></i></span>　重命名：請設定重命名模式</div>`;
                    }
                }

                if (operations.includes('qc_generation')) {
                    const qcSuffix = document.getElementById('qcSuffix')?.value || '_QC';
                    const qcName = file.replace('.pts', `${qcSuffix}.pts`);
                    previewHTML += `<div class="preview-item success file-qc"><span class="status-icon"><i class="fas fa-check"></i></span>　QC檔案：${file} → ${qcName}</div>`;
                }

                if (operations.includes('directory_creation')) {
                    const dirName = file.replace('.pts', '');
                    previewHTML += `<div class="preview-item success"><span class="status-icon"><i class="fas fa-folder"></i></span>　創建目錄：${dirName}/</div>`;
                }
            });

            previewHTML += '<div class="preview-item"><span class="status-icon"></span>&nbsp;</div>';
        });

        previewList.innerHTML = previewHTML;
        
        // 添加動畫效果
        const items = previewList.querySelectorAll('.preview-item');
        items.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('pts-fade-in');
            }, index * 50);
        });
    }

    applyRenamePattern(filename, oldPattern, newPattern) {
        const baseName = filename.replace('.pts', '');
        const extension = '.pts';
        
        let result = newPattern;
        result = result.replace(/\{old\}/g, baseName);
        result = result.replace(/\{ext\}/g, extension);
        result = result.replace(/\{num\}/g, Math.floor(Math.random() * 1000).toString().padStart(3, '0'));
        
        if (oldPattern && filename.includes(oldPattern)) {
            result = filename.replace(new RegExp(oldPattern, 'g'), newPattern);
        }
        
        return result + (result.endsWith('.pts') ? '' : '.pts');
    }

    clearPreview() {
        const previewList = document.getElementById('previewList');
        if (!previewList) return;
        
        previewList.innerHTML = `
            <div class="preview-empty">
                <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                請上傳檔案並選擇處理選項後，點擊「預覽處理結果」查看效果
            </div>
        `;
    }

    getSelectedOperations() {
        const operations = [];
        if (document.getElementById('renameEnabled')?.checked) operations.push('rename');
        if (document.getElementById('qcEnabled')?.checked) operations.push('qc_generation');
        if (document.getElementById('createDirectories')?.checked) operations.push('directory_creation');
        return operations;
    }

    getRenameConfig() {
        return {
            old_pattern: document.getElementById('oldPattern')?.value || '',
            new_pattern: document.getElementById('newPattern')?.value || ''
        };
    }

    async executeProcessing() {
        if (this.isProcessing) return;

        const operations = this.getSelectedOperations();
        if (this.uploadedFiles.length === 0 || operations.length === 0) {
            this.showNotification('請確認已上傳檔案並選擇處理選項', 'error');
            return;
        }

        // 驗證重命名設定
        if (operations.includes('rename')) {
            const oldPattern = document.getElementById('oldPattern')?.value;
            const newPattern = document.getElementById('newPattern')?.value;
            if (!oldPattern || !newPattern) {
                this.showNotification('啟用重命名時，請輸入重命名模式', 'error');
                return;
            }
        }

        try {
            await this.startProcessing(operations);
        } catch (error) {
            console.error('處理失敗:', error);
            this.showNotification('處理失敗：' + error.message, 'error');
            this.stopProcessing();
        }
    }

    async startProcessing(operations) {
        this.isProcessing = true;
        this.updateProcessingUI(true);

        try {
            // 檢查是否有上傳ID（檔案應該在 addFiles 中已經上傳）
            if (!this.currentUploadId) {
                throw new Error('找不到上傳ID，請重新上傳檔案');
            }
            
            // 開始處理
            const processResult = await this.api.processFiles({
                upload_id: this.currentUploadId,
                operations: operations,
                rename_config: this.getRenameConfig(),
                qc_enabled: operations.includes('qc_generation'),
                create_directories: operations.includes('directory_creation')
            });
            
            if (processResult.success) {
                this.currentJobId = processResult.job_id;
                await this.monitorProcessing();
            } else {
                // 增強錯誤資訊顯示
                let errorMessage = '處理請求失敗: ';
                if (processResult.error) {
                    errorMessage += processResult.error.message || '未知錯誤';
                    
                    // 顯示詳細的調試信息
                    if (processResult.error.details) {
                        console.error('詳細錯誤信息:', processResult.error.details);
                        errorMessage += '\n詳細資訊: ' + JSON.stringify(processResult.error.details, null, 2);
                    }
                    
                    // 顯示錯誤代碼
                    if (processResult.error.error_code) {
                        errorMessage += '\n錯誤代碼: ' + processResult.error.error_code;
                    }
                } else {
                    errorMessage += '未知錯誤';
                }
                
                // 記錄完整的處理結果供調試
                console.error('完整處理結果:', processResult);
                console.error('當前上傳ID:', this.currentUploadId);
                console.error('處理操作:', operations);
                
                throw new Error(errorMessage);
            }
            
        } catch (error) {
            throw error;
        }
    }

    async monitorProcessing() {
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        
        if (progressBar) {
            progressBar.classList.remove('hidden');
        }
        
        let attempts = 0;
        const maxAttempts = 120; // 最多監控2分鐘
        
        return new Promise((resolve, reject) => {
            const checkStatus = async () => {
                try {
                    attempts++;
                    
                    if (attempts > maxAttempts) {
                        throw new Error('處理超時');
                    }
                    
                    const statusResult = await this.api.getJobStatus(this.currentJobId);
                    
                    if (statusResult.success) {
                        const status = statusResult.status;
                        const progress = statusResult.progress || 0;
                        
                        // 更新進度條
                        if (progressFill) {
                            progressFill.style.width = progress + '%';
                            progressFill.textContent = Math.round(progress) + '%';
                        }
                        
                        if (status === 'completed') {
                            this.finishProcessing(statusResult);
                            resolve();
                        } else if (status === 'failed' || status === 'error') {
                            throw new Error(statusResult.error?.message || '處理失敗');
                        } else {
                            // 繼續監控
                            setTimeout(checkStatus, 1000);
                        }
                    } else {
                        throw new Error(statusResult.error?.message || '狀態查詢失敗');
                    }
                } catch (error) {
                    reject(error);
                }
            };
            
            checkStatus();
        });
    }

    updateProcessingUI(processing) {
        const executeBtn = document.getElementById('executeBtn');
        const progressBar = document.getElementById('progressBar');
        
        if (!executeBtn) return;

        if (processing) {
            executeBtn.innerHTML = '<span class="status-indicator status-processing"></span>處理中...';
            executeBtn.disabled = true;
            if (progressBar) {
                progressBar.classList.remove('hidden');
            }
        } else {
            executeBtn.innerHTML = '<span class="status-indicator status-ready"></span>開始處理';
            executeBtn.disabled = false;
            if (progressBar) {
                progressBar.classList.add('hidden');
            }
        }
    }

    finishProcessing(statusResult = null) {
        const executeBtn = document.getElementById('executeBtn');
        const progressBar = document.getElementById('progressBar');

        // 顯示完成狀態
        if (executeBtn) {
            executeBtn.innerHTML = '<span class="status-indicator status-ready"></span>處理完成！';
        }
        
        // 更新預覽顯示處理結果
        this.displayProcessingResults(statusResult);

        // 顯示下載區域
        setTimeout(() => {
            this.showDownloadSection(statusResult);
        }, 1000);

        // 重置按鈕狀態
        setTimeout(() => {
            this.stopProcessing();
        }, 3000);
        
        this.showNotification('檔案處理完成！', 'success');
    }

    stopProcessing() {
        this.isProcessing = false;
        this.updateProcessingUI(false);
        this.updateUI();
    }

    updatePreviewDisplay(message) {
        const previewList = document.getElementById('previewList');
        if (previewList) {
            previewList.innerHTML = `
                <div style="color: #888; text-align: center; padding: 20px;">
                    ${message}
                </div>
            `;
        }
    }

    displayPreviewResults(previewData) {
        const previewList = document.getElementById('previewList');
        if (!previewList) return;
        
        let html = '<div class="preview-item info">📋 處理預覽結果</div>';
        
        if (previewData && Array.isArray(previewData)) {
            previewData.forEach(item => {
                html += `<div class="preview-item success">✓ ${item.operation}: ${item.description}</div>`;
            });
        } else {
            // 生成模擬預覽
            const operations = this.getSelectedOperations();
            const operationNames = {
                'rename': '檔案重命名',
                'qc_generation': 'QC 檔案生成',
                'directory_creation': '目錄創建'
            };
            
            operations.forEach(op => {
                const name = operationNames[op] || op;
                html += `<div class="preview-item success">✓ ${name}：將會執行</div>`;
            });
            
            html += `<div class="preview-item info">📦 處理檔案：${this.uploadedFiles.length} 個壓縮檔</div>`;
        }
        
        previewList.innerHTML = html;
    }

    displayProcessingResults(statusResult) {
        const previewList = document.getElementById('previewList');
        if (!previewList) return;
        
        let html = '<div class="preview-item success">🎉 處理完成！</div>';
        
        if (statusResult && statusResult.results) {
            statusResult.results.forEach(result => {
                html += `<div class="preview-item success">✓ ${result.message}</div>`;
            });
        } else {
            const operations = this.getSelectedOperations();
            const operationNames = {
                'rename': '檔案重命名',
                'qc_generation': 'QC 檔案生成',
                'directory_creation': '目錄創建'
            };
            
            operations.forEach(op => {
                const name = operationNames[op] || op;
                html += `<div class="preview-item success">✓ ${name}：已完成</div>`;
            });
            
            html += `<div class="preview-item info">📦 已處理：${this.uploadedFiles.length} 個壓縮檔</div>`;
        }
        
        previewList.innerHTML = html;
    }

    showDownloadSection(statusResult = null) {
        const downloadSection = document.getElementById('downloadSection');
        const downloadList = document.getElementById('downloadList');

        if (!downloadSection || !downloadList) return;

        downloadSection.classList.remove('hidden');

        let downloadHTML = '';
        
        if (this.currentJobId) {
            // 實際下載連結
            const downloadUrl = `/pts-renamer/api/download/${this.currentJobId}`;
            const processedFileName = `processed_files_${new Date().getTime()}.zip`;
            
            downloadHTML = `
                <div class="download-item">
                    <div class="download-info">
                        <div class="download-icon">⬇️</div>
                        <div class="download-details">
                            <div class="download-name">${processedFileName}</div>
                            <div class="download-meta">
                                處理時間：${new Date().toLocaleString()}
                            </div>
                        </div>
                    </div>
                    <a href="${downloadUrl}" class="download-btn" target="_blank">
                        下載結果
                    </a>
                </div>
            `;
        }

        downloadList.innerHTML = downloadHTML;

        // 滾動到下載區域
        setTimeout(() => {
            downloadSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 500);
    }

    getSelectedOperations() {
        const operations = [];
        if (document.getElementById('renameEnabled')?.checked) operations.push('rename');
        if (document.getElementById('qcEnabled')?.checked) operations.push('qc_generation');
        if (document.getElementById('createDirectories')?.checked) operations.push('directory_creation');
        return operations;
    }

    getRenameConfig() {
        return {
            old_pattern: document.getElementById('oldPattern')?.value || '',
            new_pattern: document.getElementById('newPattern')?.value || ''
        };
    }

    removeUploadedFile(index) {
        if (index >= 0 && index < this.uploadedFiles.length) {
            this.uploadedFiles.splice(index, 1);
            this.updateUploadedFilesList();
            this.updateUI();
            this.showNotification('檔案已移除', 'info');
        }
    }

    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 建立簡單的通知
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            max-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        
        // 設定顏色
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        notification.style.background = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        // 顯示動畫
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // 自動隱藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// 全域實例和函數 - 與模板中的函數兼容
let ptsRenamerUI;

// 全域函數供模板調用
function toggleRenameFields() {
    const renameFields = document.getElementById('renameFields');
    const renameEnabled = document.getElementById('renameEnabled').checked;
    
    if (renameEnabled) {
        renameFields.classList.remove('hidden');
    } else {
        renameFields.classList.add('hidden');
    }
}

function previewProcessing() {
    if (ptsRenamerUI) {
        ptsRenamerUI.previewProcessing();
    } else {
        console.log('Preview processing called - UI not initialized');
    }
}

function executeProcessing() {
    if (ptsRenamerUI) {
        ptsRenamerUI.executeProcessing();
    } else {
        console.log('Execute processing called - UI not initialized');
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    ptsRenamerUI = new PTSRenamerUI();
    
    // 設定全域引用
    window.ptsRenamer = ptsRenamerUI;
    window.ptsRenamerUI = ptsRenamerUI;
    
    console.log('PTS Renamer UI loaded and ready');
});