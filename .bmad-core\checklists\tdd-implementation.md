# TDD Implementation Checklist

## TDD 實施檢查清單 (開發階段強制執行)

### 開發前準備檢查
- [ ] **TDD 計劃已制定**: docs/qa/test-plans/tdd-plan-{story-id}.md 存在且完整
- [ ] **Story 要求已理解**: 完整閱讀和理解 Story 中的業務邏輯要求
- [ ] **驗收標準已確認**: 理解所有驗收標準和期望行為
- [ ] **測試環境已準備**: 開發和測試環境已正確設定
- [ ] **測試框架已配置**: 單元測試、整合測試框架已準備就緒

## Red Phase (測試先行階段) 檢查清單

### 測試設計完整性檢查
- [ ] **涵蓋所有 CRUD 操作**: 每個資料模型的 Create、Read、Update、Delete 都有對應測試
- [ ] **涵蓋所有業務規則**: 每個業務規則都有對應的測試案例
- [ ] **涵蓋所有錯誤情況**: 異常處理和錯誤回復都有測試
- [ ] **涵蓋所有邊界條件**: 邊界值和極限情況都有測試
- [ ] **涵蓋所有權限檢查**: 權限和授權檢查都有測試

### 單元測試編寫檢查
- [ ] **測試方法命名清晰**: 測試名稱明確描述測試意圖
- [ ] **測試結構正確**: 遵循 AAA 模式 (Arrange-Act-Assert)
- [ ] **測試獨立性**: 每個測試都是獨立的，不依賴其他測試
- [ ] **測試數據準備**: 測試數據準備充分且真實
- [ ] **斷言明確**: 斷言清晰且涵蓋預期行為

### 整合測試編寫檢查
- [ ] **API 端點測試**: 所有 API 端點都有對應測試
- [ ] **資料庫整合測試**: 資料存取層有完整測試
- [ ] **服務整合測試**: 服務間整合有測試覆蓋
- [ ] **外部依賴測試**: 外部服務整合有測試（或 Mock）
- [ ] **端到端流程測試**: 完整業務流程有測試覆蓋

### 測試執行驗證檢查
- [ ] **所有測試都失敗**: 確認所有新寫的測試初始狀態為 FAIL
- [ ] **失敗原因正確**: 測試失敗是因為功能未實現，而非測試錯誤
- [ ] **錯誤訊息清晰**: 測試失敗時錯誤訊息明確且有幫助
- [ ] **執行速度合理**: 測試執行時間在可接受範圍內
- [ ] **測試環境穩定**: 測試在不同環境下都能穩定執行

### Red Phase 記錄檢查
- [ ] **記錄測試案例數量**: 文檔化編寫的測試數量和覆蓋範圍
- [ ] **記錄失敗狀態**: 截圖或記錄所有測試的失敗狀態
- [ ] **記錄實現目標**: 明確記錄為了讓測試通過需要實現什麼
- [ ] **記錄時間投入**: 記錄 Red Phase 的時間投入

## Green Phase (最小實現階段) 檢查清單

### 實現策略檢查
- [ ] **最小實現原則**: 只實現讓測試通過的最小代碼
- [ ] **避免過度實現**: 不實現測試未涵蓋的功能
- [ ] **實現順序正確**: 按照合理順序實現功能
- [ ] **保持簡單**: 避免過度設計和複雜實現

### 業務邏輯實現檢查
- [ ] **所有 CRUD 操作實現**: Create、Read、Update、Delete 都必須實現
- [ ] **業務規則實現**: 所有業務規則驗證都實現
- [ ] **資料驗證實現**: 所有輸入驗證都實現
- [ ] **錯誤處理實現**: 所有異常處理都實現
- [ ] **權限檢查實現**: 所有授權檢查都實現
- [ ] **事務處理實現**: 必要的事務處理都實現

### 代碼品質基本檢查
- [ ] **代碼可讀性**: 代碼清晰易懂
- [ ] **命名規範**: 變數、方法、類別命名符合規範
- [ ] **註釋適當**: 必要的註釋已添加
- [ ] **無硬編碼**: 避免硬編碼值，使用配置或常數
- [ ] **無死代碼**: 沒有未使用的代碼

### 測試通過驗證檢查
- [ ] **所有測試通過**: 之前失敗的測試現在都通過
- [ ] **沒有跳過測試**: 沒有因為實現困難而跳過或註釋測試
- [ ] **沒有修改測試**: 沒有為了讓實現通過而修改測試邏輯
- [ ] **新增測試通過**: 如果實現過程中發現需要新增測試，新測試也通過
- [ ] **回歸測試通過**: 確保沒有破壞既有功能

### Green Phase 記錄檢查
- [ ] **記錄實現內容**: 詳細記錄實現的功能和代碼
- [ ] **記錄設計決策**: 記錄重要的設計決策和理由
- [ ] **記錄測試結果**: 記錄所有測試通過的狀態
- [ ] **記錄時間投入**: 記錄 Green Phase 的時間投入

## Refactor Phase (重構優化階段) 檢查清單

### 重構活動檢查
- [ ] **重構計劃制定**: 明確重構目標和範圍
- [ ] **重構步驟執行**: 按計劃執行重構活動
- [ ] **重構範圍適當**: 重構範圍不過大，保持風險可控
- [ ] **重構目標達成**: 達成預定的重構目標

### 代碼品質改善檢查
- [ ] **消除重複代碼**: 識別並消除重複的代碼
- [ ] **改善命名**: 改善變數、方法、類別的命名
- [ ] **提取方法**: 將複雜方法分解為更小的方法
- [ ] **提取類別**: 將複雜類別分解為更專注的類別
- [ ] **改善註釋**: 改善或添加必要的註釋

### 設計模式應用檢查
- [ ] **設計模式適用**: 適當應用設計模式改善設計
- [ ] **SOLID 原則**: 遵循 SOLID 設計原則
- [ ] **DRY 原則**: 遵循 Don't Repeat Yourself 原則
- [ ] **KISS 原則**: 保持設計簡單
- [ ] **職責分離**: 確保每個類別和方法職責單一

### 性能優化檢查
- [ ] **性能分析**: 識別性能瓶頸
- [ ] **查詢優化**: 優化資料庫查詢
- [ ] **演算法優化**: 改善演算法效率
- [ ] **記憶體優化**: 優化記憶體使用
- [ ] **快取策略**: 適當使用快取提升性能

### 測試持續性檢查
- [ ] **所有測試仍通過**: 重構後所有測試仍然通過
- [ ] **測試覆蓋率維持**: 測試覆蓋率沒有降低
- [ ] **測試執行時間**: 測試執行時間沒有顯著增加
- [ ] **測試穩定性**: 測試在重構後仍然穩定

### Refactor Phase 記錄檢查
- [ ] **記錄重構活動**: 詳細記錄執行的重構活動
- [ ] **記錄改善效果**: 記錄重構帶來的改善
- [ ] **記錄設計變更**: 記錄重要的設計變更
- [ ] **記錄時間投入**: 記錄 Refactor Phase 的時間投入

## 整體 TDD 循環檢查

### 循環完整性檢查
- [ ] **完整執行循環**: 完整執行 Red → Green → Refactor 循環
- [ ] **循環次數合理**: TDD 循環次數符合功能複雜度
- [ ] **循環範圍適當**: 每個循環的範圍適中，不過大也不過小
- [ ] **循環連接性**: 循環之間有邏輯連接，整體推進功能開發

### 循環記錄檢查
- [ ] **每個循環有記錄**: 每個 TDD 循環都有完整記錄
- [ ] **時間記錄完整**: 每個階段的時間投入都有記錄
- [ ] **決策記錄完整**: 重要決策和理由都有記錄
- [ ] **問題記錄完整**: 遇到的問題和解決方案都有記錄

### 品質驗證檢查
- [ ] **測試品質良好**: 測試代碼品質符合標準
- [ ] **實現品質良好**: 實現代碼品質符合標準
- [ ] **覆蓋率達標**: 測試覆蓋率達到要求標準
- [ ] **性能達標**: 實現的性能符合要求

## 常見問題預防檢查

### 假 TDD 檢測
- [ ] **避免後寫測試**: 確認沒有先寫實現再補測試
- [ ] **避免修改測試**: 確認沒有為實現修改測試邏輯
- [ ] **避免跳過 Red**: 確認沒有跳過 Red Phase
- [ ] **避免過度實現**: 確認沒有在 Green Phase 過度實現
- [ ] **避免跳過 Refactor**: 確認沒有跳過 Refactor Phase

### 品質風險檢測
- [ ] **避免測試依賴**: 確認測試之間沒有依賴關係
- [ ] **避免測試脆弱**: 確認測試不會因為小改動而失敗
- [ ] **避免測試過慢**: 確認測試執行速度合理
- [ ] **避免測試重複**: 確認沒有重複的測試案例
- [ ] **避免測試不完整**: 確認測試覆蓋所有重要場景

### 實現風險檢測
- [ ] **避免緊耦合**: 確認代碼沒有過度耦合
- [ ] **避免硬編碼**: 確認沒有硬編碼的值或邏輯
- [ ] **避免過度複雜**: 確認實現不會過度複雜
- [ ] **避免安全漏洞**: 確認沒有明顯的安全問題
- [ ] **避免性能問題**: 確認沒有明顯的性能問題

## 環境清理檢查

### 開發環境清理
- [ ] **移除臨時代碼**: 移除所有臨時的 Debug 代碼
- [ ] **移除測試數據**: 移除開發過程中的臨時測試數據
- [ ] **移除臨時檔案**: 移除所有臨時檔案和快照
- [ ] **移除註釋代碼**: 移除已註釋掉的舊代碼
- [ ] **移除未使用依賴**: 移除未使用的依賴項目

### 測試環境清理
- [ ] **清理測試數據**: 確保測試數據不會互相干擾
- [ ] **重置測試狀態**: 每個測試都從乾淨狀態開始
- [ ] **清理快取**: 清理測試過程中的快取數據
- [ ] **清理日誌**: 清理開發測試過程中的日誌檔案
- [ ] **清理配置**: 移除開發專用的配置設定

## TDD 完成標準

### 功能完成標準
- [ ] **所有功能實現**: Story 中要求的所有功能都實現
- [ ] **所有測試通過**: 所有測試都穩定通過
- [ ] **測試覆蓋率達標**: 覆蓋率達到要求標準
- [ ] **性能符合要求**: 實現的性能符合要求
- [ ] **安全檢查通過**: 基本安全檢查通過

### 品質完成標準
- [ ] **代碼品質良好**: 代碼品質符合標準
- [ ] **設計品質良好**: 軟體設計品質良好
- [ ] **測試品質良好**: 測試代碼品質良好
- [ ] **文檔完整**: 必要的文檔已完成
- [ ] **可維護性良好**: 代碼易於維護和擴展

### 交付準備標準
- [ ] **環境清理完成**: 開發和測試環境已清理
- [ ] **配置正確**: 相關配置已正確設定
- [ ] **部署準備**: 部署相關的準備工作已完成
- [ ] **監控準備**: 監控和日誌記錄已準備
- [ ] **文檔更新**: 相關文檔已更新

## 檢查失敗處理

### 階段性失敗處理
如果任何階段的檢查失敗：
1. **立即停止後續開發**
2. **分析失敗原因**
3. **制定改進計劃**
4. **重新執行失敗階段**
5. **通過檢查後繼續**

### 整體失敗處理
如果整體 TDD 流程失敗：
1. **回到 TDD 計劃階段**
2. **重新分析需求和設計**
3. **調整 TDD 策略**
4. **重新開始 TDD 循環**
5. **尋求技術支援（如需要）**

### 改進機制
- [ ] **記錄失敗原因**: 詳細記錄每次失敗的原因
- [ ] **分析失敗模式**: 分析常見的失敗模式
- [ ] **改善流程**: 基於失敗經驗改善 TDD 流程
- [ ] **提升技能**: 針對弱項提升 TDD 技能
- [ ] **更新標準**: 基於經驗更新檢查標準
