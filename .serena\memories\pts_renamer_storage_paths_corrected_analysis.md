# PTS Renamer 檔案存儲路徑更正分析

## ❗ **重要發現：配置系統不一致**

經過深入調查，發現了一個重要的配置不一致問題：

### **現狀分析**

#### **1. 系統中存在兩套獨立的配置系統**

**A. 通用檔案上傳系統** (`backend/file_management/`) ✅
- **使用環境變數**: 正確讀取 `.env` 檔案
- **配置檔案**: `upload_config.py`
- **環境變數**:
  ```bash
  UPLOAD_TEMP_DIR=d:/temp/uploads
  EXTRACT_TEMP_DIR=d:/temp  
  ```

**B. PTS Renamer 獨立系統** (`backend/pts_renamer/`) ❌  
- **硬編碼路徑**: 不讀取 `.env` 檔案
- **配置檔案**: `pts_rename_models.py`
- **硬編碼默認值**:
  ```python
  temp_storage_path: str = Field("/tmp/pts_renamer", description="Temporary storage path")
  result_storage_path: str = Field("/tmp/pts_renamer/results", description="Result storage path")
  ```

#### **2. 實際檔案存儲路徑（更正版）**

根據程式碼分析，**實際情況**取決於 PTS Renamer 如何被初始化：

**情況 A: 如果 PTS Renamer 使用預設配置**
```
上傳檔案: /tmp/pts_renamer/{upload_id}/
解壓縮檔案: /tmp/pts_renamer/{upload_id}/{filename}_extracted/
處理後壓縮檔: /tmp/pts_renamer/results/{job_id}/
```

**情況 B: 如果 PTS Renamer 被正確整合（應該要做的）**
```
上傳檔案: d:/temp/uploads/{upload_id}/
解壓縮檔案: d:/temp/{upload_id}/{filename}_extracted/  
處理後壓縮檔: d:/temp/results/{job_id}/
```

### **3. 發現的問題**

❌ **配置不一致**: PTS Renamer 沒有使用 `.env` 環境變數
❌ **路徑衝突**: Linux 風格路徑 (`/tmp/`) vs Windows 實際路徑 (`d:/temp/`)
❌ **重複配置**: 兩套獨立的檔案管理系統

### **4. 正確的整合方式**

PTS Renamer 應該修改為使用環境變數配置：

```python
# 應該修改 PTSRenameConfig 來讀取環境變數
import os
from dotenv import load_dotenv

class PTSRenameConfig(BaseModel):
    temp_storage_path: str = Field(
        default_factory=lambda: os.getenv('UPLOAD_TEMP_DIR', '/tmp/pts_renamer'),
        description="Temporary storage path"
    )
    result_storage_path: str = Field(
        default_factory=lambda: os.getenv('EXTRACT_TEMP_DIR', '/tmp/pts_renamer/results'), 
        description="Result storage path"
    )
```

### **5. 目前實際運作情況**

基於程式碼分析，**目前 PTS Renamer 實際使用的路徑是**：

```yaml
上傳檔案存儲: 
  - 硬編碼路徑: /tmp/pts_renamer/{upload_id}/
  - 在 Windows 上實際可能是: C:\tmp\pts_renamer\{upload_id}\

解壓縮檔案存儲:
  - 硬編碼路徑: /tmp/pts_renamer/{upload_id}/{filename}_extracted/
  - Dramatiq extract_archive_task 執行位置

處理後壓縮檔存儲:
  - 硬編碼路徑: /tmp/pts_renamer/results/{job_id}/
  - 下載檔案存放位置

前端下載功能:
  - ✅ 可以正常運作
  - URL: http://localhost:5000/pts-renamer/api/download/{download_token}
  - 安全令牌系統，24小時過期
```

### **6. Dramatiq 整合狀況**

✅ **已確認**: PTS Renamer 正確使用 Dramatiq
- ✅ 解壓縮: `extract_archive_task`  
- ✅ 壓縮: `create_download_archive_task`, `pts_file_compression_task`
- ✅ 隊列: 都使用 `processing_queue`

### **7. 建議的修復措施**

1. **短期解決方案**: 確認 `/tmp/pts_renamer` 目錄在 Windows 系統上可以正常創建和使用
   
2. **長期解決方案**: 修改 PTS Renamer 配置系統，整合 `.env` 環境變數：
   - 讀取 `UPLOAD_TEMP_DIR` 作為 `temp_storage_path`
   - 讀取 `EXTRACT_TEMP_DIR` 作為 `result_storage_path` 的基礎路徑
   - 統一配置系統，避免重複

### **8. 回答用戶問題**

**您的問題很重要！** 理論上 PTS Renamer **應該**使用 `.env` 中的環境變數：
```
UPLOAD_TEMP_DIR=d:/temp/uploads
EXTRACT_TEMP_DIR=d:/temp
```

但目前的實現中，PTS Renamer 使用自己的硬編碼配置，**沒有**讀取這些環境變數。這是一個需要修復的配置不一致問題。

**實際運作情況**：
- 上傳檔案: `/tmp/pts_renamer/{upload_id}/` (硬編碼)
- 解壓縮檔案: `/tmp/pts_renamer/{upload_id}/{filename}_extracted/` (硬編碼)
- 壓縮結果: `/tmp/pts_renamer/results/{job_id}/` (硬編碼)
- 前端下載: ✅ 正常運作，安全令牌系統

**建議**: 修改 PTS Renamer 配置系統，讓它正確讀取 `.env` 環境變數，以保持系統配置的一致性。