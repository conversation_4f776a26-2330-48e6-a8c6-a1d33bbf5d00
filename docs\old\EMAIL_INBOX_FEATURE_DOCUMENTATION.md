# Email Inbox 頁面功能說明文檔

## 📧 概覽

**頁面路徑**: `http://localhost:5000/email/inbox`  
**主要功能**: 半導體郵件處理系統的核心頁面，提供完整的郵件管理、解析、處理和監控功能

## 🏗️ 系統架構

### 支援的半導體廠商 (11個)
- **TSMC** (台積電)
- **UMC** (聯電) 
- **ASE** (日月光)
- **MediaTek** (聯發科)
- **Foxconn** (鴻海)
- **Realtek** (瑞昱)
- **Advanced Semiconductor Engineering**
- **其他半導體合作夥伴**

### 核心子系統整合
1. **EQC處理系統** - 品質控制數據處理
2. **任務管理系統** - 批次作業排程
3. **監控儀表板** - 即時狀態監控
4. **檔案管理系統** - 網路檔案瀏覽
5. **FT-Summary分析** - 測試結果統計

---

## 🎛️ 頂部導航按鈕

### 1. 同步郵件 (`sync-btn`)
**功能**: 手動觸發郵件同步  
**API端點**: `POST /email/api/sync`  
**圖示**: ↻  
**行為**:
- 從郵件伺服器下載最新郵件 (預設最多100封)
- 顯示同步進度條
- 更新統計資料
- 自動刷新郵件列表

**參數**:
```json
{
  "max_emails": 100
}
```

### 2. 自動同步 (`auto-sync-btn`)
**功能**: 啟動/停止自動同步機制  
**API端點**: 
- 啟動: `POST /email/api/sync/auto/start`
- 停止: `POST /email/api/sync/auto/stop`
**圖示**: ⏱  
**行為**:
- 設定自動同步間隔 (預設60秒)
- 背景持續監控新郵件
- 按鈕狀態切換 (啟動/停止)

**參數**:
```json
{
  "interval": 60
}
```

### 3. 批次解析 (`batch-parse-btn`)
**功能**: 大量解析待處理郵件  
**API端點**: `POST /email/api/parser/emails/batch-parse`  
**圖示**: 🔎  
**行為**:
- 彈出批次解析對話框
- 支援三種解析模式:
  - 解析待解析郵件
  - 重新解析失敗郵件  
  - 解析所有未解析郵件
- 每次最多處理50封郵件

**解析流程**:
1. 廠商識別 (基於寄件者和主旨)
2. 內容解析 (產品編號、批次號、良率等)
3. 資料驗證和儲存
4. 通知發送

### 4. 處理 (`batch-process-btn`)
**功能**: 批次處理已解析郵件的附件和檔案  
**API端點**: `POST /email/api/parser/emails/batch-process`  
**圖示**: 📁  
**行為**:
- 查找已解析但未處理的郵件
- 複製附件到目標路徑 `D:\temp\{pd}\{lot}`
- 處理廠商檔案 (從網路路徑)
- 更新處理狀態

**處理邏輯**:
```
已解析郵件 → 建立目標資料夾 → 複製附件 → 處理廠商檔案 → 標記已處理
```

### 5. FT-EQC 處理 (`FT-EQC處理按鈕`)
**功能**: 前往 FT-EQC 品質控制處理頁面  
**連結**: `/eqc/ft-eqc`  
**圖示**: ►  
**行為**:
- 在新分頁開啟
- 處理半導體測試數據
- 品質控制分析

### 6. 資料庫管理 (`資料庫管理按鈕`)
**功能**: 前往資料庫管理界面  
**連結**: `/monitoring/database-manager`  
**圖示**: DB  
**行為**:
- 資料庫監控和維護
- 資料清理和備份
- 效能監控

### 7. FT-Summary UI (`ft-summary-link`)
**功能**: 開啟測試結果統計分析界面  
**連結**: 動態設定 (預設 `http://127.0.0.1:8010/ft-summary-ui#`)  
**圖示**: 📈  
**行為**:
- 動態偵測服務可用性
- 測試數據視覺化
- 統計報表生成

### 8. 排程管理 (`排程管理按鈕`)
**功能**: 前往任務排程儀表板  
**連結**: `/tasks/scheduler-dashboard`  
**圖示**: ⏰  
**行為**:
- 查看和管理定時任務
- 監控批次作業狀態

### 9. 網路瀏覽器 (`網路瀏覽器按鈕`)
**功能**: 前往網路檔案瀏覽器  
**連結**: `/files/network-browser`  
**圖示**: 🌐  
**行為**:
- 瀏覽網路檔案系統
- 檔案上傳下載管理

### 10. 監控儀表板 (`監控儀表板按鈕`)
**功能**: 前往系統監控儀表板  
**連結**: `/monitoring/dashboard`  
**圖示**: 📊  
**行為**:
- 系統健康監控
- 效能指標追蹤
- 警報管理

### 11. 清空所有 (`clear-all-btn`)
**功能**: 清空所有郵件 (危險操作)  
**API端點**: `POST /email/api/clear-all`  
**圖示**: 🗑️  
**行為**:
- 彈出確認對話框
- 需要二次確認
- 清空所有郵件和寄件者記錄

**確認參數**:
```json
{
  "confirm": true
}
```

---

## 📊 統計資料區域

### 統計項目顯示
**位置**: 頁面頂部左側  
**API端點**: `GET /email/api/statistics`

1. **總郵件** (`total-emails`): 資料庫中所有郵件數量
2. **未讀** (`unread-emails`): 未標記為已讀的郵件數量  
3. **寄件者** (`total-senders`): 不重複寄件者數量

### 同步狀態提示 (`sync-status`)
**顯示條件**: 同步進行中  
**內容**:
- 狀態圖示 (⌛)
- 同步訊息
- 進度條動畫

---

## 🔍 搜尋和篩選功能

### 搜尋框 (`search-input`)
**功能**: 全文搜尋郵件  
**API端點**: `GET /email/api/search`  
**搜尋範圍**:
- 郵件主旨
- 郵件內容
- 寄件者

**搜尋按鈕** (`search-btn`): 觸發搜尋操作

### 排序選擇 (`sort-select`)
**選項**:
- **時間 (新到舊)** (`received_time_desc`) - 預設
- **時間 (舊到新)** (`received_time_asc`)
- **寄件者 (A-Z)** (`sender_asc`)
- **主旨 (A-Z)** (`subject_asc`)

### 篩選器
**只顯示未讀** (`unread-only`): 僅顯示未讀郵件的核取方塊

---

## 📋 郵件列表功能

### 列表標題欄
1. **全選核取方塊** (`select-all`): 選擇/取消選擇所有郵件
2. **寄件者欄位**: 顯示寄件者資訊
3. **主旨欄位**: 顯示郵件主題
4. **時間欄位**: 顯示接收時間
5. **附件欄位**: 顯示附件數量或圖示
6. **操作欄位**: 個別操作按鈕

### 郵件列表 (`email-list`)
**API端點**: `GET /email/api/list`  
**功能**:
- 分頁顯示郵件
- 動態載入
- 即時更新
- 多選操作

### 載入狀態
**載入占位符**: 顯示載入動畫和提示文字

---

## 👀 郵件詳情預覽

### 詳情面板 (`email-detail`)
**API端點**: `GET /email/api/{email_id}`  
**觸發**: 點擊郵件列表項目

### 詳情標題 (`detail-header`)
- **主旨顯示** (`detail-subject`)
- **關閉按鈕** (`close-detail`): × 圖示

### 詳情資訊 (`detail-meta`)
1. **寄件者** (`detail-sender`)
2. **時間** (`detail-time`)  
3. **附件** (`detail-attachments`): 附件列表

### 詳情內容 (`detail-content`)
**郵件內容** (`detail-body`): 顯示郵件正文

### 相關操作
- **標記已讀**: 自動標記為已讀
- **下載附件**: 點擊附件名稱下載

---

## 🔄 批量操作功能

### 批量操作面板 (`batch-actions`)
**顯示條件**: 選擇一個或多個郵件時出現

### 選擇統計 (`selected-count`)
顯示已選擇的郵件數量

### 批量按鈕
1. **標記已讀** (`batch-mark-read`)
   - **API端點**: `POST /email/api/batch-mark-read`
   - **功能**: 批量標記選中郵件為已讀

2. **刪除** (`batch-delete`)
   - **API端點**: `POST /email/api/batch-delete`
   - **功能**: 批量刪除選中郵件
   - **確認**: 需要確認對話框

3. **處理** (`batch-process`)
   - **功能**: 批量處理選中已解析郵件
   - **行為**: 複製附件和處理檔案

### API 參數格式
```json
{
  "email_ids": [1, 2, 3, 4, 5]
}
```

---

## 📄 分頁控制

### 分頁資訊 (`pagination-info`)
- **當前範圍**: 顯示 X - Y
- **總數**: 共 Z 封郵件

### 分頁控制按鈕 (`pagination-controls`)
1. **上一頁** (`prev-page`): 前往上一頁
2. **頁碼** (`page-numbers`): 顯示頁碼按鈕
3. **下一頁** (`next-page`): 前往下一頁

### 分頁邏輯
- 預設每頁50封郵件
- 動態計算總頁數
- 按鈕啟用/禁用狀態管理

---

## 🗂️ 對話框系統

### 1. 確認對話框 (`confirm-dialog`)
**用途**: 危險操作二次確認  
**組件**:
- **標題** (`dialog-title`): 確認操作
- **訊息** (`dialog-message`): 操作說明
- **取消按鈕** (`dialog-cancel`)
- **確認按鈕** (`dialog-confirm`)

### 2. 批次解析對話框 (`batch-parse-dialog`)
**用途**: 批次解析選項設定  
**選項**:
- **解析待解析郵件** (`pending`): 處理狀態為 pending 的郵件
- **重新解析失敗郵件** (`failed`): 重新處理失敗的郵件
- **解析所有未解析郵件** (`all`): 處理所有未解析郵件

**限制**: 每次最多處理50封郵件

### 3. 載入遮罩 (`loading-overlay`)
**用途**: 長時間操作期間顯示  
**內容**:
- 載入動畫
- "處理中..." 提示

---

## 🔔 通知系統

### 通知訊息 (`notification`)
**觸發情況**:
- 操作成功/失敗
- 系統狀態變更
- 錯誤提示

**組件**:
- **圖示** (`notification-icon`): i
- **訊息文字** (`notification-text`)
- **關閉按鈕** (`notification-close`): ×

### 通知類型
- **成功**: 綠色背景
- **錯誤**: 紅色背景  
- **警告**: 橙色背景
- **資訊**: 藍色背景

---

## 🔧 個別郵件操作

### 單一郵件 API

#### 1. 查看詳情
**API**: `GET /email/api/{email_id}`  
**功能**: 獲取單一郵件完整資訊

#### 2. 標記已讀/未讀
**API**: `PUT /email/api/{email_id}/read`  
**參數**:
```json
{
  "is_read": true/false
}
```

#### 3. 刪除郵件
**API**: `DELETE /email/api/{email_id}`  
**功能**: 刪除單一郵件

#### 4. 處理郵件
**API**: `POST /email/api/{email_id}/process`  
**功能**: 使用 ALL IN ONE 流程處理單一郵件
- 解析郵件內容
- 處理附件
- 處理廠商檔案
- 發送通知
- 更新資料庫

#### 5. 獲取解析失敗詳情
**API**: `GET /email/api/{email_id}/failed-analysis`  
**功能**: 查看解析失敗的詳細資訊

---

## 🔌 系統整合 API

### 連接狀態檢測
1. **測試連接**: `GET /email/api/connection/test`
2. **連接狀態**: `GET /email/api/connection/status`
3. **同步狀態**: `GET /email/api/sync/status`
4. **模組狀態**: `GET /email/api/status`

### 統計數據
- **統計資料**: `GET /email/api/statistics`
- **寄件者列表**: `GET /email/api/senders`

---

## 📱 前端互動邏輯

### JavaScript 模組架構
1. **email-ui-utils.js**: UI 工具函數
2. **email-attachments.js**: 附件處理
3. **email-operations.js**: 郵件操作
4. **email-list-manager.js**: 列表管理
5. **email-detail.js**: 詳情面板
6. **email-inbox-core.js**: 核心功能
7. **email-parser-ui.js**: 解析界面

### 動態 URL 配置
- **自動偵測作業系統和主機**
- **動態調整服務連結**
- **連接性測試**
- **回退機制**

### 即時更新機制
- **WebSocket 連接** (可選)
- **定時輪詢**
- **狀態同步**
- **快取管理**

---

## 🚨 錯誤處理

### 前端錯誤處理
- **API 請求失敗**: 顯示錯誤通知
- **網路連接問題**: 重試機制
- **載入超時**: 超時提示
- **資料格式錯誤**: 驗證提示

### 後端錯誤回應
```json
{
  "success": false,
  "message": "錯誤描述",
  "error_code": "ERROR_CODE",
  "details": {}
}
```

### 常見錯誤代碼
- `EMAIL_NOT_FOUND`: 郵件不存在
- `INVALID_PARAMETERS`: 參數無效
- `PROCESSING_FAILED`: 處理失敗
- `PERMISSION_DENIED`: 權限不足

---

## 🔐 安全性考量

### 前端安全
- **XSS 防護**: 輸入過濾和轉義
- **CSRF 防護**: Token 驗證
- **輸入驗證**: 客戶端驗證

### 後端安全
- **API 金鑰認證**: X-API-Key header
- **參數驗證**: 伺服器端驗證
- **SQL 注入防護**: 參數化查詢
- **檔案上傳限制**: 類型和大小限制

---

## 📈 效能優化

### 前端優化
- **虛擬滾動**: 大量郵件列表
- **懶載入**: 按需載入資料
- **快取策略**: 本地快取常用資料
- **防抖處理**: 搜尋輸入防抖

### 後端優化
- **資料庫索引**: 查詢優化
- **分頁查詢**: 限制返回數量
- **背景處理**: 耗時操作非同步處理
- **快取機制**: Redis 快取熱點資料

---

## 🎯 使用者操作指南

### 基本操作流程

#### 1. 郵件同步
```
1. 點擊 "同步郵件" 按鈕
2. 等待同步完成
3. 查看統計資料更新
4. 瀏覽新到達的郵件
```

#### 2. 批次解析
```
1. 點擊 "批次解析" 按鈕
2. 選擇解析類型 (待解析/失敗/全部)
3. 點擊 "開始解析"
4. 等待解析完成
5. 查看解析結果
```

#### 3. 郵件處理
```
1. 確保郵件已解析
2. 點擊 "處理" 按鈕
3. 系統自動處理附件和檔案
4. 查看處理結果
```

#### 4. 批量操作
```
1. 使用核取方塊選擇郵件
2. 選擇批量操作 (標記已讀/刪除/處理)
3. 確認操作
4. 等待批量處理完成
```

### 高級功能

#### 自動化設定
- **啟用自動同步**: 設定同步間隔
- **設定篩選規則**: 自動分類郵件
- **配置通知**: 重要郵件提醒

#### 監控和維護
- **查看系統狀態**: 監控儀表板
- **資料庫維護**: 定期清理和備份
- **錯誤追蹤**: 查看和處理錯誤日誌

---

## 📝 總結

Email Inbox 頁面是半導體郵件處理系統的核心界面，提供完整的郵件生命週期管理功能：

### 核心價值
1. **統一處理**: 整合11個半導體廠商的郵件處理流程
2. **自動化**: 減少手動操作，提高處理效率
3. **可視化**: 即時監控和統計，便於管理決策
4. **可擴展**: 模組化設計，易於添加新功能

### 主要優勢
- **高效率**: 批次處理大量郵件
- **高可靠**: 完整的錯誤處理和恢復機制
- **高可用**: 24/7 自動化監控和處理
- **高擴展**: 支援新廠商和格式的快速接入

此系統大幅提升了半導體行業郵件處理的自動化程度，減少了人工干預，提高了數據處理的準確性和及時性。