"""
EmailSyncService 下載狀態管理整合測試
Epic-02 Story 2.3: EmailSyncService 整合測試

測試郵件同步服務與下載狀態管理的整合
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from backend.shared.infrastructure.adapters.email_inbox.email_sync_service import EmailSyncService
from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
from backend.email.models.email_models import EmailData


class TestEmailSyncServiceIntegration:
    """
    EmailSyncService 下載狀態管理整合測試
    """
    
    @pytest.fixture
    def mock_database(self):
        """模擬資料庫"""
        mock_db = Mock(spec=EmailDatabase)
        
        # 模擬session context manager
        mock_session = Mock()
        context_manager = MagicMock()
        context_manager.__enter__ = Mock(return_value=mock_session)
        context_manager.__exit__ = Mock(return_value=None)
        mock_db.get_session.return_value = context_manager
        
        # 模擬save_email返回
        mock_db.save_email.return_value = 1
        
        return mock_db
    
    @pytest.fixture
    def email_sync_service(self, mock_database):
        """創建郵件同步服務實例"""
        with patch.multiple(
            'backend.shared.infrastructure.adapters.email_inbox.email_sync_service',
            SyncAttachmentHandler=Mock(),
            ParserFactory=Mock(),
            LineNotificationService=Mock()
        ):
            service = EmailSyncService(database=mock_database)
            return service
    
    def test_email_sync_service_initialization_with_download_management(self, email_sync_service):
        """測試郵件同步服務初始化，包含下載管理服務"""
        # 驗證下載管理相關服務已初始化
        assert hasattr(email_sync_service, 'download_repository')
        assert hasattr(email_sync_service, 'download_manager')
        assert hasattr(email_sync_service, 'download_lifecycle_manager')
        assert hasattr(email_sync_service, 'download_performance_monitor')
        
        # 驗證回調已註冊
        assert len(email_sync_service.download_lifecycle_manager.status_change_callbacks) > 0
    
    def test_download_status_callback(self, email_sync_service):
        """測試下載狀態變更回調"""
        # 測試回調方法不會拋出異常
        email_sync_service._on_download_status_change(1, 'pending', 'downloading', {})
        
        # 驗證日誌記錄（通過不拋出異常來驗證）
        assert True  # 如果沒有異常，測試通過
    
    def test_get_download_statistics(self, email_sync_service):
        """測試獲取下載統計"""
        stats = email_sync_service.get_download_statistics("daily")
        
        assert 'period' in stats
        assert 'management_stats' in stats
        assert 'performance_stats' in stats
        assert 'lifecycle_stats' in stats
        assert stats['period'] == "daily"
    
    def test_get_download_health_status(self, email_sync_service):
        """測試獲取下載健康狀態"""
        health = email_sync_service.get_download_health_status()
        
        assert 'overall_status' in health
        assert 'download_manager' in health
        assert 'lifecycle_manager' in health
        assert 'performance_monitor' in health
        assert health['overall_status'] == 'healthy'
    
    @patch('backend.shared.application.services.unified_email_processor.UnifiedEmailProcessor')
    def test_email_processing_with_download_tracking(self, mock_unified_processor, email_sync_service, mock_database):
        """測試郵件處理過程中的下載追蹤"""
        # 模擬UnifiedEmailProcessor
        mock_processor_instance = Mock()
        mock_unified_processor.return_value = mock_processor_instance
        
        # 模擬處理結果
        mock_result = Mock()
        mock_result.is_success = True
        mock_result.vendor_code = "TEST_VENDOR"
        mock_result.parsing_result.extraction_method = "test_method"
        mock_result.processing_time = 1.5
        mock_processor_instance.process_email_complete.return_value = mock_result
        
        # 模擬郵件數據
        email = EmailData(
            message_id="test_123",
            sender="<EMAIL>",
            subject="Test Email",
            body="Test body content",
            received_time=datetime.now()
        )
        
        # 模擬郵件讀取器
        mock_email_reader = Mock()
        mock_email_reader.connect.return_value = True
        mock_email_reader.read_emails.return_value = [email]
        mock_email_reader.disconnect.return_value = True
        mock_email_reader.mark_as_processed.return_value = True
        
        email_sync_service.email_reader = mock_email_reader
        
        # 模擬_is_email_already_processed返回False（未處理過）
        with patch.object(email_sync_service, '_is_email_already_processed', return_value=False):
            # 執行同步 - 使用asyncio運行async方法
            import asyncio
            result = asyncio.run(email_sync_service.sync_emails_once(max_emails=1))
        
        # 驗證結果
        assert result['success'] is True
        assert result['data']['sync_count'] == 1
        
        # 驗證下載管理方法被調用
        # 這裡我們主要測試集成沒有拋出異常
        assert True


class TestEmailSyncServiceDownloadIntegrationError:
    """
    EmailSyncService 下載狀態管理錯誤處理測試
    """
    
    @pytest.fixture
    def mock_database(self):
        """模擬資料庫"""
        mock_db = Mock(spec=EmailDatabase)
        
        # 模擬session context manager
        mock_session = Mock()
        context_manager = MagicMock()
        context_manager.__enter__ = Mock(return_value=mock_session)
        context_manager.__exit__ = Mock(return_value=None)
        mock_db.get_session.return_value = context_manager
        
        return mock_db
    
    @pytest.fixture
    def email_sync_service(self, mock_database):
        """創建郵件同步服務實例"""
        with patch.multiple(
            'backend.shared.infrastructure.adapters.email_inbox.email_sync_service',
            SyncAttachmentHandler=Mock(),
            ParserFactory=Mock(),
            LineNotificationService=Mock()
        ):
            service = EmailSyncService(database=mock_database)
            return service
    
    def test_download_statistics_error_handling(self, email_sync_service):
        """測試獲取下載統計時的錯誤處理"""
        # 模擬下載管理器拋出異常
        with patch.object(email_sync_service.download_manager, 'get_download_statistics', side_effect=Exception("Test error")):
            stats = email_sync_service.get_download_statistics("daily")
            
            assert 'error' in stats
            assert stats['period'] == "daily"
    
    def test_download_health_error_handling(self, email_sync_service):
        """測試獲取下載健康狀態時的錯誤處理"""
        # 模擬下載管理器拋出異常
        with patch.object(email_sync_service.download_manager, 'get_service_health', side_effect=Exception("Test error")):
            health = email_sync_service.get_download_health_status()
            
            assert health['overall_status'] == 'unhealthy'
            assert 'error' in health


if __name__ == "__main__":
    pytest.main([__file__, "-v"])