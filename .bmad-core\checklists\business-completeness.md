# Business Completeness Checklist

## 業務完整性檢查清單 (強制執行)

### 資料模型 CRUD 完整性檢查
**每個資料模型都必須檢查以下四個操作:**

#### Create (新增) 操作檢查
- [ ] 新增 API 端點已實現
- [ ] 資料驗證規則已實現
- [ ] 錯誤處理機制已實現
- [ ] 事務處理已實現
- [ ] 權限檢查已實現
- [ ] 審計日誌已實現
- [ ] 前端新增界面已實現（如適用）
- [ ] 單元測試已覆蓋
- [ ] 整合測試已覆蓋

#### Read (查詢) 操作檢查
- [ ] 查詢 API 端點已實現
- [ ] 分頁機制已實現
- [ ] 篩選和排序已實現
- [ ] 權限檢查已實現
- [ ] 性能優化已實現
- [ ] 快取機制已實現（如需要）
- [ ] 前端顯示界面已實現
- [ ] 單元測試已覆蓋
- [ ] 整合測試已覆蓋

#### Update (更新) 操作檢查
- [ ] 更新 API 端點已實現
- [ ] 資料驗證規則已實現
- [ ] 並發控制已實現
- [ ] 版本控制已實現（如需要）
- [ ] 錯誤處理機制已實現
- [ ] 事務處理已實現
- [ ] 權限檢查已實現
- [ ] 審計日誌已實現
- [ ] 前端編輯界面已實現（如適用）
- [ ] 單元測試已覆蓋
- [ ] 整合測試已覆蓋

#### Delete (刪除) 操作檢查
- [ ] 刪除 API 端點已實現
- [ ] 軟刪除/硬刪除邏輯已實現
- [ ] 關聯資料處理已實現
- [ ] 權限檢查已實現
- [ ] 事務處理已實現
- [ ] 審計日誌已實現
- [ ] 前端刪除功能已實現（如適用）
- [ ] 單元測試已覆蓋
- [ ] 整合測試已覆蓋

### 業務流程完整性檢查

#### 狀態管理檢查
- [ ] 所有業務狀態已定義
- [ ] 狀態轉換規則已實現
- [ ] 狀態轉換權限已實現
- [ ] 狀態轉換事件已實現
- [ ] 狀態轉換日誌已實現
- [ ] 狀態轉換測試已覆蓋

#### 業務規則檢查
- [ ] 所有業務規則已識別
- [ ] 業務規則驗證已實現
- [ ] 業務規則例外處理已實現
- [ ] 業務規則測試已覆蓋

#### 工作流程檢查
- [ ] 工作流程步驟已定義
- [ ] 工作流程邏輯已實現
- [ ] 工作流程回滾機制已實現
- [ ] 工作流程監控已實現
- [ ] 工作流程測試已覆蓋

### 資料一致性檢查

#### 事務處理檢查
- [ ] 事務邊界已正確定義
- [ ] ACID 特性已保證
- [ ] 分散式事務已處理（如適用）
- [ ] 事務回滾機制已實現
- [ ] 事務隔離層級已設定
- [ ] 死鎖防止機制已實現

#### 併發控制檢查
- [ ] 樂觀鎖已實現（如適用）
- [ ] 悲觀鎖已實現（如適用）
- [ ] 版本控制已實現（如適用）
- [ ] 競爭條件已處理
- [ ] 併發測試已覆蓋

#### 資料完整性檢查
- [ ] 主鍵約束已實現
- [ ] 外鍵約束已實現
- [ ] 唯一性約束已實現
- [ ] 檢查約束已實現
- [ ] 非空約束已實現
- [ ] 資料類型約束已實現

### 錯誤處理完整性檢查

#### 異常處理檢查
- [ ] 所有可能異常已識別
- [ ] 異常處理機制已實現
- [ ] 異常日誌已實現
- [ ] 異常恢復機制已實現
- [ ] 異常測試已覆蓋

#### 驗證錯誤處理檢查
- [ ] 資料格式驗證已實現
- [ ] 業務規則驗證已實現
- [ ] 權限驗證已實現
- [ ] 驗證錯誤訊息已實現
- [ ] 驗證錯誤測試已覆蓋

### API 完整性檢查

#### REST API 檢查
- [ ] 所有必要端點已實現
- [ ] HTTP 方法使用正確
- [ ] 狀態碼回應正確
- [ ] 請求/回應格式一致
- [ ] API 文檔已更新
- [ ] API 測試已覆蓋

#### API 安全檢查
- [ ] 身份驗證已實現
- [ ] 授權檢查已實現
- [ ] 輸入驗證已實現
- [ ] 輸出編碼已實現
- [ ] 速率限制已實現（如需要）
- [ ] CORS 設定已實現（如需要）

## 不完整實現的常見模式 (自動檢測)

### ❌ 禁止的不完整模式
1. **只讀功能模式**
   - 只實現 Read 操作，缺少 Create/Update/Delete
   - 只有查詢界面，沒有編輯功能

2. **展示模式**
   - 只實現前端顯示，沒有後端業務邏輯
   - 使用假數據或測試數據

3. **部分狀態模式**
   - 只實現部分狀態轉換
   - 缺少錯誤狀態處理

4. **測試數據模式**
   - 使用硬編碼測試數據
   - 沒有真實的資料來源

5. **Mock 服務模式**
   - 使用 Mock 服務代替真實實現
   - 沒有真實的資料庫操作

### ✅ 必須的完整實現標準
1. **完整 CRUD 模式**
   - 所有四個操作都必須實現
   - 包含完整的錯誤處理

2. **端到端功能模式**
   - 從前端到後端的完整實現
   - 包含真實的資料庫操作

3. **完整業務流程模式**
   - 包含所有業務規則和驗證
   - 包含所有狀態轉換

4. **生產就緒模式**
   - 包含完整的錯誤處理
   - 包含完整的安全檢查
   - 包含完整的監控和日誌

## 檢查失敗處理

### 自動回滾條件
如果發現以下任一不完整情況，將自動觸發回滾：
- [ ] 任何資料模型缺少 CRUD 操作
- [ ] 業務流程有缺失的步驟
- [ ] 缺少錯誤處理機制
- [ ] 缺少事務處理
- [ ] 使用測試數據或 Mock 服務

### 改進要求
當檢查失敗時，必須：
1. 詳細記錄不完整的具體項目
2. 提供完整實現的具體要求
3. 設定完成標準和驗收條件
4. 重新執行開發階段直到完整實現
