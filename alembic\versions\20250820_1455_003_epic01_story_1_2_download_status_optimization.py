"""Epic-01 Story 1.2: 郵件下載狀態表優化

Revision ID: 003_epic01_status_opt
Revises: 002_epic01_retry_log
Create Date: 2025-08-20 14:55:00
Epic: 01 - Database Infrastructure
Story: 1.2 - EmailDownloadStatusDB Optimization

Migration Type: Schema Optimization
Risk Level: LOW
Rollback Safe: YES

Description:
優化現有的 email_download_status 表，新增進度追蹤欄位和效能優化索引。
如果表已經包含這些欄位，此遷移將驗證並確保索引存在。

Changes:
- 新增 download_progress 欄位: Float (0-100)
- 新增 downloaded_bytes 欄位: BigInteger
- 新增 file_size_bytes 欄位: BigInteger  
- 新增 updated_at 欄位: DateTime with auto-update
- 建立複合效能索引
- 新增數據完整性約束

Prerequisites:
- email_download_status 表必須存在
- emails 表必須存在 (外鍵依賴)
- 確保資料庫備份已創建

Verification Steps:
- 檢查新欄位是否正確創建
- 驗證數據類型和約束
- 確認索引建立成功
- 測試進度更新功能

Performance Impact:
- 預計遷移時間: <30秒
- 儲存空間增加: 約24位元組/記錄
- 查詢性能提升: 進度和大小查詢

Author: Database Admin
Reviewer: Performance Engineer
"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '003_epic01_status_opt'
down_revision = '002_epic01_retry_log'
branch_labels = None
depends_on = None


def upgrade():
    """
    Epic-01 Story 1.2: 優化郵件下載狀態表
    
    這個遷移主要針對現有的 email_download_status 表進行優化，
    新增進度追蹤和性能監控欄位。
    
    操作序列:
    1. 檢查表是否存在
    2. 檢查欄位是否已存在
    3. 新增缺失的欄位
    4. 建立優化索引
    5. 新增數據完整性約束
    6. 驗證優化效果
    """
    
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    print(f"[{datetime.now()}] 開始 Epic-01 Story 1.2 遷移...")
    
    # 步驟1: 檢查表是否存在
    if 'email_download_status' not in inspector.get_table_names():
        print("⚠ email_download_status 表不存在，跳過優化")
        return
    
    print("✓ 找到 email_download_status 表")
    
    # 步驟2: 檢查現有欄位
    existing_columns = inspector.get_columns('email_download_status')
    column_names = [col['name'] for col in existing_columns]
    
    # 定義需要新增的欄位
    new_columns = [
        ('download_progress', 'REAL DEFAULT 0.0 NOT NULL', 
         "下載進度 (0-100)"),
        ('downloaded_bytes', 'BIGINT DEFAULT 0 NOT NULL', 
         "已下載位元組數"),
        ('file_size_bytes', 'BIGINT', 
         "檔案總大小"),
        ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL', 
         "記錄更新時間")
    ]
    
    # 步驟3: 新增缺失的欄位
    added_columns = []
    for column_name, column_def, comment in new_columns:
        if column_name not in column_names:
            try:
                # 根據資料庫類型選擇適當的欄位類型
                if column_name == 'download_progress':
                    op.add_column('email_download_status',
                        sa.Column('download_progress', sa.Float(), 
                                 nullable=False, default=0.0,
                                 comment=comment))
                elif column_name == 'downloaded_bytes':
                    op.add_column('email_download_status',
                        sa.Column('downloaded_bytes', sa.BigInteger(), 
                                 nullable=False, default=0,
                                 comment=comment))
                elif column_name == 'file_size_bytes':
                    op.add_column('email_download_status',
                        sa.Column('file_size_bytes', sa.BigInteger(), 
                                 nullable=True,
                                 comment=comment))
                elif column_name == 'updated_at':
                    op.add_column('email_download_status',
                        sa.Column('updated_at', sa.DateTime(), 
                                 nullable=False, default=datetime.utcnow,
                                 onupdate=datetime.utcnow,
                                 comment=comment))
                
                added_columns.append(column_name)
                print(f"✓ 新增 {column_name} 欄位")
                
            except Exception as e:
                print(f"✗ 新增 {column_name} 欄位失敗: {e}")
                raise
        else:
            print(f"⚠ {column_name} 欄位已存在，跳過")
    
    # 步驟4: 新增數據完整性約束
    database_type = conn.dialect.name
    
    if database_type == 'sqlite' and added_columns:
        try:
            # 進度範圍約束
            if 'download_progress' in added_columns:
                op.create_check_constraint(
                    'ck_download_progress_range',
                    'email_download_status',
                    "download_progress >= 0 AND download_progress <= 100"
                )
                print("✓ 創建進度範圍約束")
            
            # 位元組數邏輯約束
            if 'downloaded_bytes' in added_columns and 'file_size_bytes' in added_columns:
                op.create_check_constraint(
                    'ck_bytes_consistency',
                    'email_download_status',
                    "file_size_bytes IS NULL OR downloaded_bytes <= file_size_bytes"
                )
                print("✓ 創建位元組數一致性約束")
                
        except Exception as e:
            print(f"✗ 約束創建失敗: {e}")
            # 不阻止遷移繼續
    
    # 步驟5: 建立優化索引
    try:
        existing_indexes = inspector.get_indexes('email_download_status')
        index_names = [idx['name'] for idx in existing_indexes]
        
        # 定義需要的索引
        indexes_to_create = [
            ('idx_download_status_progress', ['download_progress']),
            ('idx_download_status_updated', ['updated_at']),
            ('idx_download_status_composite_opt', ['email_id', 'status']),
            ('idx_download_status_size', ['file_size_bytes']),
        ]
        
        for index_name, columns in indexes_to_create:
            if index_name not in index_names:
                try:
                    op.create_index(index_name, 'email_download_status', columns)
                    print(f"✓ 創建 {index_name} 索引")
                except Exception as e:
                    print(f"⚠ 索引 {index_name} 創建失敗: {e}")
            else:
                print(f"⚠ 索引 {index_name} 已存在，跳過")
                
    except Exception as e:
        print(f"✗ 索引創建過程失敗: {e}")
        # 不阻止遷移完成
    
    # 步驟6: 更新現有記錄的預設值
    if added_columns:
        try:
            # 為現有記錄設置合理的預設值
            update_sql = "UPDATE email_download_status SET "
            updates = []
            
            if 'download_progress' in added_columns:
                updates.append("download_progress = CASE WHEN status = 'completed' THEN 100.0 ELSE 0.0 END")
            
            if 'downloaded_bytes' in added_columns:
                updates.append("downloaded_bytes = CASE WHEN download_size_bytes IS NOT NULL THEN download_size_bytes ELSE 0 END")
            
            if 'updated_at' in added_columns:
                updates.append("updated_at = COALESCE(completed_at, started_at, created_at)")
            
            if updates:
                update_sql += ", ".join(updates) + " WHERE id > 0"
                conn.execute(sa.text(update_sql))
                
                # 獲取更新計數
                result = conn.execute(sa.text("SELECT COUNT(*) FROM email_download_status"))
                count = result.scalar()
                print(f"✓ 更新 {count} 筆現有記錄的預設值")
                
        except Exception as e:
            print(f"✗ 現有記錄更新失敗: {e}")
            # 不阻止遷移完成
    
    # 步驟7: 驗證優化效果
    try:
        # 重新檢查表結構
        new_columns_check = inspector.get_columns('email_download_status')
        new_column_names = [col['name'] for col in new_columns_check]
        
        # 檢查關鍵欄位
        key_columns = ['download_progress', 'downloaded_bytes', 'file_size_bytes', 'updated_at']
        missing = [col for col in key_columns if col not in new_column_names]
        
        if missing:
            print(f"⚠ 以下欄位可能未正確創建: {missing}")
        else:
            print("✓ 所有優化欄位驗證通過")
        
        # 檢查約束（如果支援）
        if database_type == 'sqlite':
            # SQLite 約束檢查較複雜，簡單驗證即可
            result = conn.execute(sa.text(
                "SELECT COUNT(*) FROM email_download_status WHERE download_progress < 0 OR download_progress > 100"
            ))
            invalid_progress = result.scalar()
            if invalid_progress > 0:
                print(f"⚠ 發現 {invalid_progress} 筆無效進度記錄")
            else:
                print("✓ 進度數據完整性驗證通過")
        
    except Exception as e:
        print(f"✗ 優化驗證失敗: {e}")
        
    print(f"[{datetime.now()}] Epic-01 Story 1.2 遷移完成 ✓")


def downgrade():
    """
    Epic-01 Story 1.2: 回滾郵件下載狀態表優化
    
    警告: 此操作將移除所有 Story 1.2 新增的欄位和索引
    進度追蹤數據將永久遺失，請確保有適當備份
    
    回滾序列:
    1. 刪除優化索引
    2. 移除約束
    3. 刪除新增欄位
    4. 驗證回滾完整性
    """
    
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    print(f"[{datetime.now()}] 開始 Epic-01 Story 1.2 回滾...")
    
    # 檢查表是否存在
    if 'email_download_status' not in inspector.get_table_names():
        print("⚠ email_download_status 表不存在，跳過回滾")
        return
    
    # 步驟1: 刪除優化索引
    try:
        indexes_to_drop = [
            'idx_download_status_size',
            'idx_download_status_composite_opt', 
            'idx_download_status_updated',
            'idx_download_status_progress'
        ]
        
        for index_name in indexes_to_drop:
            try:
                op.drop_index(index_name, table_name='email_download_status')
                print(f"✓ 刪除 {index_name} 索引")
            except Exception:
                print(f"⚠ {index_name} 索引不存在或已刪除")
                
    except Exception as e:
        print(f"✗ 索引刪除過程失敗: {e}")
    
    # 步驟2: 移除約束 (SQLite)
    database_type = conn.dialect.name
    if database_type == 'sqlite':
        try:
            # SQLite 中刪除約束需要重建表，這裡跳過
            print("⚠ SQLite 約束移除需要重建表，建議使用備份恢復")
        except Exception as e:
            print(f"⚠ 約束移除失敗: {e}")
    
    # 步驟3: 刪除新增欄位
    try:
        columns_to_drop = ['updated_at', 'file_size_bytes', 'downloaded_bytes', 'download_progress']
        
        for column_name in columns_to_drop:
            try:
                # 注意：SQLite 不支援 DROP COLUMN
                if database_type == 'sqlite':
                    print(f"⚠ SQLite 不支援刪除欄位 {column_name}，建議使用備份恢復")
                else:
                    op.drop_column('email_download_status', column_name)
                    print(f"✓ 刪除 {column_name} 欄位")
            except Exception as e:
                print(f"⚠ 欄位 {column_name} 刪除失敗: {e}")
                
    except Exception as e:
        print(f"✗ 欄位刪除過程失敗: {e}")
    
    # 步驟4: 驗證回滾
    try:
        columns = inspector.get_columns('email_download_status')
        column_names = [col['name'] for col in columns]
        
        removed_columns = ['download_progress', 'downloaded_bytes', 'file_size_bytes', 'updated_at']
        remaining = [col for col in removed_columns if col in column_names]
        
        if remaining:
            print(f"⚠ 以下欄位未能完全移除: {remaining}")
            print("建議使用資料庫備份進行完整恢復")
        else:
            print("✓ 回滾驗證通過")
            
    except Exception as e:
        print(f"✗ 回滾驗證失敗: {e}")
        
    print(f"[{datetime.now()}] Epic-01 Story 1.2 回滾完成")