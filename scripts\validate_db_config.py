#!/usr/bin/env python3
"""
數據庫配置驗證腳本
確保 SQLite 數據庫路徑配置正確
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 設定中文編碼環境變數（Windows 兼容性）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['LANG'] = 'zh_TW.UTF-8'


class DatabaseConfigValidator:
    """數據庫配置驗證器"""
    
    def __init__(self):
        self.config_sources = {}
        self.validation_results = {}
        self.errors = []
        
    def validate_all_configs(self) -> Dict[str, Any]:
        """驗證所有數據庫配置"""
        print("🔧 驗證數據庫配置...")
        print("=" * 50)
        
        # 1. 驗證環境變數配置
        self._validate_env_config()
        
        # 2. 驗證 Flask 前端配置
        self._validate_flask_config()
        
        # 3. 驗證後端服務配置
        self._validate_backend_config()
        
        # 4. 驗證配置一致性
        self._validate_config_consistency()
        
        # 5. 生成配置報告
        self._generate_config_report()
        
        return {
            'config_sources': self.config_sources,
            'validation_results': self.validation_results,
            'errors': self.errors
        }
    
    def _validate_env_config(self):
        """驗證環境變數配置"""
        print("🌍 檢查環境變數配置...")
        
        env_vars = {
            'DATABASE_URL': os.environ.get('DATABASE_URL'),
            'DB_PATH': os.environ.get('DB_PATH'),
            'FLASK_ENV': os.environ.get('FLASK_ENV', 'development'),
            'DASHBOARD_DB_PATH': os.environ.get('DASHBOARD_DB_PATH')
        }
        
        self.config_sources['environment'] = env_vars
        
        # 檢查 .env 文件
        env_files = ['.env', '.env.example']
        for env_file in env_files:
            if Path(env_file).exists():
                print(f"  ✅ 找到環境配置文件: {env_file}")
                self._parse_env_file(env_file)
            else:
                print(f"  ⚠️  環境配置文件不存在: {env_file}")
        
        # 驗證關鍵環境變數
        if env_vars['DATABASE_URL']:
            print(f"  ✅ DATABASE_URL: {env_vars['DATABASE_URL']}")
        else:
            print(f"  ⚠️  DATABASE_URL 未設定，將使用預設值")
        
        if env_vars['DB_PATH']:
            print(f"  ✅ DB_PATH: {env_vars['DB_PATH']}")
        else:
            print(f"  ⚠️  DB_PATH 未設定")
    
    def _parse_env_file(self, env_file: str):
        """解析環境配置文件"""
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                env_content = {}
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_content[key.strip()] = value.strip()
                
                self.config_sources[f'env_file_{env_file}'] = env_content
                
        except Exception as e:
            self.errors.append(f"解析環境文件失敗 {env_file}: {e}")
    
    def _validate_flask_config(self):
        """驗證 Flask 前端配置"""
        print("\n🌐 檢查 Flask 前端配置...")
        
        try:
            from frontend.config import Config, config
            
            # 檢查各環境配置
            environments = ['development', 'testing', 'production']
            flask_configs = {}
            
            for env_name in environments:
                if env_name in config:
                    config_class = config[env_name]
                    flask_configs[env_name] = {
                        'DATABASE_URL': getattr(config_class, 'DATABASE_URL', None),
                        'SQLALCHEMY_DATABASE_URI': getattr(config_class, 'SQLALCHEMY_DATABASE_URI', None),
                        'HOST': getattr(config_class, 'HOST', None),
                        'PORT': getattr(config_class, 'PORT', None)
                    }
                    
                    db_url = flask_configs[env_name]['DATABASE_URL']
                    print(f"  ✅ {env_name}: {db_url}")
                    
                    # 驗證 SQLite 路徑
                    if db_url and db_url.startswith('sqlite:///'):
                        db_path = db_url.replace('sqlite:///', '')
                        if not db_path.startswith('/'):  # 相對路徑
                            full_path = Path(db_path)
                            if full_path.exists():
                                print(f"    ✅ 數據庫文件存在: {full_path}")
                            else:
                                print(f"    ⚠️  數據庫文件不存在: {full_path}")
                                # 檢查父目錄是否存在
                                parent_dir = full_path.parent
                                if parent_dir.exists():
                                    print(f"    ✅ 父目錄存在: {parent_dir}")
                                else:
                                    print(f"    ❌ 父目錄不存在: {parent_dir}")
                                    self.errors.append(f"數據庫父目錄不存在: {parent_dir}")
            
            self.config_sources['flask_frontend'] = flask_configs
            self.validation_results['flask_config'] = True
            
        except Exception as e:
            self.validation_results['flask_config'] = False
            self.errors.append(f"Flask 配置驗證失敗: {e}")
            print(f"  ❌ Flask 配置載入失敗: {e}")
    
    def _validate_backend_config(self):
        """驗證後端服務配置"""
        print("\n🔧 檢查後端服務配置...")
        
        # 檢查郵件服務數據庫配置
        self._check_email_service_db_config()
        
        # 檢查 EQC 服務數據庫配置
        self._check_eqc_service_db_config()
        
        # 檢查監控服務數據庫配置
        self._check_monitoring_service_db_config()
    
    def _check_email_service_db_config(self):
        """檢查郵件服務數據庫配置"""
        try:
            from backend.shared.infrastructure.adapters.database.models import db_engine
            
            # 獲取數據庫引擎配置
            db_url = db_engine.database_url
            print(f"  ✅ 郵件服務數據庫: {db_url}")
            
            # 驗證 SQLite 路徑
            if db_url.startswith('sqlite:///'):
                db_path = db_url.replace('sqlite:///', '')
                full_path = Path(db_path)
                
                if full_path.exists():
                    size = full_path.stat().st_size
                    print(f"    ✅ 數據庫文件存在: {full_path} ({size:,} bytes)")
                else:
                    print(f"    ⚠️  數據庫文件不存在: {full_path}")
                    # 檢查是否可以創建
                    parent_dir = full_path.parent
                    if parent_dir.exists():
                        print(f"    ✅ 可以創建數據庫，父目錄存在: {parent_dir}")
                    else:
                        print(f"    ❌ 無法創建數據庫，父目錄不存在: {parent_dir}")
                        self.errors.append(f"郵件服務數據庫父目錄不存在: {parent_dir}")
            
            self.config_sources['email_service'] = {'database_url': db_url}
            self.validation_results['email_service_config'] = True
            
        except Exception as e:
            self.validation_results['email_service_config'] = False
            self.errors.append(f"郵件服務數據庫配置驗證失敗: {e}")
            print(f"  ❌ 郵件服務配置檢查失敗: {e}")
    
    def _check_eqc_service_db_config(self):
        """檢查 EQC 服務數據庫配置"""
        try:
            from backend.shared.infrastructure.database.task_status_db import get_task_status_db
            
            # 獲取任務狀態數據庫配置
            task_db = get_task_status_db()
            db_path = task_db.db_path
            
            print(f"  ✅ EQC 服務數據庫: {db_path}")
            
            full_path = Path(db_path)
            if full_path.exists():
                size = full_path.stat().st_size
                print(f"    ✅ 數據庫文件存在: {full_path} ({size:,} bytes)")
            else:
                print(f"    ⚠️  數據庫文件不存在: {full_path}")
                # 檢查是否可以創建
                parent_dir = full_path.parent
                if parent_dir.exists():
                    print(f"    ✅ 可以創建數據庫，父目錄存在: {parent_dir}")
                else:
                    print(f"    ❌ 無法創建數據庫，父目錄不存在: {parent_dir}")
                    self.errors.append(f"EQC 服務數據庫父目錄不存在: {parent_dir}")
            
            self.config_sources['eqc_service'] = {'database_path': db_path}
            self.validation_results['eqc_service_config'] = True
            
        except Exception as e:
            self.validation_results['eqc_service_config'] = False
            self.errors.append(f"EQC 服務數據庫配置驗證失敗: {e}")
            print(f"  ❌ EQC 服務配置檢查失敗: {e}")
    
    def _check_monitoring_service_db_config(self):
        """檢查監控服務數據庫配置"""
        try:
            # 檢查監控服務配置文件
            config_files = [
                'config/dashboard_monitoring.json.example',
                'config/dashboard_monitoring.env.example'
            ]
            
            monitoring_configs = {}
            
            for config_file in config_files:
                if Path(config_file).exists():
                    print(f"  ✅ 找到監控配置文件: {config_file}")
                    
                    if config_file.endswith('.json.example'):
                        import json
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config_data = json.load(f)
                            monitoring_configs['json_config'] = config_data
                    
                    elif config_file.endswith('.env.example'):
                        with open(config_file, 'r', encoding='utf-8') as f:
                            env_data = {}
                            for line in f:
                                line = line.strip()
                                if line and not line.startswith('#') and '=' in line:
                                    key, value = line.split('=', 1)
                                    env_data[key.strip()] = value.strip()
                            monitoring_configs['env_config'] = env_data
                else:
                    print(f"  ⚠️  監控配置文件不存在: {config_file}")
            
            self.config_sources['monitoring_service'] = monitoring_configs
            self.validation_results['monitoring_service_config'] = True
            
        except Exception as e:
            self.validation_results['monitoring_service_config'] = False
            self.errors.append(f"監控服務配置驗證失敗: {e}")
            print(f"  ❌ 監控服務配置檢查失敗: {e}")
    
    def _validate_config_consistency(self):
        """驗證配置一致性"""
        print("\n🔍 檢查配置一致性...")
        
        # 收集所有數據庫路徑
        db_paths = []
        
        # Flask 配置中的路徑
        flask_configs = self.config_sources.get('flask_frontend', {})
        for env_name, config in flask_configs.items():
            db_url = config.get('DATABASE_URL')
            if db_url and db_url.startswith('sqlite:///'):
                db_path = db_url.replace('sqlite:///', '')
                db_paths.append(('flask_' + env_name, db_path))
        
        # 郵件服務配置中的路徑
        email_config = self.config_sources.get('email_service', {})
        if email_config.get('database_url'):
            db_url = email_config['database_url']
            if db_url.startswith('sqlite:///'):
                db_path = db_url.replace('sqlite:///', '')
                db_paths.append(('email_service', db_path))
        
        # EQC 服務配置中的路徑
        eqc_config = self.config_sources.get('eqc_service', {})
        if eqc_config.get('database_path'):
            db_paths.append(('eqc_service', eqc_config['database_path']))
        
        # 檢查路徑一致性
        unique_paths = set(path for _, path in db_paths)
        
        print(f"  📊 發現 {len(db_paths)} 個數據庫配置，{len(unique_paths)} 個唯一路徑")
        
        for service, path in db_paths:
            print(f"    - {service}: {path}")
        
        # 檢查是否有衝突
        path_services = {}
        for service, path in db_paths:
            if path not in path_services:
                path_services[path] = []
            path_services[path].append(service)
        
        for path, services in path_services.items():
            if len(services) > 1:
                print(f"  ⚠️  多個服務使用相同數據庫路徑: {path}")
                print(f"    服務: {', '.join(services)}")
        
        self.validation_results['config_consistency'] = True
    
    def _generate_config_report(self):
        """生成配置報告"""
        print("\n" + "=" * 50)
        print("📋 數據庫配置驗證報告")
        print("=" * 50)
        
        # 統計驗證結果
        total_checks = len(self.validation_results)
        passed_checks = sum(1 for result in self.validation_results.values() if result)
        
        print(f"總體狀態: {passed_checks}/{total_checks} 項檢查通過")
        
        # 詳細結果
        for check_name, result in self.validation_results.items():
            status = "✅" if result else "❌"
            print(f"  {status} {check_name}")
        
        # 錯誤摘要
        if self.errors:
            print(f"\n❌ 發現的問題 ({len(self.errors)}):")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        else:
            print(f"\n✅ 未發現配置問題")
        
        # 建議
        print(f"\n💡 建議:")
        print(f"  1. 確保所有數據庫文件的父目錄存在")
        print(f"  2. 檢查文件權限，確保應用程式可以讀寫數據庫")
        print(f"  3. 在生產環境中使用絕對路徑")
        print(f"  4. 定期備份重要數據庫文件")


def main():
    """主函數"""
    print("🔧 數據庫配置驗證工具")
    print("=" * 50)
    
    validator = DatabaseConfigValidator()
    results = validator.validate_all_configs()
    
    # 返回適當的退出碼
    if all(results['validation_results'].values()) and not results['errors']:
        print(f"\n✅ 所有配置驗證通過")
        sys.exit(0)
    elif results['errors']:
        print(f"\n❌ 發現配置問題")
        sys.exit(1)
    else:
        print(f"\n⚠️  部分配置需要注意")
        sys.exit(0)


if __name__ == "__main__":
    main()