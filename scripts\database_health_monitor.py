#!/usr/bin/env python3
"""
資料庫健康監控腳本
監控資料庫性能指標、連接狀態、資料完整性和異常情況
"""

import sys
import sqlite3
import time
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass, asdict
import threading
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/database_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class DatabaseMetrics:
    """資料庫指標數據結構"""
    timestamp: str
    database_size_mb: float
    table_counts: Dict[str, int]
    connection_test_ms: float
    query_performance_ms: Dict[str, float]
    integrity_status: str
    disk_usage_percent: float
    backup_status: Dict[str, Any]
    alert_conditions: List[str]
    overall_health: str

class DatabaseHealthMonitor:
    """資料庫健康監控器"""
    
    def __init__(self, 
                 db_path: str = "data/email_inbox.db",
                 alert_config_path: str = "config/database_alerts.json"):
        self.db_path = Path(db_path)
        self.alert_config_path = Path(alert_config_path)
        self.metrics_history: List[DatabaseMetrics] = []
        self.alert_config = self._load_alert_config()
        
        # 創建監控記錄目錄
        self.monitor_dir = Path("logs/database_monitoring")
        self.monitor_dir.mkdir(parents=True, exist_ok=True)
    
    def _load_alert_config(self) -> Dict[str, Any]:
        """載入告警配置"""
        default_config = {
            "thresholds": {
                "max_db_size_mb": 1000,  # 資料庫最大大小 MB
                "max_connection_time_ms": 1000,  # 最大連接時間 ms
                "max_query_time_ms": 5000,  # 最大查詢時間 ms
                "max_disk_usage_percent": 85,  # 最大磁碟使用率 %
                "min_backup_age_hours": 25  # 最小備份間隔小時
            },
            "alert_settings": {
                "email_enabled": False,
                "email_recipients": [],
                "email_smtp_server": "localhost",
                "email_smtp_port": 587,
                "log_enabled": True,
                "console_enabled": True
            },
            "monitoring": {
                "check_interval_seconds": 300,  # 檢查間隔 5 分鐘
                "history_retention_hours": 168,  # 保留 7 天歷史
                "performance_samples": 3  # 性能測試樣本數
            }
        }
        
        try:
            if self.alert_config_path.exists():
                with open(self.alert_config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合併預設配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                        elif isinstance(value, dict):
                            for subkey, subvalue in value.items():
                                if subkey not in config[key]:
                                    config[key][subkey] = subvalue
                    return config
            else:
                # 創建預設配置檔案
                self.alert_config_path.parent.mkdir(parents=True, exist_ok=True)
                with open(self.alert_config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                logger.info(f"已創建預設告警配置: {self.alert_config_path}")
                return default_config
        except Exception as e:
            logger.error(f"載入告警配置失敗: {e}")
            return default_config
    
    def collect_metrics(self) -> DatabaseMetrics:
        """收集資料庫指標"""
        timestamp = datetime.now().isoformat()
        alert_conditions = []
        
        # 基本檔案資訊
        database_size_mb = 0
        disk_usage_percent = 0
        
        if self.db_path.exists():
            database_size_mb = self.db_path.stat().st_size / (1024 * 1024)
            
            # 計算磁碟使用率
            disk_total, disk_used, disk_free = self._get_disk_usage(self.db_path)
            disk_usage_percent = (disk_used / disk_total) * 100 if disk_total > 0 else 0
        else:
            alert_conditions.append(f"資料庫檔案不存在: {self.db_path}")
        
        # 連接測試
        connection_test_ms = self._test_connection_performance()
        
        # 表格計數
        table_counts = self._get_table_counts()
        
        # 查詢性能測試
        query_performance_ms = self._test_query_performance()
        
        # 完整性檢查
        integrity_status = self._check_database_integrity()
        
        # 備份狀態
        backup_status = self._check_backup_status()
        
        # 檢查告警條件
        thresholds = self.alert_config["thresholds"]
        
        if database_size_mb > thresholds["max_db_size_mb"]:
            alert_conditions.append(f"資料庫大小超過閾值: {database_size_mb:.2f}MB > {thresholds['max_db_size_mb']}MB")
        
        if connection_test_ms > thresholds["max_connection_time_ms"]:
            alert_conditions.append(f"連接時間過長: {connection_test_ms:.2f}ms > {thresholds['max_connection_time_ms']}ms")
        
        if disk_usage_percent > thresholds["max_disk_usage_percent"]:
            alert_conditions.append(f"磁碟使用率過高: {disk_usage_percent:.1f}% > {thresholds['max_disk_usage_percent']}%")
        
        for table, query_time in query_performance_ms.items():
            if query_time > thresholds["max_query_time_ms"]:
                alert_conditions.append(f"{table} 查詢時間過長: {query_time:.2f}ms > {thresholds['max_query_time_ms']}ms")
        
        if integrity_status != "ok":
            alert_conditions.append(f"資料庫完整性檢查失敗: {integrity_status}")
        
        if backup_status.get("hours_since_last_backup", 999) > thresholds["min_backup_age_hours"]:
            alert_conditions.append(f"備份時間過舊: {backup_status.get('hours_since_last_backup', 0):.1f}小時")
        
        # 計算總體健康狀況
        if not alert_conditions:
            overall_health = "healthy"
        elif len(alert_conditions) <= 2 and not any("失敗" in condition for condition in alert_conditions):
            overall_health = "warning"
        else:
            overall_health = "critical"
        
        metrics = DatabaseMetrics(
            timestamp=timestamp,
            database_size_mb=round(database_size_mb, 2),
            table_counts=table_counts,
            connection_test_ms=round(connection_test_ms, 2),
            query_performance_ms={k: round(v, 2) for k, v in query_performance_ms.items()},
            integrity_status=integrity_status,
            disk_usage_percent=round(disk_usage_percent, 1),
            backup_status=backup_status,
            alert_conditions=alert_conditions,
            overall_health=overall_health
        )
        
        return metrics
    
    def _test_connection_performance(self) -> float:
        """測試資料庫連接性能"""
        if not self.db_path.exists():
            return float('inf')
        
        try:
            start_time = time.time()
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
            end_time = time.time()
            return (end_time - start_time) * 1000  # 轉換為毫秒
        except Exception as e:
            logger.error(f"連接測試失敗: {e}")
            return float('inf')
    
    def _get_table_counts(self) -> Dict[str, int]:
        """獲取各表記錄數量"""
        table_counts = {}
        
        if not self.db_path.exists():
            return table_counts
        
        try:
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                cursor = conn.cursor()
                
                # 獲取所有表名
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                # 計算每個表的記錄數
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        table_counts[table] = count
                    except Exception as e:
                        logger.warning(f"無法查詢表 {table}: {e}")
                        table_counts[table] = -1
        except Exception as e:
            logger.error(f"獲取表計數失敗: {e}")
        
        return table_counts
    
    def _test_query_performance(self) -> Dict[str, float]:
        """測試查詢性能"""
        performance_results = {}
        
        if not self.db_path.exists():
            return performance_results
        
        # 定義測試查詢
        test_queries = {
            "emails_recent": "SELECT COUNT(*) FROM emails WHERE created_at > datetime('now', '-7 days')",
            "emails_by_sender": "SELECT sender, COUNT(*) FROM emails GROUP BY sender LIMIT 10",
            "attachments_size": "SELECT SUM(size_bytes) FROM attachments",
            "download_status": "SELECT status, COUNT(*) FROM email_download_status GROUP BY status"
        }
        
        try:
            with sqlite3.connect(self.db_path, timeout=30) as conn:
                cursor = conn.cursor()
                
                for query_name, query_sql in test_queries.items():
                    try:
                        # 多次測試取平均值
                        times = []
                        samples = self.alert_config["monitoring"]["performance_samples"]
                        
                        for _ in range(samples):
                            start_time = time.time()
                            cursor.execute(query_sql)
                            cursor.fetchall()
                            end_time = time.time()
                            times.append((end_time - start_time) * 1000)
                        
                        avg_time = sum(times) / len(times)
                        performance_results[query_name] = avg_time
                        
                    except Exception as e:
                        logger.warning(f"查詢 {query_name} 測試失敗: {e}")
                        performance_results[query_name] = float('inf')
        except Exception as e:
            logger.error(f"查詢性能測試失敗: {e}")
        
        return performance_results
    
    def _check_database_integrity(self) -> str:
        """檢查資料庫完整性"""
        if not self.db_path.exists():
            return "database_not_found"
        
        try:
            with sqlite3.connect(self.db_path, timeout=30) as conn:
                cursor = conn.cursor()
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()[0]
                return result
        except Exception as e:
            logger.error(f"完整性檢查失敗: {e}")
            return f"check_failed: {str(e)}"
    
    def _get_disk_usage(self, path: Path) -> Tuple[int, int, int]:
        """獲取磁碟使用情況"""
        try:
            import shutil
            total, used, free = shutil.disk_usage(path.parent)
            return total, used, free
        except Exception as e:
            logger.warning(f"獲取磁碟使用情況失敗: {e}")
            return 0, 0, 0
    
    def _check_backup_status(self) -> Dict[str, Any]:
        """檢查備份狀態"""
        backup_status = {
            "last_backup_time": None,
            "hours_since_last_backup": None,
            "backup_file_exists": False,
            "backup_file_size_mb": 0
        }
        
        # 檢查備份目錄
        backup_dirs = [
            Path("backups/automated/daily"),
            Path("backups/automated/weekly"),
            Path("backups/manual")
        ]
        
        latest_backup = None
        latest_backup_time = None
        
        for backup_dir in backup_dirs:
            if backup_dir.exists():
                for backup_file in backup_dir.glob("*.db*"):
                    try:
                        mtime = datetime.fromtimestamp(backup_file.stat().st_mtime)
                        if latest_backup_time is None or mtime > latest_backup_time:
                            latest_backup = backup_file
                            latest_backup_time = mtime
                    except Exception as e:
                        logger.warning(f"檢查備份檔案 {backup_file} 失敗: {e}")
        
        if latest_backup and latest_backup_time:
            backup_status["last_backup_time"] = latest_backup_time.isoformat()
            backup_status["hours_since_last_backup"] = (
                datetime.now() - latest_backup_time
            ).total_seconds() / 3600
            backup_status["backup_file_exists"] = True
            backup_status["backup_file_size_mb"] = round(
                latest_backup.stat().st_size / (1024 * 1024), 2
            )
        
        return backup_status
    
    def save_metrics(self, metrics: DatabaseMetrics):
        """保存指標到檔案"""
        # 添加到內存歷史
        self.metrics_history.append(metrics)
        
        # 保留指定時間內的歷史記錄
        retention_hours = self.alert_config["monitoring"]["history_retention_hours"]
        cutoff_time = datetime.now() - timedelta(hours=retention_hours)
        
        self.metrics_history = [
            m for m in self.metrics_history 
            if datetime.fromisoformat(m.timestamp) > cutoff_time
        ]
        
        # 保存到檔案
        metrics_file = self.monitor_dir / f"metrics_{datetime.now().strftime('%Y%m%d')}.jsonl"
        
        try:
            with open(metrics_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(asdict(metrics), ensure_ascii=False) + '\n')
        except Exception as e:
            logger.error(f"保存指標失敗: {e}")
    
    def send_alerts(self, metrics: DatabaseMetrics):
        """發送告警通知"""
        if not metrics.alert_conditions:
            return
        
        alert_settings = self.alert_config["alert_settings"]
        
        # 日誌告警
        if alert_settings.get("log_enabled", True):
            for condition in metrics.alert_conditions:
                logger.warning(f"資料庫告警: {condition}")
        
        # 控制台告警
        if alert_settings.get("console_enabled", True):
            print(f"🚨 資料庫告警 - 總體狀況: {metrics.overall_health}")
            for condition in metrics.alert_conditions:
                print(f"   ⚠️  {condition}")
        
        # 郵件告警
        if alert_settings.get("email_enabled", False):
            self._send_email_alert(metrics)
    
    def _send_email_alert(self, metrics: DatabaseMetrics):
        """發送郵件告警"""
        try:
            alert_settings = self.alert_config["alert_settings"]
            
            msg = MIMEMultipart()
            msg['From'] = "database-monitor@system"
            msg['To'] = ", ".join(alert_settings["email_recipients"])
            msg['Subject'] = f"資料庫告警 - {metrics.overall_health.upper()}"
            
            body = f"""
資料庫健康監控告警

時間: {metrics.timestamp}
總體狀況: {metrics.overall_health}
資料庫大小: {metrics.database_size_mb} MB
磁碟使用率: {metrics.disk_usage_percent}%

告警條件:
{chr(10).join(f"• {condition}" for condition in metrics.alert_conditions)}

詳細指標:
• 連接時間: {metrics.connection_test_ms} ms
• 完整性狀態: {metrics.integrity_status}
• 表格記錄數: {json.dumps(metrics.table_counts, indent=2)}
• 查詢性能: {json.dumps(metrics.query_performance_ms, indent=2)}

此為系統自動發送的告警郵件。
"""
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(
                alert_settings["email_smtp_server"], 
                alert_settings["email_smtp_port"]
            )
            server.send_message(msg)
            server.quit()
            
            logger.info("告警郵件已發送")
            
        except Exception as e:
            logger.error(f"發送告警郵件失敗: {e}")
    
    def run_monitoring_cycle(self):
        """執行一次監控週期"""
        try:
            logger.info("開始資料庫健康檢查")
            metrics = self.collect_metrics()
            self.save_metrics(metrics)
            
            # 發送告警
            if metrics.alert_conditions:
                self.send_alerts(metrics)
            
            logger.info(f"資料庫健康檢查完成 - 狀況: {metrics.overall_health}, "
                       f"告警數量: {len(metrics.alert_conditions)}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"監控週期執行失敗: {e}")
            return None
    
    def generate_health_report(self) -> Dict[str, Any]:
        """生成健康報告"""
        if not self.metrics_history:
            return {"error": "無歷史資料"}
        
        recent_metrics = self.metrics_history[-10:]  # 最近 10 次檢查
        
        # 計算統計資訊
        avg_connection_time = sum(m.connection_test_ms for m in recent_metrics) / len(recent_metrics)
        avg_db_size = sum(m.database_size_mb for m in recent_metrics) / len(recent_metrics)
        
        health_counts = {}
        for m in recent_metrics:
            health_counts[m.overall_health] = health_counts.get(m.overall_health, 0) + 1
        
        report = {
            "report_time": datetime.now().isoformat(),
            "monitoring_period": {
                "from": recent_metrics[0].timestamp,
                "to": recent_metrics[-1].timestamp,
                "samples": len(recent_metrics)
            },
            "current_status": asdict(recent_metrics[-1]) if recent_metrics else None,
            "trends": {
                "avg_connection_time_ms": round(avg_connection_time, 2),
                "avg_database_size_mb": round(avg_db_size, 2),
                "health_distribution": health_counts
            },
            "recommendations": self._generate_recommendations(recent_metrics)
        }
        
        return report
    
    def _generate_recommendations(self, metrics_list: List[DatabaseMetrics]) -> List[str]:
        """生成優化建議"""
        recommendations = []
        
        if not metrics_list:
            return recommendations
        
        latest = metrics_list[-1]
        
        # 資料庫大小建議
        if latest.database_size_mb > 500:
            recommendations.append("考慮執行 VACUUM 指令來優化資料庫大小")
        
        # 查詢性能建議
        slow_queries = [q for q, time in latest.query_performance_ms.items() if time > 1000]
        if slow_queries:
            recommendations.append(f"以下查詢性能較慢，建議添加索引: {', '.join(slow_queries)}")
        
        # 備份建議
        if latest.backup_status.get("hours_since_last_backup", 0) > 12:
            recommendations.append("建議執行資料庫備份")
        
        # 磁碟空間建議
        if latest.disk_usage_percent > 80:
            recommendations.append("磁碟使用率較高，建議清理舊檔案或擴充儲存空間")
        
        # 連接性能建議
        if latest.connection_test_ms > 500:
            recommendations.append("資料庫連接較慢，檢查是否有長時間執行的查詢")
        
        return recommendations


def main():
    """主要執行函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description="資料庫健康監控工具")
    parser.add_argument("--once", action="store_true", help="執行一次檢查")
    parser.add_argument("--monitor", action="store_true", help="持續監控模式")
    parser.add_argument("--report", action="store_true", help="生成健康報告")
    parser.add_argument("--config", type=str, help="指定配置檔案路徑")
    
    args = parser.parse_args()
    
    config_path = args.config or "config/database_alerts.json"
    monitor = DatabaseHealthMonitor(alert_config_path=config_path)
    
    if args.report:
        report = monitor.generate_health_report()
        print(json.dumps(report, indent=2, ensure_ascii=False))
    elif args.monitor:
        logger.info("啟動持續監控模式")
        interval = monitor.alert_config["monitoring"]["check_interval_seconds"]
        
        try:
            while True:
                monitor.run_monitoring_cycle()
                time.sleep(interval)
        except KeyboardInterrupt:
            logger.info("監控已停止")
    else:
        # 預設執行一次檢查
        metrics = monitor.run_monitoring_cycle()
        if metrics:
            print(json.dumps(asdict(metrics), indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()