"""
API 超時保護工具
為所有 API 端點提供統一的超時保護機制
"""

import asyncio
import functools
from typing import Any, Callable, Dict, Union
from fastapi import HTTPException
from loguru import logger


class APITimeoutConfig:
    """API 超時配置"""
    # 不同類型操作的默認超時時間（秒）
    QUICK_OPERATION = 5      # 快速操作（文件讀取、狀態查詢）
    STANDARD_OPERATION = 10  # 標準操作（列表查詢、數據處理）
    COMPLEX_OPERATION = 15   # 複雜操作（鎖定管理、會話操作）
    FILE_OPERATION = 20      # 文件操作（創建任務、文件處理）
    HEAVY_OPERATION = 30     # 重型操作（任務執行、數據處理）


def with_api_timeout(
    timeout_seconds: int = APITimeoutConfig.STANDARD_OPERATION,
    operation_name: str = "API操作"
):
    """
    API 超時保護裝飾器
    
    Args:
        timeout_seconds: 超時時間（秒）
        operation_name: 操作名稱，用於日誌記錄
    
    Returns:
        裝飾器函數
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                async with asyncio.timeout(timeout_seconds):
                    return await func(*args, **kwargs)
            except asyncio.TimeoutError:
                error_msg = f"{operation_name}超時（{timeout_seconds}秒）"
                logger.warning(f"⏰ {error_msg}")
                raise HTTPException(
                    status_code=408,
                    detail=f"{error_msg}，請稍後重試"
                )
        return wrapper
    return decorator


async def execute_with_timeout(
    operation: Callable,
    timeout_seconds: int,
    operation_name: str = "操作",
    *args,
    **kwargs
) -> Any:
    """
    執行帶超時保護的異步操作
    
    Args:
        operation: 要執行的異步操作
        timeout_seconds: 超時時間（秒）
        operation_name: 操作名稱
        *args, **kwargs: 傳遞給操作的參數
    
    Returns:
        操作結果
        
    Raises:
        HTTPException: 超時時拋出 408 錯誤
    """
    try:
        async with asyncio.timeout(timeout_seconds):
            if asyncio.iscoroutinefunction(operation):
                return await operation(*args, **kwargs)
            else:
                # 對於同步操作，在線程池中執行
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, operation, *args, **kwargs)
    except asyncio.TimeoutError:
        error_msg = f"{operation_name}超時（{timeout_seconds}秒）"
        logger.warning(f"⏰ {error_msg}")
        raise HTTPException(
            status_code=408,
            detail=f"{error_msg}，請稍後重試"
        )


def get_timeout_config(operation_type: str) -> int:
    """
    根據操作類型獲取推薦的超時配置
    
    Args:
        operation_type: 操作類型
        
    Returns:
        超時時間（秒）
    """
    timeout_map = {
        "quick": APITimeoutConfig.QUICK_OPERATION,
        "standard": APITimeoutConfig.STANDARD_OPERATION,
        "complex": APITimeoutConfig.COMPLEX_OPERATION,
        "file": APITimeoutConfig.FILE_OPERATION,
        "heavy": APITimeoutConfig.HEAVY_OPERATION,
    }
    
    return timeout_map.get(operation_type, APITimeoutConfig.STANDARD_OPERATION)


class TimeoutContext:
    """超時上下文管理器"""
    
    def __init__(
        self, 
        timeout_seconds: int, 
        operation_name: str = "操作"
    ):
        self.timeout_seconds = timeout_seconds
        self.operation_name = operation_name
        
    async def __aenter__(self):
        logger.debug(f"🕐 開始執行 {self.operation_name}（超時: {self.timeout_seconds}秒）")
        self.timeout_context = asyncio.timeout(self.timeout_seconds)
        return await self.timeout_context.__aenter__()
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        try:
            return await self.timeout_context.__aexit__(exc_type, exc_val, exc_tb)
        except asyncio.TimeoutError:
            error_msg = f"{self.operation_name}超時（{self.timeout_seconds}秒）"
            logger.warning(f"⏰ {error_msg}")
            raise HTTPException(
                status_code=408,
                detail=f"{error_msg}，請稍後重試"
            )


# 常用的超時保護實例
timeout_quick = lambda name: TimeoutContext(APITimeoutConfig.QUICK_OPERATION, name)
timeout_standard = lambda name: TimeoutContext(APITimeoutConfig.STANDARD_OPERATION, name)
timeout_complex = lambda name: TimeoutContext(APITimeoutConfig.COMPLEX_OPERATION, name)
timeout_file = lambda name: TimeoutContext(APITimeoutConfig.FILE_OPERATION, name)
timeout_heavy = lambda name: TimeoutContext(APITimeoutConfig.HEAVY_OPERATION, name)


def log_timeout_stats():
    """記錄超時統計信息"""
    logger.info("📊 API 超時配置:")
    logger.info(f"  快速操作: {APITimeoutConfig.QUICK_OPERATION}秒")
    logger.info(f"  標準操作: {APITimeoutConfig.STANDARD_OPERATION}秒")
    logger.info(f"  複雜操作: {APITimeoutConfig.COMPLEX_OPERATION}秒")
    logger.info(f"  文件操作: {APITimeoutConfig.FILE_OPERATION}秒")
    logger.info(f"  重型操作: {APITimeoutConfig.HEAVY_OPERATION}秒")


# 導出所有公共接口
__all__ = [
    'APITimeoutConfig',
    'with_api_timeout',
    'execute_with_timeout',
    'get_timeout_config',
    'TimeoutContext',
    'timeout_quick',
    'timeout_standard',
    'timeout_complex',
    'timeout_file',
    'timeout_heavy',
    'log_timeout_stats'
]