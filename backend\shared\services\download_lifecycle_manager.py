"""
DownloadLifecycleManager - 下載狀態生命週期管理器
Epic-02 Story 2.2: 下載狀態生命週期管理

狀態轉換邏輯：
PENDING → DOWNLOADING → COMPLETED
       ↓        ↓           
     FAILED   FAILED      
       ↓        ↓          
    RETRY    RETRY        

延續Epic-01的卓越標準：
- 企業級代碼品質
- 100%測試覆蓋
- 完整狀態轉換驗證
- 性能優化設計
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Set
from collections import defaultdict, deque

from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    EmailDownloadStatusDB, DownloadStatus, RetryStrategy, 
    validate_status_transition, VALID_STATUS_TRANSITIONS
)
from backend.shared.infrastructure.logging.logger_manager import LoggerManager


class StatusHistoryEntry:
    """
    狀態歷史記錄條目
    """
    
    def __init__(self, email_id: int, old_status: DownloadStatus, new_status: DownloadStatus, 
                 metadata: Optional[Dict[str, Any]] = None):
        self.email_id = email_id
        self.old_status = old_status
        self.new_status = new_status
        self.timestamp = datetime.utcnow()
        self.metadata = metadata or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            'email_id': self.email_id,
            'old_status': self.old_status.value if self.old_status else None,
            'new_status': self.new_status.value,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }


class DownloadLifecycleManager:
    """
    下載生命週期管理器 - Epic-02 Story 2.2 核心實現
    
    職責：
    - 狀態轉換驗證
    - 狀態變更處理
    - 狀態歷史追蹤
    - 生命週期監控
    """
    
    def __init__(self, database: EmailDatabase, download_service=None):
        """
        初始化生命週期管理器
        
        Args:
            database: 郵件資料庫實例
            download_service: 下載管理服務（可選）
        """
        self.database = database
        self.download_service = download_service
        self.logger = LoggerManager().get_logger("DownloadLifecycleManager")
        
        # 狀態歷史記錄（記憶體中暫存，生產環境應使用資料庫）
        self.status_history: Dict[int, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # 終結狀態集合
        self.terminal_statuses: Set[DownloadStatus] = {DownloadStatus.COMPLETED}
        
        # 狀態轉換回調函數
        self.status_change_callbacks = []
        
        self.logger.info("DownloadLifecycleManager 已初始化")
    
    # ============================================================================
    # 狀態轉換核心邏輯
    # ============================================================================
    
    def validate_status_transition(self, current_status: DownloadStatus, new_status: DownloadStatus) -> bool:
        """
        驗證狀態轉換是否有效
        
        Args:
            current_status: 當前狀態
            new_status: 新狀態
            
        Returns:
            轉換是否有效
        """
        try:
            # 使用已有的驗證函數
            is_valid = validate_status_transition(current_status, new_status)
            
            self.logger.debug(f"狀態轉換驗證: {current_status} -> {new_status}, 結果: {is_valid}")
            return is_valid
            
        except Exception as e:
            self.logger.error(f"狀態轉換驗證失敗: {e}")
            return False
    
    def get_valid_transitions(self, current_status: DownloadStatus) -> List[DownloadStatus]:
        """
        獲取當前狀態的所有有效轉換目標
        
        Args:
            current_status: 當前狀態
            
        Returns:
            有效轉換目標列表
        """
        try:
            if current_status not in VALID_STATUS_TRANSITIONS:
                return []
            
            valid_transitions = VALID_STATUS_TRANSITIONS[current_status]
            self.logger.debug(f"狀態 {current_status} 的有效轉換: {valid_transitions}")
            
            return list(valid_transitions)
            
        except Exception as e:
            self.logger.error(f"獲取有效轉換失敗: {e}")
            return []
    
    def is_terminal_status(self, status: DownloadStatus) -> bool:
        """
        判斷是否為終結狀態
        
        Args:
            status: 狀態
            
        Returns:
            是否為終結狀態
        """
        return status in self.terminal_statuses
    
    # ============================================================================
    # 狀態變更處理
    # ============================================================================
    
    def handle_status_change(self, email_id: int, new_status: DownloadStatus, 
                           metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        處理狀態變更
        
        Args:
            email_id: 郵件ID
            new_status: 新狀態
            metadata: 變更元數據
            
        Returns:
            處理成功與否
        """
        try:
            # 獲取當前狀態
            current_status_info = None
            if self.download_service:
                current_status_info = self.download_service.get_download_status(email_id)
            
            current_status = None
            if current_status_info:
                current_status = DownloadStatus(current_status_info['status'])
            
            # 驗證狀態轉換（如果有當前狀態）
            if current_status and not self.validate_status_transition(current_status, new_status):
                self.logger.error(f"無效的狀態轉換: email_id={email_id}, {current_status} -> {new_status}")
                return False
            
            # 記錄狀態歷史
            history_entry = StatusHistoryEntry(email_id, current_status, new_status, metadata)
            self.status_history[email_id].append(history_entry)
            
            # 執行狀態變更回調
            self._execute_status_change_callbacks(email_id, current_status, new_status, metadata)
            
            # 如果是終結狀態，清理相關資源
            if self.is_terminal_status(new_status):
                self._handle_terminal_status(email_id, new_status)
            
            self.logger.info(f"狀態變更成功: email_id={email_id}, {current_status} -> {new_status}")
            return True
            
        except Exception as e:
            self.logger.error(f"處理狀態變更失敗: email_id={email_id}, error={e}")
            return False
    
    def _execute_status_change_callbacks(self, email_id: int, old_status: Optional[DownloadStatus], 
                                       new_status: DownloadStatus, metadata: Optional[Dict[str, Any]]):
        """
        執行狀態變更回調函數
        
        Args:
            email_id: 郵件ID
            old_status: 舊狀態
            new_status: 新狀態
            metadata: 元數據
        """
        for callback in self.status_change_callbacks:
            try:
                callback(email_id, old_status, new_status, metadata)
            except Exception as e:
                self.logger.error(f"狀態變更回調執行失敗: {e}")
    
    def _handle_terminal_status(self, email_id: int, status: DownloadStatus):
        """
        處理終結狀態
        
        Args:
            email_id: 郵件ID
            status: 終結狀態
        """
        try:
            if status == DownloadStatus.COMPLETED:
                self.logger.info(f"下載完成: email_id={email_id}")
                # 可以在這裡添加完成後的處理邏輯
                
        except Exception as e:
            self.logger.error(f"處理終結狀態失敗: email_id={email_id}, error={e}")
    
    # ============================================================================
    # 狀態歷史管理
    # ============================================================================
    
    def get_status_history(self, email_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """
        獲取狀態歷史
        
        Args:
            email_id: 郵件ID
            limit: 記錄數量限制
            
        Returns:
            狀態歷史列表
        """
        try:
            history = self.status_history.get(email_id, deque())
            
            # 轉換為字典列表，按時間倒序
            history_list = [entry.to_dict() for entry in reversed(list(history))]
            
            # 應用限制
            return history_list[:limit]
            
        except Exception as e:
            self.logger.error(f"獲取狀態歷史失敗: email_id={email_id}, error={e}")
            return []
    
    def get_status_statistics(self, period_hours: int = 24) -> Dict[str, Any]:
        """
        獲取狀態統計信息
        
        Args:
            period_hours: 統計週期（小時）
            
        Returns:
            統計信息
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=period_hours)
            
            status_counts = defaultdict(int)
            transition_counts = defaultdict(int)
            total_changes = 0
            
            for email_id, history in self.status_history.items():
                for entry in history:
                    if entry.timestamp >= cutoff_time:
                        status_counts[entry.new_status.value] += 1
                        if entry.old_status:
                            transition_key = f"{entry.old_status.value} -> {entry.new_status.value}"
                            transition_counts[transition_key] += 1
                        total_changes += 1
            
            return {
                'period_hours': period_hours,
                'total_changes': total_changes,
                'status_counts': dict(status_counts),
                'transition_counts': dict(transition_counts),
                'active_downloads': len([k for k, v in self.status_history.items() if v])
            }
            
        except Exception as e:
            self.logger.error(f"獲取狀態統計失敗: {e}")
            return {
                'period_hours': period_hours,
                'error': str(e)
            }
    
    # ============================================================================
    # 回調函數管理
    # ============================================================================
    
    def add_status_change_callback(self, callback):
        """
        添加狀態變更回調函數
        
        Args:
            callback: 回調函數，簽名為 (email_id, old_status, new_status, metadata)
        """
        if callback not in self.status_change_callbacks:
            self.status_change_callbacks.append(callback)
            self.logger.debug("添加狀態變更回調函數")
    
    def remove_status_change_callback(self, callback):
        """
        移除狀態變更回調函數
        
        Args:
            callback: 要移除的回調函數
        """
        if callback in self.status_change_callbacks:
            self.status_change_callbacks.remove(callback)
            self.logger.debug("移除狀態變更回調函數")
    
    # ============================================================================
    # 高級功能
    # ============================================================================
    
    def get_lifecycle_insights(self, email_id: int) -> Dict[str, Any]:
        """
        獲取生命週期洞察
        
        Args:
            email_id: 郵件ID
            
        Returns:
            生命週期洞察信息
        """
        try:
            history = self.status_history.get(email_id, deque())
            if not history:
                return {
                    'email_id': email_id,
                    'insights': 'No lifecycle data available'
                }
            
            # 計算生命週期統計
            total_time = None
            status_durations = {}
            
            if len(history) >= 2:
                first_entry = list(history)[0]
                last_entry = list(history)[-1]
                total_time = (last_entry.timestamp - first_entry.timestamp).total_seconds()
            
            # 分析狀態持續時間
            prev_entry = None
            for entry in history:
                if prev_entry:
                    duration = (entry.timestamp - prev_entry.timestamp).total_seconds()
                    status_key = prev_entry.new_status.value
                    if status_key not in status_durations:
                        status_durations[status_key] = []
                    status_durations[status_key].append(duration)
                prev_entry = entry
            
            return {
                'email_id': email_id,
                'total_lifecycle_time': total_time,
                'status_count': len(history),
                'status_durations': {
                    status: {
                        'count': len(durations),
                        'total_time': sum(durations),
                        'average_time': sum(durations) / len(durations)
                    }
                    for status, durations in status_durations.items()
                },
                'current_status': list(history)[-1].new_status.value if history else None
            }
            
        except Exception as e:
            self.logger.error(f"獲取生命週期洞察失敗: email_id={email_id}, error={e}")
            return {
                'email_id': email_id,
                'error': str(e)
            }
    
    def cleanup_old_history(self, days: int = 30):
        """
        清理舊的狀態歷史記錄
        
        Args:
            days: 保留天數
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(days=days)
            cleaned_count = 0
            
            for email_id in list(self.status_history.keys()):
                history = self.status_history[email_id]
                
                # 過濾掉舊記錄
                new_history = deque(
                    [entry for entry in history if entry.timestamp >= cutoff_time],
                    maxlen=100
                )
                
                cleaned_count += len(history) - len(new_history)
                
                if new_history:
                    self.status_history[email_id] = new_history
                else:
                    del self.status_history[email_id]
            
            self.logger.info(f"清理舊狀態歷史: 清理了 {cleaned_count} 條記錄")
            
        except Exception as e:
            self.logger.error(f"清理舊狀態歷史失敗: {e}")
    
    # ============================================================================
    # 健康檢查
    # ============================================================================
    
    def get_manager_health(self) -> Dict[str, Any]:
        """
        獲取管理器健康狀態
        
        Returns:
            健康狀態信息
        """
        try:
            active_downloads = len(self.status_history)
            total_history_entries = sum(len(history) for history in self.status_history.values())
            
            return {
                'status': 'healthy',
                'active_downloads': active_downloads,
                'total_history_entries': total_history_entries,
                'callbacks_count': len(self.status_change_callbacks),
                'memory_usage': 'normal',  # 簡化的記憶體使用指標
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"獲取管理器健康狀態失敗: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }