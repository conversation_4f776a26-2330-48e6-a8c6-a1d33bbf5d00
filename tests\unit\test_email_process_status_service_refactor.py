"""
測試 Epic-03 Story 3.1: EmailProcessStatusService REFACTOR PHASE
完整的業務邏輯和資料庫整合測試
"""

import pytest
import asyncio
from datetime import datetime, timedelta, timezone
from unittest.mock import Mock, MagicMock, patch
from typing import Dict, Any, Optional

# 導入待測試的服務
from backend.shared.services.email_process_status_service import (
    EmailProcessStatusService,
    ProcessingMetrics,
    ProcessStatusInfo
)
from backend.shared.services.process_status_enums import ProcessStatus
from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
from backend.shared.infrastructure.adapters.database.models import EmailProcessStatusDB


class TestEmailProcessStatusServiceRefactor:
    """EmailProcessStatusService REFACTOR PHASE 完整測試"""
    
    @pytest.fixture
    def real_database(self):
        """真實資料庫連接"""
        db = EmailDatabase()
        return db
    
    @pytest.fixture  
    def refactor_service(self, real_database):
        """REFACTOR PHASE 測試服務實例"""
        return EmailProcessStatusService(real_database)
    
    # ===========================================
    # 完整業務邏輯測試
    # ===========================================
    
    def test_create_process_status_with_database(self, refactor_service):
        """測試創建處理狀態 - 真實資料庫操作"""
        email_id = 999999  # 使用大數值避免衝突
        step_name = "test_processing"
        
        # 創建狀態
        status_id = refactor_service.create_process_status(
            email_id=email_id, 
            step_name=step_name
        )
        
        # 驗證返回值
        assert isinstance(status_id, int)
        assert status_id > 0
        
        # 驗證可以查詢到
        status_info = refactor_service.get_process_status(
            email_id=email_id, 
            step_name=step_name
        )
        assert status_info is not None
        assert status_info.email_id == email_id
        assert status_info.step_name == step_name
        assert status_info.status == ProcessStatus.PENDING.value
        
        # 清理測試數據
        refactor_service.delete_process_status(status_id)
    
    def test_complete_processing_workflow(self, refactor_service):
        """測試完整的處理工作流程"""
        import time
        email_id = int(time.time() * 1000) % 1000000  # 生成唯一ID
        step_name = f"complete_workflow_test_{email_id}"
        
        status_id = None
        try:
            # 1. 開始處理
            status_id = refactor_service.start_processing(
                email_id=email_id,
                step_name=step_name
            )
            
            # 2. 更新到處理中 (需要滿足條件)
            refactor_service.update_status(
                status_id, 
                ProcessStatus.PROCESSING.value,
                {
                    'progress_percentage': 50,
                    'parsing_completed': True,
                    'parsed_data': 'test_parsed_data'
                }
            )
            
            # 3. 更新到驗證中 (需要滿足條件)
            refactor_service.update_status(
                status_id,
                ProcessStatus.VALIDATION.value,
                {
                    'progress_percentage': 80,
                    'processing_completed': True,
                    'processed_data': 'test_processed_data'
                }
            )
            
            # 4. 完成處理 (需要滿足條件)
            refactor_service.update_status(
                status_id,
                ProcessStatus.COMPLETED.value,
                {
                    'validation_passed': True,
                    'final_result': {'summary': 'Processing completed successfully'},
                    'progress_percentage': 100
                }
            )
            
            # 5. 驗證最終狀態
            final_status = refactor_service.get_process_status(
                email_id=email_id,
                step_name=step_name
            )
            
            assert final_status.status == ProcessStatus.COMPLETED.value
            assert final_status.progress_percentage == 100
            assert final_status.completed_at is not None
            
        finally:
            # 清理測試數據
            if status_id:
                try:
                    refactor_service.delete_process_status(status_id)
                except:
                    pass
    
    def test_failure_processing_workflow(self, refactor_service):
        """測試處理失敗工作流程"""
        import time
        email_id = int(999997000 + time.time() % 1000)  # 確保唯一性
        step_name = "failure_workflow_test"
        status_id = None
        
        # 清理任何現有記錄
        try:
            existing_status = refactor_service.get_process_status(email_id, step_name)
            if existing_status:
                refactor_service.delete_process_status(existing_status.id)
        except:
            pass
        
        try:
            # 1. 開始處理
            status_id = refactor_service.start_processing(
                email_id=email_id,
                step_name=step_name
            )
            
            # 2. 模擬處理失敗
            error_message = "模擬的處理錯誤"
            refactor_service.fail_processing(status_id, error_message)
            
            # 3. 驗證失敗狀態
            failed_status = refactor_service.get_process_status(
                email_id=email_id,
                step_name=step_name
            )
            
            assert failed_status.status == ProcessStatus.FAILED.value
            assert failed_status.error_message == error_message
            assert failed_status.completed_at is not None
            
        finally:
            # 清理測試數據
            if status_id:
                try:
                    refactor_service.delete_process_status(status_id)
                except:
                    pass
    
    def test_status_transition_validation(self, refactor_service):
        """測試狀態轉換驗證"""
        import time
        email_id = int(999996000 + time.time() % 1000)  # 確保唯一性
        step_name = "transition_test"
        
        try:
            # 創建初始狀態
            status_id = refactor_service.create_process_status(
                email_id=email_id,
                step_name=step_name
            )
            
            # 測試合法轉換: PENDING -> PARSING
            refactor_service.update_status(
                status_id,
                ProcessStatus.PARSING.value
            )
            
            # 測試非法轉換: PARSING -> COMPLETED (跳過中間狀態)
            with pytest.raises(ValueError, match="非法的狀態轉換"):
                refactor_service.update_status(
                    status_id,
                    ProcessStatus.COMPLETED.value
                )
            
        finally:
            # 清理測試數據
            refactor_service.delete_process_status(status_id)
    
    def test_get_processing_history(self, refactor_service):
        """測試獲取處理歷史"""
        email_id = 999995
        
        # 創建多個處理記錄
        status_ids = []
        try:
            for i in range(3):
                step_name = f"history_test_step_{i}"
                status_id = refactor_service.create_process_status(
                    email_id=email_id,
                    step_name=step_name
                )
                status_ids.append(status_id)
            
            # 獲取處理歷史
            history = refactor_service.get_processing_history(email_id)
            
            # 驗證歷史記錄
            assert len(history) >= 3
            assert all(record.email_id == email_id for record in history)
            
        finally:
            # 清理測試數據
            for status_id in status_ids:
                refactor_service.delete_process_status(status_id)
    
    def test_cleanup_completed_records(self, refactor_service):
        """測試清理已完成記錄"""
        email_id = 999994
        step_name = "cleanup_test"
        
        try:
            # 創建並完成一個處理
            status_id = refactor_service.start_processing(
                email_id=email_id,
                step_name=step_name
            )
            
            # 手動設置完成時間為過去時間
            past_time = datetime.now(timezone.utc) - timedelta(days=35)
            refactor_service.update_process_status(
                status_id,
                completed_at=past_time,
                status=ProcessStatus.COMPLETED.value
            )
            
            # 執行清理 (30天)
            cleaned_count = refactor_service.cleanup_completed_process_records(days=30)
            
            # 由於是測試環境，清理數量可能不確定，但應該是非負數
            assert cleaned_count >= 0
            
        except Exception:
            # 測試數據清理失敗，手動清理
            try:
                refactor_service.delete_process_status(status_id)
            except:
                pass
    
    # ===========================================
    # 錯誤處理測試
    # ===========================================
    
    def test_invalid_email_id_handling(self, refactor_service):
        """測試無效 email_id 處理"""
        with pytest.raises(ValueError, match="無效的 email_id"):
            refactor_service.create_process_status(email_id=0)
        
        with pytest.raises(ValueError, match="無效的 email_id"):
            refactor_service.create_process_status(email_id=-1)
        
        with pytest.raises(ValueError, match="無效的 email_id"):
            refactor_service.get_process_status(email_id=None)
    
    def test_invalid_status_id_handling(self, refactor_service):
        """測試無效 status_id 處理"""
        with pytest.raises(ValueError, match="無效的 status_id"):
            refactor_service.update_process_status(status_id=0, status="processing")
        
        with pytest.raises(ValueError, match="無效的 status_id"):
            refactor_service.update_process_status(status_id=-1, status="processing")
    
    def test_nonexistent_status_record(self, refactor_service):
        """測試不存在的狀態記錄"""
        # 使用一個不太可能存在的 ID
        nonexistent_id = 999999999
        
        # 更新不存在的記錄應該返回 False
        result = refactor_service.update_process_status(
            nonexistent_id,
            status=ProcessStatus.COMPLETED.value
        )
        assert result == False
    
    # ===========================================
    # 性能測試
    # ===========================================
    
    def test_caching_mechanism(self, refactor_service):
        """測試快取機制"""
        email_id = 999993
        step_name = "cache_test"
        
        try:
            # 創建狀態
            status_id = refactor_service.create_process_status(
                email_id=email_id,
                step_name=step_name
            )
            
            # 第一次查詢 (從資料庫)
            import time
            start_time = time.time()
            status1 = refactor_service.get_process_status(
                email_id=email_id,
                step_name=step_name
            )
            first_query_time = time.time() - start_time
            
            # 第二次查詢 (從快取)
            start_time = time.time()
            status2 = refactor_service.get_process_status(
                email_id=email_id,
                step_name=step_name
            )
            second_query_time = time.time() - start_time
            
            # 驗證結果一致
            assert status1.id == status2.id
            assert status1.status == status2.status
            
            # 第二次查詢應該更快 (快取效果)
            # 注：在測試環境中這個差異可能很小
            
        finally:
            # 清理測試數據
            refactor_service.delete_process_status(status_id)


class TestProcessingMetricsRefactor:
    """ProcessingMetrics REFACTOR PHASE 測試"""
    
    @pytest.fixture
    def refactor_metrics(self):
        """REFACTOR PHASE ProcessingMetrics 實例"""
        return ProcessingMetrics()
    
    def test_metrics_collection_workflow(self, refactor_metrics):
        """測試指標收集工作流程"""
        email_id = 888888
        processing_type = "test_processing"
        
        # 1. 開始追蹤
        refactor_metrics.track_processing_start(email_id, processing_type)
        
        # 2. 追蹤各個階段
        refactor_metrics.track_processing_stage(email_id, "parsing", 1.5)
        refactor_metrics.track_processing_stage(email_id, "processing", 3.2)
        refactor_metrics.track_processing_stage(email_id, "validation", 0.8)
        
        # 3. 完成追蹤
        total_time = 5.5
        refactor_metrics.track_processing_completion(email_id, total_time, True)
        
        # 4. 獲取指標
        metrics = refactor_metrics.get_performance_metrics("daily")
        
        # 驗證指標結構
        assert isinstance(metrics, dict)
        assert 'average_processing_time' in metrics
        assert 'success_rate' in metrics
        assert 'stage_performance' in metrics
        assert 'throughput' in metrics
        assert 'total_processed' in metrics
    
    def test_stage_performance_tracking(self, refactor_metrics):
        """測試階段性能追蹤"""
        # 追蹤多個郵件的處理階段
        for i in range(5):
            email_id = 777777 + i
            refactor_metrics.track_processing_stage(email_id, "parsing", 1.0 + i * 0.1)
            refactor_metrics.track_processing_stage(email_id, "processing", 2.0 + i * 0.2)
        
        # 獲取性能指標
        metrics = refactor_metrics.get_performance_metrics("daily")
        
        # 驗證階段性能數據
        assert 'stage_performance' in metrics
        stage_perf = metrics['stage_performance']
        
        if 'parsing' in stage_perf:
            parsing_stats = stage_perf['parsing']
            assert 'average_duration' in parsing_stats
            assert 'min_duration' in parsing_stats
            assert 'max_duration' in parsing_stats
            assert 'count' in parsing_stats
    
    def test_success_rate_calculation(self, refactor_metrics):
        """測試成功率計算"""
        # 模擬多個處理完成事件
        successful_count = 8
        failed_count = 2
        
        for i in range(successful_count):
            email_id = 666666 + i
            refactor_metrics.track_processing_completion(email_id, 3.0, True)
        
        for i in range(failed_count):
            email_id = 666666 + successful_count + i
            refactor_metrics.track_processing_completion(email_id, 2.0, False)
        
        # 獲取指標
        metrics = refactor_metrics.get_performance_metrics("daily")
        
        # 驗證成功率計算
        expected_success_rate = successful_count / (successful_count + failed_count)
        assert abs(metrics['success_rate'] - expected_success_rate) < 0.01
    
    def test_metrics_error_handling(self, refactor_metrics):
        """測試指標收集的錯誤處理"""
        # 這些調用應該不會拋出異常
        refactor_metrics.track_processing_start(None, "test")
        refactor_metrics.track_processing_stage(-1, "test", -1.0)
        refactor_metrics.track_processing_completion(0, None, None)
        
        # 獲取指標應該仍然工作
        metrics = refactor_metrics.get_performance_metrics("daily")
        assert isinstance(metrics, dict)


class TestEmailProcessStatusServiceIntegration:
    """完整的整合測試"""
    
    @pytest.fixture
    def integration_service(self):
        """整合測試服務"""
        db = EmailDatabase()
        return EmailProcessStatusService(db)
    
    def test_concurrent_status_updates(self, integration_service):
        """測試並發狀態更新"""
        import threading
        import queue
        
        email_id = 555555
        step_name = "concurrent_test"
        results = queue.Queue()
        
        def create_and_update():
            try:
                status_id = integration_service.create_process_status(
                    email_id=email_id,
                    step_name=f"{step_name}_{threading.current_thread().ident}"
                )
                
                # 快速狀態更新
                integration_service.update_status(
                    status_id,
                    ProcessStatus.PROCESSING.value
                )
                
                integration_service.complete_processing(
                    status_id,
                    {'result': 'success'}
                )
                
                results.put(('success', status_id))
                
            except Exception as e:
                results.put(('error', str(e)))
        
        # 創建多個併發線程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_and_update)
            threads.append(thread)
            thread.start()
        
        # 等待所有線程完成
        for thread in threads:
            thread.join()
        
        # 收集結果
        success_count = 0
        error_count = 0
        status_ids = []
        
        while not results.empty():
            result_type, result_value = results.get()
            if result_type == 'success':
                success_count += 1
                status_ids.append(result_value)
            else:
                error_count += 1
                print(f"併發錯誤: {result_value}")
        
        # 驗證大部分操作成功
        assert success_count >= 3  # 至少 3 個成功
        
        # 清理測試數據
        for status_id in status_ids:
            try:
                integration_service.delete_process_status(status_id)
            except:
                pass
    
    def test_statistics_integration(self, integration_service):
        """測試統計功能整合"""
        # 創建一些測試數據
        test_email_ids = [444441, 444442, 444443]
        status_ids = []
        
        try:
            for email_id in test_email_ids:
                status_id = integration_service.start_processing(
                    email_id=email_id,
                    step_name="statistics_test"
                )
                status_ids.append(status_id)
                
                # 隨機完成或失敗
                if email_id % 2 == 0:
                    integration_service.complete_processing(
                        status_id,
                        {'result': 'completed'}
                    )
                else:
                    integration_service.fail_processing(
                        status_id,
                        "測試失敗"
                    )
            
            # 獲取統計
            stats = integration_service.get_processing_statistics("daily")
            
            # 驗證統計結構
            assert isinstance(stats, dict)
            assert 'total_processed' in stats
            assert 'success_count' in stats
            assert 'failed_count' in stats
            assert 'success_rate' in stats
            assert stats['total_processed'] >= len(test_email_ids)
            
        finally:
            # 清理測試數據
            for status_id in status_ids:
                try:
                    integration_service.delete_process_status(status_id)
                except:
                    pass


if __name__ == "__main__":
    # 運行 REFACTOR PHASE 測試
    pytest.main([__file__, "-v", "--tb=short"])