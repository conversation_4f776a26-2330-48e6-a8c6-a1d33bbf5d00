"""
檔案清理服務模組
提供臨時檔案清理、檔案清理調度器管理、清理狀態監控等功能
"""

import os
from typing import Dict, Any, List, Optional
from pathlib import Path
from loguru import logger
from fastapi import HTTPException
from fastapi.responses import JSONResponse

from backend.shared.infrastructure.adapters.api_utils import ResponseFormatter, LoggingUtils, UploadProcessorManager, SystemConfig


class CleanupService:
    """檔案清理服務類別"""
    
    def __init__(self):
        self.cleanup_scheduler = None
    
    def set_cleanup_scheduler(self, scheduler):
        """設定清理調度器實例"""
        self.cleanup_scheduler = scheduler
        logger.info("[TOOL] 清理調度器已設定")
    
    async def cleanup_temp_files(self) -> Dict[str, Any]:
        """
        清理暫存檔案端點
        
        Returns:
            Dict[str, Any]: 清理結果
        """
        try:
            LoggingUtils.log_api_start("cleanup_temp_files")
            
            from infrastructure.adapters.file_upload import TempFileManager
            
            temp_manager = TempFileManager()
            temp_manager.cleanup_old_files()
            
            result = ResponseFormatter.create_success_response(
                message="暫存檔案清理完成"
            )
            
            LoggingUtils.log_api_success("cleanup_temp_files", "暫存檔案清理完成")
            return result
            
        except Exception as e:
            LoggingUtils.log_api_error("cleanup_temp_files", e)
            raise HTTPException(
                status_code=500,
                detail=f"清理暫存檔案失敗: {str(e)}"
            )
    
    async def get_temp_files_info(self) -> Dict[str, Any]:
        """
        取得暫存檔案資訊
        
        Returns:
            Dict[str, Any]: 暫存檔案列表和統計
        """
        try:
            LoggingUtils.log_api_start("get_temp_files_info")
            
            from infrastructure.adapters.file_upload import TempFileManager
            
            temp_manager = TempFileManager()
            files_info = temp_manager.list_temp_files()
            
            result = {
                "status": "success",
                "message": "成功取得暫存檔案資訊",
                "files_info": files_info,
                "total_files": len(files_info)
            }
            
            LoggingUtils.log_api_success("get_temp_files_info", f"{len(files_info)} 個暫存檔案")
            return result
            
        except Exception as e:
            LoggingUtils.log_api_error("get_temp_files_info", e)
            raise HTTPException(
                status_code=500,
                detail=f"取得暫存檔案資訊失敗: {str(e)}"
            )
    
    async def clear_duplicate_cache(self) -> Dict[str, Any]:
        """
        清[EXCEPT_CHAR]重複上傳快取記錄
        
        Returns:
            Dict[str, Any]: 清[EXCEPT_CHAR]結果
        """
        try:
            LoggingUtils.log_api_start("clear_duplicate_cache")
            
            upload_processor = UploadProcessorManager.get_global_upload_processor()
            cleared_count = upload_processor.clear_all_duplicate_records()
            
            result = {
                "status": "success",
                "message": f"已清[EXCEPT_CHAR] {cleared_count} 個重複上傳記錄",
                "cleared_count": cleared_count
            }
            
            LoggingUtils.log_api_success("clear_duplicate_cache", f"清[EXCEPT_CHAR] {cleared_count} 個記錄")
            return result
            
        except Exception as e:
            LoggingUtils.log_api_error("clear_duplicate_cache", e)
            return ResponseFormatter.create_error_response(
                status_code=500,
                message=f"清[EXCEPT_CHAR]失敗: {str(e)}",
                error_code="CACHE_CLEAR_FAILED"
            )
    
    def get_existing_directories(self, target_directories: List[str] = None) -> List[str]:
        """獲取實際存在的目錄列表"""
        if target_directories is None:
            target_directories = SystemConfig.DEFAULT_DIRECTORIES
        
        existing_dirs = [d for d in target_directories if os.path.exists(d)]
        logger.info(f"[FOLDER] 檢查目錄存在性: {len(existing_dirs)}/{len(target_directories)} 個目錄存在")
        return existing_dirs
    
    async def initialize_cleanup_scheduler(self) -> bool:
        """初始化檔案清理調度器"""
        try:
            import sys
            import os
            # 確保能正確導入FileCleanupScheduler
            sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
            from backend.tasks.services.scheduler import FileCleanupScheduler
            
            self.cleanup_scheduler = FileCleanupScheduler(logger=logger)
            
            # 只清理實際存在的目錄
            existing_dirs = self.get_existing_directories()
            
            if existing_dirs:
                # 啟動定時清理：每1小時檢查，清理24小時前的檔案
                self.cleanup_scheduler.start_cleanup_job(
                    target_directories=existing_dirs,
                    cleanup_interval_hours=1,
                    file_retention_hours=24
                )
                logger.info(f"[OK] 檔案清理調度器已啟動，監控目錄: {existing_dirs}")
                return True
            else:
                logger.warning("[WARNING] 沒有找到需要清理的目錄")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] 檔案清理調度器啟動失敗: {e}")
            return False
    
    async def shutdown_cleanup_scheduler(self):
        """停止檔案清理調度器"""
        if self.cleanup_scheduler and hasattr(self.cleanup_scheduler, 'is_running') and self.cleanup_scheduler.is_running:
            self.cleanup_scheduler.stop_cleanup_job()
            logger.info("[OK] 檔案清理調度器已停止")
    
    async def manual_cleanup_files(self) -> Dict[str, Any]:
        """
        手動觸發檔案清理
        
        Returns:
            清理結果資訊
        """
        try:
            LoggingUtils.log_api_start("manual_cleanup_files")
            
            if not self.cleanup_scheduler:
                raise HTTPException(
                    status_code=503,
                    detail="檔案清理服務尚未初始化"
                )
            
            # 只清理實際存在的目錄
            existing_dirs = self.get_existing_directories()
            
            if not existing_dirs:
                result = {
                    "status": "success",
                    "message": "沒有找到需要清理的目錄",
                    "total_cleaned": 0,
                    "directories": {},
                    "errors": []
                }
                return result
            
            # 執行手動清理
            results = self.cleanup_scheduler.manual_cleanup(
                target_directories=existing_dirs,
                retention_hours=24
            )
            
            result = {
                "status": "success",
                "message": f"清理完成，共清理 {results['total_cleaned']} 個檔案",
                "data": results
            }
            
            LoggingUtils.log_api_success("manual_cleanup_files", f"清理 {results['total_cleaned']} 個檔案")
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            LoggingUtils.log_api_error("manual_cleanup_files", e)
            raise HTTPException(
                status_code=500,
                detail=f"清理失敗: {str(e)}"
            )
    
    async def get_cleanup_status(self) -> Dict[str, Any]:
        """
        獲取檔案清理服務狀態
        
        Returns:
            清理服務狀態資訊
        """
        try:
            LoggingUtils.log_api_start("get_cleanup_status")
            
            if not self.cleanup_scheduler:
                result = {
                    "status": "not_initialized",
                    "message": "檔案清理服務尚未初始化",
                    "scheduler_info": {}
                }
                return result
            
            status_info = self.cleanup_scheduler.get_status()
            
            result = {
                "status": "success",
                "message": "清理服務狀態正常",
                "scheduler_info": status_info
            }
            
            LoggingUtils.log_api_success("get_cleanup_status", "狀態查詢成功")
            return result
            
        except Exception as e:
            LoggingUtils.log_api_error("get_cleanup_status", e)
            raise HTTPException(
                status_code=500,
                detail=f"獲取狀態失敗: {str(e)}"
            )
    
    async def start_scheduler(self) -> Dict[str, Any]:
        """
        啟動清理調度器
        
        Returns:
            啟動結果資訊
        """
        try:
            LoggingUtils.log_api_start("start_scheduler")
            
            if self.cleanup_scheduler and self.cleanup_scheduler.is_running:
                result = {
                    "status": "already_running",
                    "message": "清理調度器已在運行中",
                    "is_running": True
                }
                return result
            
            # 初始化調度器（如果還沒有）
            if not self.cleanup_scheduler:
                success = await self.initialize_cleanup_scheduler()
                if not success:
                    raise HTTPException(
                        status_code=500,
                        detail="初始化清理調度器失敗"
                    )
            
            # 啟動調度器
            existing_dirs = self.get_existing_directories()
            if existing_dirs:
                self.cleanup_scheduler.start_cleanup_job(
                    target_directories=existing_dirs,
                    cleanup_interval_hours=1,
                    file_retention_hours=24
                )
                
                result = {
                    "status": "success",
                    "message": "清理調度器已啟動",
                    "is_running": True,
                    "target_directories": existing_dirs
                }
            else:
                result = {
                    "status": "no_directories",
                    "message": "沒有找到需要清理的目錄",
                    "is_running": False,
                    "target_directories": []
                }
            
            LoggingUtils.log_api_success("start_scheduler", "調度器啟動成功")
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            LoggingUtils.log_api_error("start_scheduler", e)
            raise HTTPException(
                status_code=500,
                detail=f"啟動調度器失敗: {str(e)}"
            )
    
    async def stop_scheduler(self) -> Dict[str, Any]:
        """
        停止清理調度器
        
        Returns:
            停止結果資訊
        """
        try:
            LoggingUtils.log_api_start("stop_scheduler")
            
            if not self.cleanup_scheduler:
                result = {
                    "status": "not_initialized",
                    "message": "清理調度器尚未初始化",
                    "is_running": False
                }
                return result
            
            if not self.cleanup_scheduler.is_running:
                result = {
                    "status": "already_stopped",
                    "message": "清理調度器已停止",
                    "is_running": False
                }
                return result
            
            # 停止調度器
            self.cleanup_scheduler.stop_cleanup_job()
            
            result = {
                "status": "success",
                "message": "清理調度器已停止",
                "is_running": False
            }
            
            LoggingUtils.log_api_success("stop_scheduler", "調度器停止成功")
            return result
            
        except Exception as e:
            LoggingUtils.log_api_error("stop_scheduler", e)
            raise HTTPException(
                status_code=500,
                detail=f"停止調度器失敗: {str(e)}"
            )


# 全域清理服務實例
cleanup_service = CleanupService()


# 依賴注入函數
def get_cleanup_service() -> CleanupService:
    """取得清理服務實例（用於 FastAPI 依賴注入）"""
    return cleanup_service


# 事件處理函數
async def startup_cleanup_service():
    """應用啟動事件：初始化檔案清理調度器"""
    success = await cleanup_service.initialize_cleanup_scheduler()
    return success


async def shutdown_cleanup_service():
    """應用關閉事件：停止檔案清理調度器"""
    await cleanup_service.shutdown_cleanup_scheduler()