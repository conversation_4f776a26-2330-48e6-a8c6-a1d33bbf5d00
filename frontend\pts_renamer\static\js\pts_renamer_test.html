<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PTS Renamer JavaScript Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #2b2b2b;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #3a3a3a;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-pass {
            background-color: #28a745;
            color: white;
        }
        .test-fail {
            background-color: #dc3545;
            color: white;
        }
        .test-info {
            background-color: #17a2b8;
            color: white;
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a9e;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 PTS Renamer JavaScript 測試</h1>
        
        <div id="testResults"></div>
        
        <h3>手動測試</h3>
        <button onclick="testAPIClass()">測試 API 類別</button>
        <button onclick="testUIClass()">測試 UI 類別</button>
        <button onclick="testGlobalFunctions()">測試全域函數</button>
        <button onclick="runAllTests()">執行所有測試</button>
        
        <h3>模擬 API 響應測試</h3>
        <button onclick="testMockUpload()">測試模擬上傳</button>
        <button onclick="testMockPreview()">測試模擬預覽</button>
        <button onclick="testMockProcess()">測試模擬處理</button>
    </div>

    <!-- 載入主要 JavaScript 檔案 -->
    <script src="pts_renamer.js"></script>
    
    <script>
        function addTestResult(message, type = 'info') {
            const resultsContainer = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsContainer.appendChild(resultDiv);
            
            // 自動滾動到最新結果
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function testAPIClass() {
            try {
                const api = new PTSRenamerAPI();
                if (api.baseUrl === '/pts-renamer/api') {
                    addTestResult('✓ PTSRenamerAPI 類別初始化成功', 'pass');
                } else {
                    addTestResult('✗ PTSRenamerAPI 基礎 URL 不正確', 'fail');
                }
                
                if (typeof api.uploadFiles === 'function') {
                    addTestResult('✓ uploadFiles 方法存在', 'pass');
                } else {
                    addTestResult('✗ uploadFiles 方法不存在', 'fail');
                }
                
                if (typeof api.processFiles === 'function') {
                    addTestResult('✓ processFiles 方法存在', 'pass');
                } else {
                    addTestResult('✗ processFiles 方法不存在', 'fail');
                }
                
            } catch (error) {
                addTestResult(`✗ API 類別測試失敗: ${error.message}`, 'fail');
            }
        }

        function testUIClass() {
            try {
                if (window.ptsRenamerUI) {
                    addTestResult('✓ PTSRenamerUI 實例已創建', 'pass');
                    
                    if (typeof window.ptsRenamerUI.addFiles === 'function') {
                        addTestResult('✓ addFiles 方法存在', 'pass');
                    } else {
                        addTestResult('✗ addFiles 方法不存在', 'fail');
                    }
                    
                    if (typeof window.ptsRenamerUI.previewProcessing === 'function') {
                        addTestResult('✓ previewProcessing 方法存在', 'pass');
                    } else {
                        addTestResult('✗ previewProcessing 方法不存在', 'fail');
                    }
                    
                } else {
                    addTestResult('✗ PTSRenamerUI 實例未創建', 'fail');
                }
            } catch (error) {
                addTestResult(`✗ UI 類別測試失敗: ${error.message}`, 'fail');
            }
        }

        function testGlobalFunctions() {
            try {
                if (typeof toggleRenameFields === 'function') {
                    addTestResult('✓ toggleRenameFields 全域函數存在', 'pass');
                } else {
                    addTestResult('✗ toggleRenameFields 全域函數不存在', 'fail');
                }
                
                if (typeof previewProcessing === 'function') {
                    addTestResult('✓ previewProcessing 全域函數存在', 'pass');
                } else {
                    addTestResult('✗ previewProcessing 全域函數不存在', 'fail');
                }
                
                if (typeof executeProcessing === 'function') {
                    addTestResult('✓ executeProcessing 全域函數存在', 'pass');
                } else {
                    addTestResult('✗ executeProcessing 全域函數不存在', 'fail');
                }
                
            } catch (error) {
                addTestResult(`✗ 全域函數測試失敗: ${error.message}`, 'fail');
            }
        }

        function runAllTests() {
            document.getElementById('testResults').innerHTML = '';
            addTestResult('開始執行所有測試...', 'info');
            
            setTimeout(() => testAPIClass(), 100);
            setTimeout(() => testUIClass(), 200);
            setTimeout(() => testGlobalFunctions(), 300);
            setTimeout(() => addTestResult('所有測試完成', 'info'), 400);
        }

        function testMockUpload() {
            addTestResult('測試模擬檔案上傳...', 'info');
            
            // 創建模擬檔案
            const mockFile = new File(['test content'], 'test.zip', { type: 'application/zip' });
            
            if (window.ptsRenamerUI) {
                try {
                    window.ptsRenamerUI.addFiles([mockFile]);
                    addTestResult('✓ 模擬檔案上傳成功', 'pass');
                } catch (error) {
                    addTestResult(`✗ 模擬檔案上傳失敗: ${error.message}`, 'fail');
                }
            } else {
                addTestResult('✗ PTSRenamerUI 未初始化', 'fail');
            }
        }

        function testMockPreview() {
            addTestResult('測試模擬預覽功能...', 'info');
            
            if (window.ptsRenamerUI) {
                try {
                    // 直接調用預覽顯示方法
                    window.ptsRenamerUI.displayPreviewResults([
                        { operation: 'rename', description: '測試重命名' },
                        { operation: 'qc_generation', description: '測試 QC 生成' }
                    ]);
                    addTestResult('✓ 模擬預覽顯示成功', 'pass');
                } catch (error) {
                    addTestResult(`✗ 模擬預覽顯示失敗: ${error.message}`, 'fail');
                }
            } else {
                addTestResult('✗ PTSRenamerUI 未初始化', 'fail');
            }
        }

        function testMockProcess() {
            addTestResult('測試模擬處理功能...', 'info');
            
            if (window.ptsRenamerUI) {
                try {
                    // 測試操作獲取
                    const operations = window.ptsRenamerUI.getSelectedOperations();
                    addTestResult(`✓ 獲取操作列表: [${operations.join(', ')}]`, 'pass');
                    
                    // 測試重命名配置
                    const config = window.ptsRenamerUI.getRenameConfig();
                    addTestResult(`✓ 獲取重命名配置: ${JSON.stringify(config)}`, 'pass');
                    
                } catch (error) {
                    addTestResult(`✗ 模擬處理測試失敗: ${error.message}`, 'fail');
                }
            } else {
                addTestResult('✗ PTSRenamerUI 未初始化', 'fail');
            }
        }

        // 頁面載入完成後自動執行基本測試
        window.addEventListener('load', function() {
            setTimeout(() => {
                addTestResult('頁面載入完成，開始自動測試...', 'info');
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>