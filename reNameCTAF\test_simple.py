import tkinter as tk
from tkinter import messagebox
import os
import glob

def test_pts_files():
    # 測試文件夾路徑
    test_folder = r"D:\BenFolder\LearningCursor\reNameCTAF\GMT_G2515BL_CTAF4_F1_01ENG01"
    
    if os.path.exists(test_folder):
        # 搜尋.pts檔案
        pts_pattern = os.path.join(test_folder, "*.pts")
        pts_files = glob.glob(pts_pattern)
        
        result = f"在文件夾中找到 {len(pts_files)} 個.pts檔案：\n\n"
        for file in pts_files:
            result += f"- {os.path.basename(file)}\n"
        
        messagebox.showinfo("測試結果", result)
    else:
        messagebox.showerror("錯誤", "測試文件夾不存在！")

def main():
    root = tk.Tk()
    root.withdraw()  # 隱藏主視窗
    
    test_pts_files()
    
    root.destroy()

if __name__ == "__main__":
    main()
