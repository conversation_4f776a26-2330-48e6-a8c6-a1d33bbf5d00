"""
PTS Renamer Database Connection

SQLAlchemy database connection class following the existing patterns
in the shared infrastructure.
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from pathlib import Path
from typing import Optional
from contextlib import contextmanager
from .pts_rename_sqlalchemy_models import Base


class PTSRenameDatabaseConnection:
    """
    SQLAlchemy database connection manager for PTS Renamer
    
    Follows the pattern established in the main DatabaseEngine
    for consistency with the existing infrastructure.
    """
    
    def __init__(self, db_path: str = "outlook.db"):
        """
        Initialize database connection
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path
        self.database_url = f"sqlite:///{db_path}"
        self.engine = None
        self.SessionLocal = None
        self._ensure_db_exists()
        self.initialize()
    
    def _ensure_db_exists(self):
        """Ensure database directory exists"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def initialize(self):
        """Initialize SQLAlchemy engine and session"""
        self.engine = create_engine(
            self.database_url,
            echo=False,  # Set to True to see SQL queries
            pool_pre_ping=True,
            connect_args={"check_same_thread": False}
        )
        
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # Create all tables
        Base.metadata.create_all(bind=self.engine)
    
    @contextmanager
    def get_session(self):
        """
        Get SQLAlchemy database session context manager
        
        Provides proper SQLAlchemy session for ORM operations.
        """
        if not self.SessionLocal:
            self.initialize()
        
        session = self.SessionLocal()
        try:
            yield session
        except Exception:
            session.rollback()
            raise
        else:
            session.commit()
        finally:
            session.close()
    
    def close(self):
        """Close database connection"""
        if self.engine:
            self.engine.dispose()


# ============================================================================
# 🔧 Factory Functions
# ============================================================================

def get_pts_database_connection(db_path: str = "outlook.db") -> PTSRenameDatabaseConnection:
    """
    Factory function to get PTS database connection
    
    Args:
        db_path: Path to the database file
        
    Returns:
        PTSRenameDatabaseConnection: Database connection instance
    """
    return PTSRenameDatabaseConnection(db_path)