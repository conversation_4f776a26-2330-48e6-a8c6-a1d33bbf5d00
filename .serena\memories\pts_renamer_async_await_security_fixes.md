# PTS Renamer async/await 和安全驗證修復

## async/await 問題修復

### 問題位置
`backend/pts_renamer/services/pts_rename_upload_service.py`

### 修復內容
1. **第 247 行** - 移除同步方法的 await：
```python
# 修復前（錯誤）
file_content = await file_data.read() if hasattr(file_data, 'read') else file_data

# 修復後（正確）
file_content = file_data.read() if hasattr(file_data, 'read') else file_data
```

2. **第 333-340 行** - 直接使用 ArchiveExtractor 而非 Dramatiq 任務：
```python
# 修復前（async 錯誤）
extraction_result = await some_dramatiq_task(archive_path)

# 修復後（同步正確）
extractor = ArchiveExtractor()
extraction_result = extractor.extract_archive(str(archive_path))
```

## 安全驗證問題修復

### 問題描述
二進位壓縮檔（7z, zip, rar）被錯誤標記為包含惡意內容模式，導致上傳失敗。

### 修復位置
`backend/pts_renamer/services/pts_rename_upload_service.py:445-469`

### 修復邏輯
```python
# 跳過二進位壓縮檔的內容模式掃描
skip_content_scanning = is_archive_file(filename)

if not skip_content_scanning:
    # 只對非壓縮檔進行內容模式掃描
    malicious_patterns = [b'<script', b'javascript:', ...]
    for pattern in malicious_patterns:
        if pattern in content_lower:
            threats_detected.append(f"Suspicious content pattern detected: {pattern}")
else:
    logger.debug(f"[SECURITY] Skipping content pattern scanning for binary archive: {filename}")
```

## 路徑處理修復

### 問題位置
`backend/file_management/adapters/file_upload/temp_file_manager.py`

### 修復內容
確保 Path 物件正確轉換：
```python
# 修復前
directory_path.mkdir(parents=True, exist_ok=True)  # 可能是字串

# 修復後
if isinstance(directory, str):
    directory_path = Path(directory)
else:
    directory_path = directory
directory_path.mkdir(parents=True, exist_ok=True)
```

## JSON 序列化修復

### 問題位置
`backend/pts_renamer/models/pts_rename_models.py`

### 修復內容
為 Pydantic 模型添加 datetime JSON 編碼器：
```python
class Config:
    json_encoders = {
        datetime: lambda v: v.isoformat() if v else None
    }
```

## 測試結果
- ✅ 7z 檔案上傳成功
- ✅ 安全掃描正確跳過二進位內容
- ✅ 檔案解壓縮正常運作
- ✅ 無 async/await 錯誤