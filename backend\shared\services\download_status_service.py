"""
Story 1.2: EmailDownloadStatus 服務層
提供完整的 CRUD 操作和業務邏輯管理
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    EmailDownloadStatusDB, DownloadStatus, validate_status_transition, validate_download_status_data
)


class DownloadStatusRepository:
    """下載狀態資料庫操作倉庫"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def create_download_status(self, email_id: int, **kwargs) -> EmailDownloadStatusDB:
        """
        創建下載狀態記錄
        
        Args:
            email_id: 郵件ID
            **kwargs: 其他可選參數
            
        Returns:
            EmailDownloadStatusDB: 創建的下載狀態記錄
            
        Raises:
            ValueError: 如果資料驗證失敗
            IntegrityError: 如果違反唯一約束
        """
        # 資料驗證
        data = {'email_id': email_id, **kwargs}
        validation_errors = validate_download_status_data(data)
        if validation_errors:
            raise ValueError(f"資料驗證失敗: {', '.join(validation_errors)}")
        
        # 檢查是否已存在
        existing = self.get_by_email_id(email_id)
        if existing:
            raise ValueError(f"郵件 ID {email_id} 的下載狀態記錄已存在")
        
        # 創建記錄
        status_record = EmailDownloadStatusDB(email_id=email_id, **kwargs)
        self.session.add(status_record)
        self.session.commit()
        
        return status_record
    
    def get_by_email_id(self, email_id: int) -> Optional[EmailDownloadStatusDB]:
        """
        根據郵件ID查詢下載狀態 - 使用索引優化
        
        Args:
            email_id: 郵件ID
            
        Returns:
            Optional[EmailDownloadStatusDB]: 下載狀態記錄或None
        """
        return self.session.query(EmailDownloadStatusDB)\
            .filter(EmailDownloadStatusDB.email_id == email_id)\
            .first()
    
    def get_by_id(self, status_id: int) -> Optional[EmailDownloadStatusDB]:
        """
        根據狀態ID查詢下載狀態
        
        Args:
            status_id: 狀態記錄ID
            
        Returns:
            Optional[EmailDownloadStatusDB]: 下載狀態記錄或None
        """
        return self.session.query(EmailDownloadStatusDB)\
            .filter(EmailDownloadStatusDB.id == status_id)\
            .first()
    
    def get_by_status(self, status: DownloadStatus, limit: int = 100) -> List[EmailDownloadStatusDB]:
        """
        根據狀態查詢 - 使用索引優化
        
        Args:
            status: 下載狀態
            limit: 查詢結果數量限制
            
        Returns:
            List[EmailDownloadStatusDB]: 符合條件的下載狀態記錄列表
        """
        return self.session.query(EmailDownloadStatusDB)\
            .filter(EmailDownloadStatusDB.status == status)\
            .limit(limit)\
            .all()
    
    def get_by_status_and_email_ids(self, status: DownloadStatus, 
                                   email_ids: List[int]) -> List[EmailDownloadStatusDB]:
        """
        複合索引查詢 - 根據狀態和郵件ID列表查詢
        
        Args:
            status: 下載狀態
            email_ids: 郵件ID列表
            
        Returns:
            List[EmailDownloadStatusDB]: 符合條件的下載狀態記錄列表
        """
        return self.session.query(EmailDownloadStatusDB)\
            .filter(and_(
                EmailDownloadStatusDB.status == status,
                EmailDownloadStatusDB.email_id.in_(email_ids)
            ))\
            .all()
    
    def update_download_progress(self, status_id: int, **fields) -> bool:
        """
        更新下載進度和狀態
        
        Args:
            status_id: 狀態記錄ID
            **fields: 要更新的欄位
            
        Returns:
            bool: 更新是否成功
            
        Raises:
            ValueError: 如果資料驗證失敗或狀態轉換無效
        """
        status_record = self.get_by_id(status_id)
        if not status_record:
            raise ValueError(f"找不到ID為 {status_id} 的下載狀態記錄")
        
        # 資料驗證
        validation_errors = validate_download_status_data(fields)
        if validation_errors:
            raise ValueError(f"資料驗證失敗: {', '.join(validation_errors)}")
        
        # 狀態轉換驗證
        if 'status' in fields:
            new_status = fields['status']
            if not validate_status_transition(status_record.status, new_status):
                raise ValueError(f"無效的狀態轉換: {status_record.status} -> {new_status}")
        
        # 更新欄位
        for field_name, field_value in fields.items():
            if hasattr(status_record, field_name):
                setattr(status_record, field_name, field_value)
        
        # updated_at 會自動更新
        self.session.commit()
        return True
    
    def update_progress_percentage(self, email_id: int, progress: float, 
                                  downloaded_bytes: int = None) -> bool:
        """
        更新下載進度百分比
        
        Args:
            email_id: 郵件ID
            progress: 進度百分比 (0-100)
            downloaded_bytes: 已下載位元組數 (可選)
            
        Returns:
            bool: 更新是否成功
        """
        status_record = self.get_by_email_id(email_id)
        if not status_record:
            raise ValueError(f"找不到郵件ID為 {email_id} 的下載狀態記錄")
        
        update_fields = {'download_progress': progress}
        if downloaded_bytes is not None:
            update_fields['downloaded_bytes'] = downloaded_bytes
        
        return self.update_download_progress(status_record.id, **update_fields)
    
    def mark_as_downloading(self, email_id: int) -> bool:
        """
        標記為下載中狀態
        
        Args:
            email_id: 郵件ID
            
        Returns:
            bool: 更新是否成功
        """
        return self.update_status_by_email_id(email_id, DownloadStatus.DOWNLOADING, {
            'started_at': datetime.utcnow()
        })
    
    def mark_as_completed(self, email_id: int, downloaded_bytes: int = None) -> bool:
        """
        標記為完成狀態
        
        Args:
            email_id: 郵件ID
            downloaded_bytes: 已下載位元組數 (可選)
            
        Returns:
            bool: 更新是否成功
        """
        update_fields = {
            'status': DownloadStatus.COMPLETED,
            'completed_at': datetime.utcnow(),
            'download_progress': 100.0
        }
        
        if downloaded_bytes is not None:
            update_fields['downloaded_bytes'] = downloaded_bytes
        
        status_record = self.get_by_email_id(email_id)
        if status_record:
            return self.update_download_progress(status_record.id, **update_fields)
        return False
    
    def mark_as_failed(self, email_id: int, error_message: str = None) -> bool:
        """
        標記為失敗狀態
        
        Args:
            email_id: 郵件ID
            error_message: 錯誤訊息 (可選)
            
        Returns:
            bool: 更新是否成功
        """
        update_fields = {'status': DownloadStatus.FAILED}
        
        if error_message:
            update_fields['error_message'] = error_message
        
        return self.update_status_by_email_id(email_id, DownloadStatus.FAILED, update_fields)
    
    def update_status_by_email_id(self, email_id: int, new_status: DownloadStatus, 
                                 additional_fields: Dict[str, Any] = None) -> bool:
        """
        根據郵件ID更新狀態
        
        Args:
            email_id: 郵件ID
            new_status: 新狀態
            additional_fields: 額外要更新的欄位
            
        Returns:
            bool: 更新是否成功
        """
        status_record = self.get_by_email_id(email_id)
        if not status_record:
            return False
        
        update_fields = {'status': new_status}
        if additional_fields:
            update_fields.update(additional_fields)
        
        return self.update_download_progress(status_record.id, **update_fields)
    
    def get_download_statistics(self, period_days: int = 7) -> Dict[str, Any]:
        """
        獲取下載統計 - 優化聚合查詢
        
        Args:
            period_days: 統計期間天數
            
        Returns:
            Dict[str, Any]: 統計資料
        """
        cutoff_date = datetime.utcnow() - timedelta(days=period_days)
        
        # 狀態統計
        status_stats = self.session.query(
            EmailDownloadStatusDB.status,
            func.count(EmailDownloadStatusDB.id).label('count')
        ).filter(
            EmailDownloadStatusDB.created_at >= cutoff_date
        ).group_by(EmailDownloadStatusDB.status).all()
        
        status_counts = {status.value: count for status, count in status_stats}
        
        # 進度統計
        progress_stats = self.session.query(
            func.avg(EmailDownloadStatusDB.download_progress).label('avg_progress'),
            func.min(EmailDownloadStatusDB.download_progress).label('min_progress'),
            func.max(EmailDownloadStatusDB.download_progress).label('max_progress')
        ).filter(
            EmailDownloadStatusDB.created_at >= cutoff_date,
            EmailDownloadStatusDB.download_progress > 0
        ).first()
        
        # 效能統計
        performance_stats = self.session.query(
            func.avg(EmailDownloadStatusDB.download_duration_seconds).label('avg_duration'),
            func.sum(EmailDownloadStatusDB.downloaded_bytes).label('total_bytes')
        ).filter(
            EmailDownloadStatusDB.created_at >= cutoff_date,
            EmailDownloadStatusDB.status == DownloadStatus.COMPLETED
        ).first()
        
        return {
            'period_days': period_days,
            'status_counts': status_counts,
            'progress_stats': {
                'average': float(progress_stats.avg_progress or 0),
                'minimum': float(progress_stats.min_progress or 0),
                'maximum': float(progress_stats.max_progress or 0)
            },
            'performance_stats': {
                'average_duration': float(performance_stats.avg_duration or 0),
                'total_downloaded_bytes': int(performance_stats.total_bytes or 0)
            }
        }
    
    def cleanup_completed_download_records(self, days: int = 30) -> int:
        """
        清理歷史記錄 - 刪除指定天數前的已完成記錄
        
        Args:
            days: 保留天數
            
        Returns:
            int: 清理的記錄數量
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        deleted_count = self.session.query(EmailDownloadStatusDB)\
            .filter(and_(
                EmailDownloadStatusDB.status == DownloadStatus.COMPLETED,
                EmailDownloadStatusDB.completed_at < cutoff_date
            ))\
            .delete()
        
        self.session.commit()
        return deleted_count
    
    def get_pending_downloads(self, limit: int = 50) -> List[EmailDownloadStatusDB]:
        """
        獲取等待下載的記錄
        
        Args:
            limit: 結果數量限制
            
        Returns:
            List[EmailDownloadStatusDB]: 等待下載的記錄列表
        """
        return self.get_by_status(DownloadStatus.PENDING, limit)
    
    def get_failed_downloads(self, limit: int = 50) -> List[EmailDownloadStatusDB]:
        """
        獲取下載失敗的記錄
        
        Args:
            limit: 結果數量限制
            
        Returns:
            List[EmailDownloadStatusDB]: 下載失敗的記錄列表
        """
        return self.get_by_status(DownloadStatus.FAILED, limit)
    
    def get_progress_summary(self, email_ids: List[int]) -> Dict[int, Dict[str, Any]]:
        """
        批量獲取進度摘要
        
        Args:
            email_ids: 郵件ID列表
            
        Returns:
            Dict[int, Dict[str, Any]]: 郵件ID對應的進度摘要
        """
        records = self.session.query(EmailDownloadStatusDB)\
            .filter(EmailDownloadStatusDB.email_id.in_(email_ids))\
            .all()
        
        return {
            record.email_id: {
                'status': record.status.value,
                'progress': record.download_progress,
                'downloaded_bytes': record.downloaded_bytes,
                'file_size_bytes': record.file_size_bytes,
                'created_at': record.created_at.isoformat() if record.created_at else None,
                'updated_at': record.updated_at.isoformat() if record.updated_at else None,
                'completed_at': record.completed_at.isoformat() if record.completed_at else None
            }
            for record in records
        }


class DownloadStatusService:
    """下載狀態業務服務"""
    
    def __init__(self, repository: DownloadStatusRepository):
        self.repository = repository
    
    def start_download(self, email_id: int, file_size_bytes: int = None) -> EmailDownloadStatusDB:
        """
        開始下載流程
        
        Args:
            email_id: 郵件ID
            file_size_bytes: 檔案大小 (可選)
            
        Returns:
            EmailDownloadStatusDB: 下載狀態記錄
        """
        # 檢查是否已存在
        existing = self.repository.get_by_email_id(email_id)
        if existing:
            if existing.status in [DownloadStatus.COMPLETED]:
                raise ValueError(f"郵件 {email_id} 已經下載完成")
            elif existing.status == DownloadStatus.DOWNLOADING:
                return existing  # 返回現有的下載記錄
        
        # 創建或更新記錄
        if existing:
            self.repository.update_status_by_email_id(
                email_id, DownloadStatus.DOWNLOADING, 
                {'started_at': datetime.utcnow()}
            )
            return self.repository.get_by_email_id(email_id)
        else:
            kwargs = {}
            if file_size_bytes:
                kwargs['file_size_bytes'] = file_size_bytes
            
            return self.repository.create_download_status(
                email_id, 
                status=DownloadStatus.DOWNLOADING,
                started_at=datetime.utcnow(),
                **kwargs
            )
    
    def update_progress(self, email_id: int, progress: float, 
                       downloaded_bytes: int = None) -> bool:
        """
        更新下載進度
        
        Args:
            email_id: 郵件ID
            progress: 進度百分比
            downloaded_bytes: 已下載位元組數 (可選)
            
        Returns:
            bool: 更新是否成功
        """
        return self.repository.update_progress_percentage(email_id, progress, downloaded_bytes)
    
    def complete_download(self, email_id: int, downloaded_bytes: int = None) -> bool:
        """
        完成下載
        
        Args:
            email_id: 郵件ID
            downloaded_bytes: 已下載位元組數 (可選)
            
        Returns:
            bool: 更新是否成功
        """
        return self.repository.mark_as_completed(email_id, downloaded_bytes)
    
    def fail_download(self, email_id: int, error_message: str = None) -> bool:
        """
        標記下載失敗
        
        Args:
            email_id: 郵件ID
            error_message: 錯誤訊息 (可選)
            
        Returns:
            bool: 更新是否成功
        """
        return self.repository.mark_as_failed(email_id, error_message)
    
    def get_download_status(self, email_id: int) -> Optional[Dict[str, Any]]:
        """
        獲取下載狀態
        
        Args:
            email_id: 郵件ID
            
        Returns:
            Optional[Dict[str, Any]]: 下載狀態資訊
        """
        record = self.repository.get_by_email_id(email_id)
        if not record:
            return None
        
        return {
            'id': record.id,
            'email_id': record.email_id,
            'status': record.status.value,
            'progress': record.download_progress,
            'downloaded_bytes': record.downloaded_bytes,
            'file_size_bytes': record.file_size_bytes,
            'created_at': record.created_at.isoformat() if record.created_at else None,
            'started_at': record.started_at.isoformat() if record.started_at else None,
            'completed_at': record.completed_at.isoformat() if record.completed_at else None,
            'updated_at': record.updated_at.isoformat() if record.updated_at else None,
            'error_message': record.error_message
        }
    
    def get_statistics(self, period_days: int = 7) -> Dict[str, Any]:
        """
        獲取下載統計
        
        Args:
            period_days: 統計期間
            
        Returns:
            Dict[str, Any]: 統計資料
        """
        return self.repository.get_download_statistics(period_days)
    
    def cleanup_old_records(self, days: int = 30) -> int:
        """
        清理舊記錄
        
        Args:
            days: 保留天數
            
        Returns:
            int: 清理的記錄數量
        """
        return self.repository.cleanup_completed_download_records(days)