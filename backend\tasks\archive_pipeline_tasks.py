"""
解壓縮和完整處理管道任務模組
專門處理解壓縮、檔案處理、程式碼比較的完整工作流程

🎯 功能：
  - 解壓縮 Worker 任務
  - 完整處理管道：解壓縮 → 檔案處理 → 程式碼比較
  - 支援多種壓縮格式 (ZIP, 7Z, RAR, TAR, GZ, BZ2)
  - 整合現有的 Dramatiq 任務系統

🔧 技術要求：
  - 使用 Dramatiq @actor 裝飾器
  - 支援重試機制和錯誤處理
  - 完整的日誌記錄和監控
  - 管道上下文傳遞
"""

import asyncio
import traceback
import json
import os
import shutil
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
import uuid

import dramatiq
from dramatiq import actor
from loguru import logger

# 導入現有管道任務
from .pipeline_tasks import create_full_processing_pipeline


# ============================================================================
# 🗂️ 解壓縮 Worker 任務
# ============================================================================

@actor(
    queue_name="processing_queue",
    max_retries=3,
    time_limit=900000,  # 15分鐘
    store_results=True,
    retry_when=lambda retries_so_far, exception: retries_so_far < 3 and not isinstance(exception, (ValueError, FileNotFoundError))
)
async def extract_archive_task(
    archive_path: str,
    extract_to: str = None,
    pipeline_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    解壓縮檔案的異步任務
    
    Args:
        archive_path: 壓縮檔路徑
        extract_to: 解壓縮目標路徑，如果為 None 則自動生成
        pipeline_context: 管道上下文資訊
        
    Returns:
        Dict[str, Any]: 解壓縮結果
    """
    import time
    start_time = time.time()
    
    # 獲取任務ID
    task_id = None
    try:
        current_message = dramatiq.get_current_message()
        if current_message:
            task_id = current_message.message_id
    except Exception:
        task_id = str(uuid.uuid4())
    
    logger.info(f"[EXTRACT] 開始解壓縮任務: {archive_path}")
    logger.info(f"   任務ID: {task_id}")
    logger.info(f"   目標路徑: {extract_to or '自動生成'}")
    
    try:
        # 動態導入以避免循環依賴
        from backend.file_management.adapters.file_upload.archive_extractor import ArchiveExtractor
        
        # 建立解壓縮器
        extractor = ArchiveExtractor()
        
        # 準備解壓縮參數
        if extract_to is None:
            # 自動生成解壓縮目標路徑
            archive_path_obj = Path(archive_path)
            extract_to = str(archive_path_obj.parent / f"{archive_path_obj.stem}_extracted")
        
        # 確保目標目錄存在
        os.makedirs(extract_to, exist_ok=True)
        
        logger.info(f"   實際目標路徑: {extract_to}")
        
        # 執行解壓縮 - 傳遞目標路徑
        result = extractor.extract_archive(archive_path, extract_to)
        
        # 檢查解壓縮結果
        if result.get('success', False):
            extracted_files = result.get('extracted_files', [])
            actual_extract_dir = result.get('extract_dir', extract_to)
            
            processing_time = time.time() - start_time
            
            # Enhanced verification - check if files actually exist
            actual_file_paths = []
            if Path(actual_extract_dir).exists():
                for file_path in Path(actual_extract_dir).rglob('*'):
                    if file_path.is_file():
                        actual_file_paths.append(str(file_path))
            
            logger.info(f"✅ 解壓縮完成: {len(extracted_files)} 個檔案")
            logger.info(f"   解壓縮目錄: {actual_extract_dir}")
            logger.info(f"   目錄存在: {Path(actual_extract_dir).exists()}")
            logger.info(f"   實際檔案數量: {len(actual_file_paths)}")
            logger.info(f"   處理時間: {processing_time:.2f} 秒")
            
            return {
                'success': True,
                'message': f'解壓縮完成: {len(extracted_files)} 個檔案',
                'extracted_path': actual_extract_dir,
                'extracted_files': extracted_files,
                'verified_files': actual_file_paths,  # Added for verification
                'processing_time': processing_time,
                'task_id': task_id,
                'task_type': 'extract_archive',
                'pipeline_context': pipeline_context or {},
                # 為下一個任務準備參數
                'next_task_params': {
                    'input_path': actual_extract_dir,
                    'source_archive': archive_path
                }
            }
        else:
            error_message = result.get('error', '解壓縮失敗')
            logger.error(f"❌ 解壓縮失敗: {error_message}")
            
            return {
                'success': False,
                'message': f'解壓縮失敗: {error_message}',
                'error': error_message,
                'extracted_path': extract_to,
                'processing_time': time.time() - start_time,
                'task_id': task_id,
                'task_type': 'extract_archive',
                'pipeline_context': pipeline_context or {}
            }
            
    except Exception as e:
        processing_time = time.time() - start_time
        error_message = str(e)
        
        logger.error(f"❌ 解壓縮任務執行錯誤: {error_message}")
        logger.error(f"   堆疊追蹤: {traceback.format_exc()}")
        
        return {
            'success': False,
            'message': f'解壓縮任務執行錯誤: {error_message}',
            'error': error_message,
            'processing_time': processing_time,
            'task_id': task_id,
            'task_type': 'extract_archive',
            'pipeline_context': pipeline_context or {}
        }


# ============================================================================
# 🎯 完整處理管道 - 包含解壓縮、檔案處理和程式碼比較
# ============================================================================

async def create_complete_processing_pipeline(
    input_path: str,
    vendor_code: str = None,
    mo: str = None,
    pd: str = None,
    lot: str = None,
    email_subject: str = "",
    email_body: str = "",
    pipeline_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    創建完整的處理管道：解壓縮 → 檔案處理 → 程式碼比較
    
    Args:
        input_path: 輸入路徑（可以是壓縮檔或目錄）
        vendor_code: 廠商代碼
        mo: MO 編號
        pd: 產品代碼
        lot: 批號
        email_subject: 郵件主旨
        email_body: 郵件內文
        pipeline_context: 管道上下文
        
    Returns:
        Dict[str, Any]: 管道執行結果
    """
    pipeline_id = str(uuid.uuid4())
    pipeline_context = pipeline_context or {}
    pipeline_context.update({
        'pipeline_id': pipeline_id,
        'input_path': input_path,
        'vendor_code': vendor_code,
        'mo': mo,
        'pd': pd,
        'lot': lot,
        'email_subject': email_subject,
        'email_body': email_body,
        'created_at': datetime.now().isoformat(),
        'pipeline_type': 'complete_processing'
    })
    
    logger.info(f"[COMPLETE] 創建完整處理管道: {pipeline_id}")
    logger.info(f"   輸入路徑: {input_path}")
    logger.info(f"   廠商代碼: {vendor_code}")
    
    steps = []
    
    # 檢查輸入是否為壓縮檔
    input_path_obj = Path(input_path)
    is_archive = input_path_obj.suffix.lower() in ['.zip', '.7z', '.rar', '.tar', '.gz', '.bz2']
    
    if is_archive:
        # 步驟 1: 解壓縮
        extract_to = str(input_path_obj.parent / f"{input_path_obj.stem}_extracted")
        steps.append({
            'task_name': 'extract_archive_task',
            'params': {
                'archive_path': input_path,
                'extract_to': extract_to,
                'pipeline_context': pipeline_context
            }
        })
        # 更新後續步驟的輸入路徑
        processing_path = extract_to
    else:
        # 直接使用輸入路徑
        processing_path = input_path
    
    # 步驟 2: 廠商檔案處理（如果有廠商資訊）
    if vendor_code and mo:
        steps.append({
            'task_name': 'process_vendor_files_task',
            'params': {
                'vendor_code': vendor_code,
                'mo': mo,
                'temp_path': processing_path,
                'pd': pd or 'default',
                'lot': lot or mo,
                'email_subject': email_subject,
                'email_body': email_body,
                'pipeline_context': pipeline_context
            }
        })
    
    # 步驟 3: 程式碼比較
    steps.append({
        'task_name': 'run_code_comparison_task',
        'params': {
            'input_path': processing_path,
            'pipeline_context': pipeline_context
        }
    })
    
    logger.info(f"   管道步驟數: {len(steps)}")
    
    # 執行管道
    try:
        # create_full_processing_pipeline 返回 pipeline_id (字符串)，不是 coroutine
        actual_pipeline_id = create_full_processing_pipeline(
            vendor_files=[{
                'vendor_code': vendor_code,
                'mo': mo,
                'temp_path': processing_path,
                'pd': pd or 'default',
                'lot': lot or mo,
                'email_subject': email_subject,
                'email_body': email_body
            }] if vendor_code and mo else [],
            include_code_comparison=True,
            pipeline_context=pipeline_context
        )
        
        result = {
            'success': True,
            'pipeline_id': actual_pipeline_id,
            'original_pipeline_id': pipeline_id,
            'total_steps': len(steps),
            'steps': steps,
            'pipeline_context': pipeline_context
        }
        
        logger.info(f"[OK] 完整處理管道執行完成: {pipeline_id}")
        logger.info(f"   實際管道ID: {actual_pipeline_id}")
        logger.info(f"   總步驟數: {len(steps)}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 完整處理管道執行失敗: {e}")
        return {
            'success': False,
            'error': str(e),
            'pipeline_id': pipeline_id,
            'pipeline_context': pipeline_context
        }


async def trigger_archive_extraction_pipeline(
    archive_path: str,
    extract_to: str = None,
    pipeline_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    觸發解壓縮管道（單純解壓縮）
    
    Args:
        archive_path: 壓縮檔路徑
        extract_to: 解壓縮目標路徑
        pipeline_context: 管道上下文
        
    Returns:
        Dict[str, Any]: 管道執行結果
    """
    pipeline_id = str(uuid.uuid4())
    pipeline_context = pipeline_context or {}
    pipeline_context.update({
        'pipeline_id': pipeline_id,
        'archive_path': archive_path,
        'extract_to': extract_to,
        'created_at': datetime.now().isoformat(),
        'pipeline_type': 'extraction_only'
    })
    
    logger.info(f"[EXTRACT_ONLY] 觸發解壓縮管道: {pipeline_id}")
    logger.info(f"   壓縮檔: {archive_path}")
    logger.info(f"   目標路徑: {extract_to or '自動生成'}")
    
    steps = [
        {
            'task_name': 'extract_archive_task',
            'params': {
                'archive_path': archive_path,
                'extract_to': extract_to,
                'pipeline_context': pipeline_context
            }
        }
    ]
    
    # 執行管道
    try:
        # 直接發送解壓縮任務
        from backend.tasks.services.dramatiq_tasks import extract_archive_task
        
        message = extract_archive_task.send(
            archive_path=archive_path,
            extract_to=extract_to,
            pipeline_context=pipeline_context
        )
        
        result = {
            'success': True,
            'pipeline_id': pipeline_id,
            'task_sent': True,
            'message_id': str(message.message_id) if message else None,
            'steps': steps,
            'pipeline_context': pipeline_context
        }
        
        logger.info(f"[OK] 解壓縮管道執行完成: {pipeline_id}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 解壓縮管道執行失敗: {e}")
        return {
            'success': False,
            'error': str(e),
            'pipeline_id': pipeline_id,
            'pipeline_context': pipeline_context
        }


def get_supported_archive_formats() -> List[str]:
    """
    取得支援的壓縮格式列表
    
    Returns:
        List[str]: 支援的壓縮格式列表
    """
    return ['.zip', '.7z', '.rar', '.tar', '.gz', '.bz2', '.tar.gz', '.tar.bz2']


def is_archive_file(file_path: str) -> bool:
    """
    檢查檔案是否為壓縮檔
    
    Args:
        file_path: 檔案路徑
        
    Returns:
        bool: 是否為壓縮檔
    """
    file_path_obj = Path(file_path)
    
    # 檢查單一副檔名
    if file_path_obj.suffix.lower() in ['.zip', '.7z', '.rar', '.tar', '.gz', '.bz2']:
        return True
    
    # 檢查雙重副檔名
    if len(file_path_obj.suffixes) >= 2:
        double_suffix = ''.join(file_path_obj.suffixes[-2:]).lower()
        if double_suffix in ['.tar.gz', '.tar.bz2']:
            return True
    
    return False


async def create_archive_processing_chain(
    archive_paths: List[str],
    base_extract_dir: str = None,
    pipeline_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    創建批次解壓縮處理鏈
    
    Args:
        archive_paths: 壓縮檔路徑列表
        base_extract_dir: 基礎解壓縮目錄
        pipeline_context: 管道上下文
        
    Returns:
        Dict[str, Any]: 批次處理結果
    """
    pipeline_id = str(uuid.uuid4())
    pipeline_context = pipeline_context or {}
    pipeline_context.update({
        'pipeline_id': pipeline_id,
        'archive_count': len(archive_paths),
        'base_extract_dir': base_extract_dir,
        'created_at': datetime.now().isoformat(),
        'pipeline_type': 'batch_extraction'
    })
    
    logger.info(f"[BATCH] 創建批次解壓縮處理鏈: {pipeline_id}")
    logger.info(f"   壓縮檔數量: {len(archive_paths)}")
    
    results = []
    successful_count = 0
    
    for i, archive_path in enumerate(archive_paths):
        try:
            if base_extract_dir:
                extract_to = os.path.join(base_extract_dir, f"archive_{i+1}")
            else:
                extract_to = None
            
            # 觸發單一解壓縮管道
            result = await trigger_archive_extraction_pipeline(
                archive_path=archive_path,
                extract_to=extract_to,
                pipeline_context=pipeline_context
            )
            
            results.append(result)
            
            if result.get('success', False):
                successful_count += 1
            
        except Exception as e:
            logger.error(f"❌ 處理壓縮檔失敗 {archive_path}: {e}")
            results.append({
                'success': False,
                'error': str(e),
                'archive_path': archive_path
            })
    
    logger.info(f"🏁 批次解壓縮完成: {successful_count}/{len(archive_paths)} 成功")
    
    return {
        'success': successful_count > 0,
        'pipeline_id': pipeline_id,
        'successful_count': successful_count,
        'total_count': len(archive_paths),
        'results': results,
        'pipeline_context': pipeline_context
    }


# 導出所有功能
__all__ = [
    'extract_archive_task',
    'create_complete_processing_pipeline',
    'trigger_archive_extraction_pipeline',
    'create_archive_processing_chain',
    'get_supported_archive_formats',
    'is_archive_file'
]