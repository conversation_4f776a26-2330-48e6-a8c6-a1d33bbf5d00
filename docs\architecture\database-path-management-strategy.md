# Database Path Management & Backup Strategy

## 🎯 Executive Summary

**Issue Resolved**: The system was reading from `data/email_inbox.db` (3 records) instead of the main database `email_inbox.db` (34 records).

**Solution Implemented**: Updated all configuration files to point to the correct main database.

---

## 📊 Current Database Status

### Primary Database
- **Location**: `D:\project\python\outlook_summary\email_inbox.db`
- **Size**: 2,368 KB
- **Records**: 34 emails, 2 senders, 43 attachments
- **Date Range**: 2025-07-24 to 2025-08-08
- **Status**: ✅ **ACTIVE** - System now correctly reads from this database

### Secondary Database (Legacy)
- **Location**: `D:\project\python\outlook_summary\data\email_inbox.db`
- **Size**: 124 KB
- **Records**: 3 emails, 2 senders, 1 attachment
- **Date Range**: 2025-07-16 (older data)
- **Status**: 🔄 **BACKUP** - Contains older test data

---

## 🔧 Configuration Changes Made

### 1. Database Model Configuration
**File**: `src/infrastructure/adapters/database/models.py`
```python
# BEFORE (Line 146)
def __init__(self, database_url: str = "sqlite:///data/email_inbox.db"):

# AFTER (Fixed)
def __init__(self, database_url: str = "sqlite:///email_inbox.db"):
```

### 2. Frontend Configuration
**File**: `frontend/config.py`
```python
# BEFORE (Line 66)
DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///data/email_inbox.db'

# AFTER (Fixed)
DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///email_inbox.db'
```

### 3. Environment Configuration
**File**: `.env` (if exists)
```bash
# Ensure correct database path
DATABASE_URL=sqlite:///email_inbox.db
```

### 4. Backend Services
**Files Updated**:
- `backend/shared/infrastructure/config/config_manager.py`
- `backend/email/infrastructure/repositories/email_repository.py`
- `start_integrated_services.py`

---

## 📂 Database Directory Structure

```
D:\project\python\outlook_summary\
├── email_inbox.db                    # ✅ PRIMARY DATABASE (34 records)
├── data/
│   └── email_inbox.db               # 🔄 BACKUP DATABASE (3 records)
├── database/
│   ├── migrations/                  # Database migration scripts
│   ├── seeds/                       # Seed data for testing
│   └── backups/                     # Automated backup location
└── logs/
    └── database.log                 # Database operation logs
```

---

## 🔄 Database Migration Strategy

### Current Status
- **Schema Version**: Latest (all migrations applied)
- **Data Integrity**: Verified - all 34 records accessible
- **Backup Status**: Legacy data preserved in `data/` directory

### Migration Path
1. **Phase 1**: ✅ Path configuration fixed
2. **Phase 2**: 📋 Implement automated backup system
3. **Phase 3**: 📋 Set up database monitoring
4. **Phase 4**: 📋 Production database migration

---

## 🛠️ Database Management Tools

### 1. Database Inspection Script
```python
# database_inspector.py
import sqlite3
import os

def inspect_database(db_path):
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get table info
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print(f"📊 Database: {db_path}")
    print(f"📈 Size: {os.path.getsize(db_path) / 1024:.1f} KB")
    
    for table in tables:
        table_name = table[0]
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"📋 {table_name}: {count} records")
    
    conn.close()

# Usage
inspect_database("email_inbox.db")
inspect_database("data/email_inbox.db")
```

### 2. Backup Script
```bash
#!/bin/bash
# backup_database.sh

DATE=$(date +%Y%m%d_%H%M%S)
SOURCE_DB="email_inbox.db"
BACKUP_DIR="database/backups"
BACKUP_FILE="${BACKUP_DIR}/email_inbox_${DATE}.db"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create backup
cp $SOURCE_DB $BACKUP_FILE

# Verify backup
if [ -f "$BACKUP_FILE" ]; then
    echo "✅ Backup created: $BACKUP_FILE"
    echo "📊 Size: $(du -h $BACKUP_FILE | cut -f1)"
else
    echo "❌ Backup failed"
    exit 1
fi

# Keep only last 30 backups
find $BACKUP_DIR -name "email_inbox_*.db" -type f -mtime +30 -delete
```

### 3. Health Check Script
```python
# database_health_check.py
import sqlite3
import os
from datetime import datetime

def health_check():
    db_path = "email_inbox.db"
    
    if not os.path.exists(db_path):
        return {"status": "error", "message": "Database not found"}
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if we can read emails
        cursor.execute("SELECT COUNT(*) FROM emails")
        email_count = cursor.fetchone()[0]
        
        # Check latest email date
        cursor.execute("SELECT MAX(created_at) FROM emails")
        latest_email = cursor.fetchone()[0]
        
        # Check database integrity
        cursor.execute("PRAGMA integrity_check")
        integrity = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            "status": "healthy",
            "email_count": email_count,
            "latest_email": latest_email,
            "integrity": integrity,
            "size_kb": round(os.path.getsize(db_path) / 1024, 1),
            "checked_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {"status": "error", "message": str(e)}

# Usage
print(health_check())
```

---

## 📈 Monitoring & Alerting

### Database Metrics to Monitor
1. **Size Growth**: Track database file size over time
2. **Record Count**: Monitor email and attachment counts
3. **Query Performance**: Log slow queries (>1 second)
4. **Connection Pool**: Monitor active connections
5. **Disk Space**: Ensure adequate free space

### Alert Thresholds
- **Database Size**: Alert if >1GB
- **Query Time**: Alert if query >5 seconds
- **Disk Space**: Alert if <500MB free
- **Connection Pool**: Alert if >80% utilization

---

## 🔒 Security & Access Control

### Database Security
- **File Permissions**: Restrict database file access
- **Connection Encryption**: Use SQLCipher for production
- **Access Logging**: Log all database operations
- **Regular Backups**: Automated daily backups

### Access Pattern
```python
# Secure database access pattern
class DatabaseManager:
    def __init__(self):
        self.db_path = os.environ.get('DATABASE_URL', 'sqlite:///email_inbox.db')
        self.connection_pool = None
    
    def get_connection(self):
        # Implement connection pooling
        pass
    
    def execute_query(self, query, params=None):
        # Log query execution
        # Implement timeout
        # Handle errors gracefully
        pass
```

---

## 🚀 Production Recommendations

### 1. Database Migration to PostgreSQL
```yaml
Production Setup:
  Database: PostgreSQL 13+
  Connection Pool: SQLAlchemy + pgbouncer
  Backup Strategy: 
    - Daily full backups
    - Hourly incremental backups
    - Point-in-time recovery
  Monitoring: 
    - Prometheus + Grafana
    - Database-specific metrics
```

### 2. High Availability Setup
```yaml
HA Configuration:
  Primary Database: Write operations
  Read Replicas: Read-only queries
  Failover: Automatic with health checks
  Load Balancing: Read query distribution
```

### 3. Performance Optimization
```sql
-- Recommended indexes for production
CREATE INDEX idx_emails_created_at ON emails(created_at);
CREATE INDEX idx_emails_sender ON emails(sender);
CREATE INDEX idx_emails_status ON emails(status);
CREATE INDEX idx_parsing_results_email_id ON parsing_results(email_id);
CREATE INDEX idx_parsing_results_mo_number ON parsing_results(mo_number);
```

---

## ✅ Verification Steps

### 1. Current Configuration Verification
```bash
# Check current database path in all configs
grep -r "email_inbox.db" . --include="*.py" --include="*.yaml" --include="*.json"

# Verify main database is being used
python -c "
import sqlite3
conn = sqlite3.connect('email_inbox.db')
cursor = conn.cursor()
cursor.execute('SELECT COUNT(*) FROM emails')
print(f'Main DB emails: {cursor.fetchone()[0]}')
conn.close()
"
```

### 2. Application Testing
```python
# Test database connectivity from application
from frontend.app import create_app

app = create_app()
with app.app_context():
    from frontend.shared.database import db
    # Test query
    result = db.session.execute("SELECT COUNT(*) FROM emails").scalar()
    print(f"Application can read {result} emails")
```

### 3. Data Consistency Check
```sql
-- Verify data relationships
SELECT 
    e.id as email_id,
    e.subject,
    pr.mo_number,
    pr.lot_number,
    COUNT(a.id) as attachment_count
FROM emails e
LEFT JOIN parsing_results pr ON e.id = pr.email_id
LEFT JOIN attachments a ON e.id = a.email_id
GROUP BY e.id
ORDER BY e.created_at DESC
LIMIT 5;
```

---

## 🎯 Next Steps

### Immediate (Week 1)
1. ✅ Verify all configurations point to main database
2. 📋 Implement automated backup script
3. 📋 Set up database health monitoring
4. 📋 Create database documentation

### Short Term (Month 1)
1. 📋 Implement database connection pooling
2. 📋 Add query performance monitoring
3. 📋 Set up automated testing for database operations
4. 📋 Create database maintenance procedures

### Long Term (Quarter 1)
1. 📋 Plan PostgreSQL migration
2. 📋 Implement database replication
3. 📋 Set up comprehensive monitoring
4. 📋 Optimize for production workload

---

**Status**: ✅ **Configuration Fixed and Verified**  
**Current Database**: 34 emails successfully accessible  
**Next Priority**: Implement automated backup and monitoring