# PTS Renamer 生產問題修復任務

## 問題識別
基於 2025-08-22 01:55:35 的錯誤日誌：

### 1. Dramatiq 循環導入問題
- **錯誤**: `cannot import name 'process_pts_rename_job_task' from partially initialized module`
- **文件**: `backend/pts_renamer/services/pts_rename_dramatiq_integration.py`
- **原因**: 循環導入導致模組初始化問題

### 2. 缺失 python-multipart 依賴
- **錯誤**: `RuntimeError: Form data requires "python-multipart" to be installed`
- **影響**: Flask 檔案上傳功能無法運作

## 修復要求
- 使用虛擬環境 venv_win_3_11_9
- 驗證方式: 運行 `start_integrated_services.py`
- 清理根目錄下的臨時檔案 (*.py, *.log, *.md 測試檔案)
- 不產生報告檔案，直接更新到 Serena 記憶

## Agent 委派策略
1. python-pro: 修復循環導入問題
2. devops-troubleshooter: 安裝缺失依賴並驗證
3. debugger: 清理臨時檔案並進行最終驗證