# PTS Renamer 檔案上傳重複選擇 Bug 修復

## 問題描述
用戶反映在 http://localhost:5000/pts-renamer/ 頁面中，按了「上傳壓縮檔」後會再次出現檔案選擇對話框，需要重複選擇檔案。

## 問題分析

### 根本原因
檔案上傳流程中存在重複的檔案處理邏輯：

1. **HTML 模板**: 點擊上傳區域觸發檔案選擇 (`onclick="document.getElementById('fileInput').click()"`)
2. **JavaScript**: 檔案被選擇後觸發 `handleFileSelect` 事件
3. **問題**: `<input type="file">` 的值未被清除，導致後續點擊可能重複觸發事件

### 具體問題點
- `frontend/pts_renamer/static/js/pts_renamer.js` 中的 `handleFileSelect` 方法沒有清除 file input 值
- `frontend/pts_renamer/templates/pts_rename_main.html` 中的拖拽處理邏輯有狀態不一致問題
- 重複的檔案處理邏輯導致用戶體驗問題

## 修復方案

### 1. JavaScript 檔案修復
**檔案**: `frontend/pts_renamer/static/js/pts_renamer.js`

**修改1**: `handleFileSelect` 方法添加輸入框清除
```javascript
handleFileSelect(event) {
    const files = Array.from(event.target.files);
    this.addFiles(files);
    
    // 清除檔案輸入框的值，避免重複選擇
    if (event.target) {
        event.target.value = '';
    }
}
```

**修改2**: 拖拽處理添加輸入框清除
```javascript
uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = Array.from(e.dataTransfer.files);
    this.addFiles(files);
    
    // 清除檔案輸入框的值，避免狀態不一致
    if (fileInput) {
        fileInput.value = '';
    }
});
```

### 2. HTML 模板修復
**檔案**: `frontend/pts_renamer/templates/pts_rename_main.html`

**修改1**: `handleFileSelect` 函數添加輸入框清除
```javascript
if (window.ptsRenamerUI) {
    const files = Array.from(fileInput.files);
    window.ptsRenamerUI.addFiles(files);
    // 清除檔案輸入框的值，避免重複選擇
    fileInput.value = '';
}
```

**修改2**: 拖拽處理優化邏輯
```javascript
// 直接處理檔案，不使用 file input
if (window.ptsRenamerUI) {
    window.ptsRenamerUI.addFiles(files);
} else {
    // 設定到 file input 作為備用
    const dt = new DataTransfer();
    files.forEach(file => dt.items.add(file));
    fileInput.files = dt.files;
    
    // 處理檔案
    handleFileSelect();
}
```

## 修復效果

### 修復前
1. 用戶點擊上傳區域 → 選擇檔案 → 檔案被處理
2. 用戶再次點擊上傳區域 → 又彈出檔案選擇對話框 (重複行為)

### 修復後
1. 用戶點擊上傳區域 → 選擇檔案 → 檔案被處理 → input 值被清除
2. 用戶再次點擊上傳區域 → 正常彈出新的檔案選擇對話框

## 技術細節

### File Input 狀態管理
- 每次檔案處理完成後立即清除 `input.value = ''`
- 避免瀏覽器檔案輸入框的狀態殘留
- 確保每次選擇都是全新的操作

### 拖拽與點擊的統一處理
- 拖拽時直接調用 `addFiles` 不經過 file input
- 點擊時經過 file input 但處理後立即清除
- 兩種方式的最終效果保持一致

### 向後兼容性
- 保留備用的檔案處理邏輯 (`else` 分支)
- 確保在 JavaScript 未載入時仍有基本功能
- 不影響現有的檔案驗證和大小限制

## 測試建議

1. **功能測試**:
   - 點擊上傳 → 選擇檔案 → 確認檔案顯示
   - 再次點擊上傳 → 確認新的檔案選擇對話框
   - 拖拽上傳 → 確認檔案正常處理

2. **邊界測試**:
   - 取消檔案選擇對話框 → 確認不會有異常
   - 選擇相同檔案多次 → 確認去重機制正常
   - 混合使用點擊和拖拽 → 確認狀態一致

3. **瀏覽器兼容性**:
   - Chrome, Firefox, Safari, Edge 測試
   - 確認 `DataTransfer` API 支持正常

## 相關檔案

- `frontend/pts_renamer/static/js/pts_renamer.js` - 主要 JavaScript 邏輯
- `frontend/pts_renamer/templates/pts_rename_main.html` - HTML 模板
- Bug 修復日期: 2025-08-22

這個修復解決了用戶體驗問題，使檔案上傳流程更加直觀和可靠。