"""
Story 1.3: EmailDownloadRetryLogDB 表建立 - Green Phase 測試
測試重試記錄模型的基本功能實現
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

from backend.shared.infrastructure.adapters.database.models import Base, EmailDB
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    EmailDownloadStatusDB, EmailDownloadRetryLogDB, DownloadStatus, RetryStrategy, RetryStatus
)


class TestRetryLogGreenPhase:
    """Green Phase: 測試重試記錄模型的基本功能實現"""

    def setup_method(self):
        """設置測試環境"""
        self.engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(self.engine)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.session = self.SessionLocal()

    def teardown_method(self):
        """清理測試環境"""
        self.session.close()
        Base.metadata.drop_all(self.engine)

    def test_retry_status_enum_exists(self):
        """Green Phase: 測試 RetryStatus 枚舉完整實現"""
        # 檢查所有預期的狀態
        expected_statuses = ['SCHEDULED', 'RUNNING', 'SUCCESS', 'FAILED', 'CANCELLED', 'TIMEOUT']
        actual_statuses = [status.name for status in RetryStatus]
        
        for status in expected_statuses:
            assert status in actual_statuses, f"Missing RetryStatus.{status}"
        
        # 檢查值的正確性
        assert RetryStatus.SCHEDULED.value == "scheduled"
        assert RetryStatus.RUNNING.value == "running"
        assert RetryStatus.SUCCESS.value == "success"
        assert RetryStatus.FAILED.value == "failed"
        assert RetryStatus.CANCELLED.value == "cancelled"
        assert RetryStatus.TIMEOUT.value == "timeout"

    def test_enhanced_retry_strategy_enum_exists(self):
        """Green Phase: 測試增強的 RetryStrategy 枚舉"""
        # 檢查所有預期的策略
        expected_strategies = ['LINEAR', 'EXPONENTIAL', 'FIXED_DELAY', 'CUSTOM', 'ADAPTIVE']
        actual_strategies = [strategy.name for strategy in RetryStrategy]
        
        for strategy in expected_strategies:
            assert strategy in actual_strategies, f"Missing RetryStrategy.{strategy}"
        
        # 檢查值的正確性
        assert RetryStrategy.LINEAR.value == "linear"
        assert RetryStrategy.EXPONENTIAL.value == "exponential"
        assert RetryStrategy.FIXED_DELAY.value == "fixed_delay"
        assert RetryStrategy.CUSTOM.value == "custom"
        assert RetryStrategy.ADAPTIVE.value == "adaptive"

    def test_retry_log_enhanced_fields_exist(self):
        """Green Phase: 測試重試記錄模型包含所有增強欄位"""
        # 創建測試數據
        email = EmailDB(
            message_id="<EMAIL>",
            sender="<EMAIL>",
            subject="Test Email",
            body="Test Body", 
            received_time=datetime.utcnow()
        )
        self.session.add(email)
        self.session.commit()

        download_status = EmailDownloadStatusDB(email_id=email.id)
        self.session.add(download_status)
        self.session.commit()

        # 創建包含所有新欄位的重試記錄
        now = datetime.utcnow()
        retry_log = EmailDownloadRetryLogDB(
            download_status_id=download_status.id,
            email_id=email.id,
            retry_attempt=1,
            retry_strategy=RetryStrategy.EXPONENTIAL,
            scheduled_at=now,
            started_at=now + timedelta(seconds=1),
            completed_at=now + timedelta(seconds=5),
            status=RetryStatus.SUCCESS,
            error_type=None,
            error_message=None,
            retry_delay_seconds=60,
            success=True,
            duration_ms=4000
        )

        # 驗證所有欄位都存在且可以設置
        self.session.add(retry_log)
        self.session.commit()

        # 重新查詢驗證數據正確性
        saved_log = self.session.query(EmailDownloadRetryLogDB).filter_by(id=retry_log.id).first()
        assert saved_log is not None
        assert saved_log.email_id == email.id
        assert saved_log.retry_strategy == RetryStrategy.EXPONENTIAL
        assert saved_log.status == RetryStatus.SUCCESS
        assert saved_log.success is True
        assert saved_log.retry_delay_seconds == 60
        assert saved_log.duration_ms == 4000

    def test_retry_log_default_values(self):
        """Green Phase: 測試重試記錄模型的預設值設置"""
        # 創建測試數據
        email = EmailDB(
            message_id="<EMAIL>",
            sender="<EMAIL>",
            subject="Test Email 2",
            body="Test Body",
            received_time=datetime.utcnow()
        )
        self.session.add(email)
        self.session.commit()

        download_status = EmailDownloadStatusDB(email_id=email.id)
        self.session.add(download_status)
        self.session.commit()

        # 創建只有必要欄位的重試記錄
        retry_log = EmailDownloadRetryLogDB(
            download_status_id=download_status.id,
            email_id=email.id,
            retry_attempt=1,
            retry_strategy=RetryStrategy.LINEAR,
            scheduled_at=datetime.utcnow(),
            retry_delay_seconds=30
        )

        self.session.add(retry_log)
        self.session.commit()

        # 驗證預設值
        assert retry_log.status == RetryStatus.SCHEDULED
        assert retry_log.success is False
        assert retry_log.created_at is not None
        assert retry_log.updated_at is not None

    def test_retry_log_validation_constraints(self):
        """Green Phase: 測試重試記錄模型的驗證約束"""
        # 創建測試數據
        email = EmailDB(
            message_id="<EMAIL>",
            sender="<EMAIL>",
            subject="Test Email 3",
            body="Test Body",
            received_time=datetime.utcnow()
        )
        self.session.add(email)
        self.session.commit()

        download_status = EmailDownloadStatusDB(email_id=email.id)
        self.session.add(download_status)
        self.session.commit()

        # 測試無效的重試次數
        with pytest.raises(ValueError, match="Retry attempt must be between 1-10"):
            retry_log = EmailDownloadRetryLogDB(
                download_status_id=download_status.id,
                email_id=email.id,
                retry_attempt=15,  # 超過最大值
                retry_strategy=RetryStrategy.EXPONENTIAL,
                scheduled_at=datetime.utcnow(),
                retry_delay_seconds=60
            )
            self.session.add(retry_log)
            self.session.commit()

        # 測試無效的延遲時間
        with pytest.raises(ValueError, match="Retry delay must be between 0-3600 seconds"):
            retry_log = EmailDownloadRetryLogDB(
                download_status_id=download_status.id,
                email_id=email.id,
                retry_attempt=2,
                retry_strategy=RetryStrategy.EXPONENTIAL,
                scheduled_at=datetime.utcnow(),
                retry_delay_seconds=7200  # 超過最大值
            )
            self.session.add(retry_log)
            self.session.commit()

    def test_retry_log_time_validation(self):
        """Green Phase: 測試重試記錄模型的時間驗證"""
        # 創建測試數據
        email = EmailDB(
            message_id="<EMAIL>",
            sender="<EMAIL>",
            subject="Test Email 4",
            body="Test Body",
            received_time=datetime.utcnow()
        )
        self.session.add(email)
        self.session.commit()

        download_status = EmailDownloadStatusDB(email_id=email.id)
        self.session.add(download_status)
        self.session.commit()

        # 創建一個記錄並直接測試時間邏輯驗證方法
        now = datetime.utcnow()
        retry_log = EmailDownloadRetryLogDB(
            download_status_id=download_status.id,
            email_id=email.id,
            retry_attempt=1,
            retry_strategy=RetryStrategy.LINEAR,
            scheduled_at=now,
            retry_delay_seconds=60
        )

        # 手動直接設置屬性來測試時間邏輯驗證（避免 SQLAlchemy 驗證器）
        # 使用 __dict__ 來直接設置，避免觸發 validates 裝飾器
        retry_log.__dict__['scheduled_at'] = now + timedelta(hours=1)
        retry_log.__dict__['started_at'] = now  # started_at < scheduled_at
        retry_log.__dict__['completed_at'] = now + timedelta(hours=2)

        # 使用業務規則驗證方法
        errors = retry_log.validate_time_logic()
        assert len(errors) > 0
        
        # 檢查錯誤訊息
        error_messages = " ".join(errors).lower()
        assert "開始時間不能早於調度時間" in errors or "started" in error_messages

    def test_retry_log_business_rules_validation(self):
        """Green Phase: 測試重試記錄模型的業務規則驗證"""
        # 創建測試數據
        email = EmailDB(
            message_id="<EMAIL>",
            sender="<EMAIL>",
            subject="Test Email 5",
            body="Test Body",
            received_time=datetime.utcnow()
        )
        self.session.add(email)
        self.session.commit()

        download_status = EmailDownloadStatusDB(email_id=email.id)
        self.session.add(download_status)
        self.session.commit()

        # 測試成功標誌與狀態不一致
        retry_log = EmailDownloadRetryLogDB(
            download_status_id=download_status.id,
            email_id=email.id,
            retry_attempt=1,
            retry_strategy=RetryStrategy.EXPONENTIAL,
            scheduled_at=datetime.utcnow(),
            retry_delay_seconds=60,
            status=RetryStatus.FAILED,
            success=True  # 狀態不一致
        )

        errors = retry_log.validate_business_rules()
        assert len(errors) > 0
        assert "成功標誌與狀態不一致" in errors

    def test_retry_log_indexes_exist(self):
        """Green Phase: 測試重試記錄模型包含性能索引"""
        retry_log_table = EmailDownloadRetryLogDB.__table__
        
        # 檢查預期的索引
        expected_indexes = [
            'idx_retry_log_download_status_id',
            'idx_retry_log_email_id',
            'idx_retry_log_status',
            'idx_retry_log_scheduled_at',
            'idx_retry_log_retry_strategy',
            'idx_retry_log_composite',
            'idx_retry_log_error_type'
        ]

        actual_indexes = [idx.name for idx in retry_log_table.indexes if idx.name]
        
        for expected_idx in expected_indexes:
            assert expected_idx in actual_indexes, f"Missing index: {expected_idx}"

    def test_retry_log_relationships(self):
        """Green Phase: 測試重試記錄模型的關聯關係"""
        # 創建測試數據
        email = EmailDB(
            message_id="<EMAIL>",
            sender="<EMAIL>",
            subject="Test Email 6",
            body="Test Body",
            received_time=datetime.utcnow()
        )
        self.session.add(email)
        self.session.commit()

        download_status = EmailDownloadStatusDB(email_id=email.id)
        self.session.add(download_status)
        self.session.commit()

        retry_log = EmailDownloadRetryLogDB(
            download_status_id=download_status.id,
            email_id=email.id,
            retry_attempt=1,
            retry_strategy=RetryStrategy.ADAPTIVE,
            scheduled_at=datetime.utcnow(),
            retry_delay_seconds=120
        )

        self.session.add(retry_log)
        self.session.commit()

        # 測試關聯關係
        assert retry_log.download_status == download_status
        assert retry_log.email == email
        assert retry_log in email.retry_logs
        assert retry_log in download_status.retry_logs


if __name__ == "__main__":
    pytest.main([__file__, "-v"])