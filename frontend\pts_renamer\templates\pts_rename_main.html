<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PTS檔案批量重命名工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #2b2b2b;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #3a3a3a;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #555;
            padding-bottom: 20px;
        }

        .header h1 {
            color: #ffffff;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .header .subtitle {
            color: #aaaaaa;
            font-size: 14px;
        }

        .section {
            background-color: #404040;
            border: 1px solid #555;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .section-title {
            color: #ffffff;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #666;
        }

        .upload-area {
            background-color: #2b2b2b;
            border: 2px dashed #666;
            border-radius: 8px;
            min-height: 150px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            margin-bottom: 15px;
        }

        .upload-area:hover {
            border-color: #007acc;
            background-color: #353535;
        }

        .upload-area.dragover {
            border-color: #28a745;
            background-color: #353535;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 48px;
            color: #666;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #cccccc;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #888;
            font-size: 14px;
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .uploaded-files {
            margin-top: 15px;
        }

        .file-item {
            background-color: #2b2b2b;
            border: 1px solid #555;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .file-info {
            display: flex;
            align-items: center;
            flex-grow: 1;
        }

        .file-icon {
            font-size: 24px;
            margin-right: 12px;
            color: #007acc;
        }

        .file-details {
            flex-grow: 1;
        }

        .file-name {
            color: #ffffff;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .file-size {
            color: #aaa;
            font-size: 12px;
        }

        .remove-file-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
        }

        .remove-file-btn:hover {
            background-color: #c82333;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            background-color: #007acc;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #005a9e;
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        .btn-success {
            background-color: #28a745;
        }

        .btn-success:hover {
            background-color: #1e7e34;
        }

        .btn-danger {
            background-color: #dc3545;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .checkbox-group {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #007acc;
        }

        .checkbox-item label {
            color: #ffffff;
            font-size: 14px;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .form-group input[type="text"] {
            width: 100%;
            background-color: #2b2b2b;
            color: white;
            border: 1px solid #666;
            border-radius: 4px;
            padding: 10px;
            font-size: 14px;
        }

        .form-group input[type="text"]:focus {
            outline: none;
            border-color: #007acc;
            box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
        }

        .preview-list {
            background-color: #2b2b2b;
            border: 1px solid #666;
            border-radius: 6px;
            min-height: 150px;
            padding: 10px;
            overflow-y: auto;
            max-height: 200px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .preview-item {
            padding: 4px 0;
            border-bottom: 1px solid #444;
            color: #cccccc;
        }

        .preview-item:last-child {
            border-bottom: none;
        }

        .preview-item.success {
            color: #28a745;
        }

        .preview-item.error {
            color: #dc3545;
        }

        .preview-item.info {
            color: #17a2b8;
        }

        .execute-section {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #555;
        }

        .execute-btn {
            background-color: #007acc;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px 40px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 200px;
        }

        .execute-btn:hover {
            background-color: #005a9e;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 122, 204, 0.3);
        }

        .execute-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .hidden {
            display: none !important;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready {
            background-color: #28a745;
        }

        .status-processing {
            background-color: #ffc107;
            animation: pulse 1.5s infinite;
        }

        .status-error {
            background-color: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .progress-bar {
            width: 100%;
            background-color: #444;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 20px;
            background-color: #007acc;
            transition: width 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .download-section {
            background-color: #28a745;
            border: 1px solid #1e7e34;
        }

        .download-section .section-title {
            color: #ffffff;
        }

        .download-item {
            background-color: #2b2b2b;
            border: 1px solid #555;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .download-info {
            display: flex;
            align-items: center;
            flex-grow: 1;
        }

        .download-icon {
            font-size: 24px;
            margin-right: 15px;
            color: #28a745;
        }

        .download-details {
            flex-grow: 1;
        }

        .download-name {
            color: #ffffff;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .download-meta {
            color: #aaa;
            font-size: 12px;
        }

        .download-btn {
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-size: 14px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .download-btn:hover {
            background-color: #1e7e34;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .checkbox-group {
                flex-direction: column;
                gap: 10px;
            }

            .file-item, .download-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .file-info, .download-info {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 PTS檔案批量重命名工具</h1>
            <div class="subtitle">專業的半導體測試檔案管理系統 - 簡化版本</div>
        </div>

        <!-- 檔案上傳區域 -->
        <div class="section">
            <div class="section-title">📤 上傳壓縮檔案</div>
            <div class="upload-area" id="uploadArea">
                <input type="file" id="fileInput" class="file-input" accept=".zip,.7z,.rar" multiple>
                <div class="upload-icon">📦</div>
                <div class="upload-text">點擊或拖拽檔案到此處上傳</div>
                <div class="upload-hint">支援格式: ZIP, 7Z, RAR (包含 .pts 檔案)</div>
            </div>
            
            <div class="uploaded-files" id="uploadedFiles">
                <!-- 上傳的檔案列表會在這裡顯示 -->
            </div>
        </div>

        <!-- 處理選項區域 -->
        <div class="section">
            <div class="section-title">⚙️ 處理選項</div>
            
            <div class="checkbox-group">
                <div class="checkbox-item">
                    <input type="checkbox" id="renameEnabled" onchange="toggleRenameFields()">
                    <label for="renameEnabled">重新命名</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="qcEnabled">
                    <label for="qcEnabled">增加QC</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="createDirectories">
                    <label for="createDirectories">創建目錄</label>
                </div>
            </div>

            <div id="renameFields" class="hidden">
                <div class="form-group">
                    <label for="oldPattern">替換前的檔案名稱模式:</label>
                    <input type="text" id="oldPattern" placeholder="例如: old_ 或 file_(\\d+)">
                </div>
                <div class="form-group">
                    <label for="newPattern">替換後的檔案名稱模式:</label>
                    <input type="text" id="newPattern" placeholder="例如: new_ 或 newfile_$1">
                </div>
            </div>
        </div>

        <!-- 預覽結果區域 -->
        <div class="section">
            <div class="section-title">👁️ 處理預覽</div>
            <div class="preview-list" id="previewList">
                <div style="color: #888; text-align: center; padding: 20px;">
                    請上傳壓縮檔案後點擊「預覽處理結果」查看效果
                </div>
            </div>
            <div class="button-group" style="margin-top: 15px;">
                <button class="btn btn-secondary" onclick="previewProcessing()">預覽處理結果</button>
            </div>
        </div>

        <!-- 執行按鈕 -->
        <div class="execute-section">
            <button class="execute-btn" id="executeBtn" onclick="executeProcessing()">
                <span class="status-indicator status-ready"></span>
                開始處理
            </button>
            <div class="progress-bar hidden" id="progressBar">
                <div class="progress-fill" id="progressFill" style="width: 0%">0%</div>
            </div>
        </div>

        <!-- 下載區域 -->
        <div class="section download-section hidden" id="downloadSection">
            <div class="section-title">⬇️ 處理完成 - 下載結果</div>
            <div id="downloadList">
                <!-- 下載連結會在這裡顯示 -->
            </div>
        </div>
    </div>

    <script>
        // 確保與後端 API 相容的函數名稱
        function toggleRenameFields() {
            const renameFields = document.getElementById('renameFields');
            const renameEnabled = document.getElementById('renameEnabled').checked;
            
            if (renameEnabled) {
                renameFields.classList.remove('hidden');
            } else {
                renameFields.classList.add('hidden');
            }
        }

        function previewProcessing() {
            // 這個函數會被後端 JavaScript 覆蓋或增強
            console.log('Preview processing called');
        }

        function executeProcessing() {
            // 這個函數會被後端 JavaScript 覆蓋或增強
            console.log('Execute processing called');
        }

        // 處理檔案選擇事件
        function handleFileSelect() {
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            
            if (fileInput && fileInput.files.length > 0) {
                // 如果 ptsRenamerUI 存在，使用它處理檔案
                if (window.ptsRenamerUI) {
                    // 檢查上傳狀態鎖
                    if (window.ptsRenamerUI.isUploading) {
                        console.log('檔案正在處理中，跳過重複處理');
                        fileInput.value = '';
                        return;
                    }
                    
                    const files = Array.from(fileInput.files);
                    window.ptsRenamerUI.addFiles(files);
                    // 清除檔案輸入框的值，避免重複選擇
                    fileInput.value = '';
                } else {
                    // 備用處理：顯示檔案列表
                    const filesContainer = document.getElementById('uploadedFiles');
                    const files = Array.from(fileInput.files);
                    
                    let html = '';
                    files.forEach((file, index) => {
                        html += `
                            <div class="file-item">
                                <div class="file-info">
                                    <div class="file-icon">📦</div>
                                    <div class="file-details">
                                        <div class="file-name">${file.name}</div>
                                        <div class="file-size">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    
                    if (filesContainer) {
                        filesContainer.innerHTML = html;
                    }
                    // 清除檔案輸入框的值，避免重複選擇
                    fileInput.value = '';
                }
            }
        }

        // 基本的拖拽功能
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        if (uploadArea && fileInput) {
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = Array.from(e.dataTransfer.files);
                
                // 直接處理檔案，不使用 file input
                if (window.ptsRenamerUI) {
                    window.ptsRenamerUI.addFiles(files);
                } else {
                    // 設定到 file input 作為備用
                    const dt = new DataTransfer();
                    files.forEach(file => dt.items.add(file));
                    fileInput.files = dt.files;
                    
                    // 處理檔案
                    handleFileSelect();
                }
            });
            
            // 監聽檔案選擇事件
            fileInput.addEventListener('change', handleFileSelect);
        }
    </script>

    <!-- 載入主要 JavaScript 檔案 -->
    <script src="{{ url_for('pts_renamer.static', filename='js/pts_renamer.js') }}"></script>
</body>
</html>