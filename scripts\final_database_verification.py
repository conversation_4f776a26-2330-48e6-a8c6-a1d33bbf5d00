#!/usr/bin/env python3
"""
最終數據庫驗證腳本
專注於生產環境數據庫的連接和完整性驗證
"""

import os
import sys
import sqlite3
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 設定中文編碼環境變數（Windows 兼容性）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['LANG'] = 'zh_TW.UTF-8'


class FinalDatabaseVerifier:
    """最終數據庫驗證器"""
    
    def __init__(self):
        self.verification_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'database_connections': {},
            'service_configurations': {},
            'data_integrity': {},
            'summary': {},
            'recommendations': []
        }
    
    def run_final_verification(self) -> Dict[str, Any]:
        """執行最終驗證"""
        print("🔍 最終數據庫連接與完整性驗證")
        print("=" * 60)
        print("專注於生產環境數據庫驗證，確保重構後系統正常運作")
        print("=" * 60)
        
        # 1. 驗證生產數據庫連接
        self._verify_production_databases()
        
        # 2. 驗證服務配置
        self._verify_service_configs()
        
        # 3. 驗證數據完整性
        self._verify_data_integrity()
        
        # 4. 執行功能測試
        self._perform_functional_tests()
        
        # 5. 生成最終報告
        self._generate_final_report()
        
        return self.verification_results
    
    def _verify_production_databases(self):
        """驗證生產數據庫連接"""
        print("📊 驗證生產數據庫連接...")
        
        # 生產環境使用的數據庫
        production_dbs = {
            'primary_email_db': 'data/email_inbox.db',
            'secondary_email_db': 'email_inbox.db',
            'frontend_email_db': 'frontend/email_inbox.db',
            'outlook_db': 'outlook.db',
            'task_status_db': 'data/eqc_task_status.db'
        }
        
        for db_name, db_path in production_dbs.items():
            print(f"\n  🔍 檢查 {db_name}: {db_path}")
            
            result = {
                'path': db_path,
                'exists': False,
                'readable': False,
                'writable': False,
                'connection_ok': False,
                'integrity_ok': False,
                'tables': [],
                'size_bytes': 0,
                'error': None
            }
            
            try:
                # 檢查文件存在性
                if not Path(db_path).exists():
                    result['error'] = '數據庫文件不存在'
                    print(f"    ❌ 文件不存在")
                    self.verification_results['database_connections'][db_name] = result
                    continue
                
                result['exists'] = True
                result['size_bytes'] = Path(db_path).stat().st_size
                
                # 檢查文件權限
                result['readable'] = os.access(db_path, os.R_OK)
                result['writable'] = os.access(db_path, os.W_OK)
                
                if not result['readable']:
                    result['error'] = '數據庫文件不可讀'
                    print(f"    ❌ 文件不可讀")
                    self.verification_results['database_connections'][db_name] = result
                    continue
                
                # 測試數據庫連接
                conn = sqlite3.connect(db_path, timeout=10.0)
                conn.row_factory = sqlite3.Row
                
                # 獲取表列表
                tables_result = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
                result['tables'] = [row[0] for row in tables_result]
                
                # 檢查完整性
                integrity_result = conn.execute("PRAGMA integrity_check").fetchone()
                result['integrity_ok'] = integrity_result and integrity_result[0] == 'ok'
                
                # 測試基本查詢
                conn.execute("SELECT 1").fetchone()
                result['connection_ok'] = True
                
                conn.close()
                
                print(f"    ✅ 連接成功")
                print(f"    📊 大小: {result['size_bytes']:,} bytes")
                print(f"    📋 表數量: {len(result['tables'])}")
                print(f"    🔍 完整性: {'通過' if result['integrity_ok'] else '失敗'}")
                
            except Exception as e:
                result['error'] = str(e)
                print(f"    ❌ 連接失敗: {e}")
            
            self.verification_results['database_connections'][db_name] = result
    
    def _verify_service_configs(self):
        """驗證服務配置"""
        print(f"\n⚙️  驗證服務配置...")
        
        # 1. Flask 前端配置
        print(f"  🌐 Flask 前端配置...")
        try:
            from frontend.config import Config, config
            
            flask_config = {
                'config_loaded': True,
                'environments': {}
            }
            
            for env_name in ['development', 'testing', 'production']:
                if env_name in config:
                    env_config = config[env_name]
                    flask_config['environments'][env_name] = {
                        'database_url': getattr(env_config, 'DATABASE_URL', None),
                        'host': getattr(env_config, 'HOST', None),
                        'port': getattr(env_config, 'PORT', None)
                    }
            
            self.verification_results['service_configurations']['flask_frontend'] = flask_config
            print(f"    ✅ Flask 前端配置正常")
            
        except Exception as e:
            self.verification_results['service_configurations']['flask_frontend'] = {
                'config_loaded': False,
                'error': str(e)
            }
            print(f"    ❌ Flask 前端配置失敗: {e}")
        
        # 2. 郵件服務配置
        print(f"  📧 郵件服務配置...")
        try:
            from backend.shared.infrastructure.adapters.database.models import db_engine
            
            email_config = {
                'database_engine_loaded': True,
                'database_url': db_engine.database_url
            }
            
            self.verification_results['service_configurations']['email_service'] = email_config
            print(f"    ✅ 郵件服務配置正常")
            print(f"    📊 數據庫 URL: {db_engine.database_url}")
            
        except Exception as e:
            self.verification_results['service_configurations']['email_service'] = {
                'database_engine_loaded': False,
                'error': str(e)
            }
            print(f"    ❌ 郵件服務配置失敗: {e}")
        
        # 3. EQC 服務配置
        print(f"  📋 EQC 服務配置...")
        try:
            from backend.shared.infrastructure.database.task_status_db import get_task_status_db
            
            task_db = get_task_status_db()
            
            eqc_config = {
                'task_db_loaded': True,
                'database_path': task_db.db_path
            }
            
            self.verification_results['service_configurations']['eqc_service'] = eqc_config
            print(f"    ✅ EQC 服務配置正常")
            print(f"    📊 數據庫路徑: {task_db.db_path}")
            
        except Exception as e:
            self.verification_results['service_configurations']['eqc_service'] = {
                'task_db_loaded': False,
                'error': str(e)
            }
            print(f"    ❌ EQC 服務配置失敗: {e}")
    
    def _verify_data_integrity(self):
        """驗證數據完整性"""
        print(f"\n🔍 驗證數據完整性...")
        
        # 檢查郵件數據庫的數據
        email_dbs = ['data/email_inbox.db', 'email_inbox.db', 'frontend/email_inbox.db']
        
        for db_path in email_dbs:
            if not Path(db_path).exists():
                continue
                
            print(f"  📧 檢查郵件數據庫: {db_path}")
            
            try:
                conn = sqlite3.connect(db_path, timeout=10.0)
                conn.row_factory = sqlite3.Row
                
                # 檢查郵件表
                tables = [row[0] for row in conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()]
                
                integrity_data = {
                    'tables': tables,
                    'email_count': 0,
                    'sender_count': 0,
                    'attachment_count': 0,
                    'has_email_table': False,
                    'has_sender_table': False,
                    'has_attachment_table': False
                }
                
                # 檢查郵件表
                if 'emails' in tables:
                    integrity_data['has_email_table'] = True
                    email_count = conn.execute("SELECT COUNT(*) FROM emails").fetchone()[0]
                    integrity_data['email_count'] = email_count
                    print(f"    📊 郵件數量: {email_count}")
                
                # 檢查寄件者表
                if 'senders' in tables:
                    integrity_data['has_sender_table'] = True
                    sender_count = conn.execute("SELECT COUNT(*) FROM senders").fetchone()[0]
                    integrity_data['sender_count'] = sender_count
                    print(f"    👤 寄件者數量: {sender_count}")
                
                # 檢查附件表
                if 'attachments' in tables:
                    integrity_data['has_attachment_table'] = True
                    attachment_count = conn.execute("SELECT COUNT(*) FROM attachments").fetchone()[0]
                    integrity_data['attachment_count'] = attachment_count
                    print(f"    📎 附件數量: {attachment_count}")
                
                conn.close()
                
                self.verification_results['data_integrity'][db_path] = integrity_data
                print(f"    ✅ 數據完整性檢查完成")
                
            except Exception as e:
                self.verification_results['data_integrity'][db_path] = {
                    'error': str(e)
                }
                print(f"    ❌ 數據完整性檢查失敗: {e}")
        
        # 檢查任務數據庫的數據
        task_db_path = 'data/eqc_task_status.db'
        if Path(task_db_path).exists():
            print(f"  📋 檢查任務數據庫: {task_db_path}")
            
            try:
                conn = sqlite3.connect(task_db_path, timeout=10.0)
                conn.row_factory = sqlite3.Row
                
                task_integrity = {
                    'execution_count': 0,
                    'status_count': 0,
                    'has_execution_table': False,
                    'has_status_table': False
                }
                
                # 檢查任務執行表
                if conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='eqc_task_execution'").fetchone():
                    task_integrity['has_execution_table'] = True
                    execution_count = conn.execute("SELECT COUNT(*) FROM eqc_task_execution").fetchone()[0]
                    task_integrity['execution_count'] = execution_count
                    print(f"    📊 任務執行記錄: {execution_count}")
                
                # 檢查任務狀態表
                if conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='eqc_task_status'").fetchone():
                    task_integrity['has_status_table'] = True
                    status_count = conn.execute("SELECT COUNT(*) FROM eqc_task_status").fetchone()[0]
                    task_integrity['status_count'] = status_count
                    print(f"    📊 任務狀態記錄: {status_count}")
                
                conn.close()
                
                self.verification_results['data_integrity'][task_db_path] = task_integrity
                print(f"    ✅ 任務數據完整性檢查完成")
                
            except Exception as e:
                self.verification_results['data_integrity'][task_db_path] = {
                    'error': str(e)
                }
                print(f"    ❌ 任務數據完整性檢查失敗: {e}")
    
    def _perform_functional_tests(self):
        """執行功能測試"""
        print(f"\n🧪 執行功能測試...")
        
        functional_results = {
            'database_read_test': False,
            'database_write_test': False,
            'service_import_test': False
        }
        
        # 1. 數據庫讀取測試
        print(f"  📖 數據庫讀取測試...")
        try:
            # 測試主要郵件數據庫讀取
            main_db = 'data/email_inbox.db'
            if Path(main_db).exists():
                conn = sqlite3.connect(main_db, timeout=5.0)
                result = conn.execute("SELECT COUNT(*) FROM emails").fetchone()
                conn.close()
                functional_results['database_read_test'] = True
                print(f"    ✅ 數據庫讀取測試通過")
            else:
                print(f"    ⚠️  主數據庫不存在，跳過讀取測試")
        except Exception as e:
            print(f"    ❌ 數據庫讀取測試失敗: {e}")
        
        # 2. 數據庫寫入測試（使用測試表）
        print(f"  ✏️  數據庫寫入測試...")
        try:
            main_db = 'data/email_inbox.db'
            if Path(main_db).exists():
                conn = sqlite3.connect(main_db, timeout=5.0)
                
                # 創建測試表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS verification_test (
                        id INTEGER PRIMARY KEY,
                        test_data TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 插入測試數據
                test_data = f"verification_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                conn.execute("INSERT INTO verification_test (test_data) VALUES (?)", (test_data,))
                
                # 驗證插入
                result = conn.execute("SELECT test_data FROM verification_test WHERE test_data = ?", (test_data,)).fetchone()
                
                if result:
                    functional_results['database_write_test'] = True
                    print(f"    ✅ 數據庫寫入測試通過")
                    
                    # 清理測試數據
                    conn.execute("DELETE FROM verification_test WHERE test_data = ?", (test_data,))
                
                conn.commit()
                conn.close()
            else:
                print(f"    ⚠️  主數據庫不存在，跳過寫入測試")
        except Exception as e:
            print(f"    ❌ 數據庫寫入測試失敗: {e}")
        
        # 3. 服務導入測試
        print(f"  📦 服務導入測試...")
        try:
            # 測試關鍵服務導入
            from frontend.config import Config
            from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
            from backend.shared.infrastructure.database.task_status_db import get_task_status_db
            
            functional_results['service_import_test'] = True
            print(f"    ✅ 服務導入測試通過")
            
        except Exception as e:
            print(f"    ❌ 服務導入測試失敗: {e}")
        
        self.verification_results['functional_tests'] = functional_results
    
    def _generate_final_report(self):
        """生成最終報告"""
        print(f"\n" + "=" * 60)
        print("📋 最終數據庫驗證報告")
        print("=" * 60)
        
        # 統計結果
        db_connections = self.verification_results['database_connections']
        service_configs = self.verification_results['service_configurations']
        functional_tests = self.verification_results.get('functional_tests', {})
        
        # 數據庫連接統計
        total_dbs = len(db_connections)
        connected_dbs = sum(1 for db in db_connections.values() if db.get('connection_ok', False))
        
        # 服務配置統計
        total_services = len(service_configs)
        working_services = sum(1 for service in service_configs.values() 
                              if service.get('config_loaded', False) or service.get('database_engine_loaded', False) or service.get('task_db_loaded', False))
        
        # 功能測試統計
        total_functional_tests = len(functional_tests)
        passed_functional_tests = sum(1 for test in functional_tests.values() if test)
        
        print(f"📊 統計摘要:")
        print(f"  數據庫連接: {connected_dbs}/{total_dbs} 成功")
        print(f"  服務配置: {working_services}/{total_services} 正常")
        print(f"  功能測試: {passed_functional_tests}/{total_functional_tests} 通過")
        
        # 詳細數據庫狀態
        print(f"\n💾 數據庫連接狀態:")
        for db_name, db_info in db_connections.items():
            if db_info.get('connection_ok', False):
                size_mb = db_info.get('size_bytes', 0) / 1024 / 1024
                print(f"  ✅ {db_name}: {db_info['path']} ({size_mb:.1f} MB, {len(db_info.get('tables', []))} 表)")
            else:
                error = db_info.get('error', '未知錯誤')
                print(f"  ❌ {db_name}: {error}")
        
        # 服務配置狀態
        print(f"\n⚙️  服務配置狀態:")
        for service_name, service_info in service_configs.items():
            if (service_info.get('config_loaded', False) or 
                service_info.get('database_engine_loaded', False) or 
                service_info.get('task_db_loaded', False)):
                print(f"  ✅ {service_name}: 配置正常")
            else:
                error = service_info.get('error', '未知錯誤')
                print(f"  ❌ {service_name}: {error}")
        
        # 功能測試狀態
        print(f"\n🧪 功能測試狀態:")
        for test_name, test_result in functional_tests.items():
            status = "✅" if test_result else "❌"
            print(f"  {status} {test_name}")
        
        # 計算總體狀態
        total_checks = connected_dbs + working_services + passed_functional_tests
        max_checks = total_dbs + total_services + total_functional_tests
        success_rate = (total_checks / max_checks * 100) if max_checks > 0 else 0
        
        if success_rate >= 90:
            self.verification_results['overall_status'] = 'excellent'
            status_emoji = '🟢'
            status_text = '優秀'
        elif success_rate >= 75:
            self.verification_results['overall_status'] = 'good'
            status_emoji = '🟡'
            status_text = '良好'
        elif success_rate >= 50:
            self.verification_results['overall_status'] = 'fair'
            status_emoji = '🟠'
            status_text = '一般'
        else:
            self.verification_results['overall_status'] = 'poor'
            status_emoji = '🔴'
            status_text = '需要改進'
        
        print(f"\n🎯 總體評估:")
        print(f"  狀態: {status_emoji} {status_text}")
        print(f"  成功率: {success_rate:.1f}% ({total_checks}/{max_checks})")
        
        # 建議
        recommendations = []
        
        if connected_dbs < total_dbs:
            recommendations.append("修復無法連接的數據庫")
        
        if working_services < total_services:
            recommendations.append("檢查服務配置問題")
        
        if passed_functional_tests < total_functional_tests:
            recommendations.append("修復功能測試失敗項目")
        
        if not recommendations:
            recommendations.append("所有檢查都通過，系統狀態良好")
        
        print(f"\n💡 建議:")
        for i, recommendation in enumerate(recommendations, 1):
            print(f"  {i}. {recommendation}")
        
        self.verification_results['recommendations'] = recommendations
        self.verification_results['summary'] = {
            'total_databases': total_dbs,
            'connected_databases': connected_dbs,
            'total_services': total_services,
            'working_services': working_services,
            'total_functional_tests': total_functional_tests,
            'passed_functional_tests': passed_functional_tests,
            'success_rate': success_rate
        }
        
        # 保存詳細報告
        report_path = "logs/final_database_verification_report.json"
        Path("logs").mkdir(exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.verification_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 詳細報告已保存至: {report_path}")


def main():
    """主函數"""
    print("🔍 最終數據庫連接與完整性驗證工具")
    print("=" * 60)
    
    verifier = FinalDatabaseVerifier()
    results = verifier.run_final_verification()
    
    # 返回適當的退出碼
    if results['overall_status'] in ['excellent', 'good']:
        print(f"\n✅ 數據庫驗證通過，系統可以正常運作")
        sys.exit(0)
    elif results['overall_status'] == 'fair':
        print(f"\n⚠️  數據庫驗證基本通過，但有改進空間")
        sys.exit(0)
    else:
        print(f"\n❌ 數據庫驗證失敗，需要修復問題")
        sys.exit(1)


if __name__ == "__main__":
    main()