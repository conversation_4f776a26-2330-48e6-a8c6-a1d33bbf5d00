/**
 * 郵件列表管理模組
 * 負責郵件列表的載入、渲染、顯示和詳情展示
 */

/**
 * 郵件列表管理類
 */
class EmailListManager {
    constructor(inbox) {
        this.inbox = inbox;
    }
    
    /**
     * 載入郵件列表
     */
    async loadEmails() {
        if (this.inbox.isLoading) return;
        
        this.inbox.isLoading = true;
        EmailUIUtils.showLoading(this.inbox.elements);
        
        try {
            // 解析排序設置
            const sortValue = this.inbox.elements.sortSelect?.value || 'received_time_desc';
            const [field, order] = sortValue.split('_');

            // 檢查未讀郵件篩選設定
            const unreadOnly = this.inbox.elements.unreadOnlyCheckbox?.checked || false;
            
            const params = new URLSearchParams({
                limit: this.inbox.pageSize,
                offset: (this.inbox.currentPage - 1) * this.inbox.pageSize,
                order_by: field,
                order_desc: order === 'desc' ? 'true' : 'false',
                unread_only: unreadOnly ? 'true' : 'false'
            });
            
            const response = await fetch(`/email/api/list?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.inbox.emails = result.data.emails;
                this.inbox.totalCount = result.data.total_count;
                this.renderEmailList();
                this.updatePagination();
            } else {
                EmailUIUtils.showNotification(this.inbox.elements, '載入郵件失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('載入郵件失敗:', error);
            EmailUIUtils.showNotification(this.inbox.elements, '載入郵件失敗', 'error');
        } finally {
            this.inbox.isLoading = false;
            EmailUIUtils.hideLoading(this.inbox.elements);
        }
    }
    
    /**
     * 渲染郵件列表
     */
    renderEmailList() {
        if (!this.inbox.elements.emailList) return;
        
        if (this.inbox.emails.length === 0) {
            this.inbox.elements.emailList.innerHTML = `
                <div class="loading-placeholder">
                    <p>沒有郵件</p>
                </div>
            `;
            return;
        }
        
        const emailItems = this.inbox.emails.map(email => this.createEmailItem(email)).join('');
        this.inbox.elements.emailList.innerHTML = emailItems;
        
        // 重新綁定事件
        this.bindEmailItemEvents();
    }
    
    /**
     * 創建郵件項目 HTML
     */
    createEmailItem(email) {
        const receivedTime = new Date(email.received_time).toLocaleString('zh-TW');
        const isUnread = !email.is_read;
        const isSelected = this.inbox.selectedEmails.has(email.id);
        
        // 解析狀態顯示
        let parseStatusHtml = '';
        if (email.parse_status === 'parsed') {
            // 已解析成功，顯示解析信息
            const parseMethod = email.extraction_method || '未知';
            const parseInfo = this.createParseInfo(email);
            parseStatusHtml = `
                <div class="email-parse-info">
                    ${parseInfo}
                    <span class="parse-tag ${parseMethod.toLowerCase()}">${parseMethod}</span>
                </div>
            `;
        } else if (email.parse_status === 'failed') {
            // 解析失敗，顯示失敗狀態
            parseStatusHtml = `
                <div class="email-parse-info">
                    <span class="parse-status failed"></span>
                    <span class="parse-tag failed">解析失敗</span>
                </div>
            `;
        } else {
            // 未解析
            parseStatusHtml = `
                <div class="email-parse-info">
                    <span class="parse-status pending"></span>
                    <span class="parse-tag pending">待解析</span>
                </div>
            `;
        }
        
        return `
            <div class="email-item ${isUnread ? 'unread' : ''} ${isSelected ? 'selected' : ''} clickable-row" 
                 data-email-id="${email.id}" 
                 title="點擊查看郵件詳情">
                <div class="email-checkbox">
                    <input type="checkbox" ${isSelected ? 'checked' : ''} 
                           onchange="window.emailInbox.operations.toggleEmailSelection(${email.id}, this.checked)">
                </div>
                <div class="email-sender" title="${EmailUIUtils.escapeHtml(email.sender)}">
                    ${EmailUIUtils.escapeHtml(EmailUIUtils.truncateText(email.sender_display_name || email.sender, 25))}
                </div>
                <div class="email-subject" title="${EmailUIUtils.escapeHtml(email.subject)}">
                    ${EmailUIUtils.escapeHtml(EmailUIUtils.truncateText(email.subject, 50))}
                    ${parseStatusHtml}
                </div>
                <div class="email-time">
                    ${receivedTime}
                </div>
                <div class="email-attachment">
                    ${email.has_attachments ? `📎 ${email.attachment_count}` : ''}
                </div>
                <div class="email-actions">
                    <button class="action-btn delete" onclick="window.emailInbox.operations.deleteEmail(${email.id})" title="刪除">
                        ❌
                    </button>
                    <button class="action-btn process" onclick="window.emailInbox.operations.processEmail(${email.id})" title="處理">
                        🚀
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * 創建解析信息標籤
     */
    createParseInfo(email) {
        let tags = [];
        
        if (email.vendor_code) {
            tags.push(`<span class="parse-tag vendor">${email.vendor_code}</span>`);
        }
        
        if (email.pd) {
            tags.push(`<span class="parse-tag pd">PD: ${EmailUIUtils.truncateText(email.pd, 12)}</span>`);
        }
        
        if (email.lot) {
            tags.push(`<span class="parse-tag lot">LOT: ${EmailUIUtils.truncateText(email.lot, 12)}</span>`);
        }
        
        if (email.mo) {
            tags.push(`<span class="parse-tag mo">MO: ${EmailUIUtils.truncateText(email.mo, 12)}</span>`);
        }
        
        if (email.yield_value) {
            tags.push(`<span class="parse-tag yield">良率: ${email.yield_value}</span>`);
        }
        
        return tags.join(' ');
    }
    
    /**
     * 綁定郵件項目事件
     */
    bindEmailItemEvents() {
        const emailItems = document.querySelectorAll('.email-item');
        emailItems.forEach(item => {
            item.addEventListener('click', (e) => {
                if (e.target.type === 'checkbox' || e.target.classList.contains('action-btn')) {
                    return;
                }
                
                const emailId = parseInt(item.dataset.emailId);
                this.showEmailDetail(emailId);
            });
        });
    }
    
    /**
     * 顯示郵件詳情
     */
    async showEmailDetail(emailId) {
        try {
            const response = await fetch(`/email/api/${emailId}`);
            const result = await response.json();
            
            if (result.success) {
                const email = result.data;
                this.renderEmailDetail(email);
                this.inbox.elements.emailDetail.classList.remove('hidden');
                this.inbox.elements.emailDetail.classList.add('show');
            } else {
                EmailUIUtils.showNotification(this.inbox.elements, '載入郵件詳情失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('載入郵件詳情失敗:', error);
            EmailUIUtils.showNotification(this.inbox.elements, '載入郵件詳情失敗', 'error');
        }
    }
    
    /**
     * 渲染郵件詳情
     */
    renderEmailDetail(email) {
        if (!this.inbox.elements.emailDetail) return;
        
        // 更新基本信息
        if (this.inbox.elements.detailSubject) {
            this.inbox.elements.detailSubject.textContent = email.subject;
        }
        if (this.inbox.elements.detailSender) {
            this.inbox.elements.detailSender.textContent = email.sender;
        }
        if (this.inbox.elements.detailTime) {
            this.inbox.elements.detailTime.textContent = new Date(email.received_time).toLocaleString('zh-TW');
        }
        if (this.inbox.elements.detailBody) {
            this.inbox.elements.detailBody.textContent = email.body || '(無內容)';
        }
        
        // 渲染附件
        this.renderEmailAttachments(email);
    }
    
    /**
     * 渲染郵件附件
     */
    renderEmailAttachments(email) {
        if (!this.inbox.elements.detailAttachments) return;
        
        if (email.attachments && email.attachments.length > 0) {
            // 使用附件管理器渲染附件列表
            const attachmentsContainer = document.createElement('div');
            attachmentsContainer.id = 'email-detail-attachments';
            
            window.emailAttachments.renderAttachmentsList(email.attachments, 'email-detail-attachments');
            
            this.inbox.elements.detailAttachments.innerHTML = '';
            this.inbox.elements.detailAttachments.appendChild(attachmentsContainer);
        } else {
            this.inbox.elements.detailAttachments.innerHTML = '<p>無附件</p>';
        }
    }
    
    /**
     * 關閉郵件詳情
     */
    closeEmailDetail() {
        if (this.inbox.elements.emailDetail) {
            this.inbox.elements.emailDetail.classList.add('hidden');
            this.inbox.elements.emailDetail.classList.remove('show');
        }
    }
    
    /**
     * 更新分頁
     */
    updatePagination() {
        EmailUIUtils.updatePaginationDisplay(
            this.inbox.elements,
            this.inbox.currentPage,
            this.inbox.pageSize,
            this.inbox.totalCount
        );
    }
    
    /**
     * 上一頁
     */
    previousPage() {
        if (this.inbox.currentPage > 1) {
            this.inbox.currentPage--;
            this.loadEmails();
        }
    }
    
    /**
     * 下一頁
     */
    nextPage() {
        const maxPage = Math.ceil(this.inbox.totalCount / this.inbox.pageSize);
        if (this.inbox.currentPage < maxPage) {
            this.inbox.currentPage++;
            this.loadEmails();
        }
    }
    
    /**
     * 跳轉到指定頁面
     */
    goToPage(page) {
        const maxPage = Math.ceil(this.inbox.totalCount / this.inbox.pageSize);
        if (page >= 1 && page <= maxPage && page !== this.inbox.currentPage) {
            this.inbox.currentPage = page;
            this.loadEmails();
        }
    }
    
    /**
     * 搜尋郵件
     */
    async searchEmails() {
        const searchTerm = this.inbox.elements.searchInput?.value.trim();
        if (!searchTerm) {
            this.loadEmails();
            return;
        }
        
        EmailUIUtils.showLoading(this.inbox.elements);
        
        try {
            // 檢查未讀郵件篩選設定
            const unreadOnly = this.inbox.elements.unreadOnlyCheckbox?.checked || false;
            
            const params = new URLSearchParams({
                q: searchTerm,
                fields: 'subject,body,sender',
                limit: this.inbox.pageSize,
                unread_only: unreadOnly ? 'true' : 'false'
            });
            
            if (this.inbox.currentSender !== 'all') {
                params.append('sender', this.inbox.currentSender);
            }
            
            const response = await fetch(`/email/api/search?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.inbox.emails = result.data.emails;
                this.inbox.totalCount = result.data.total_count;
                this.renderEmailList();
                this.updatePagination();
            } else {
                EmailUIUtils.showNotification(this.inbox.elements, '搜尋失敗: ' + result.message, 'error');
            }
            
        } catch (error) {
            console.error('搜尋郵件失敗:', error);
            EmailUIUtils.showNotification(this.inbox.elements, '搜尋郵件失敗', 'error');
        } finally {
            EmailUIUtils.hideLoading(this.inbox.elements);
        }
    }
    
    /**
     * 更新郵件項目選擇狀態
     */
    updateEmailItemSelection(emailId, selected) {
        const emailItem = document.querySelector(`[data-email-id="${emailId}"]`);
        if (emailItem) {
            const checkbox = emailItem.querySelector('input[type="checkbox"]');
            if (checkbox) {
                checkbox.checked = selected;
            }
            
            if (selected) {
                emailItem.classList.add('selected');
            } else {
                emailItem.classList.remove('selected');
            }
        }
    }
    
    /**
     * 更新所有郵件項目的選擇狀態
     */
    updateAllEmailItemSelections(selectAll) {
        // 更新所有複選框
        const checkboxes = document.querySelectorAll('.email-item input[type="checkbox"]');
        checkboxes.forEach(cb => {
            cb.checked = selectAll;
        });
        
        // 更新項目樣式
        const emailItems = document.querySelectorAll('.email-item');
        emailItems.forEach(item => {
            if (selectAll) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        });
    }
    
    /**
     * 重新載入郵件列表並保持當前頁面
     */
    async refreshEmails() {
        await this.loadEmails();
        
        // 更新統計資訊
        if (this.inbox.updateStatistics) {
            await this.inbox.updateStatistics();
        }
    }
    
    /**
     * 設置排序並重新載入
     */
    setSortAndReload(sortValue) {
        this.inbox.currentPage = 1; // 重置到第一頁
        this.loadEmails();
    }
    
    /**
     * 格式化郵件列表項目的時間顯示
     */
    formatEmailTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0) {
            // 今天 - 顯示時間
            return date.toLocaleTimeString('zh-TW', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        } else if (diffDays === 1) {
            // 昨天
            return '昨天';
        } else if (diffDays < 7) {
            // 一週內 - 顯示星期
            return date.toLocaleDateString('zh-TW', { weekday: 'long' });
        } else {
            // 一週前 - 顯示日期
            return date.toLocaleDateString('zh-TW', { 
                month: 'short', 
                day: 'numeric' 
            });
        }
    }
}

// 全域可用
window.EmailListManager = EmailListManager;