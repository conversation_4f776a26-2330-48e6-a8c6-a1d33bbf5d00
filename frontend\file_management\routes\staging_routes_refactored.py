"""網路瀏覽器 API - 暫存相關路由 (重構版 - 使用依賴注入)
展示如何將現有路由重構為使用依賴注入模式
"""

import uuid
from typing import List, Optional
from fastapi import APIRouter, Query, HTTPException, Depends
from fastapi.responses import JSONResponse

from loguru import logger

try:
    from backend.services.staging.models import StagingError, InsufficientSpaceError, FileIntegrityError
    from backend.services.staging import FileStagingService
except ImportError:
    # 當直接執行時的回退
    StagingError = Exception
    InsufficientSpaceError = Exception
    FileIntegrityError = Exception
    FileStagingService = None

# 導入重構後的依賴注入
from .dependencies import (
    get_staging_service, 
    get_api_state, 
    require_staging_service,
    get_all_services,
    APIState,
    ServiceDependencies
)

# 建立路由器
router = APIRouter(prefix="/api/staging/v2", tags=["File Staging (Refactored)"])


@router.post("/create")
async def create_staging_task_v2(
    product_name: str = Query(..., description="產品名稱"),
    source_files: List[str] = Query(..., description="來源檔案路徑列表"),
    preserve_structure: bool = Query(default=True, description="是否保持目錄結構"),
    use_unique_name: bool = Query(default=True, description="是否使用唯一產品名稱"),
    # ✅ 使用必需服務依賴注入 - 如果服務不可用會自動拋出 HTTP 503
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """建立檔案暫存任務（重構版 - 使用強制依賴注入）
    
    優勢：
    - 不需要檢查 staging_service 是否為 None
    - 自動處理服務不可用的情況
    - 代碼更簡潔，專注於業務邏輯
    """
    try:
        # 追蹤請求
        api_state.increment_request_count()

        logger.info(f"建立暫存任務請求: 產品={product_name}, 檔案數={len(source_files)}")

        # 驗證輸入參數
        if not product_name.strip():
            raise HTTPException(status_code=400, detail="產品名稱不能為空")

        if not source_files or len(source_files) == 0:
            raise HTTPException(status_code=400, detail="必須提供至少一個來源檔案")

        # 建立暫存任務
        task_id = str(uuid.uuid4())
        
        # 直接使用服務，不需要 None 檢查
        result = await staging_service.create_staging_task(
            task_id=task_id,
            product_name=product_name,
            source_files=source_files,
            preserve_structure=preserve_structure,
            use_unique_name=use_unique_name
        )

        logger.info(f"暫存任務已建立: {task_id}")
        
        return {
            "success": True,
            "message": "暫存任務已建立",
            "task_id": task_id,
            "result": result.to_dict() if hasattr(result, 'to_dict') else str(result)
        }

    except HTTPException:
        raise
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"建立暫存任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"建立暫存任務失敗: {str(e)}")


@router.get("/tasks")
async def list_staging_tasks_v2(
    status: Optional[str] = Query(None, description="任務狀態過濾"),
    limit: int = Query(default=50, ge=1, le=1000, description="返回結果數量限制"),
    # ✅ 使用可選服務依賴注入 - 允許服務不可用但需要手動處理
    staging_service: Optional[FileStagingService] = Depends(get_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """列出暫存任務（重構版 - 使用可選依賴注入）
    
    優勢：
    - 可以優雅處理服務不可用的情況
    - 提供友善的錯誤訊息
    - 統一的錯誤格式
    """
    try:
        api_state.increment_request_count()

        if staging_service is None:
            # 優雅處理服務不可用
            return JSONResponse(
                status_code=503,
                content={
                    "success": False,
                    "message": "檔案暫存服務暫時不可用",
                    "tasks": [],
                    "total": 0,
                    "service_available": False
                }
            )

        logger.info(f"列出暫存任務請求: status={status}, limit={limit}")

        # 使用服務獲取任務列表
        tasks = staging_service.list_tasks(status=status, limit=limit)
        
        return {
            "success": True,
            "message": "成功獲取暫存任務列表",
            "tasks": [task.to_dict() if hasattr(task, 'to_dict') else str(task) for task in tasks],
            "total": len(tasks),
            "service_available": True
        }

    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"列出暫存任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"列出暫存任務失敗: {str(e)}")


@router.get("/tasks/{task_id}")
async def get_staging_task_status_v2(
    task_id: str,
    include_details: bool = Query(default=False, description="是否包含詳細資訊"),
    # ✅ 使用組合服務依賴注入 - 一次注入多個服務
    services: ServiceDependencies = Depends(get_all_services)
):
    """獲取暫存任務狀態（重構版 - 使用組合依賴注入）
    
    優勢：
    - 一次性獲取所有需要的服務
    - 可以檢查多個服務的可用性
    - 更靈活的服務組合使用
    """
    try:
        services.api_state.increment_request_count()

        if not services.has_staging():
            raise HTTPException(
                status_code=503, 
                detail="檔案暫存服務不可用"
            )

        logger.info(f"獲取暫存任務狀態: {task_id}")

        # 使用暫存服務獲取任務狀態
        task_status = services.staging.get_task_status(task_id)
        
        if task_status is None:
            raise HTTPException(
                status_code=404, 
                detail=f"找不到任務: {task_id}"
            )

        result = {
            "success": True,
            "message": "成功獲取任務狀態",
            "task_id": task_id,
            "status": task_status,
            "available_services": services.get_available_services()
        }

        # 如果處理服務也可用，可以獲取額外資訊
        if include_details and services.has_processing():
            try:
                # 嘗試獲取相關的處理任務
                processing_tasks = services.processing.get_tasks_by_staging_id(task_id)
                result["related_processing_tasks"] = len(processing_tasks)
            except Exception as e:
                logger.warning(f"獲取相關處理任務失敗: {e}")

        return result

    except HTTPException:
        raise
    except Exception as e:
        services.api_state.increment_error_count()
        logger.error(f"獲取任務狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"獲取任務狀態失敗: {str(e)}")


@router.delete("/tasks/{task_id}")
async def cancel_staging_task_v2(
    task_id: str,
    force: bool = Query(default=False, description="是否強制取消"),
    # ✅ 使用必需服務但包含錯誤處理的依賴注入
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """取消暫存任務（重構版 - 使用強制依賴注入）"""
    try:
        api_state.increment_request_count()

        logger.info(f"取消暫存任務: {task_id}, force={force}")

        # 嘗試取消任務
        success = await staging_service.cancel_task(task_id, force=force)
        
        if success:
            return {
                "success": True,
                "message": f"任務 {task_id} 已成功取消",
                "task_id": task_id
            }
        else:
            raise HTTPException(
                status_code=400, 
                detail=f"無法取消任務 {task_id}，可能已完成或不存在"
            )

    except HTTPException:
        raise
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"取消任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任務失敗: {str(e)}")


@router.get("/stats")
async def get_staging_stats_v2(
    # ✅ 展示不同類型的依賴注入組合
    services: ServiceDependencies = Depends(get_all_services)
):
    """獲取暫存服務統計（重構版 - 展示服務狀態檢查）"""
    try:
        services.api_state.increment_request_count()

        # 基本統計
        stats = {
            "service_available": services.has_staging(),
            "api_stats": services.api_state.get_stats(),
            "available_services": services.get_available_services()
        }

        # 如果暫存服務可用，獲取詳細統計
        if services.has_staging():
            try:
                staging_stats = services.staging.get_service_statistics()
                stats["staging_stats"] = staging_stats
            except Exception as e:
                logger.warning(f"獲取暫存統計失敗: {e}")
                stats["staging_stats"] = {"error": str(e)}

        # 如果處理服務也可用，獲取處理統計
        if services.has_processing():
            try:
                processing_tasks = services.processing.list_tasks()
                stats["processing_stats"] = {
                    "total_tasks": len(processing_tasks),
                    "service_available": True
                }
            except Exception as e:
                logger.warning(f"獲取處理統計失敗: {e}")
                stats["processing_stats"] = {"error": str(e)}

        return {
            "success": True,
            "message": "成功獲取統計資訊",
            "stats": stats
        }

    except Exception as e:
        services.api_state.increment_error_count()
        logger.error(f"獲取統計失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"獲取統計失敗: {str(e)}")


# ✅ 示範自定義依賴注入函數
def get_validated_product_name(
    product_name: str = Query(..., min_length=1, max_length=100, description="產品名稱")
) -> str:
    """自定義依賴注入：驗證產品名稱"""
    # 移除前後空白
    product_name = product_name.strip()
    
    # 檢查特殊字符
    invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
    for char in invalid_chars:
        if char in product_name:
            raise HTTPException(
                status_code=400, 
                detail=f"產品名稱不能包含特殊字符: {char}"
            )
    
    return product_name


@router.post("/create-validated")
async def create_staging_task_with_validation(
    # ✅ 組合自定義依賴注入
    product_name: str = Depends(get_validated_product_name),
    source_files: List[str] = Query(..., description="來源檔案路徑列表"),
    staging_service: FileStagingService = Depends(require_staging_service),
    api_state: APIState = Depends(get_api_state)
):
    """建立暫存任務（帶自定義驗證的依賴注入範例）"""
    try:
        api_state.increment_request_count()

        logger.info(f"建立驗證暫存任務: 產品={product_name}, 檔案數={len(source_files)}")

        # 產品名稱已經通過依賴注入驗證，可以直接使用
        task_id = str(uuid.uuid4())
        
        result = await staging_service.create_staging_task(
            task_id=task_id,
            product_name=product_name,  # 已驗證的產品名稱
            source_files=source_files,
            preserve_structure=True,
            use_unique_name=True
        )

        return {
            "success": True,
            "message": "驗證暫存任務已建立",
            "task_id": task_id,
            "validated_product_name": product_name,
            "result": result.to_dict() if hasattr(result, 'to_dict') else str(result)
        }

    except HTTPException:
        raise
    except Exception as e:
        api_state.increment_error_count()
        logger.error(f"建立驗證暫存任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"建立驗證暫存任務失敗: {str(e)}")


# ✅ 健康檢查端點展示服務狀態
@router.get("/health")
async def staging_service_health(
    services: ServiceDependencies = Depends(get_all_services)
):
    """暫存服務健康檢查（展示完整的服務狀態檢查）"""
    try:
        health_status = {
            "service": "staging",
            "status": "healthy" if services.has_staging() else "unhealthy",
            "timestamp": services.api_state.system_stats["startup_time"].isoformat(),
            "available_services": services.get_available_services(),
            "api_stats": services.api_state.get_stats()
        }

        if services.has_staging():
            try:
                # 測試服務基本功能
                test_result = services.staging.health_check()
                health_status["staging_health"] = test_result
            except Exception as e:
                health_status["staging_health"] = {"error": str(e)}
                health_status["status"] = "degraded"

        return health_status

    except Exception as e:
        logger.error(f"健康檢查失敗: {str(e)}")
        return {
            "service": "staging",
            "status": "error",
            "error": str(e),
            "timestamp": services.api_state.system_stats["startup_time"].isoformat()
        }