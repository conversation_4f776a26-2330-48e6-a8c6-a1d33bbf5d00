#!/usr/bin/env python3
"""
資料庫自動備份腳本
提供完整的備份策略，包括定期備份、保留策略、壓縮和驗證
"""

import os
import sqlite3
import shutil
import gzip
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import hashlib

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/database_backup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseBackupManager:
    """資料庫備份管理器"""
    
    def __init__(self, 
                 source_db_path: str = "data/email_inbox.db",
                 backup_base_dir: str = "backups/automated"):
        self.source_db_path = Path(source_db_path)
        self.backup_base_dir = Path(backup_base_dir)
        
        # 建立備份目錄結構
        self.daily_dir = self.backup_base_dir / "daily"
        self.weekly_dir = self.backup_base_dir / "weekly"
        self.monthly_dir = self.backup_base_dir / "monthly"
        
        for dir_path in [self.daily_dir, self.weekly_dir, self.monthly_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def create_backup(self, backup_type: str = "daily") -> Dict[str, any]:
        """建立資料庫備份"""
        try:
            if not self.source_db_path.exists():
                raise FileNotFoundError(f"源資料庫不存在: {self.source_db_path}")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 根據備份類型選擇目標目錄
            if backup_type == "daily":
                target_dir = self.daily_dir
            elif backup_type == "weekly":
                target_dir = self.weekly_dir
            elif backup_type == "monthly":
                target_dir = self.monthly_dir
            else:
                target_dir = self.daily_dir
            
            backup_filename = f"email_inbox_backup_{backup_type}_{timestamp}.db"
            backup_path = target_dir / backup_filename
            
            # 開始備份
            start_time = datetime.now()
            logger.info(f"開始 {backup_type} 備份: {backup_path}")
            
            # 使用 SQLite 的 VACUUM INTO 進行原子性備份
            with sqlite3.connect(self.source_db_path) as source_conn:
                source_conn.execute(f"VACUUM INTO '{backup_path}'")
            
            # 驗證備份完整性
            if not self._verify_backup(backup_path):
                raise Exception("備份完整性驗證失敗")
            
            # 計算檔案大小和 checksum
            file_size = backup_path.stat().st_size
            checksum = self._calculate_checksum(backup_path)
            
            # 壓縮備份（可選）
            compressed_path = None
            if backup_type in ["weekly", "monthly"]:
                compressed_path = self._compress_backup(backup_path)
                if compressed_path:
                    backup_path.unlink()  # 刪除未壓縮版本
                    backup_path = compressed_path
                    file_size = backup_path.stat().st_size
            
            duration = datetime.now() - start_time
            
            backup_info = {
                "timestamp": timestamp,
                "backup_type": backup_type,
                "backup_path": str(backup_path),
                "file_size": file_size,
                "file_size_mb": round(file_size / (1024*1024), 2),
                "checksum": checksum,
                "duration_seconds": duration.total_seconds(),
                "compressed": compressed_path is not None,
                "status": "success"
            }
            
            logger.info(f"備份完成: {backup_info['file_size_mb']} MB, "
                       f"耗時 {backup_info['duration_seconds']:.2f} 秒")
            
            # 記錄備份資訊
            self._log_backup_info(backup_info)
            
            return backup_info
            
        except Exception as e:
            logger.error(f"備份失敗: {e}")
            return {
                "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
                "backup_type": backup_type,
                "status": "failed",
                "error": str(e)
            }
    
    def _verify_backup(self, backup_path: Path) -> bool:
        """驗證備份完整性"""
        try:
            with sqlite3.connect(backup_path) as conn:
                # 檢查資料庫可以正常開啟
                cursor = conn.cursor()
                
                # 驗證表結構
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                expected_tables = [
                    'emails', 'senders', 'attachments', 
                    'email_process_status', 'email_download_status', 
                    'email_download_retry_log'
                ]
                
                for table in expected_tables:
                    if table not in tables:
                        logger.warning(f"備份驗證警告: 表 {table} 不存在")
                
                # 基本完整性檢查
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()[0]
                
                if result != "ok":
                    logger.error(f"資料庫完整性檢查失敗: {result}")
                    return False
                
                return True
                
        except Exception as e:
            logger.error(f"備份驗證失敗: {e}")
            return False
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """計算檔案 SHA256 checksum"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def _compress_backup(self, backup_path: Path) -> Optional[Path]:
        """壓縮備份檔案"""
        try:
            compressed_path = backup_path.with_suffix('.db.gz')
            
            with open(backup_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            logger.info(f"備份已壓縮: {compressed_path}")
            return compressed_path
            
        except Exception as e:
            logger.error(f"壓縮備份失敗: {e}")
            return None
    
    def _log_backup_info(self, backup_info: Dict):
        """記錄備份資訊"""
        log_file = self.backup_base_dir / "backup_log.json"
        
        # 讀取現有記錄
        if log_file.exists():
            with open(log_file, 'r', encoding='utf-8') as f:
                logs = json.load(f)
        else:
            logs = []
        
        # 添加新記錄
        logs.append(backup_info)
        
        # 保持最近 100 筆記錄
        if len(logs) > 100:
            logs = logs[-100:]
        
        # 寫入檔案
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(logs, f, indent=2, ensure_ascii=False)
    
    def cleanup_old_backups(self) -> Dict[str, int]:
        """清理舊備份檔案"""
        cleanup_stats = {
            "daily_deleted": 0,
            "weekly_deleted": 0,
            "monthly_deleted": 0
        }
        
        now = datetime.now()
        
        try:
            # 每日備份保留 7 天
            daily_cutoff = now - timedelta(days=7)
            cleanup_stats["daily_deleted"] = self._cleanup_directory(
                self.daily_dir, daily_cutoff
            )
            
            # 每週備份保留 4 週
            weekly_cutoff = now - timedelta(weeks=4)
            cleanup_stats["weekly_deleted"] = self._cleanup_directory(
                self.weekly_dir, weekly_cutoff
            )
            
            # 每月備份保留 12 個月
            monthly_cutoff = now - timedelta(days=365)
            cleanup_stats["monthly_deleted"] = self._cleanup_directory(
                self.monthly_dir, monthly_cutoff
            )
            
            total_deleted = sum(cleanup_stats.values())
            logger.info(f"清理完成，共刪除 {total_deleted} 個舊備份檔案")
            
        except Exception as e:
            logger.error(f"清理舊備份失敗: {e}")
        
        return cleanup_stats
    
    def _cleanup_directory(self, directory: Path, cutoff_date: datetime) -> int:
        """清理指定目錄中的舊檔案"""
        deleted_count = 0
        
        for backup_file in directory.glob("email_inbox_backup_*.db*"):
            try:
                # 從檔名提取時間戳記
                parts = backup_file.stem.split('_')
                if len(parts) >= 4:
                    timestamp_str = f"{parts[-2]}_{parts[-1]}"
                    file_date = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                    
                    if file_date < cutoff_date:
                        backup_file.unlink()
                        deleted_count += 1
                        logger.info(f"已刪除舊備份: {backup_file}")
                        
            except Exception as e:
                logger.warning(f"處理檔案 {backup_file} 時出錯: {e}")
        
        return deleted_count
    
    def get_backup_status(self) -> Dict[str, any]:
        """獲取備份狀態資訊"""
        status = {
            "source_database": {
                "path": str(self.source_db_path),
                "exists": self.source_db_path.exists(),
                "size_mb": 0,
                "last_modified": None
            },
            "backup_directories": {},
            "recent_backups": [],
            "disk_usage": {}
        }
        
        # 源資料庫資訊
        if self.source_db_path.exists():
            stat = self.source_db_path.stat()
            status["source_database"]["size_mb"] = round(stat.st_size / (1024*1024), 2)
            status["source_database"]["last_modified"] = datetime.fromtimestamp(
                stat.st_mtime
            ).isoformat()
        
        # 備份目錄資訊
        for backup_type, directory in [
            ("daily", self.daily_dir),
            ("weekly", self.weekly_dir), 
            ("monthly", self.monthly_dir)
        ]:
            if directory.exists():
                backups = list(directory.glob("email_inbox_backup_*.db*"))
                total_size = sum(f.stat().st_size for f in backups)
                
                status["backup_directories"][backup_type] = {
                    "path": str(directory),
                    "count": len(backups),
                    "total_size_mb": round(total_size / (1024*1024), 2)
                }
        
        # 最近備份記錄
        log_file = self.backup_base_dir / "backup_log.json"
        if log_file.exists():
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
                status["recent_backups"] = logs[-10:]  # 最近 10 筆
            except Exception as e:
                logger.warning(f"讀取備份記錄失敗: {e}")
        
        return status


def main():
    """主要執行函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description="資料庫備份管理工具")
    parser.add_argument("--type", choices=["daily", "weekly", "monthly"], 
                       default="daily", help="備份類型")
    parser.add_argument("--cleanup", action="store_true", help="清理舊備份")
    parser.add_argument("--status", action="store_true", help="顯示備份狀態")
    
    args = parser.parse_args()
    
    backup_manager = DatabaseBackupManager()
    
    if args.status:
        status = backup_manager.get_backup_status()
        print(json.dumps(status, indent=2, ensure_ascii=False))
    elif args.cleanup:
        cleanup_stats = backup_manager.cleanup_old_backups()
        print(f"清理完成: {cleanup_stats}")
    else:
        backup_info = backup_manager.create_backup(args.type)
        print(f"備份結果: {backup_info}")


if __name__ == "__main__":
    main()