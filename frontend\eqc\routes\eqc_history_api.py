"""
EQC 處理歷史記錄 API
提供今日處理記錄、詳細資料查詢、檔案下載等功能
"""

from fastapi import APIRouter, HTTPException, Response
from fastapi.responses import FileResponse
from typing import List, Optional
from pathlib import Path
import json
from datetime import datetime
from loguru import logger

from backend.shared.infrastructure.database.task_status_db import get_task_status_db

router = APIRouter(prefix="/ft-eqc/api/eqc/history", tags=["EQC History"])


@router.get("/today")
async def get_today_processing_records():
    """獲取今日處理記錄"""
    try:
        task_db = get_task_status_db()
        records = task_db.get_today_processing_history(limit=50)
        
        # 格式化返回數據
        formatted_records = []
        for record in records:
            formatted_record = {
                "id": record["id"],
                "task_id": record["task_id"],
                "session_id": record["session_id"],
                "folder_name": record["folder_name"],
                "folder_path": record["folder_path"],
                "status": record["status"],
                "processing_time": record["processing_time_seconds"],
                "files_processed": record["files_processed"],
                "total_records": record["total_records"],
                "success_records": record["success_records"],
                "error_records": record["error_records"],
                "has_eqc_file": bool(record["eqc_file_path"]),
                "created_at": record["created_at"],
                "completed_at": record["completed_at"]
            }
            formatted_records.append(formatted_record)
        
        return {
            "status": "success",
            "data": formatted_records,
            "total": len(formatted_records),
            "date": datetime.now().strftime("%Y-%m-%d")
        }
        
    except Exception as e:
        logger.error(f"❌ 獲取今日處理記錄失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取今日處理記錄失敗: {str(e)}")


@router.get("/recent")
async def get_recent_processing_records(days: int = 7, limit: int = 100):
    """獲取最近處理記錄"""
    try:
        task_db = get_task_status_db()
        records = task_db.get_processing_history(days=days, limit=limit)
        
        # 格式化返回數據
        formatted_records = []
        for record in records:
            formatted_record = {
                "id": record["id"],
                "task_id": record["task_id"],
                "session_id": record["session_id"],
                "folder_name": record["folder_name"],
                "folder_path": record["folder_path"],
                "status": record["status"],
                "processing_time": record["processing_time_seconds"],
                "files_processed": record["files_processed"],
                "total_records": record["total_records"],
                "success_records": record["success_records"],
                "error_records": record["error_records"],
                "has_eqc_file": bool(record["eqc_file_path"]),
                "created_at": record["created_at"],
                "completed_at": record["completed_at"]
            }
            formatted_records.append(formatted_record)
        
        return {
            "status": "success",
            "data": formatted_records,
            "total": len(formatted_records),
            "days": days
        }
        
    except Exception as e:
        logger.error(f"❌ 獲取處理記錄失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取處理記錄失敗: {str(e)}")


@router.get("/detail/{task_id}")
async def get_processing_detail(task_id: str):
    """獲取處理詳細資料"""
    try:
        task_db = get_task_status_db()
        
        # 獲取任務詳細信息
        task_info = task_db.get_task_status(task_id=task_id)
        if not task_info:
            raise HTTPException(status_code=404, detail="任務不存在")
        
        # 獲取步驟詳細記錄
        with task_db._get_connection() as conn:
            step_records = conn.execute("""
                SELECT * FROM eqc_task_status 
                WHERE task_id = ? 
                ORDER BY created_at ASC
            """, (task_id,)).fetchall()
        
        # 格式化步驟記錄
        formatted_steps = []
        for step in step_records:
            step_dict = dict(step)
            if step_dict.get('details'):
                try:
                    step_dict['details'] = json.loads(step_dict['details'])
                except:
                    pass
            formatted_steps.append(step_dict)
        
        # 解析結果數據
        result_data = None
        if task_info.get('result'):
            try:
                result_data = json.loads(task_info['result'])
            except:
                result_data = task_info['result']
        
        return {
            "status": "success",
            "data": {
                "task_info": task_info,
                "step_records": formatted_steps,
                "result_data": result_data,
                "has_eqc_file": bool(task_info.get("eqc_file_path"))
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 獲取處理詳細資料失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取處理詳細資料失敗: {str(e)}")


@router.get("/download/{task_id}")
async def download_eqc_file(task_id: str):
    """下載 EQCTOTALDATA.xlsx 檔案"""
    try:
        task_db = get_task_status_db()
        
        # 獲取檔案信息
        file_info = task_db.get_eqc_file_info(task_id=task_id)
        if not file_info:
            raise HTTPException(status_code=404, detail="找不到 EQC 檔案")
        
        eqc_file_path = file_info["eqc_file_path"]
        if not eqc_file_path or not Path(eqc_file_path).exists():
            raise HTTPException(status_code=404, detail="EQC 檔案不存在")
        
        # 更新下載次數
        task_db.update_download_count(task_id)
        
        # 返回檔案
        filename = Path(eqc_file_path).name
        return FileResponse(
            path=eqc_file_path,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 下載 EQC 檔案失敗: {e}")
        raise HTTPException(status_code=500, detail=f"下載檔案失敗: {str(e)}")


@router.get("/download/session/{session_id}")
async def download_eqc_file_by_session(session_id: str):
    """通過會話ID下載 EQCTOTALDATA.xlsx 檔案"""
    try:
        task_db = get_task_status_db()
        
        # 獲取檔案信息
        file_info = task_db.get_eqc_file_info(session_id=session_id)
        if not file_info:
            raise HTTPException(status_code=404, detail="找不到 EQC 檔案")
        
        eqc_file_path = file_info["eqc_file_path"]
        if not eqc_file_path or not Path(eqc_file_path).exists():
            raise HTTPException(status_code=404, detail="EQC 檔案不存在")
        
        # 更新下載次數
        task_db.update_download_count(file_info["task_id"])
        
        # 返回檔案
        filename = Path(eqc_file_path).name
        return FileResponse(
            path=eqc_file_path,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 下載 EQC 檔案失敗: {e}")
        raise HTTPException(status_code=500, detail=f"下載檔案失敗: {str(e)}")


@router.get("/stats/today")
async def get_today_stats():
    """獲取今日處理統計"""
    try:
        task_db = get_task_status_db()
        
        with task_db._get_connection() as conn:
            # 今日統計
            stats = conn.execute("""
                SELECT 
                    COUNT(*) as total_tasks,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_tasks,
                    SUM(files_processed) as total_files,
                    SUM(total_records) as total_records,
                    SUM(success_records) as success_records,
                    SUM(error_records) as error_records,
                    AVG(processing_time_seconds) as avg_processing_time
                FROM eqc_processing_history 
                WHERE DATE(created_at) = DATE('now', 'localtime')
            """).fetchone()
        
        if stats:
            return {
                "status": "success",
                "data": dict(stats),
                "date": datetime.now().strftime("%Y-%m-%d")
            }
        else:
            return {
                "status": "success",
                "data": {
                    "total_tasks": 0,
                    "completed_tasks": 0,
                    "failed_tasks": 0,
                    "total_files": 0,
                    "total_records": 0,
                    "success_records": 0,
                    "error_records": 0,
                    "avg_processing_time": 0
                },
                "date": datetime.now().strftime("%Y-%m-%d")
            }
        
    except Exception as e:
        logger.error(f"❌ 獲取今日統計失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取統計失敗: {str(e)}")


@router.delete("/delete/{task_id}")
async def delete_task_record(task_id: str):
    """刪除指定的任務記錄"""
    try:
        task_db = get_task_status_db()

        with task_db._get_connection() as conn:
            # 檢查任務是否存在
            existing = conn.execute(
                "SELECT task_id, eqc_file_path FROM eqc_task_execution WHERE task_id = ?",
                (task_id,)
            ).fetchone()

            if not existing:
                raise HTTPException(status_code=404, detail="任務記錄不存在")

            # 刪除相關的 EQC 檔案（如果存在）
            if existing["eqc_file_path"] and Path(existing["eqc_file_path"]).exists():
                try:
                    Path(existing["eqc_file_path"]).unlink()
                    logger.info(f"🗑️ 已刪除 EQC 檔案: {existing['eqc_file_path']}")
                except Exception as e:
                    logger.warning(f"⚠️ 刪除 EQC 檔案失敗: {e}")

            # 刪除資料庫記錄
            conn.execute("DELETE FROM eqc_task_status WHERE task_id = ?", (task_id,))
            conn.execute("DELETE FROM eqc_processing_history WHERE task_id = ?", (task_id,))
            conn.execute("DELETE FROM eqc_task_execution WHERE task_id = ?", (task_id,))

            conn.commit()

            logger.info(f"✅ 任務記錄已刪除: {task_id}")

            return {
                "status": "success",
                "message": "任務記錄已成功刪除",
                "task_id": task_id
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 刪除任務記錄失敗: {e}")
        raise HTTPException(status_code=500, detail=f"刪除記錄失敗: {str(e)}")


@router.post("/cleanup")
async def cleanup_old_records():
    """清理舊的任務記錄（24小時前）"""
    try:
        task_db = get_task_status_db()
        deleted_count = 0
        deleted_files = 0

        with task_db._get_connection() as conn:
            # 查找24小時前的記錄
            old_records = conn.execute("""
                SELECT task_id, eqc_file_path
                FROM eqc_task_execution
                WHERE datetime(started_at) < datetime('now', '-1 day')
            """).fetchall()

            for record in old_records:
                task_id = record["task_id"]
                eqc_file_path = record["eqc_file_path"]

                # 刪除相關的 EQC 檔案（如果存在）
                if eqc_file_path and Path(eqc_file_path).exists():
                    try:
                        Path(eqc_file_path).unlink()
                        deleted_files += 1
                        logger.info(f"🗑️ 已刪除舊 EQC 檔案: {eqc_file_path}")
                    except Exception as e:
                        logger.warning(f"⚠️ 刪除舊 EQC 檔案失敗: {e}")

                # 刪除資料庫記錄
                conn.execute("DELETE FROM eqc_task_status WHERE task_id = ?", (task_id,))
                conn.execute("DELETE FROM eqc_processing_history WHERE task_id = ?", (task_id,))
                conn.execute("DELETE FROM eqc_task_execution WHERE task_id = ?", (task_id,))

                deleted_count += 1

            conn.commit()

            logger.info(f"🧹 清理完成: 刪除了 {deleted_count} 筆記錄，{deleted_files} 個檔案")

            return {
                "status": "success",
                "message": "舊記錄清理完成",
                "deleted_count": deleted_count,
                "deleted_files": deleted_files
            }

    except Exception as e:
        logger.error(f"❌ 清理舊記錄失敗: {e}")
        raise HTTPException(status_code=500, detail=f"清理失敗: {str(e)}")


@router.get("/recent")
async def get_recent_records(limit: int = 10):
    """獲取最近的處理記錄"""
    try:
        task_db = get_task_status_db()

        with task_db._get_connection() as conn:
            records = conn.execute("""
                SELECT
                    id, task_id, session_id, folder_path,
                    CASE
                        WHEN INSTR(folder_path, '\') > 0 THEN SUBSTR(folder_path, INSTR(folder_path, '\', -1) + 1)
                        WHEN INSTR(folder_path, '/') > 0 THEN SUBSTR(folder_path, INSTR(folder_path, '/', -1) + 1)
                        ELSE folder_path
                    END as folder_name,
                    status, processing_time_seconds as processing_time,
                    files_processed, total_records, success_records, error_records,
                    CASE WHEN eqc_file_path IS NOT NULL AND eqc_file_path != '' THEN 1 ELSE 0 END as has_eqc_file,
                    datetime(created_at, 'localtime') as created_at,
                    datetime(completed_at, 'localtime') as completed_at
                FROM eqc_processing_history
                ORDER BY created_at DESC
                LIMIT ?
            """, (limit,)).fetchall()

        records_list = [dict(record) for record in records]

        return {
            "status": "success",
            "data": records_list,
            "total": len(records_list)
        }

    except Exception as e:
        logger.error(f"❌ 獲取最近記錄失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取記錄失敗: {str(e)}")
