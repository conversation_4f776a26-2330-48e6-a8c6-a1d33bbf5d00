"""
Epic-04 Story 4.4: IntegratedRetryManager 測試
驗證 Epic-02, Epic-03, Epic-04 的完整整合

測試覆蓋：
- 下載失敗處理整合
- 處理失敗處理整合
- 跨 Epic 狀態同步
- 整合統計和監控
- 性能要求驗證
"""

import pytest
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, patch, MagicMock

from backend.shared.services.integrated_retry_manager import (
    IntegratedRetryManager, IntegratedRetryResult
)
from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
from backend.shared.infrastructure.adapters.database.download_tracking_models import RetryStrategy
from backend.shared.services.retry_service import TaskQueue


class TestIntegratedRetryManager:
    """IntegratedRetryManager 測試 - GREEN PHASE"""
    
    @pytest.fixture
    def mock_database(self):
        """模擬資料庫"""
        return Mock(spec=EmailDatabase)
    
    @pytest.fixture
    def mock_task_queue(self):
        """模擬任務隊列"""
        return Mock(spec=TaskQueue)
    
    @pytest.fixture
    def integrated_manager(self, mock_database, mock_task_queue):
        """創建整合重試管理器實例"""
        return IntegratedRetryManager(mock_database, mock_task_queue)
    
    def test_initialization(self, integrated_manager):
        """測試初始化 - GREEN PHASE"""
        # ASSERT - 驗證組件初始化
        assert integrated_manager.retry_service is not None
        assert integrated_manager.strategy_factory is not None
        assert integrated_manager.error_analyzer is not None
        assert integrated_manager.download_manager is not None
        assert integrated_manager.process_manager is not None
        
        # 驗證統計初始化
        stats = integrated_manager._operation_stats
        assert stats['download_retries'] == 0
        assert stats['processing_retries'] == 0
        assert stats['successful_retries'] == 0
        assert stats['failed_retries'] == 0
        assert stats['decisions_made'] == 0
    
    def test_handle_download_failure_should_retry(self, integrated_manager):
        """測試下載失敗處理 - 應該重試 - GREEN PHASE"""
        # ARRANGE
        email_id = 1
        error = Exception("Connection timeout")
        context = {'attempt_count': 1}
        
        # ACT
        result = integrated_manager.handle_download_failure(email_id, error, context)
        
        # ASSERT - GREEN PHASE: 驗證重試決策
        assert isinstance(result, IntegratedRetryResult)
        assert result.decision_made is True
        assert result.should_retry is True
        assert result.retry_id is not None
        assert result.retry_strategy is not None
        assert result.estimated_delay > 0
        assert "下載失敗重試" in result.reason
        assert result.confidence > 0
        
        # 驗證統計更新
        stats = integrated_manager._operation_stats
        assert stats['download_retries'] == 1
        assert stats['decisions_made'] == 1
    
    def test_handle_download_failure_should_not_retry(self, integrated_manager):
        """測試下載失敗處理 - 不應該重試 - GREEN PHASE"""
        # ARRANGE
        email_id = 1
        error = Exception("Authentication failed")  # 認證錯誤通常不重試
        context = {'attempt_count': 10}  # 超過重試次數
        
        # 模擬認證錯誤
        class AuthError(Exception):
            pass
        
        error = AuthError("Invalid credentials")
        
        # ACT
        result = integrated_manager.handle_download_failure(email_id, error, context)
        
        # ASSERT - GREEN PHASE: 驗證不重試決策
        assert isinstance(result, IntegratedRetryResult)
        assert result.decision_made is True
        assert result.should_retry is False
        assert result.retry_id is None
        assert result.retry_strategy is None
        assert result.estimated_delay == 0
        assert "下載不重試" in result.reason
        assert len(result.alternative_actions) > 0
        
        # 驗證統計更新
        stats = integrated_manager._operation_stats
        assert stats['failed_retries'] == 1
        assert stats['decisions_made'] == 1
    
    def test_handle_processing_failure_should_retry(self, integrated_manager):
        """測試處理失敗處理 - 應該重試 - GREEN PHASE"""
        # ARRANGE
        email_id = 1
        processing_step = "data_extraction"
        error = Exception("Temporary processing error")
        context = {'attempt_count': 1}
        
        # ACT
        result = integrated_manager.handle_processing_failure(
            email_id, processing_step, error, context
        )
        
        # ASSERT - GREEN PHASE: 驗證重試決策
        assert isinstance(result, IntegratedRetryResult)
        assert result.decision_made is True
        assert result.should_retry is True
        assert result.retry_id is not None
        assert result.retry_strategy is not None
        assert result.estimated_delay > 0
        assert processing_step in result.reason
        assert "處理失敗重試" in result.reason
        
        # 驗證統計更新
        stats = integrated_manager._operation_stats
        assert stats['processing_retries'] == 1
        assert stats['decisions_made'] == 1
    
    def test_handle_processing_failure_should_not_retry(self, integrated_manager):
        """測試處理失敗處理 - 不應該重試 - GREEN PHASE"""
        # ARRANGE
        email_id = 1
        processing_step = "validation"
        
        # 模擬資料格式錯誤（不可重試）
        class DataFormatError(Exception):
            pass
        
        error = DataFormatError("Invalid data format")
        context = {'attempt_count': 1}
        
        # Mock the process manager fail_processing method
        integrated_manager.process_manager.fail_processing = Mock()
        
        # ACT
        result = integrated_manager.handle_processing_failure(
            email_id, processing_step, error, context
        )
        
        # ASSERT - GREEN PHASE: 驗證不重試決策
        assert isinstance(result, IntegratedRetryResult)
        assert result.decision_made is True
        assert result.should_retry is False
        assert result.retry_id is None
        assert "處理不重試" in result.reason
        assert len(result.alternative_actions) > 0
        
        # 驗證統計更新
        stats = integrated_manager._operation_stats
        assert stats['failed_retries'] == 1
        assert stats['decisions_made'] == 1
    
    def test_execute_retry_with_sync_download(self, integrated_manager):
        """測試執行下載重試並同步狀態 - GREEN PHASE"""
        # ARRANGE
        retry_id = 1
        retry_type = "download"
        
        # Mock retry service to return success
        integrated_manager.retry_service.execute_retry = Mock(return_value=True)
        integrated_manager.retry_service.get_retry_status = Mock(return_value=Mock(original_task_id=1))
        integrated_manager.download_manager.complete_download = Mock()
        
        # ACT
        success = integrated_manager.execute_retry_with_sync(retry_id, retry_type)
        
        # ASSERT - GREEN PHASE: 驗證執行和同步
        assert success is True
        integrated_manager.retry_service.execute_retry.assert_called_once_with(retry_id)
        integrated_manager.download_manager.complete_download.assert_called_once_with(1)
        
        # 驗證統計更新
        stats = integrated_manager._operation_stats
        assert stats['successful_retries'] == 1
    
    def test_execute_retry_with_sync_processing(self, integrated_manager):
        """測試執行處理重試並同步狀態 - GREEN PHASE"""
        # ARRANGE
        retry_id = 1
        retry_type = "processing"
        
        # Mock retry service to return success
        integrated_manager.retry_service.execute_retry = Mock(return_value=True)
        integrated_manager.retry_service.get_retry_status = Mock(return_value=Mock(original_task_id=1))
        integrated_manager.process_manager.complete_processing = Mock()
        
        # ACT
        success = integrated_manager.execute_retry_with_sync(retry_id, retry_type)
        
        # ASSERT - GREEN PHASE: 驗證執行和同步
        assert success is True
        integrated_manager.retry_service.execute_retry.assert_called_once_with(retry_id)
        integrated_manager.process_manager.complete_processing.assert_called_once_with(1)
        
        # 驗證統計更新
        stats = integrated_manager._operation_stats
        assert stats['successful_retries'] == 1
    
    def test_execute_retry_with_sync_failure(self, integrated_manager):
        """測試執行重試失敗 - GREEN PHASE"""
        # ARRANGE
        retry_id = 1
        retry_type = "download"
        
        # Mock retry service to return failure
        integrated_manager.retry_service.execute_retry = Mock(return_value=False)
        
        # ACT
        success = integrated_manager.execute_retry_with_sync(retry_id, retry_type)
        
        # ASSERT - GREEN PHASE: 驗證失敗處理
        assert success is False
        
        # 驗證統計更新
        stats = integrated_manager._operation_stats
        assert stats['failed_retries'] == 1
    
    def test_get_integrated_statistics(self, integrated_manager):
        """測試獲取整合統計 - GREEN PHASE"""
        # ARRANGE
        period = "24h"
        
        # 模擬一些操作統計
        integrated_manager._operation_stats['download_retries'] = 5
        integrated_manager._operation_stats['processing_retries'] = 3
        integrated_manager._operation_stats['successful_retries'] = 6
        integrated_manager._operation_stats['failed_retries'] = 2
        integrated_manager._operation_stats['decisions_made'] = 8
        
        # ACT
        stats = integrated_manager.get_integrated_statistics(period)
        
        # ASSERT - GREEN PHASE: 驗證統計數據
        assert isinstance(stats, dict)
        assert stats['period'] == period
        assert 'operation_summary' in stats
        assert 'retry_service_stats' in stats
        assert 'error_analysis_stats' in stats
        assert 'cross_epic_coordination' in stats
        assert 'performance_metrics' in stats
        assert 'generated_at' in stats
        
        # 驗證操作摘要
        operation_summary = stats['operation_summary']
        assert operation_summary['download_retries'] == 5
        assert operation_summary['processing_retries'] == 3
        assert operation_summary['successful_retries'] == 6
        assert operation_summary['failed_retries'] == 2
        assert operation_summary['decisions_made'] == 8
        
        # 驗證跨 Epic 協調
        coordination = stats['cross_epic_coordination']
        assert coordination['download_integration'] == 'active'
        assert coordination['processing_integration'] == 'active'
        assert coordination['sync_success_rate'] > 0
    
    def test_get_cross_epic_health_status(self, integrated_manager):
        """測試獲取跨 Epic 健康狀態 - GREEN PHASE"""
        # ACT
        health_status = integrated_manager.get_cross_epic_health_status()
        
        # ASSERT - GREEN PHASE: 驗證健康狀態
        assert isinstance(health_status, dict)
        assert 'overall_health' in health_status
        assert 'epic_02_integration' in health_status
        assert 'epic_03_integration' in health_status
        assert 'epic_04_components' in health_status
        assert 'performance_indicators' in health_status
        assert 'alerts' in health_status
        assert 'recommendations' in health_status
        assert 'checked_at' in health_status
        
        # 驗證 Epic 整合狀態
        epic_02 = health_status['epic_02_integration']
        assert epic_02['status'] == 'connected'
        assert epic_02['download_manager'] == 'operational'
        
        epic_03 = health_status['epic_03_integration']
        assert epic_03['status'] == 'connected'
        assert epic_03['process_manager'] == 'operational'
        
        epic_04 = health_status['epic_04_components']
        assert epic_04['retry_service'] == 'operational'
        assert epic_04['strategy_factory'] == 'operational'
        assert epic_04['error_analyzer'] == 'operational'
    
    def test_health_status_with_warnings(self, integrated_manager):
        """測試健康狀態警告 - GREEN PHASE"""
        # ARRANGE - 模擬大量失敗
        integrated_manager._operation_stats['failed_retries'] = 15
        integrated_manager._operation_stats['successful_retries'] = 2
        
        # ACT
        health_status = integrated_manager.get_cross_epic_health_status()
        
        # ASSERT - GREEN PHASE: 驗證警告生成
        assert len(health_status['alerts']) > 0
        assert health_status['overall_health'] == 'warning'
        assert len(health_status['recommendations']) > 0
    
    def test_reset_statistics(self, integrated_manager):
        """測試重置統計 - GREEN PHASE"""
        # ARRANGE - 設置一些統計數據
        integrated_manager._operation_stats['download_retries'] = 5
        integrated_manager._operation_stats['successful_retries'] = 3
        
        # ACT
        integrated_manager.reset_statistics()
        
        # ASSERT - GREEN PHASE: 驗證統計重置
        stats = integrated_manager._operation_stats
        assert stats['download_retries'] == 0
        assert stats['processing_retries'] == 0
        assert stats['successful_retries'] == 0
        assert stats['failed_retries'] == 0
        assert stats['decisions_made'] == 0
    
    def test_cleanup_old_retry_data(self, integrated_manager):
        """測試清理舊重試資料 - GREEN PHASE"""
        # ARRANGE
        days_old = 7
        
        # ACT
        cleanup_stats = integrated_manager.cleanup_old_retry_data(days_old)
        
        # ASSERT - GREEN PHASE: 驗證清理統計
        assert isinstance(cleanup_stats, dict)
        assert 'retry_logs_cleaned' in cleanup_stats
        assert 'error_patterns_cleaned' in cleanup_stats
        assert 'total_cleaned' in cleanup_stats
        
        # GREEN PHASE 模擬值驗證
        assert cleanup_stats['retry_logs_cleaned'] > 0
        assert cleanup_stats['error_patterns_cleaned'] > 0
        assert cleanup_stats['total_cleaned'] > 0


class TestIntegratedRetryManagerPerformance:
    """IntegratedRetryManager 性能測試 - GREEN PHASE"""
    
    @pytest.fixture
    def integrated_manager(self):
        """創建整合重試管理器實例"""
        mock_database = Mock(spec=EmailDatabase)
        mock_task_queue = Mock(spec=TaskQueue)
        return IntegratedRetryManager(mock_database, mock_task_queue)
    
    def test_download_failure_handling_performance(self, integrated_manager):
        """測試下載失敗處理性能 - GREEN PHASE"""
        # ARRANGE
        email_id = 1
        error = Exception("Network timeout")
        context = {'attempt_count': 1}
        
        # ACT
        start_time = time.time()
        
        # 處理10次下載失敗
        for i in range(10):
            result = integrated_manager.handle_download_failure(email_id + i, error, context)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # ASSERT - GREEN PHASE: 驗證性能要求
        # 10次處理應該在1秒內完成
        assert processing_time < 1.0, f"下載失敗處理性能 {processing_time}s 超過 1 秒要求"
    
    def test_processing_failure_handling_performance(self, integrated_manager):
        """測試處理失敗處理性能 - GREEN PHASE"""
        # ARRANGE
        email_id = 1
        processing_step = "data_extraction"
        error = Exception("Processing error")
        context = {'attempt_count': 1}
        
        # ACT
        start_time = time.time()
        
        # 處理10次處理失敗
        for i in range(10):
            result = integrated_manager.handle_processing_failure(
                email_id + i, processing_step, error, context
            )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # ASSERT - GREEN PHASE: 驗證性能要求
        # 10次處理應該在1秒內完成
        assert processing_time < 1.0, f"處理失敗處理性能 {processing_time}s 超過 1 秒要求"
    
    def test_statistics_generation_performance(self, integrated_manager):
        """測試統計生成性能 - GREEN PHASE"""
        # ARRANGE
        period = "24h"
        
        # ACT
        start_time = time.time()
        
        # 生成10次統計
        for _ in range(10):
            stats = integrated_manager.get_integrated_statistics(period)
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        # ASSERT - GREEN PHASE: 驗證性能要求
        # 10次統計生成應該在500ms內完成
        assert generation_time < 0.5, f"統計生成性能 {generation_time}s 超過 500ms 要求"


class TestIntegratedRetryManagerIntegration:
    """IntegratedRetryManager 整合測試 - GREEN PHASE"""
    
    @pytest.fixture
    def integrated_manager(self):
        """創建整合重試管理器實例"""
        mock_database = Mock(spec=EmailDatabase)
        mock_task_queue = Mock(spec=TaskQueue)
        return IntegratedRetryManager(mock_database, mock_task_queue)
    
    def test_complete_download_retry_workflow(self, integrated_manager):
        """測試完整下載重試工作流程 - GREEN PHASE"""
        # ARRANGE
        email_id = 1
        error = Exception("Connection timeout")
        context = {'attempt_count': 1}
        
        # Mock components
        integrated_manager.retry_service.execute_retry = Mock(return_value=True)
        integrated_manager.retry_service.get_retry_status = Mock(
            return_value=Mock(original_task_id=email_id)
        )
        integrated_manager.download_manager.complete_download = Mock()
        
        # ACT & ASSERT - GREEN PHASE: 驗證完整流程
        # 1. 處理下載失敗
        result = integrated_manager.handle_download_failure(email_id, error, context)
        assert result.should_retry is True
        assert result.retry_id is not None
        
        # 2. 執行重試並同步
        retry_id = result.retry_id
        success = integrated_manager.execute_retry_with_sync(retry_id, "download")
        assert success is True
        
        # 3. 驗證同步調用
        integrated_manager.download_manager.complete_download.assert_called_once_with(email_id)
        
        # 4. 檢查統計
        stats = integrated_manager._operation_stats
        assert stats['download_retries'] == 1
        assert stats['successful_retries'] == 1
        assert stats['decisions_made'] == 1
    
    def test_complete_processing_retry_workflow(self, integrated_manager):
        """測試完整處理重試工作流程 - GREEN PHASE"""
        # ARRANGE
        email_id = 1
        processing_step = "data_extraction"
        error = Exception("Processing timeout")
        context = {'attempt_count': 1}
        
        # Mock components
        integrated_manager.retry_service.execute_retry = Mock(return_value=True)
        integrated_manager.retry_service.get_retry_status = Mock(
            return_value=Mock(original_task_id=email_id)
        )
        integrated_manager.process_manager.complete_processing = Mock()
        
        # ACT & ASSERT - GREEN PHASE: 驗證完整流程
        # 1. 處理處理失敗
        result = integrated_manager.handle_processing_failure(
            email_id, processing_step, error, context
        )
        assert result.should_retry is True
        assert result.retry_id is not None
        
        # 2. 執行重試並同步
        retry_id = result.retry_id
        success = integrated_manager.execute_retry_with_sync(retry_id, "processing")
        assert success is True
        
        # 3. 驗證同步調用
        integrated_manager.process_manager.complete_processing.assert_called_once_with(email_id)
        
        # 4. 檢查統計
        stats = integrated_manager._operation_stats
        assert stats['processing_retries'] == 1
        assert stats['successful_retries'] == 1
        assert stats['decisions_made'] == 1
    
    def test_mixed_retry_scenarios(self, integrated_manager):
        """測試混合重試場景 - GREEN PHASE"""
        # ARRANGE
        scenarios = [
            (1, "download", Exception("Network error")),
            (2, "processing", Exception("Processing error")),
            (3, "download", Exception("Timeout")),
            (4, "processing", Exception("Data error"))
        ]
        
        # ACT
        results = []
        for email_id, retry_type, error in scenarios:
            if retry_type == "download":
                result = integrated_manager.handle_download_failure(
                    email_id, error, {'attempt_count': 1}
                )
            else:
                result = integrated_manager.handle_processing_failure(
                    email_id, "test_step", error, {'attempt_count': 1}
                )
            results.append(result)
        
        # ASSERT - GREEN PHASE: 驗證混合場景處理
        assert len(results) == 4
        
        # 驗證各種結果類型
        retry_results = [r for r in results if r.should_retry]
        no_retry_results = [r for r in results if not r.should_retry]
        
        # 應該有部分重試，部分不重試
        assert len(retry_results) > 0
        assert all(r.retry_id is not None for r in retry_results)
        assert all(r.retry_id is None for r in no_retry_results)
        
        # 驗證統計
        stats = integrated_manager._operation_stats
        assert stats['decisions_made'] == 4
        assert (stats['download_retries'] + stats['processing_retries'] + 
                stats['failed_retries']) == 4


if __name__ == "__main__":
    # 運行測試以驗證 GREEN PHASE
    pytest.main([__file__, "-v", "--tb=short"])