# 專案文檔目錄

本目錄包含 outlook_summary 專案的所有結構化文檔。

## 📁 目錄結構

### 📋 核心文檔（根目錄）
- `README.md` - 專案主要文檔和快速開始指南
- `CHANGELOG.md` - 專案變更歷史記錄  
- `CLAUDE.md` - AI 開發指導規則和 Agent 協作流程
- `DOC_SUMMARY.md` - 文檔摘要和自動更新狀態

### 🏗️ 架構設計（architecture/）
- 系統架構設計文檔
- 企業級架構規劃
- 技術堆疊說明
- 編碼標準和最佳實踐

### 🔄 專案遷移（migration/）
- Backend Architecture Refactor 完成報告
- Task 3, 4, 5 系列完成報告
- 前端架構遷移文檔
- 分支管理和整合報告

### ✅ 驗證測試（validation/）
- API 修復和驗證報告
- 安全性修復摘要
- 系統功能驗證文檔
- 偵錯和問題解決報告

### 📚 開發指南（development/）
- 開發環境設置指南
- 團隊協作流程
- 快速參考文檔

### 🗄️ 歷史存檔（archive/）
- 過時或不再使用的文檔
- 歷史分析報告
- 臨時實驗文檔

## 🔍 快速導航

### 新手入門
1. 閱讀 [README.md](../README.md) 了解專案概況
2. 查看 [development/DEVELOPMENT_SETUP_GUIDE.md](development/DEVELOPMENT_SETUP_GUIDE.md) 設置開發環境
3. 參考 [CLAUDE.md](../CLAUDE.md) 了解 AI 輔助開發流程

### 架構了解
1. 查看 [architecture/](architecture/) 目錄了解系統設計
2. 閱讀 [migration/backend-architecture-refactor-final-completion-report.md](migration/backend-architecture-refactor-final-completion-report.md) 了解最新架構狀態

### 問題排查
1. 檢查 [validation/](validation/) 目錄尋找相關修復報告
2. 查看 [CHANGELOG.md](../CHANGELOG.md) 了解最近變更

## 🔄 文檔維護

本文檔結構由 Documentation-Maintainer Agent 自動維護，確保：
- 文檔分類清晰合理
- 交叉引用保持更新
- 過時文檔及時歸檔
- 新文檔自動分類

---
*最後更新: 2025-08-19* (PTS Renamer MVP 架構重構)  
*維護者: Documentation-Maintainer Agent + Specialist-Agent: python-pro*