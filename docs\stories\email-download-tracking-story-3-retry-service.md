# Story 3: 重試機制實現 - EmailDownloadRetryService

## 📋 Story 概要

**Story ID**: EDTS-003  
**Story 名稱**: 重試機制實現  
**Epic**: 郵件下載狀態追蹤  
**優先級**: P0 (Critical)  
**估計工作量**: 18 Story Points (3-4 工作日)  
**依賴**: Story 1, Story 2

### User Story
**As a** 系統管理員  
**I want** 實現智能的郵件下載重試機制  
**So that** 系統能夠自動處理臨時性的下載失敗，提高整體成功率和系統可靠性

---

## 🎯 驗收標準

### 必須完成的功能 (Must Have)
- [ ] ✅ 實現 EmailDownloadRetryService 重試服務
- [ ] ✅ 自動處理待重試的下載任務
- [ ] ✅ 支援手動觸發重試功能
- [ ] ✅ 整合 Dramatiq 異步任務處理
- [ ] ✅ 實現定期重試處理調度
- [ ] ✅ 智能重試決策邏輯
- [ ] ✅ 重試失敗的降級處理

### 應該有的功能 (Should Have)
- [ ] ✅ 批處理重試以提高效率
- [ ] ✅ 重試優先級管理
- [ ] ✅ 重試統計和監控
- [ ] ✅ 重試過程的詳細日誌

### 期望的功能 (Could Have)
- [ ] ✅ 自適應重試策略
- [ ] ✅ 重試效果分析
- [ ] ✅ 重試性能優化

---

## 🏗️ 技術實現詳情

### 服務架構設計

```
EmailDownloadRetryService (重試服務)
├── RetryTaskProcessor (任務處理器)
├── RetryScheduler (調度器)
├── RetryPriorityManager (優先級管理)
├── RetryBatchProcessor (批處理器)
└── RetryMonitor (監控器)

Dramatiq Integration (任務隊列)
├── retry_email_download_task (單個重試任務)
├── process_pending_retries_task (批處理任務)
├── schedule_retry_processing_task (調度任務)
└── cleanup_completed_retries_task (清理任務)
```

### 實現文件結構

```
backend/
├── email/
│   └── services/
│       ├── email_download_retry_service.py     # 主重試服務
│       ├── retry_task_processor.py             # 任務處理器
│       ├── retry_scheduler.py                  # 調度器
│       └── retry_monitor.py                    # 監控器
├── tasks/
│   ├── email_download_tasks.py                 # Dramatiq 任務
│   └── services/
│       └── retry_task_manager.py               # 任務管理器
└── tests/
    ├── unit/
    │   └── email/
    │       └── services/
    │           └── test_email_download_retry_service.py
    └── integration/
        └── test_retry_mechanism_integration.py
```

---

## 🔧 詳細實現步驟

### Step 1: 核心重試服務 (2天)

**檔案位置**: `backend/email/services/email_download_retry_service.py`

```python
"""
郵件下載重試服務
實現智能的郵件下載重試機制
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import asyncio
import json
from dataclasses import dataclass
from enum import Enum

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.email.services.email_download_tracker import EmailDownloadTracker
from backend.shared.infrastructure.adapters.email_inbox.email_sync_service import EmailSyncService
from backend.email.services.retry_task_processor import RetryTaskProcessor
from backend.email.services.retry_scheduler import RetryScheduler
from backend.email.services.retry_monitor import RetryMonitor

class RetryPriority(Enum):
    """重試優先級"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class RetryTask:
    """重試任務數據模型"""
    tracking_id: int
    email_id: int
    attempt: int
    max_attempts: int
    error_type: str
    error_message: str
    scheduled_at: datetime
    priority: RetryPriority
    retry_reason: str
    metadata: Dict[str, Any]

class EmailDownloadRetryService:
    """
    郵件下載重試服務
    
    職責:
    1. 處理待重試的下載任務
    2. 管理重試優先級和調度
    3. 執行實際的重試操作
    4. 監控重試效果和性能
    5. 提供手動重試接口
    """
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("EmailDownloadRetryService")
        self.download_tracker = EmailDownloadTracker()
        self.task_processor = RetryTaskProcessor()
        self.scheduler = RetryScheduler()
        self.monitor = RetryMonitor()
        self.email_sync_service = None
        
        # 配置參數
        self.config = {
            'batch_size': 10,           # 批處理大小
            'max_concurrent': 5,        # 最大並發重試
            'timeout_per_retry': 300,   # 單次重試超時（秒）
            'circuit_breaker_threshold': 10,  # 斷路器閾值
            'priority_weights': {       # 優先級權重
                RetryPriority.URGENT: 4,
                RetryPriority.HIGH: 3,
                RetryPriority.NORMAL: 2,
                RetryPriority.LOW: 1
            }
        }
        
        # 狀態管理
        self._is_initialized = False
        self._is_processing = False
        self._circuit_breaker_open = False
        self._last_circuit_check = datetime.utcnow()
        self._failure_count = 0
    
    async def initialize(self):
        """初始化服務"""
        if self._is_initialized:
            return
        
        try:
            # 初始化 EmailSyncService
            self.email_sync_service = EmailSyncService()
            await self.email_sync_service.initialize_email_reader()
            
            # 初始化子組件
            await self.task_processor.initialize()
            await self.scheduler.initialize()
            await self.monitor.initialize()
            
            self._is_initialized = True
            self.logger.info("EmailDownloadRetryService 初始化完成")
            
        except Exception as e:
            self.logger.error(f"EmailDownloadRetryService 初始化失敗: {e}")
            raise
    
    # ==================== 主要處理接口 ====================
    
    async def process_pending_retries(
        self, 
        batch_size: Optional[int] = None,
        priority_filter: Optional[RetryPriority] = None
    ) -> Dict[str, Any]:
        """
        處理待重試的下載任務
        
        Args:
            batch_size: 批處理大小
            priority_filter: 優先級過濾
            
        Returns:
            處理結果統計
        """
        if not self._is_initialized:
            await self.initialize()
        
        if self._is_processing:
            return {
                'success': False,
                'message': '重試處理正在進行中',
                'processed_count': 0
            }
        
        # 檢查斷路器
        if await self._check_circuit_breaker():
            return {
                'success': False,
                'message': '斷路器開啟，暫停重試處理',
                'processed_count': 0
            }
        
        self._is_processing = True
        
        try:
            # 獲取待重試任務
            retry_tasks = await self._get_prioritized_retry_tasks(
                batch_size or self.config['batch_size'],
                priority_filter
            )
            
            if not retry_tasks:
                return {
                    'success': True,
                    'message': '沒有待重試任務',
                    'processed_count': 0
                }
            
            # 執行批處理重試
            results = await self._execute_batch_retries(retry_tasks)
            
            # 更新監控統計
            await self.monitor.update_batch_statistics(results)
            
            # 記錄處理結果
            success_count = sum(1 for r in results if r['success'])
            failure_count = len(results) - success_count
            
            self.logger.info(
                f"批處理重試完成: 總數={len(results)}, 成功={success_count}, 失敗={failure_count}"
            )
            
            return {
                'success': True,
                'message': f'批處理重試完成: 成功={success_count}, 失敗={failure_count}',
                'processed_count': len(results),
                'success_count': success_count,
                'failure_count': failure_count,
                'results': results
            }
            
        except Exception as e:
            self.logger.error(f"處理重試任務失敗: {e}")
            return {
                'success': False,
                'message': f'處理重試任務失敗: {e}',
                'processed_count': 0
            }
        
        finally:
            self._is_processing = False
    
    async def trigger_manual_retry(
        self, 
        email_ids: List[int],
        priority: RetryPriority = RetryPriority.HIGH,
        reason: str = "manual_retry"
    ) -> Dict[str, Any]:
        """
        觸發手動重試
        
        Args:
            email_ids: 要重試的郵件 ID 列表
            priority: 重試優先級
            reason: 重試原因
            
        Returns:
            重試安排結果
        """
        try:
            results = []
            
            for email_id in email_ids:
                try:
                    # 創建新的重試記錄
                    tracking_id = await self.download_tracker.start_download_tracking(
                        email_id=email_id,
                        max_retry_count=3
                    )
                    
                    # 安排高優先級重試任務
                    task_scheduled = await self._schedule_immediate_retry(
                        tracking_id=tracking_id,
                        email_id=email_id,
                        priority=priority,
                        reason=reason
                    )
                    
                    if task_scheduled:
                        results.append({
                            'email_id': email_id,
                            'tracking_id': tracking_id,
                            'status': 'scheduled',
                            'priority': priority.name
                        })
                    else:
                        results.append({
                            'email_id': email_id,
                            'tracking_id': tracking_id,
                            'status': 'failed_to_schedule',
                            'priority': priority.name
                        })
                        
                except Exception as e:
                    results.append({
                        'email_id': email_id,
                        'tracking_id': None,
                        'status': 'error',
                        'error': str(e)
                    })
            
            success_count = sum(1 for r in results if r['status'] == 'scheduled')
            
            self.logger.info(f"手動重試已安排: {success_count}/{len(email_ids)} 個郵件")
            
            return {
                'success': True,
                'message': f'已安排 {success_count}/{len(email_ids)} 個郵件重試',
                'results': results
            }
            
        except Exception as e:
            self.logger.error(f"觸發手動重試失敗: {e}")
            return {
                'success': False,
                'message': f'觸發手動重試失敗: {e}',
                'results': []
            }
    
    async def get_retry_status(self, email_id: Optional[int] = None) -> Dict[str, Any]:
        """獲取重試狀態"""
        try:
            if email_id:
                # 獲取特定郵件的重試狀態
                status = await self.download_tracker.get_email_download_status(email_id)
                return {
                    'email_id': email_id,
                    'status': status
                }
            else:
                # 獲取整體重試狀態
                pending_retries = await self.download_tracker.get_pending_retries()
                statistics = await self.monitor.get_retry_statistics()
                
                return {
                    'pending_retries_count': len(pending_retries),
                    'is_processing': self._is_processing,
                    'circuit_breaker_open': self._circuit_breaker_open,
                    'statistics': statistics
                }
                
        except Exception as e:
            self.logger.error(f"獲取重試狀態失敗: {e}")
            return {'error': str(e)}
    
    # ==================== 私有實現方法 ====================
    
    async def _get_prioritized_retry_tasks(
        self, 
        limit: int,
        priority_filter: Optional[RetryPriority] = None
    ) -> List[RetryTask]:
        """獲取按優先級排序的重試任務"""
        try:
            # 從 tracker 獲取基礎待重試任務
            pending_retries = await self.download_tracker.get_pending_retries(limit * 2)
            
            if not pending_retries:
                return []
            
            # 轉換為 RetryTask 對象並計算優先級
            retry_tasks = []
            for retry_data in pending_retries:
                priority = await self._calculate_task_priority(retry_data)
                
                # 優先級過濾
                if priority_filter and priority != priority_filter:
                    continue
                
                task = RetryTask(
                    tracking_id=retry_data['tracking_id'],
                    email_id=retry_data['email_id'],
                    attempt=retry_data['attempt'],
                    max_attempts=retry_data.get('max_retry_count', 3),
                    error_type=retry_data['error_type'],
                    error_message=retry_data['error_message'],
                    scheduled_at=datetime.fromisoformat(retry_data['next_retry_at']),
                    priority=priority,
                    retry_reason='automatic',
                    metadata={}
                )
                retry_tasks.append(task)
            
            # 按優先級和時間排序
            retry_tasks.sort(
                key=lambda t: (
                    -self.config['priority_weights'][t.priority],  # 優先級權重（降序）
                    t.scheduled_at  # 時間（升序）
                )
            )
            
            return retry_tasks[:limit]
            
        except Exception as e:
            self.logger.error(f"獲取優先級重試任務失敗: {e}")
            return []
    
    async def _execute_batch_retries(self, retry_tasks: List[RetryTask]) -> List[Dict[str, Any]]:
        """執行批處理重試"""
        semaphore = asyncio.Semaphore(self.config['max_concurrent'])
        
        async def execute_single_retry(task: RetryTask) -> Dict[str, Any]:
            async with semaphore:
                return await self._execute_single_retry(task)
        
        # 並發執行重試任務
        results = await asyncio.gather(
            *[execute_single_retry(task) for task in retry_tasks],
            return_exceptions=True
        )
        
        # 處理異常結果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'task': retry_tasks[i],
                    'success': False,
                    'error': str(result),
                    'duration': 0
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _execute_single_retry(self, task: RetryTask) -> Dict[str, Any]:
        """執行單個重試任務"""
        start_time = datetime.utcnow()
        
        try:
            # 超時控制
            timeout = self.config['timeout_per_retry']
            
            # 執行重試
            result = await asyncio.wait_for(
                self._perform_email_retry(task),
                timeout=timeout
            )
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            # 更新失敗計數器
            if result['success']:
                self._failure_count = max(0, self._failure_count - 1)
            else:
                self._failure_count += 1
            
            return {
                'task': task,
                'success': result['success'],
                'message': result['message'],
                'duration': duration,
                'metadata': result.get('metadata', {})
            }
            
        except asyncio.TimeoutError:
            self._failure_count += 1
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            # 更新超時失敗狀態
            await self.download_tracker.update_download_failure(
                task.tracking_id,
                'retry_timeout',
                f'重試超時: {timeout}秒'
            )
            
            return {
                'task': task,
                'success': False,
                'message': f'重試超時: {timeout}秒',
                'duration': duration
            }
            
        except Exception as e:
            self._failure_count += 1
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            self.logger.error(f"執行重試任務異常: {e}")
            
            return {
                'task': task,
                'success': False,
                'message': f'重試異常: {e}',
                'duration': duration
            }
    
    async def _perform_email_retry(self, task: RetryTask) -> Dict[str, Any]:
        """執行郵件重新下載"""
        try:
            self.logger.info(
                f"開始重試郵件下載: email_id={task.email_id}, "
                f"attempt={task.attempt}, priority={task.priority.name}"
            )
            
            # 重置追蹤狀態為下載中
            await self.download_tracker.start_download_tracking(
                email_id=task.email_id,
                max_retry_count=task.max_attempts
            )
            
            # 執行實際的郵件同步（這裡簡化為重新同步單個郵件）
            sync_result = await self.email_sync_service.sync_single_email(task.email_id)
            
            if sync_result['success']:
                # 重試成功
                await self.download_tracker.update_download_success(
                    task.tracking_id,
                    download_size=sync_result.get('size', 0),
                    duration=sync_result.get('duration', 0)
                )
                
                self.logger.info(f"重試成功: email_id={task.email_id}")
                
                return {
                    'success': True,
                    'message': '重試下載成功',
                    'metadata': sync_result
                }
            else:
                # 重試失敗
                await self.download_tracker.update_download_failure(
                    task.tracking_id,
                    'retry_failed',
                    sync_result.get('message', '重試下載失敗')
                )
                
                return {
                    'success': False,
                    'message': sync_result.get('message', '重試下載失敗')
                }
                
        except Exception as e:
            # 重試異常
            await self.download_tracker.update_download_failure(
                task.tracking_id,
                'retry_exception',
                str(e)
            )
            
            return {
                'success': False,
                'message': f'重試下載異常: {e}'
            }
    
    async def _calculate_task_priority(self, retry_data: Dict[str, Any]) -> RetryPriority:
        """計算任務優先級"""
        # 基礎優先級
        priority = RetryPriority.NORMAL
        
        # 根據錯誤類型調整
        error_type = retry_data.get('error_type', '')
        if error_type in ['authentication_error', 'permission_denied']:
            priority = RetryPriority.LOW  # 認證錯誤優先級較低
        elif error_type in ['rate_limit', 'server_error']:
            priority = RetryPriority.HIGH  # 服務器問題優先級較高
        elif error_type in ['timeout', 'connection_error']:
            priority = RetryPriority.NORMAL  # 網絡問題正常優先級
        
        # 根據重試次數調整
        attempt = retry_data.get('attempt', 1)
        if attempt >= 3:
            priority = RetryPriority.LOW  # 多次失敗降低優先級
        elif attempt == 1:
            priority = RetryPriority.HIGH  # 首次重試提高優先級
        
        # 根據郵件重要性調整（可以擴展）
        # 例如：根據發件人、主題關鍵字等
        
        return priority
    
    async def _schedule_immediate_retry(
        self, 
        tracking_id: int,
        email_id: int,
        priority: RetryPriority,
        reason: str
    ) -> bool:
        """安排立即重試"""
        try:
            from backend.tasks.email_download_tasks import single_email_download_retry
            
            # 發送到 Dramatiq 任務隊列
            single_email_download_retry.send(
                email_id=email_id,
                tracking_id=tracking_id,
                priority=priority.value,
                reason=reason
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"安排立即重試失敗: {e}")
            return False
    
    async def _check_circuit_breaker(self) -> bool:
        """檢查斷路器狀態"""
        current_time = datetime.utcnow()
        
        # 每分鐘檢查一次
        if (current_time - self._last_circuit_check).total_seconds() < 60:
            return self._circuit_breaker_open
        
        self._last_circuit_check = current_time
        
        # 檢查失敗率
        threshold = self.config['circuit_breaker_threshold']
        if self._failure_count >= threshold:
            if not self._circuit_breaker_open:
                self.logger.warning(f"斷路器開啟: 失敗次數 {self._failure_count} >= {threshold}")
                self._circuit_breaker_open = True
        else:
            if self._circuit_breaker_open:
                self.logger.info("斷路器關閉: 失敗次數下降")
                self._circuit_breaker_open = False
                self._failure_count = 0  # 重置計數器
        
        return self._circuit_breaker_open
    
    # ==================== 管理和監控接口 ====================
    
    async def get_service_health(self) -> Dict[str, Any]:
        """獲取服務健康狀態"""
        return {
            'is_initialized': self._is_initialized,
            'is_processing': self._is_processing,
            'circuit_breaker_open': self._circuit_breaker_open,
            'failure_count': self._failure_count,
            'last_circuit_check': self._last_circuit_check.isoformat(),
            'config': self.config
        }
    
    async def update_config(self, new_config: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            for key, value in new_config.items():
                if key in self.config:
                    self.config[key] = value
                    self.logger.info(f"配置已更新: {key} = {value}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新配置失敗: {e}")
            return False
    
    async def force_circuit_breaker_reset(self) -> bool:
        """強制重置斷路器"""
        try:
            self._circuit_breaker_open = False
            self._failure_count = 0
            self._last_circuit_check = datetime.utcnow()
            
            self.logger.info("斷路器已強制重置")
            return True
            
        except Exception as e:
            self.logger.error(f"強制重置斷路器失敗: {e}")
            return False
```

### Step 2: Dramatiq 任務實現 (1天)

**檔案位置**: `backend/tasks/email_download_tasks.py`

```python
"""
郵件下載 Dramatiq 任務
實現異步的郵件下載重試任務
"""

import dramatiq
import asyncio
from datetime import datetime
from typing import Dict, Any

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.email.services.email_download_retry_service import EmailDownloadRetryService
from backend.tasks.services.dramatiq_tasks import dramatiq_actor

logger = LoggerManager().get_logger("EmailDownloadTasks")

@dramatiq_actor(queue_name="email_retry_queue", max_retries=0, time_limit=600000)  # 10分鐘超時
def process_email_download_retries(batch_size: int = 10):
    """
    處理郵件下載重試任務（批處理）
    
    Args:
        batch_size: 批處理大小
    """
    try:
        async def run_retry_processing():
            retry_service = EmailDownloadRetryService()
            await retry_service.initialize()
            
            result = await retry_service.process_pending_retries(batch_size=batch_size)
            
            logger.info(f"批處理重試任務完成: {result}")
            return result
        
        return asyncio.run(run_retry_processing())
        
    except Exception as e:
        logger.error(f"批處理重試任務失敗: {e}")
        raise

@dramatiq_actor(queue_name="email_retry_queue", max_retries=2, time_limit=300000)  # 5分鐘超時
def single_email_download_retry(
    email_id: int, 
    tracking_id: int,
    priority: int = 2,
    reason: str = "automatic"
):
    """
    單個郵件下載重試任務
    
    Args:
        email_id: 郵件 ID
        tracking_id: 追蹤記錄 ID
        priority: 優先級 (1-4)
        reason: 重試原因
    """
    try:
        async def run_single_retry():
            retry_service = EmailDownloadRetryService()
            await retry_service.initialize()
            
            # 創建重試任務對象
            from backend.email.services.email_download_retry_service import RetryTask, RetryPriority
            
            # 獲取詳細信息
            status = await retry_service.download_tracker.get_email_download_status(email_id)
            if not status:
                raise ValueError(f"找不到郵件狀態: email_id={email_id}")
            
            task = RetryTask(
                tracking_id=tracking_id,
                email_id=email_id,
                attempt=status.get('download_attempt', 1),
                max_attempts=status.get('max_retry_count', 3),
                error_type=status.get('error_type', 'unknown'),
                error_message=status.get('error_message', ''),
                scheduled_at=datetime.utcnow(),
                priority=RetryPriority(priority),
                retry_reason=reason,
                metadata={}
            )
            
            # 執行重試
            result = await retry_service._execute_single_retry(task)
            
            logger.info(f"單個郵件重試完成: email_id={email_id}, result={result}")
            return result
        
        return asyncio.run(run_single_retry())
        
    except Exception as e:
        logger.error(f"單個郵件重試失敗: email_id={email_id}, error={e}")
        raise

@dramatiq_actor(queue_name="scheduler_queue", max_retries=0)
def schedule_retry_processing():
    """
    調度重試處理任務
    定期觸發重試處理
    """
    try:
        # 觸發批處理重試任務
        process_email_download_retries.send(batch_size=20)
        logger.info("重試處理任務已調度")
        
    except Exception as e:
        logger.error(f"調度重試處理任務失敗: {e}")
        raise

@dramatiq_actor(queue_name="maintenance_queue", max_retries=1)
def cleanup_completed_retries(days_old: int = 7):
    """
    清理已完成的重試記錄
    
    Args:
        days_old: 清理多少天前的記錄
    """
    try:
        async def run_cleanup():
            retry_service = EmailDownloadRetryService()
            await retry_service.initialize()
            
            result = await retry_service.download_tracker.cleanup_old_records(days=days_old)
            
            logger.info(f"重試記錄清理完成: {result}")
            return result
        
        return asyncio.run(run_cleanup())
        
    except Exception as e:
        logger.error(f"清理重試記錄失敗: {e}")
        raise

@dramatiq_actor(queue_name="monitoring_queue", max_retries=0)
def collect_retry_metrics():
    """
    收集重試指標
    定期收集和上報重試相關的監控指標
    """
    try:
        async def run_metrics_collection():
            retry_service = EmailDownloadRetryService()
            await retry_service.initialize()
            
            # 收集統計信息
            stats = await retry_service.download_tracker.get_download_statistics(hours=1)
            health = await retry_service.get_service_health()
            
            # 記錄關鍵指標
            logger.info(f"重試指標收集: 統計={stats}, 健康狀態={health}")
            
            # 可以在這裡集成到監控系統（如 Prometheus）
            
            return {'stats': stats, 'health': health}
        
        return asyncio.run(run_metrics_collection())
        
    except Exception as e:
        logger.error(f"收集重試指標失敗: {e}")
        raise

# ==================== 任務調度配置 ====================

def setup_retry_task_scheduling():
    """設置重試任務調度"""
    try:
        # 每5分鐘執行一次重試處理
        import dramatiq.middleware
        from dramatiq.brokers.redis import RedisBroker
        from dramatiq.middleware import CurrentMessage
        
        # 這裡可以設置定期任務
        # 實際實現取決於你的 Dramatiq 配置
        
        logger.info("重試任務調度設置完成")
        
    except Exception as e:
        logger.error(f"設置重試任務調度失敗: {e}")

# ==================== 手動觸發接口 ====================

def trigger_immediate_retry_processing():
    """手動觸發立即重試處理"""
    try:
        process_email_download_retries.send(batch_size=50)
        logger.info("已觸發立即重試處理")
        return True
        
    except Exception as e:
        logger.error(f"觸發立即重試處理失敗: {e}")
        return False

def trigger_priority_retry(email_ids: list, priority: int = 4):
    """觸發高優先級重試"""
    try:
        for email_id in email_ids:
            # 這裡需要先獲取 tracking_id
            # 實際實現中可能需要查詢數據庫
            single_email_download_retry.send(
                email_id=email_id,
                tracking_id=0,  # 需要實際的 tracking_id
                priority=priority,
                reason="manual_priority"
            )
        
        logger.info(f"已觸發高優先級重試: {len(email_ids)} 個郵件")
        return True
        
    except Exception as e:
        logger.error(f"觸發高優先級重試失敗: {e}")
        return False
```

### Step 3: 輔助服務實現 (1天)

**檔案位置**: `backend/email/services/retry_monitor.py`

```python
"""
重試監控器
收集和分析重試相關的監控指標
"""

from typing import Dict, Any, List
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json

from backend.shared.infrastructure.logging.logger_manager import LoggerManager

@dataclass
class RetryMetrics:
    """重試指標數據模型"""
    timestamp: datetime
    total_retries: int
    successful_retries: int
    failed_retries: int
    avg_retry_duration: float
    retry_success_rate: float
    error_distribution: Dict[str, int]
    priority_distribution: Dict[str, int]

class RetryMonitor:
    """重試監控器"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("RetryMonitor")
        self.metrics_history = []
        self.max_history_size = 1000
    
    async def initialize(self):
        """初始化監控器"""
        self.logger.info("RetryMonitor 初始化完成")
    
    async def update_batch_statistics(self, batch_results: List[Dict[str, Any]]):
        """更新批處理統計"""
        try:
            if not batch_results:
                return
            
            # 計算批處理指標
            total_retries = len(batch_results)
            successful_retries = sum(1 for r in batch_results if r['success'])
            failed_retries = total_retries - successful_retries
            
            # 計算平均持續時間
            durations = [r.get('duration', 0) for r in batch_results]
            avg_duration = sum(durations) / len(durations) if durations else 0
            
            # 計算成功率
            success_rate = (successful_retries / total_retries * 100) if total_retries > 0 else 0
            
            # 錯誤分佈
            error_distribution = {}
            priority_distribution = {}
            
            for result in batch_results:
                # 錯誤類型分佈
                if not result['success'] and 'task' in result:
                    error_type = result['task'].error_type
                    error_distribution[error_type] = error_distribution.get(error_type, 0) + 1
                
                # 優先級分佈
                if 'task' in result:
                    priority = result['task'].priority.name
                    priority_distribution[priority] = priority_distribution.get(priority, 0) + 1
            
            # 創建指標記錄
            metrics = RetryMetrics(
                timestamp=datetime.utcnow(),
                total_retries=total_retries,
                successful_retries=successful_retries,
                failed_retries=failed_retries,
                avg_retry_duration=avg_duration,
                retry_success_rate=success_rate,
                error_distribution=error_distribution,
                priority_distribution=priority_distribution
            )
            
            # 保存到歷史記錄
            self.metrics_history.append(metrics)
            
            # 限制歷史記錄大小
            if len(self.metrics_history) > self.max_history_size:
                self.metrics_history = self.metrics_history[-self.max_history_size:]
            
            self.logger.debug(f"批處理統計已更新: {asdict(metrics)}")
            
        except Exception as e:
            self.logger.error(f"更新批處理統計失敗: {e}")
    
    async def get_retry_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """獲取重試統計信息"""
        try:
            since_time = datetime.utcnow() - timedelta(hours=hours)
            
            # 過濾指定時間範圍的指標
            recent_metrics = [
                m for m in self.metrics_history 
                if m.timestamp >= since_time
            ]
            
            if not recent_metrics:
                return {
                    'period_hours': hours,
                    'total_batches': 0,
                    'total_retries': 0,
                    'overall_success_rate': 0,
                    'avg_batch_size': 0,
                    'error_summary': {},
                    'priority_summary': {},
                    'performance_trends': {}
                }
            
            # 計算匯總統計
            total_batches = len(recent_metrics)
            total_retries = sum(m.total_retries for m in recent_metrics)
            total_successful = sum(m.successful_retries for m in recent_metrics)
            
            overall_success_rate = (total_successful / total_retries * 100) if total_retries > 0 else 0
            avg_batch_size = total_retries / total_batches if total_batches > 0 else 0
            
            # 錯誤匯總
            error_summary = {}
            priority_summary = {}
            
            for metrics in recent_metrics:
                for error_type, count in metrics.error_distribution.items():
                    error_summary[error_type] = error_summary.get(error_type, 0) + count
                
                for priority, count in metrics.priority_distribution.items():
                    priority_summary[priority] = priority_summary.get(priority, 0) + count
            
            # 性能趨勢
            performance_trends = {
                'avg_duration_trend': [m.avg_retry_duration for m in recent_metrics[-10:]],
                'success_rate_trend': [m.retry_success_rate for m in recent_metrics[-10:]],
                'volume_trend': [m.total_retries for m in recent_metrics[-10:]]
            }
            
            return {
                'period_hours': hours,
                'total_batches': total_batches,
                'total_retries': total_retries,
                'total_successful': total_successful,
                'overall_success_rate': overall_success_rate,
                'avg_batch_size': avg_batch_size,
                'error_summary': error_summary,
                'priority_summary': priority_summary,
                'performance_trends': performance_trends,
                'last_updated': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"獲取重試統計失敗: {e}")
            return {'error': str(e)}
    
    async def get_performance_analysis(self) -> Dict[str, Any]:
        """獲取性能分析"""
        try:
            if len(self.metrics_history) < 2:
                return {'message': '數據不足，無法進行性能分析'}
            
            recent_metrics = self.metrics_history[-10:]  # 最近10個批次
            
            # 計算趨勢
            success_rates = [m.retry_success_rate for m in recent_metrics]
            durations = [m.avg_retry_duration for m in recent_metrics]
            
            # 成功率趨勢
            success_rate_trend = 'stable'
            if len(success_rates) >= 3:
                recent_avg = sum(success_rates[-3:]) / 3
                earlier_avg = sum(success_rates[-6:-3]) / 3 if len(success_rates) >= 6 else recent_avg
                
                if recent_avg > earlier_avg + 5:
                    success_rate_trend = 'improving'
                elif recent_avg < earlier_avg - 5:
                    success_rate_trend = 'declining'
            
            # 性能趨勢
            performance_trend = 'stable'
            if len(durations) >= 3:
                recent_avg_duration = sum(durations[-3:]) / 3
                earlier_avg_duration = sum(durations[-6:-3]) / 3 if len(durations) >= 6 else recent_avg_duration
                
                if recent_avg_duration > earlier_avg_duration * 1.2:
                    performance_trend = 'slower'
                elif recent_avg_duration < earlier_avg_duration * 0.8:
                    performance_trend = 'faster'
            
            # 識別問題模式
            issues = []
            if success_rates and success_rates[-1] < 70:
                issues.append('當前成功率較低')
            
            if durations and durations[-1] > 30:
                issues.append('重試持續時間過長')
            
            # 建議
            recommendations = []
            if success_rate_trend == 'declining':
                recommendations.append('檢查網絡連接和服務器狀態')
            
            if performance_trend == 'slower':
                recommendations.append('考慮優化重試策略或增加並發數')
            
            if not recommendations:
                recommendations.append('系統運行正常')
            
            return {
                'success_rate_trend': success_rate_trend,
                'performance_trend': performance_trend,
                'current_success_rate': success_rates[-1] if success_rates else 0,
                'current_avg_duration': durations[-1] if durations else 0,
                'identified_issues': issues,
                'recommendations': recommendations,
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"性能分析失敗: {e}")
            return {'error': str(e)}
    
    def export_metrics(self, format: str = 'json') -> str:
        """導出指標數據"""
        try:
            if format == 'json':
                metrics_data = []
                for metrics in self.metrics_history:
                    data = asdict(metrics)
                    data['timestamp'] = data['timestamp'].isoformat()
                    metrics_data.append(data)
                
                return json.dumps(metrics_data, indent=2)
            else:
                raise ValueError(f"不支援的格式: {format}")
                
        except Exception as e:
            self.logger.error(f"導出指標失敗: {e}")
            return ""
```

### Step 4: EmailSyncService 擴展 (0.5天)

**修改檔案**: `backend/shared/infrastructure/adapters/email_inbox/email_sync_service.py`

添加單個郵件重試支援：

```python
# 在 EmailSyncService 中添加方法

async def sync_single_email(self, email_id: int) -> Dict[str, Any]:
    """
    重新同步單個郵件
    用於重試失敗的郵件下載
    
    Args:
        email_id: 郵件 ID
        
    Returns:
        同步結果
    """
    try:
        # 根據 email_id 查找郵件信息
        email_record = self.database.get_email_by_id(email_id)
        if not email_record:
            return {
                'success': False,
                'message': f'找不到郵件記錄: email_id={email_id}'
            }
        
        start_time = time.time()
        
        # 重新連接到郵件服務器（如果需要）
        if not await self.email_reader.connect():
            return {
                'success': False,
                'message': '無法連接到郵件服務器'
            }
        
        try:
            # 根據 message_id 重新下載郵件
            email_data = await self.email_reader.get_email_by_message_id(
                email_record.message_id
            )
            
            if email_data:
                # 更新資料庫記錄
                updated = self.database.update_email(email_id, email_data)
                
                end_time = time.time()
                duration = end_time - start_time
                
                if updated:
                    return {
                        'success': True,
                        'message': '郵件重新同步成功',
                        'size': len(email_data.body.encode('utf-8')) if email_data.body else 0,
                        'duration': duration
                    }
                else:
                    return {
                        'success': False,
                        'message': '更新資料庫記錄失敗'
                    }
            else:
                return {
                    'success': False,
                    'message': '無法重新下載郵件'
                }
                
        finally:
            await self.email_reader.disconnect()
            
    except Exception as e:
        self.logger.error(f"重新同步郵件失敗: email_id={email_id}, error={e}")
        return {
            'success': False,
            'message': f'重新同步郵件異常: {e}'
        }
```

---

## 🧪 測試策略

### 單元測試

**檔案位置**: `tests/unit/email/services/test_email_download_retry_service.py`

```python
"""
EmailDownloadRetryService 單元測試
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from backend.email.services.email_download_retry_service import (
    EmailDownloadRetryService, RetryTask, RetryPriority
)

class TestEmailDownloadRetryService:
    
    @pytest.fixture
    def retry_service(self):
        return EmailDownloadRetryService()
    
    @pytest.mark.asyncio
    async def test_process_pending_retries_empty(self, retry_service):
        """測試處理空的重試隊列"""
        with patch.object(retry_service, '_get_prioritized_retry_tasks') as mock_get_tasks:
            mock_get_tasks.return_value = []
            
            result = await retry_service.process_pending_retries()
            
            assert result['success'] == True
            assert result['processed_count'] == 0
            assert '沒有待重試任務' in result['message']
    
    @pytest.mark.asyncio
    async def test_trigger_manual_retry(self, retry_service):
        """測試手動觸發重試"""
        email_ids = [1, 2, 3]
        
        with patch.object(retry_service.download_tracker, 'start_download_tracking') as mock_start:
            mock_start.return_value = 123
            
            with patch.object(retry_service, '_schedule_immediate_retry') as mock_schedule:
                mock_schedule.return_value = True
                
                result = await retry_service.trigger_manual_retry(email_ids)
                
                assert result['success'] == True
                assert len(result['results']) == 3
                assert mock_start.call_count == 3
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_mechanism(self, retry_service):
        """測試斷路器機制"""
        # 設置高失敗計數
        retry_service._failure_count = 15
        retry_service.config['circuit_breaker_threshold'] = 10
        
        # 檢查斷路器應該開啟
        is_open = await retry_service._check_circuit_breaker()
        assert is_open == True
        assert retry_service._circuit_breaker_open == True
        
        # 重置失敗計數
        retry_service._failure_count = 2
        
        # 檢查斷路器應該關閉
        is_open = await retry_service._check_circuit_breaker()
        assert is_open == False
        assert retry_service._circuit_breaker_open == False
```

### 整合測試

**檔案位置**: `tests/integration/test_retry_mechanism_integration.py`

```python
"""
重試機制整合測試
"""

import pytest
import asyncio
from unittest.mock import Mock

from backend.email.services.email_download_retry_service import EmailDownloadRetryService
from backend.tasks.email_download_tasks import process_email_download_retries

class TestRetryMechanismIntegration:
    
    @pytest.mark.asyncio
    async def test_end_to_end_retry_flow(self):
        """測試端到端重試流程"""
        retry_service = EmailDownloadRetryService()
        
        # 模擬初始化
        with patch.object(retry_service, 'initialize'):
            await retry_service.initialize()
            
            # 觸發手動重試
            result = await retry_service.trigger_manual_retry([1, 2])
            assert result['success'] == True
            
            # 處理待重試任務
            process_result = await retry_service.process_pending_retries()
            assert 'processed_count' in process_result
    
    def test_dramatiq_task_execution(self):
        """測試 Dramatiq 任務執行"""
        # 這裡可以測試任務的序列化和反序列化
        # 以及任務參數的正確性
        pass
```

---

## 📊 性能和監控

### 性能指標

1. **重試效率指標**
   - 重試成功率
   - 平均重試時間
   - 重試隊列長度

2. **系統性能指標**
   - 並發處理能力
   - 記憶體使用量
   - CPU 使用率

3. **業務指標**
   - 最終成功率提升
   - 平均恢復時間
   - 用戶滿意度

### 監控告警

1. **重試成功率 < 70%**
2. **重試隊列積壓 > 100**
3. **平均重試時間 > 60秒**
4. **斷路器觸發**

---

## 🎯 成功標準

### 功能成功標準
- ✅ 自動重試機制正常工作
- ✅ 手動重試功能可用
- ✅ Dramatiq 任務正確執行
- ✅ 斷路器保護機制有效
- ✅ 重試優先級機制運作正常

### 性能成功標準
- ✅ 重試成功率 > 80%
- ✅ 平均重試時間 < 30秒
- ✅ 並發處理能力 > 10 tasks/sec
- ✅ 系統可用性 > 99.5%

### 質量成功標準
- ✅ 單元測試覆蓋率 > 90%
- ✅ 整合測試全部通過
- ✅ 壓力測試通過
- ✅ 監控指標正常

---

## 📋 交付清單

### 代碼文件
- [ ] `email_download_retry_service.py` - 主重試服務
- [ ] `retry_monitor.py` - 監控器
- [ ] `email_download_tasks.py` - Dramatiq 任務
- [ ] `email_sync_service.py` - 擴展修改

### 測試文件
- [ ] 完整的單元測試套件
- [ ] 整合測試套件
- [ ] 壓力測試腳本

### 文檔
- [ ] 重試機制使用指南
- [ ] 監控告警配置
- [ ] 故障排除手冊

---

**Story 完成標準**: 所有驗收標準通過 ✅ 且交付清單項目完成 📋

**下一個 Story**: [Story 4: API 端點實現](./email-download-tracking-story-4-api-endpoints.md)