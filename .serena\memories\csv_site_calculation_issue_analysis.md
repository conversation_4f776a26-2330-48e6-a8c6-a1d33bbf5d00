# CSV Site計算問題分析

## 問題描述
用戶報告使用 `python .\csv_to_summary.py '.\doc\G2514\F2570680A\'` 生成的 FT_SUMMARY.csv 中：
- Site計算邏輯似乎是固定位置/固定site
- 沒有正確存儲每個summary的每個site數量
- 測試時有 1,2,4 但最終summary卻是 1,2,3
- 需要支持動態site編號 (如 1,2 或 3,4,5 等組合)

## 任務目標
1. 分析 csv_to_summary.py 中的site計算邏輯問題
2. 識別固定site邏輯的具體位置
3. 修復為動態site數量統計
4. 實際測試驗證修復結果

## 測試案例
- 目錄: `.\doc\G2514\F2570680A\`
- 預期行為: 動態檢測實際存在的site編號並正確統計
- 驗證方式: 執行修復後的腳本並檢查 FT_SUMMARY.csv 結果