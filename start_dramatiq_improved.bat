@echo off
chcp 65001 >nul 2>&1

REM Enhanced Dramatiq Worker Startup Script
REM Automatically detects virtual environment and provides better error handling

set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set LANG=en_US.UTF-8

echo.
echo ========================================
echo   Enhanced Dramatiq Worker Startup
echo   Production Grade Async Task Processing
echo ========================================
echo.

REM Auto-detect virtual environment
set VENV_PATH=
if exist "venv_win_3_11_9\Scripts\python.exe" (
    set VENV_PATH=venv_win_3_11_9
    echo [INFO] Found virtual environment: venv_win_3_11_9
) else if exist "venv_win_3_11_12\Scripts\python.exe" (
    set VENV_PATH=venv_win_3_11_12
    echo [INFO] Found virtual environment: venv_win_3_11_12
) else if exist "venv\Scripts\python.exe" (
    set VENV_PATH=venv
    echo [INFO] Found virtual environment: venv
) else (
    echo [ERROR] No virtual environment found!
    echo.
    echo Please ensure one of these exists:
    echo   - venv_win_3_11_9\Scripts\python.exe
    echo   - venv_win_3_11_12\Scripts\python.exe  
    echo   - venv\Scripts\python.exe
    echo.
    pause
    exit /b 1
)

REM Set default parameters
set PROCESSES=%1
set THREADS=%2

if "%PROCESSES%"=="" set PROCESSES=4
if "%THREADS%"=="" set THREADS=8

echo Configuration:
echo   - Virtual Environment: %VENV_PATH%
echo   - Processes: %PROCESSES%
echo   - Threads: %THREADS%
echo   - Task Module: dramatiq_tasks
echo.

REM Check Redis connection
echo [INFO] Testing Redis connection...
%VENV_PATH%\Scripts\python.exe -c "import redis; r=redis.Redis(host='localhost', port=6379, db=0); r.ping(); print('[SUCCESS] Redis connection OK')" 2>nul
if errorlevel 1 (
    echo [WARNING] Redis connection failed - using memory mode
    set USE_MEMORY_BROKER=true
) else (
    echo [SUCCESS] Redis connected - using production mode
    set USE_MEMORY_BROKER=false
)

echo.
echo [INFO] Testing dramatiq_tasks import...
%VENV_PATH%\Scripts\python.exe -c "import dramatiq_tasks; print('[SUCCESS] dramatiq_tasks imported')" 2>nul
if errorlevel 1 (
    echo [ERROR] Failed to import dramatiq_tasks module
    echo Please check the module exists and dependencies are installed
    pause
    exit /b 1
)

echo.
echo [INFO] Starting Dramatiq Worker...
echo [INFO] Press Ctrl+C to stop the worker
echo.

REM Set environment variables
set DRAMATIQ_PROCESSES=%PROCESSES%
set DRAMATIQ_THREADS=%THREADS%
set PYTHONPATH=%CD%;%PYTHONPATH%

REM Start Dramatiq Worker
%VENV_PATH%\Scripts\python.exe -m dramatiq dramatiq_tasks ^
    --processes %PROCESSES% ^
    --threads %THREADS% ^
    --verbose

REM Handle exit
if errorlevel 1 (
    echo.
    echo [ERROR] Dramatiq Worker failed to start
    echo.
    echo Troubleshooting:
    echo   1. Ensure Redis is running: redis-server
    echo   2. Check virtual environment: %VENV_PATH%\Scripts\python.exe --version
    echo   3. Verify task module: python -c "import dramatiq_tasks"
    echo   4. Check logs above for specific errors
    echo.
) else (
    echo.
    echo [INFO] Dramatiq Worker stopped gracefully
)

pause