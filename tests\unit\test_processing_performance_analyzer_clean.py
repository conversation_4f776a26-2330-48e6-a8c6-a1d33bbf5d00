"""
測試 Epic-03 Story 3.4: ProcessingPerformanceAnalyzer
處理性能監控和分析的完整測試
"""

import pytest
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from typing import Dict, Any

from backend.shared.services.processing_performance_analyzer import (
    ProcessingPerformanceAnalyzer,
    ProcessingStageMetrics,
    PerformanceAlert,
    BottleneckAnalysis
)


class TestProcessingStageMetrics:
    """ProcessingStageMetrics 數據類測試"""
    
    def test_stage_metrics_initialization(self):
        """測試階段指標初始化"""
        metrics = ProcessingStageMetrics(stage_name="test_stage")
        
        assert metrics.stage_name == "test_stage"
        assert metrics.total_count == 0
        assert metrics.success_count == 0
        assert metrics.failed_count == 0
        assert metrics.total_duration == 0.0
        assert metrics.min_duration == float('inf')
        assert metrics.max_duration == 0.0
        assert len(metrics.durations) == 0
    
    def test_stage_metrics_calculations(self):
        """測試階段指標計算"""
        metrics = ProcessingStageMetrics(stage_name="test_stage")
        
        # 添加一些測試數據
        metrics.total_count = 10
        metrics.success_count = 8
        metrics.failed_count = 2
        metrics.total_duration = 100.0
        metrics.min_duration = 5.0
        metrics.max_duration = 20.0
        metrics.durations.extend([5.0, 8.0, 10.0, 12.0, 15.0, 18.0, 20.0])
        
        # 測試計算屬性
        assert metrics.average_duration == 10.0
        assert metrics.success_rate == 0.8
        assert metrics.median_duration == 12.0
        assert metrics.percentile_95_duration > 15.0


class TestProcessingPerformanceAnalyzer:
    """ProcessingPerformanceAnalyzer 主要功能測試"""
    
    @pytest.fixture
    def analyzer(self):
        """創建測試用的性能分析器"""
        return ProcessingPerformanceAnalyzer()
    
    def test_analyzer_initialization(self, analyzer):
        """測試分析器初始化"""
        assert analyzer is not None
        assert len(analyzer.stage_metrics) == 0
        assert len(analyzer.email_processing_records) == 0
        assert len(analyzer.performance_alerts) == 0
        assert 'avg_processing_time' in analyzer.alert_thresholds
        assert 'email_parsing' in analyzer.baseline_metrics
    
    def test_start_email_processing(self, analyzer):
        """測試開始郵件處理追蹤"""
        email_id = 12345
        metadata = {'sender': '<EMAIL>', 'subject': 'Test Email'}
        
        analyzer.start_email_processing(email_id, metadata)
        
        assert email_id in analyzer.email_processing_records
        record = analyzer.email_processing_records[email_id]
        assert record['status'] == 'processing'
        assert record['metadata'] == metadata
        assert 'start_time' in record
        assert record['stages'] == {}
    
    def test_track_stage_lifecycle(self, analyzer):
        """測試階段生命週期追蹤"""
        email_id = 12345
        stage_name = "email_parsing"
        
        # 開始處理
        analyzer.start_email_processing(email_id)
        
        # 開始階段
        analyzer.track_stage_start(email_id, stage_name)
        
        assert stage_name in analyzer.email_processing_records[email_id]['stages']
        stage_info = analyzer.email_processing_records[email_id]['stages'][stage_name]
        assert stage_info['status'] == 'running'
        assert 'start_time' in stage_info
        
        # 模擬處理時間
        time.sleep(0.01)
        
        # 完成階段
        duration = analyzer.track_stage_completion(email_id, stage_name, success=True)
        
        assert duration > 0
        assert stage_info['status'] == 'completed'
        assert stage_info['success'] == True
        assert stage_info['duration'] == duration
        
        # 檢查階段指標是否更新
        assert stage_name in analyzer.stage_metrics
        metrics = analyzer.stage_metrics[stage_name]
        assert metrics.total_count == 1
        assert metrics.success_count == 1
        assert metrics.failed_count == 0
    
    def test_complete_email_processing(self, analyzer):
        """測試完成郵件處理"""
        email_id = 12345
        
        # 設置一個完整的處理流程
        analyzer.start_email_processing(email_id, {'test': 'data'})
        
        # 添加幾個處理階段
        stages = ['email_parsing', 'attachment_processing', 'database_update']
        for stage in stages:
            analyzer.track_stage_start(email_id, stage)
            time.sleep(0.01)  # 模擬處理時間
            analyzer.track_stage_completion(email_id, stage, success=True)
        
        # 完成處理
        final_metadata = {'result': 'success'}
        summary = analyzer.complete_email_processing(email_id, overall_success=True, 
                                                   final_metadata=final_metadata)
        
        # 驗證摘要
        assert summary['email_id'] == email_id
        assert summary['overall_success'] == True
        assert summary['total_duration'] > 0
        assert summary['stage_count'] == 3
        assert summary['successful_stages'] == 3
        assert 'stages_summary' in summary
        
        # 檢查記錄狀態
        record = analyzer.email_processing_records[email_id]
        assert record['status'] == 'completed'
        assert record['overall_success'] == True
        assert record['final_metadata'] == final_metadata
    
    def test_get_real_time_metrics(self, analyzer):
        """測試獲取實時性能指標"""
        # 添加一些測試數據
        email_id = 12345
        analyzer.start_email_processing(email_id)
        analyzer.track_stage_start(email_id, "email_parsing")
        analyzer.track_stage_completion(email_id, "email_parsing", success=True)
        analyzer.complete_email_processing(email_id, overall_success=True)
        
        metrics = analyzer.get_real_time_metrics()
        
        assert 'timestamp' in metrics
        assert 'active_processing_count' in metrics
        assert 'hourly_stats' in metrics
        assert 'stage_performance' in metrics
        assert 'recent_alerts' in metrics
        assert 'total_alerts_count' in metrics
        
        # 檢查階段性能數據
        assert 'email_parsing' in metrics['stage_performance']
        parsing_stats = metrics['stage_performance']['email_parsing']
        assert parsing_stats['total_count'] == 1
        assert parsing_stats['success_rate'] == 1.0
    
    def test_get_performance_trends(self, analyzer):
        """測試性能趨勢分析"""
        # 模擬不同時間的處理數據
        current_time = time.time()
        
        with patch('time.time') as mock_time:
            # 模擬2小時前的處理
            mock_time.return_value = current_time - 7200  # 2小時前
            
            for i in range(5):
                email_id = 30000 + i
                analyzer.start_email_processing(email_id)
                analyzer.complete_email_processing(email_id, overall_success=True)
            
            # 模擬1小時前的處理
            mock_time.return_value = current_time - 3600  # 1小時前
            
            for i in range(8):
                email_id = 30100 + i
                analyzer.start_email_processing(email_id)
                analyzer.complete_email_processing(email_id, overall_success=True)
        
        trends = analyzer.get_performance_trends(hours=3)
        
        assert 'time_range' in trends
        assert 'hourly_trends' in trends
        assert 'trend_analysis' in trends
        
        assert trends['time_range']['hours'] == 3
        assert len(trends['hourly_trends']) == 3
        
        trend_analysis = trends['trend_analysis']
        assert 'duration_trend' in trend_analysis
        assert 'recent_avg_duration' in trend_analysis
        assert 'performance_change_percent' in trend_analysis
    
    def test_export_performance_report(self, analyzer):
        """測試導出性能報告"""
        # 添加一些測試數據
        email_id = 40000
        analyzer.start_email_processing(email_id)
        analyzer.track_stage_start(email_id, "email_parsing")
        analyzer.track_stage_completion(email_id, "email_parsing", success=True)
        analyzer.complete_email_processing(email_id, overall_success=True)
        
        report = analyzer.export_performance_report(hours=24)
        
        # 檢查報告結構
        assert 'report_metadata' in report
        assert 'real_time_metrics' in report
        assert 'bottleneck_analysis' in report
        assert 'performance_trends' in report
        assert 'performance_alerts' in report
        assert 'stage_details' in report
        
        # 檢查元數據
        metadata = report['report_metadata']
        assert 'generated_at' in metadata
        assert metadata['time_window_hours'] == 24
        assert 'analyzer_version' in metadata
        
        # 檢查階段詳情
        assert 'email_parsing' in report['stage_details']
        parsing_details = report['stage_details']['email_parsing']
        assert parsing_details['total_count'] == 1
        assert parsing_details['success_rate'] == 1.0


class TestPerformanceIntegration:
    """性能分析器整合測試"""
    
    def test_complete_email_processing_workflow(self):
        """測試完整的郵件處理工作流程"""
        analyzer = ProcessingPerformanceAnalyzer()
        
        email_id = 50000
        stages = [
            'email_parsing',
            'attachment_processing', 
            'vendor_files_processing',
            'database_update',
            'notification_sending'
        ]
        
        # 開始處理
        analyzer.start_email_processing(email_id, {
            'sender': '<EMAIL>',
            'subject': 'Test Email',
            'attachment_count': 3
        })
        
        # 模擬各個階段的處理
        stage_durations = []
        for stage in stages:
            analyzer.track_stage_start(email_id, stage)
            
            # 模擬不同的處理時間
            if stage == 'vendor_files_processing':
                time.sleep(0.05)  # 較長的處理時間
            else:
                time.sleep(0.01)  # 正常處理時間
            
            duration = analyzer.track_stage_completion(email_id, stage, success=True)
            stage_durations.append(duration)
        
        # 完成處理
        summary = analyzer.complete_email_processing(email_id, overall_success=True)
        
        # 驗證處理摘要
        assert summary['email_id'] == email_id
        assert summary['overall_success'] == True
        assert summary['stage_count'] == len(stages)
        assert summary['successful_stages'] == len(stages)
        assert summary['processing_efficiency'] > 0.0
        
        # 驗證所有階段都有指標
        for stage in stages:
            assert stage in analyzer.stage_metrics
            metrics = analyzer.stage_metrics[stage]
            assert metrics.total_count == 1
            assert metrics.success_count == 1
        
        # 獲取實時指標
        real_time_metrics = analyzer.get_real_time_metrics()
        assert real_time_metrics['active_processing_count'] == 0  # 處理已完成
    
    def test_multiple_concurrent_processing(self):
        """測試多個並發處理的監控"""
        analyzer = ProcessingPerformanceAnalyzer()
        
        # 開始多個並發處理
        email_ids = [60001, 60002, 60003, 60004, 60005]
        
        for email_id in email_ids:
            analyzer.start_email_processing(email_id, {'concurrent_test': True})
            analyzer.track_stage_start(email_id, 'email_parsing')
        
        # 檢查活躍處理數量
        metrics = analyzer.get_real_time_metrics()
        assert metrics['active_processing_count'] == len(email_ids)
        
        # 完成部分處理
        for i, email_id in enumerate(email_ids[:3]):
            analyzer.track_stage_completion(email_id, 'email_parsing', success=True)
            analyzer.complete_email_processing(email_id, overall_success=True)
        
        # 檢查更新後的活躍處理數量
        metrics = analyzer.get_real_time_metrics()
        assert metrics['active_processing_count'] == 2  # 還有2個在處理
        
        # 完成剩餘處理
        for email_id in email_ids[3:]:
            analyzer.track_stage_completion(email_id, 'email_parsing', success=True)
            analyzer.complete_email_processing(email_id, overall_success=True)
        
        # 最終檢查
        final_metrics = analyzer.get_real_time_metrics()
        assert final_metrics['active_processing_count'] == 0
        
        # 檢查統計數據
        parsing_stats = final_metrics['stage_performance']['email_parsing']
        assert parsing_stats['total_count'] == len(email_ids)
        assert parsing_stats['success_rate'] == 1.0


if __name__ == "__main__":
    # 運行性能分析器測試
    pytest.main([__file__, "-v", "--tb=short"])