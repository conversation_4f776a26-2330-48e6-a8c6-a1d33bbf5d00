"""
郵件下載追蹤資料庫模型
"""

from datetime import datetime
from enum import Enum
from typing import List
from sqlalchemy import (
    Column, Integer, String, DateTime, Float, Text, ForeignKey, 
    Enum as SQLEnum, Boolean, BigInteger, Index, UniqueConstraint
)
from sqlalchemy.orm import relationship, validates
from backend.shared.infrastructure.adapters.database.models import Base

class DownloadStatus(str, Enum):
    PENDING = "pending"
    DOWNLOADING = "downloading"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRY_SCHEDULED = "retry_scheduled"

class RetryStrategy(str, Enum):
    LINEAR = "linear"              # 線性間隔
    EXPONENTIAL = "exponential"    # 指數退避
    FIXED_DELAY = "fixed_delay"    # 固定間隔
    CUSTOM = "custom"              # 自定義間隔
    ADAPTIVE = "adaptive"          # 自適應策略

class RetryStatus(str, Enum):
    SCHEDULED = "scheduled"        # 已調度
    RUNNING = "running"           # 執行中
    SUCCESS = "success"           # 成功
    FAILED = "failed"             # 失敗
    CANCELLED = "cancelled"       # 已取消
    TIMEOUT = "timeout"           # 超時

# 狀態轉換驗證規則
VALID_STATUS_TRANSITIONS = {
    DownloadStatus.PENDING: [DownloadStatus.DOWNLOADING, DownloadStatus.FAILED],
    DownloadStatus.DOWNLOADING: [DownloadStatus.COMPLETED, DownloadStatus.FAILED, 
                                DownloadStatus.RETRY_SCHEDULED],
    DownloadStatus.COMPLETED: [],  # 完成狀態為終結狀態
    DownloadStatus.FAILED: [DownloadStatus.RETRY_SCHEDULED],
    DownloadStatus.RETRY_SCHEDULED: [DownloadStatus.DOWNLOADING, DownloadStatus.FAILED]
}

def validate_status_transition(current_status: DownloadStatus, new_status: DownloadStatus) -> bool:
    """驗證狀態轉換是否有效"""
    if current_status not in VALID_STATUS_TRANSITIONS:
        return False
    
    valid_transitions = VALID_STATUS_TRANSITIONS[current_status]
    return new_status in valid_transitions

def validate_download_status_data(data: dict) -> List[str]:
    """資料驗證函數"""
    errors = []
    
    # 進度驗證
    if 'download_progress' in data:
        progress = data.get('download_progress', 0)
        if progress < 0 or progress > 100:
            errors.append("下載進度必須在 0-100 之間")
    
    # 檔案大小驗證
    if 'file_size_bytes' in data and 'downloaded_bytes' in data:
        file_size = data.get('file_size_bytes', 0)
        downloaded = data.get('downloaded_bytes', 0)
        if downloaded > file_size and file_size > 0:
            errors.append("已下載位元組不能超過總檔案大小")
        
    # 時間邏輯驗證
    if data.get('completed_at') and data.get('started_at'):
        if data['completed_at'] < data['started_at']:
            errors.append("完成時間不能早於開始時間")
    
    return errors

class EmailDownloadStatusDB(Base):
    __tablename__ = 'email_download_status'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    email_id = Column(Integer, ForeignKey('emails.id'), nullable=False, index=True)
    
    status = Column(SQLEnum(DownloadStatus), default=DownloadStatus.PENDING, nullable=False)
    download_attempt = Column(Integer, default=1, nullable=False)
    max_retry_count = Column(Integer, default=3, nullable=False)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    last_retry_at = Column(DateTime)
    next_retry_at = Column(DateTime, index=True)
    
    error_type = Column(String(50))
    error_message = Column(Text)
    error_details = Column(Text)
    
    download_size_bytes = Column(Integer)
    download_duration_seconds = Column(Float)
    server_response_code = Column(String(10))
    is_remote_download_success = Column(Boolean, default=False, nullable=False)
    is_processing_success = Column(Boolean, default=False, nullable=False)
    
    retry_strategy = Column(SQLEnum(RetryStrategy), default=RetryStrategy.EXPONENTIAL)
    retry_interval_seconds = Column(Integer, default=60)
    
    # Story 1.2: 新增優化欄位
    download_progress = Column(Float, default=0.0, nullable=False, comment="下載進度 (0-100)")
    downloaded_bytes = Column(BigInteger, default=0, nullable=False, comment="已下載位元組數")
    file_size_bytes = Column(BigInteger, nullable=True, comment="檔案總大小")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新時間")
    
    # Story 1.2: 優化索引和約束
    __table_args__ = (
        Index('idx_email_download_status_email_id', 'email_id'),
        Index('idx_email_download_status_status', 'status'),
        Index('idx_email_download_status_created_at', 'created_at'),
        Index('idx_email_download_status_completed_at', 'completed_at'),
        Index('idx_email_download_status_composite', 'email_id', 'status'),
        UniqueConstraint('email_id', name='uq_email_download_status_email_id'),
    )
    
    # 關聯
    email = relationship("EmailDB", back_populates="download_status")
    retry_logs = relationship("EmailDownloadRetryLogDB", back_populates="download_status", cascade="all, delete-orphan")
    
    def __init__(self, **kwargs):
        """初始化方法，確保預設值正確設置"""
        # 設置新增欄位的預設值
        if 'download_progress' not in kwargs:
            kwargs['download_progress'] = 0.0
        if 'downloaded_bytes' not in kwargs:
            kwargs['downloaded_bytes'] = 0
        if 'updated_at' not in kwargs:
            kwargs['updated_at'] = datetime.utcnow()
        
        super().__init__(**kwargs)
    
    # Story 1.2: 模型驗證方法
    def validate_progress(self):
        """驗證下載進度"""
        if not (0 <= self.download_progress <= 100):
            raise ValueError("下載進度必須在 0-100 之間")
    
    def validate_status_transition(self, new_status: DownloadStatus):
        """驗證狀態轉換"""
        if not validate_status_transition(self.status, new_status):
            raise ValueError(f"無效的狀態轉換: {self.status} -> {new_status}")
    
    @validates('download_progress')
    def validate_progress_range(self, key, value):
        """SQLAlchemy 驗證器：下載進度範圍"""
        if value is not None and (value < 0 or value > 100):
            raise ValueError("下載進度必須在 0-100 之間")
        return value
    
    @validates('downloaded_bytes', 'file_size_bytes')  
    def validate_bytes_consistency(self, key, value):
        """SQLAlchemy 驗證器：位元組數一致性"""
        if key == 'downloaded_bytes' and value is not None:
            if hasattr(self, 'file_size_bytes') and self.file_size_bytes is not None:
                if value > self.file_size_bytes:
                    raise ValueError("已下載位元組數不能超過總檔案大小")
        return value

class EmailDownloadRetryLogDB(Base):
    __tablename__ = 'email_download_retry_log'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    download_status_id = Column(Integer, ForeignKey('email_download_status.id'), nullable=False)
    email_id = Column(Integer, ForeignKey('emails.id'), nullable=False)
    retry_attempt = Column(Integer, nullable=False)
    retry_strategy = Column(SQLEnum(RetryStrategy), nullable=False)
    scheduled_at = Column(DateTime, nullable=False)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    status = Column(SQLEnum(RetryStatus), default=RetryStatus.SCHEDULED, nullable=False)
    error_type = Column(String(100), nullable=True)
    error_message = Column(Text, nullable=True)
    retry_delay_seconds = Column(Integer, nullable=False)
    success = Column(Boolean, default=False)
    duration_ms = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 索引和約束設計
    __table_args__ = (
        Index('idx_retry_log_download_status_id', 'download_status_id'),
        Index('idx_retry_log_email_id', 'email_id'),
        Index('idx_retry_log_status', 'status'),
        Index('idx_retry_log_scheduled_at', 'scheduled_at'),
        Index('idx_retry_log_retry_strategy', 'retry_strategy'),
        Index('idx_retry_log_composite', 'email_id', 'retry_attempt'),
        Index('idx_retry_log_error_type', 'error_type'),
    )
    
    # 關聯
    download_status = relationship("EmailDownloadStatusDB", back_populates="retry_logs")
    email = relationship("EmailDB", back_populates="retry_logs")
    
    def __init__(self, **kwargs):
        """初始化方法，確保預設值正確設置"""
        if 'status' not in kwargs:
            kwargs['status'] = RetryStatus.SCHEDULED
        if 'success' not in kwargs:
            kwargs['success'] = False
        if 'created_at' not in kwargs:
            kwargs['created_at'] = datetime.utcnow()
        if 'updated_at' not in kwargs:
            kwargs['updated_at'] = datetime.utcnow()
        
        super().__init__(**kwargs)
    
    @validates('retry_attempt')
    def validate_retry_attempt(self, key, value):
        """SQLAlchemy 驗證器：重試次數範圍"""
        if value is not None and (value < 1 or value > 10):
            raise ValueError("Retry attempt must be between 1-10")
        return value
    
    @validates('retry_delay_seconds')
    def validate_retry_delay(self, key, value):
        """SQLAlchemy 驗證器：重試延遲範圍"""
        if value is not None and (value < 0 or value > 3600):
            raise ValueError("Retry delay must be between 0-3600 seconds")
        return value
    
    @validates('started_at', 'completed_at')
    def validate_time_sequence(self, key, value):
        """SQLAlchemy 驗證器：時間順序驗證"""
        if key == 'started_at' and value is not None:
            if hasattr(self, 'scheduled_at') and self.scheduled_at and value < self.scheduled_at:
                raise ValueError("Started time cannot be earlier than scheduled time")
        elif key == 'completed_at' and value is not None:
            if hasattr(self, 'started_at') and self.started_at and value < self.started_at:
                raise ValueError("Completed time cannot be earlier than started time")
        return value
    
    @validates('status')
    def validate_status_consistency(self, key, value):
        """SQLAlchemy 驗證器：狀態一致性驗證"""
        if value == RetryStatus.SUCCESS and not self.success:
            self.success = True
        elif value != RetryStatus.SUCCESS and self.success:
            # 只有當狀態不是 SUCCESS 時才設為 False，避免狀態不一致
            pass
        return value
    
    def validate_time_logic(self):
        """驗證時間邏輯一致性"""
        errors = []
        
        if self.completed_at and self.started_at and self.completed_at < self.started_at:
            errors.append("完成時間不能早於開始時間")
        
        if self.started_at and self.scheduled_at and self.started_at < self.scheduled_at:
            errors.append("開始時間不能早於調度時間")
        
        if self.completed_at and self.scheduled_at and self.completed_at < self.scheduled_at:
            errors.append("完成時間不能早於調度時間")
        
        return errors
    
    def validate_business_rules(self):
        """驗證業務規則"""
        errors = []
        
        # 驗證成功標誌與狀態一致性
        if self.success and self.status != RetryStatus.SUCCESS:
            errors.append("成功標誌與狀態不一致")
        
        # 驗證重試次數
        if self.retry_attempt < 1 or self.retry_attempt > 10:
            errors.append("重試次數必須在 1-10 之間")
        
        # 驗證延遲時間
        if self.retry_delay_seconds < 0 or self.retry_delay_seconds > 3600:
            errors.append("重試延遲必須在 0-3600 秒之間")
        
        # 驗證時間邏輯
        errors.extend(self.validate_time_logic())
        
        return errors