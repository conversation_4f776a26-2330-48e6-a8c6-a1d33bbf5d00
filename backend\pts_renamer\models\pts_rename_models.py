"""
PTS Renamer Pydantic Models

This module contains Pydantic models for API requests, responses, and data validation
for the PTS File Renamer service. These models ensure type safety and automatic
validation for all API interactions.
"""

from pydantic import BaseModel, Field, validator, root_validator
from typing import List, Optional, Dict, Any, Union
from enum import Enum
from datetime import datetime
import re
import os
from pathlib import Path
import json


class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder for datetime objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class PTSRenameOperation(str, Enum):
    """Enumeration of available PTS processing operations"""
    RENAME = "rename"
    QC_GENERATION = "qc_generation"
    DIRECTORY_CREATION = "directory_creation"


class PTSRenameJobRequest(BaseModel):
    """Request model for PTS processing job"""
    upload_id: str = Field(..., min_length=1, max_length=255, description="Unique identifier for uploaded files")
    operations: List[PTSRenameOperation] = Field(..., description="List of operations to perform")
    rename_config: Optional[Dict[str, str]] = Field(None, description="Rename pattern configuration")
    qc_enabled: bool = Field(False, description="Enable QC file generation")
    create_directories: bool = Field(False, description="Enable directory structure creation")
    processing_options: Optional[Dict[str, Any]] = Field(None, description="Additional processing options")
    priority: int = Field(1, ge=1, le=10, description="Job priority (1=lowest, 10=highest)")
    timeout_seconds: Optional[int] = Field(None, ge=30, le=3600, description="Processing timeout override")
    
    @validator('upload_id')
    def validate_upload_id(cls, v):
        # Validate upload ID format (alphanumeric with hyphens/underscores)
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError("Upload ID must contain only alphanumeric characters, hyphens, and underscores")
        return v
    
    @validator('operations')
    def validate_operations(cls, v):
        if not v:
            raise ValueError("At least one operation must be specified")
        # Remove duplicates while preserving order
        seen = set()
        unique_ops = []
        for op in v:
            if op not in seen:
                seen.add(op)
                unique_ops.append(op)
        return unique_ops
    
    @validator('rename_config')
    def validate_rename_config(cls, v, values):
        if PTSRenameOperation.RENAME in values.get('operations', []):
            if not v or 'old_pattern' not in v or 'new_pattern' not in v:
                raise ValueError("Rename configuration required when rename operation is selected")
            
            # Validate regex pattern
            try:
                re.compile(v['old_pattern'])
            except re.error as e:
                raise ValueError(f"Invalid regex pattern in old_pattern: {e}")
            
            # Validate new pattern contains valid placeholders
            new_pattern = v['new_pattern']
            valid_placeholders = ['{old}', '{ext}', '{num}', '{name}', '{basename}']
            
            # Check for invalid characters in filename
            invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
            for char in invalid_chars:
                if char in new_pattern.replace('{old}', '').replace('{ext}', '').replace('{num}', '').replace('{name}', '').replace('{basename}', ''):
                    raise ValueError(f"Invalid character '{char}' in new_pattern")
            
            # Validate pattern length
            if len(new_pattern) > 200:
                raise ValueError("New pattern too long (max 200 characters)")
                
        return v
    
    @root_validator(skip_on_failure=True)
    def validate_operation_consistency(cls, values):
        operations = values.get('operations', [])
        qc_enabled = values.get('qc_enabled', False)
        create_directories = values.get('create_directories', False)
        
        # Ensure QC operation is included if qc_enabled is True
        if qc_enabled and PTSRenameOperation.QC_GENERATION not in operations:
            operations.append(PTSRenameOperation.QC_GENERATION)
            values['operations'] = operations
            
        # Ensure directory creation operation is included if create_directories is True
        if create_directories and PTSRenameOperation.DIRECTORY_CREATION not in operations:
            operations.append(PTSRenameOperation.DIRECTORY_CREATION)
            values['operations'] = operations
            
        return values


class PTSRenameJobStatus(BaseModel):
    """Status model for PTS processing job"""
    job_id: str = Field(..., description="Unique job identifier")
    status: str = Field(..., description="Current job status")
    progress: int = Field(0, ge=0, le=100, description="Progress percentage (0-100)")
    files_processed: int = Field(0, ge=0, description="Number of files processed")
    total_files: int = Field(0, ge=0, description="Total number of files to process")
    error_message: Optional[str] = Field(None, description="Error message if job failed")
    result_download_url: Optional[str] = Field(None, description="Download URL for results")
    compressed_file_size: Optional[int] = Field(None, description="Size of compressed result file")
    compressed_file_name: Optional[str] = Field(None, description="Name of compressed result file")
    download_expires_at: Optional[datetime] = Field(None, description="Download URL expiration time")
    created_at: datetime = Field(default_factory=datetime.now, description="Job creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class PTSRenamePreviewRequest(BaseModel):
    """Request model for processing preview"""
    upload_id: str = Field(..., description="Unique identifier for uploaded files")
    operations: List[PTSRenameOperation] = Field(..., description="Operations to preview")
    rename_config: Optional[Dict[str, str]] = Field(None, description="Rename pattern configuration")


class PTSFilePreview(BaseModel):
    """Preview information for a single PTS file"""
    original_name: str = Field(..., description="Original filename")
    new_name: Optional[str] = Field(None, description="New filename after rename")
    qc_file_name: Optional[str] = Field(None, description="QC file name if generated")
    directory_name: Optional[str] = Field(None, description="Directory name if created")
    file_size: int = Field(..., description="File size in bytes")
    operations_applied: List[str] = Field(..., description="List of operations that will be applied")
    warnings: List[str] = Field(default_factory=list, description="Any warnings for this file")


class PTSRenamePreviewResponse(BaseModel):
    """Response model for processing preview"""
    upload_id: str = Field(..., description="Upload identifier")
    total_files: int = Field(..., description="Total number of PTS files found")
    files_preview: List[PTSFilePreview] = Field(..., description="Preview for each file")
    operations_summary: Dict[str, int] = Field(..., description="Summary of operations to be performed")
    estimated_processing_time: int = Field(..., description="Estimated processing time in seconds")
    warnings: List[str] = Field(default_factory=list, description="General warnings")
    errors: List[str] = Field(default_factory=list, description="Validation errors")


class PTSFileUploadInfo(BaseModel):
    """Information about an uploaded file"""
    filename: str = Field(..., description="Original filename")
    size: int = Field(..., ge=0, description="File size in bytes")
    content_type: str = Field(..., description="MIME content type")
    checksum: Optional[str] = Field(None, description="File checksum (SHA-256)")
    is_compressed: bool = Field(False, description="Whether file is compressed")
    extracted_files: Optional[List[str]] = Field(None, description="List of extracted files if compressed")
    
    @validator('filename')
    def validate_filename(cls, v):
        # Basic filename validation
        if not v or len(v.strip()) == 0:
            raise ValueError("Filename cannot be empty")
        if len(v) > 255:
            raise ValueError("Filename too long (max 255 characters)")
        
        # Check for dangerous characters
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in dangerous_chars:
            if char in v:
                raise ValueError(f"Filename contains invalid character: {char}")
        
        return v.strip()
    
    @validator('size')
    def validate_file_size(cls, v):
        max_size = 100 * 1024 * 1024  # 100MB default
        if v > max_size:
            raise ValueError(f"File size exceeds maximum allowed size ({max_size} bytes)")
        return v


class PTSRenameUploadRequest(BaseModel):
    """Request model for file upload validation"""
    files: List[PTSFileUploadInfo] = Field(..., description="List of files to upload")
    extract_compressed: bool = Field(True, description="Whether to extract compressed files")
    validate_pts_files: bool = Field(True, description="Whether to validate PTS file format")
    
    @validator('files')
    def validate_files_list(cls, v):
        if not v:
            raise ValueError("At least one file must be uploaded")
        if len(v) > 50:  # Max files per upload
            raise ValueError("Too many files (max 50 files per upload)")
        
        total_size = sum(f.size for f in v)
        max_total_size = 500 * 1024 * 1024  # 500MB total
        if total_size > max_total_size:
            raise ValueError(f"Total upload size exceeds limit ({max_total_size} bytes)")
            
        return v


class PTSRenameUploadResponse(BaseModel):
    """Response model for file upload"""
    upload_id: str = Field(..., description="Unique upload identifier")
    files_uploaded: int = Field(..., description="Number of files uploaded")
    compressed_files_extracted: int = Field(..., description="Number of compressed files extracted")
    pts_files_found: int = Field(..., description="Number of PTS files found")
    upload_size: int = Field(..., description="Total upload size in bytes")
    status: str = Field(..., description="Upload status")
    message: str = Field(..., description="Status message")
    warnings: List[str] = Field(default_factory=list, description="Upload warnings")
    file_details: List[PTSFileUploadInfo] = Field(default_factory=list, description="Details of uploaded files")


class PTSRenameErrorResponse(BaseModel):
    """Error response model"""
    error_code: str = Field(..., description="Error code")
    error_message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")
    job_id: Optional[str] = Field(None, description="Job ID if applicable")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")
    user_id: Optional[str] = Field(None, description="User ID for audit trail")
    severity: str = Field("error", description="Error severity level")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
    
    @validator('severity')
    def validate_severity(cls, v):
        valid_severities = ['info', 'warning', 'error', 'critical']
        if v not in valid_severities:
            raise ValueError(f"Invalid severity level. Must be one of: {valid_severities}")
        return v


class PTSRenameAuditLog(BaseModel):
    """Audit log model for compliance tracking"""
    log_id: str = Field(..., description="Unique log identifier")
    user_id: Optional[str] = Field(None, description="User performing the action")
    action: str = Field(..., description="Action performed")
    resource_type: str = Field(..., description="Type of resource affected")
    resource_id: str = Field(..., description="ID of affected resource")
    timestamp: datetime = Field(default_factory=datetime.now, description="Action timestamp")
    ip_address: Optional[str] = Field(None, description="Client IP address")
    user_agent: Optional[str] = Field(None, description="Client user agent")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional action details")
    result: str = Field(..., description="Action result (success/failure)")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
    
    @validator('action')
    def validate_action(cls, v):
        valid_actions = [
            'upload', 'process', 'download', 'preview', 'cancel',
            'delete', 'view_status', 'configure'
        ]
        if v not in valid_actions:
            raise ValueError(f"Invalid action. Must be one of: {valid_actions}")
        return v
    
    @validator('result')
    def validate_result(cls, v):
        if v not in ['success', 'failure', 'partial']:
            raise ValueError("Result must be 'success', 'failure', or 'partial'")
        return v


class PTSRenameSecurityValidation(BaseModel):
    """Security validation model for file operations"""
    file_path: str = Field(..., description="File path to validate")
    content_type: str = Field(..., description="File content type")
    file_size: int = Field(..., ge=0, description="File size in bytes")
    checksum: str = Field(..., description="File checksum")
    scan_results: Optional[Dict[str, Any]] = Field(None, description="Security scan results")
    is_safe: bool = Field(True, description="Whether file passed security checks")
    threats_detected: List[str] = Field(default_factory=list, description="List of detected threats")
    
    @validator('file_path')
    def validate_file_path(cls, v):
        # Prevent path traversal attacks
        if '..' in v or v.startswith('/') or ':' in v:
            raise ValueError("Invalid file path detected")
        
        # Validate file extension
        allowed_extensions = ['.pts', '.zip', '.7z', '.rar', '.txt']
        path = Path(v)
        if path.suffix.lower() not in allowed_extensions:
            raise ValueError(f"File extension not allowed: {path.suffix}")
            
        return v


# Configuration models
class PTSRenameConfig(BaseModel):
    """Configuration model for PTS Renamer service"""
    # File handling limits
    max_file_size_mb: int = Field(100, ge=1, le=1000, description="Maximum file size in MB")
    max_files_per_upload: int = Field(50, ge=1, le=100, description="Maximum files per upload")
    max_total_upload_size_mb: int = Field(500, ge=10, le=5000, description="Maximum total upload size in MB")
    
    # Supported formats
    supported_formats: List[str] = Field(
        default_factory=lambda: [".pts", ".zip", ".7z", ".rar"],
        description="Supported file formats"
    )
    supported_compressed_formats: List[str] = Field(
        default_factory=lambda: [".zip", ".7z", ".rar"],
        description="Supported compressed file formats"
    )
    
    # Processing settings
    processing_timeout_seconds: int = Field(300, ge=30, le=3600, description="Processing timeout in seconds")
    max_concurrent_jobs: int = Field(10, ge=1, le=50, description="Maximum concurrent processing jobs")
    job_priority_levels: int = Field(10, ge=1, le=10, description="Number of job priority levels")
    
    # Storage and cleanup - NOW USING file_management UploadConfig
    cleanup_retention_hours: int = Field(24, ge=1, le=168, description="File cleanup retention period")
    
    # Feature flags
    enable_preview: bool = Field(True, description="Enable processing preview")
    enable_compression: bool = Field(True, description="Enable result compression")
    enable_security_scanning: bool = Field(True, description="Enable file security scanning")
    enable_audit_logging: bool = Field(True, description="Enable audit logging")
    enable_batch_processing: bool = Field(True, description="Enable batch processing")
    
    # Security settings
    allowed_ip_ranges: List[str] = Field(default_factory=list, description="Allowed IP address ranges")
    require_authentication: bool = Field(True, description="Require user authentication")
    max_login_attempts: int = Field(5, ge=1, le=20, description="Maximum login attempts")
    session_timeout_minutes: int = Field(60, ge=5, le=480, description="Session timeout in minutes")
    
    # Performance settings
    chunk_size_bytes: int = Field(8192, ge=1024, le=65536, description="File processing chunk size")
    compression_level: int = Field(6, ge=1, le=9, description="Compression level (1-9)")
    worker_pool_size: int = Field(4, ge=1, le=20, description="Worker pool size for processing")
    
    # File management integration - Use UploadConfig from backend/file_management/
    _upload_config: Optional['UploadConfig'] = None
    
    @property
    def upload_config(self) -> 'UploadConfig':
        """Get upload configuration from file_management system"""
        if self._upload_config is None:
            from backend.file_management.adapters.file_upload.upload_config import load_upload_config
            self._upload_config = load_upload_config()
        return self._upload_config
    
    @property 
    def temp_storage_path(self) -> str:
        """Get temporary storage path from UploadConfig (.env UPLOAD_TEMP_DIR)"""
        return self.upload_config.upload_temp_dir
        
    @property
    def result_storage_path(self) -> str:
        """Get result storage path from UploadConfig (.env EXTRACT_TEMP_DIR + /results)"""
        extract_dir = self.upload_config.extract_temp_dir
        return f"{extract_dir}/results"
        
    @property
    def extract_temp_dir(self) -> str:
        """Get extraction temp directory from UploadConfig (.env EXTRACT_TEMP_DIR)"""
        return self.upload_config.extract_temp_dir


# Status constants
class PTSJobStatus:
    """Constants for job status values"""
    PENDING = "pending"
    EXTRACTING = "extracting"
    PROCESSING = "processing"
    COMPRESSING = "compressing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


# Error codes
class PTSErrorCodes:
    """Constants for error codes"""
    # File validation errors
    INVALID_FILE_FORMAT = "INVALID_FILE_FORMAT"
    FILE_TOO_LARGE = "FILE_TOO_LARGE"
    TOO_MANY_FILES = "TOO_MANY_FILES"
    INVALID_FILE_NAME = "INVALID_FILE_NAME"
    UNSUPPORTED_COMPRESSION = "UNSUPPORTED_COMPRESSION"
    CORRUPTED_FILE = "CORRUPTED_FILE"
    
    # Processing errors
    PROCESSING_TIMEOUT = "PROCESSING_TIMEOUT"
    INVALID_RENAME_PATTERN = "INVALID_RENAME_PATTERN"
    FILE_ACCESS_ERROR = "FILE_ACCESS_ERROR"
    COMPRESSION_ERROR = "COMPRESSION_ERROR"
    DECOMPRESSION_ERROR = "DECOMPRESSION_ERROR"
    QC_GENERATION_ERROR = "QC_GENERATION_ERROR"
    DIRECTORY_CREATION_ERROR = "DIRECTORY_CREATION_ERROR"
    
    # Upload/Download errors
    UPLOAD_ERROR = "UPLOAD_ERROR"
    DOWNLOAD_ERROR = "DOWNLOAD_ERROR"
    STORAGE_ERROR = "STORAGE_ERROR"
    DISK_SPACE_ERROR = "DISK_SPACE_ERROR"
    
    # Security errors
    SECURITY_SCAN_FAILED = "SECURITY_SCAN_FAILED"
    MALICIOUS_FILE_DETECTED = "MALICIOUS_FILE_DETECTED"
    UNAUTHORIZED_ACCESS = "UNAUTHORIZED_ACCESS"
    AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    
    # System errors
    INTERNAL_ERROR = "INTERNAL_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    DATABASE_ERROR = "DATABASE_ERROR"
    NETWORK_ERROR = "NETWORK_ERROR"
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR"
    
    # Job management errors
    JOB_NOT_FOUND = "JOB_NOT_FOUND"
    JOB_ALREADY_CANCELLED = "JOB_ALREADY_CANCELLED"
    JOB_QUEUE_FULL = "JOB_QUEUE_FULL"
    INVALID_JOB_STATUS = "INVALID_JOB_STATUS"
    
    # Validation errors
    INVALID_REQUEST = "INVALID_REQUEST"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    INVALID_FIELD_VALUE = "INVALID_FIELD_VALUE"
    VALIDATION_ERROR = "VALIDATION_ERROR"