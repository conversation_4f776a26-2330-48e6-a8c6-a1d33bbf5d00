#!/usr/bin/env python3
"""
數據庫整合測試
測試重構後的前端與後端服務的數據庫整合
"""

import os
import sys
import pytest
import sqlite3
import tempfile
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 設定中文編碼環境變數（Windows 兼容性）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['LANG'] = 'zh_TW.UTF-8'


class TestDatabaseIntegration:
    """數據庫整合測試類"""
    
    @pytest.fixture(scope="class")
    def temp_db_dir(self):
        """創建臨時數據庫目錄"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture(scope="class")
    def email_db_path(self, temp_db_dir):
        """創建臨時郵件數據庫路徑"""
        return temp_db_dir / "test_email_inbox.db"
    
    @pytest.fixture(scope="class")
    def task_db_path(self, temp_db_dir):
        """創建臨時任務數據庫路徑"""
        return temp_db_dir / "test_eqc_task_status.db"
    
    def test_flask_frontend_database_config(self):
        """測試 Flask 前端數據庫配置"""
        try:
            from frontend.config import Config, config
            
            # 測試開發環境配置
            dev_config = config['development']
            assert hasattr(dev_config, 'DATABASE_URL')
            assert hasattr(dev_config, 'SQLALCHEMY_DATABASE_URI')
            
            # 測試測試環境配置
            test_config = config['testing']
            assert hasattr(test_config, 'DATABASE_URL')
            assert test_config.DATABASE_URL == 'sqlite:///:memory:'
            
            # 測試生產環境配置
            prod_config = config['production']
            assert hasattr(prod_config, 'DATABASE_URL')
            
            print("✅ Flask 前端數據庫配置測試通過")
            
        except Exception as e:
            pytest.fail(f"Flask 前端數據庫配置測試失敗: {e}")
    
    def test_email_database_connection(self, email_db_path):
        """測試郵件數據庫連接"""
        try:
            from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
            from backend.shared.infrastructure.database.models import db_engine
            
            # 設定測試數據庫路徑
            test_db_url = f"sqlite:///{email_db_path}"
            db_engine.database_url = test_db_url
            
            # 初始化數據庫
            email_db = EmailDatabase(test_db_url)
            
            # 測試數據庫連接
            with email_db.get_session() as session:
                # 執行簡單查詢測試連接
                from sqlalchemy import text
                result = session.execute(text("SELECT 1")).fetchone()
                assert result[0] == 1
            
            print("✅ 郵件數據庫連接測試通過")
            
        except Exception as e:
            pytest.fail(f"郵件數據庫連接測試失敗: {e}")
    
    def test_email_database_crud_operations(self, email_db_path):
        """測試郵件數據庫 CRUD 操作"""
        try:
            from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
            from backend.shared.infrastructure.database.models import db_engine
            from backend.email.models.email_models import EmailData, EmailAttachment
            
            # 設定測試數據庫路徑
            test_db_url = f"sqlite:///{email_db_path}"
            db_engine.database_url = test_db_url
            
            # 初始化數據庫
            email_db = EmailDatabase(test_db_url)
            
            # 創建測試郵件數據
            test_email = EmailData(
                message_id="test_message_001",
                sender="<EMAIL>",
                subject="測試郵件主題",
                body="這是一封測試郵件的內容",
                received_time=datetime.now(),
                attachments=[]
            )
            
            # 測試插入操作
            email_id = email_db.save_email(test_email)
            assert email_id is not None
            assert isinstance(email_id, int)
            
            # 測試查詢操作
            retrieved_email = email_db.get_email_by_id(email_id)
            assert retrieved_email is not None
            assert retrieved_email['message_id'] == test_email.message_id
            assert retrieved_email['sender'] == test_email.sender
            assert retrieved_email['subject'] == test_email.subject
            
            # 測試列表查詢
            emails = email_db.get_emails(limit=10)
            assert len(emails) >= 1
            assert any(email['id'] == email_id for email in emails)
            
            # 測試更新操作
            success = email_db.mark_email_as_read(email_id)
            assert success is True
            
            # 驗證更新
            updated_email = email_db.get_email_by_id(email_id)
            assert updated_email['is_read'] is True
            
            # 測試統計功能
            stats = email_db.get_statistics()
            assert 'total_emails' in stats
            assert stats['total_emails'] >= 1
            
            print("✅ 郵件數據庫 CRUD 操作測試通過")
            
        except Exception as e:
            pytest.fail(f"郵件數據庫 CRUD 操作測試失敗: {e}")
    
    def test_task_database_connection(self, task_db_path):
        """測試任務數據庫連接"""
        try:
            from backend.shared.infrastructure.database.task_status_db import TaskStatusDB
            
            # 創建測試任務數據庫
            task_db = TaskStatusDB(str(task_db_path))
            
            # 測試數據庫連接
            conn = task_db._get_connection()
            result = conn.execute("SELECT 1").fetchone()
            assert result[0] == 1
            conn.close()
            
            print("✅ 任務數據庫連接測試通過")
            
        except Exception as e:
            pytest.fail(f"任務數據庫連接測試失敗: {e}")
    
    def test_task_database_operations(self, task_db_path):
        """測試任務數據庫操作"""
        try:
            from backend.shared.infrastructure.database.task_status_db import TaskStatusDB
            
            # 創建測試任務數據庫
            task_db = TaskStatusDB(str(task_db_path))
            
            # 測試任務開始
            task_id = f"test_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            session_id = f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            folder_path = "/test/folder/path"
            
            # 確保數據庫連接正常工作
            conn = task_db._get_connection()
            conn.execute("SELECT 1")
            conn.close()
            
            success = task_db.start_task(task_id, session_id, folder_path)
            assert success is True
            
            # 測試步驟狀態更新
            success = task_db.update_step_status(
                task_id, session_id, "測試步驟", 1, 25, "processing", "測試訊息"
            )
            assert success is True
            
            # 測試任務狀態查詢
            task_status = task_db.get_task_status(task_id=task_id)
            assert task_status is not None
            assert task_status['task_id'] == task_id
            assert task_status['session_id'] == session_id
            assert task_status['folder_path'] == folder_path
            
            # 測試最新步驟狀態查詢
            step_status = task_db.get_latest_step_status(task_id)
            assert step_status is not None
            assert step_status['task_id'] == task_id
            assert step_status['step_name'] == "測試步驟"
            
            # 測試任務完成
            result_data = {
                'status': 'completed',
                'steps_completed': 4,
                'success': True
            }
            success = task_db.complete_task(task_id, session_id, result_data, 120.5)
            assert success is True
            
            # 驗證任務完成狀態
            final_status = task_db.get_task_status(task_id=task_id)
            assert final_status['status'] == 'completed'
            assert final_status['progress'] == 100
            
            print("✅ 任務數據庫操作測試通過")
            
        except Exception as e:
            pytest.fail(f"任務數據庫操作測試失敗: {e}")
    
    def test_database_file_permissions(self, temp_db_dir):
        """測試數據庫文件權限"""
        try:
            # 創建測試數據庫文件
            test_db_path = temp_db_dir / "permission_test.db"
            
            # 創建數據庫
            conn = sqlite3.connect(str(test_db_path))
            conn.execute("CREATE TABLE test_table (id INTEGER PRIMARY KEY, data TEXT)")
            conn.execute("INSERT INTO test_table (data) VALUES ('test')")
            conn.commit()
            conn.close()
            
            # 檢查文件是否可讀
            assert test_db_path.exists()
            assert os.access(test_db_path, os.R_OK)
            
            # 檢查文件是否可寫
            assert os.access(test_db_path, os.W_OK)
            
            # 測試讀取操作
            conn = sqlite3.connect(str(test_db_path))
            result = conn.execute("SELECT data FROM test_table").fetchone()
            assert result[0] == 'test'
            conn.close()
            
            # 測試寫入操作
            conn = sqlite3.connect(str(test_db_path))
            conn.execute("INSERT INTO test_table (data) VALUES ('test2')")
            conn.commit()
            
            count = conn.execute("SELECT COUNT(*) FROM test_table").fetchone()[0]
            assert count == 2
            conn.close()
            
            print("✅ 數據庫文件權限測試通過")
            
        except Exception as e:
            pytest.fail(f"數據庫文件權限測試失敗: {e}")
    
    def test_database_concurrent_access(self, temp_db_dir):
        """測試數據庫並發訪問"""
        try:
            import threading
            import time
            
            test_db_path = temp_db_dir / "concurrent_test.db"
            
            # 創建測試數據庫
            conn = sqlite3.connect(str(test_db_path))
            conn.execute("CREATE TABLE concurrent_test (id INTEGER PRIMARY KEY, thread_id TEXT, timestamp TEXT)")
            conn.commit()
            conn.close()
            
            # 並發寫入測試
            def write_worker(thread_id, iterations=10):
                for i in range(iterations):
                    conn = sqlite3.connect(str(test_db_path), timeout=30.0)
                    try:
                        conn.execute(
                            "INSERT INTO concurrent_test (thread_id, timestamp) VALUES (?, ?)",
                            (f"thread_{thread_id}", datetime.now().isoformat())
                        )
                        conn.commit()
                    finally:
                        conn.close()
                    time.sleep(0.01)  # 短暫延遲
            
            # 啟動多個線程
            threads = []
            for i in range(3):
                thread = threading.Thread(target=write_worker, args=(i,))
                threads.append(thread)
                thread.start()
            
            # 等待所有線程完成
            for thread in threads:
                thread.join()
            
            # 驗證結果
            conn = sqlite3.connect(str(test_db_path))
            count = conn.execute("SELECT COUNT(*) FROM concurrent_test").fetchone()[0]
            assert count == 30  # 3 threads * 10 iterations
            
            # 檢查數據完整性
            threads_data = conn.execute("SELECT DISTINCT thread_id FROM concurrent_test").fetchall()
            assert len(threads_data) == 3
            conn.close()
            
            print("✅ 數據庫並發訪問測試通過")
            
        except Exception as e:
            pytest.fail(f"數據庫並發訪問測試失敗: {e}")
    
    def test_database_error_handling(self, temp_db_dir):
        """測試數據庫錯誤處理"""
        try:
            # 測試不存在的數據庫文件
            non_existent_path = temp_db_dir / "non_existent.db"
            
            # SQLite 會自動創建不存在的數據庫文件，所以這不會失敗
            conn = sqlite3.connect(str(non_existent_path))
            conn.close()
            assert non_existent_path.exists()
            
            # 測試無效的 SQL 查詢
            conn = sqlite3.connect(str(non_existent_path))
            try:
                conn.execute("INVALID SQL QUERY")
                assert False, "應該拋出異常"
            except sqlite3.Error:
                pass  # 預期的異常
            finally:
                conn.close()
            
            # 測試數據庫鎖定情況
            conn1 = sqlite3.connect(str(non_existent_path))
            conn1.execute("CREATE TABLE lock_test (id INTEGER PRIMARY KEY)")
            conn1.execute("BEGIN EXCLUSIVE TRANSACTION")
            
            conn2 = sqlite3.connect(str(non_existent_path), timeout=1.0)
            try:
                conn2.execute("INSERT INTO lock_test DEFAULT VALUES")
                conn2.commit()
                assert False, "應該因為鎖定而失敗"
            except sqlite3.OperationalError:
                pass  # 預期的鎖定異常
            finally:
                conn2.close()
                conn1.rollback()
                conn1.close()
            
            print("✅ 數據庫錯誤處理測試通過")
            
        except Exception as e:
            pytest.fail(f"數據庫錯誤處理測試失敗: {e}")
    
    def test_database_schema_validation(self, email_db_path, task_db_path):
        """測試數據庫模式驗證"""
        try:
            # 測試郵件數據庫模式
            from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
            from backend.shared.infrastructure.database.models import db_engine
            
            test_db_url = f"sqlite:///{email_db_path}"
            db_engine.database_url = test_db_url
            email_db = EmailDatabase(test_db_url)
            
            # 檢查郵件數據庫表結構
            conn = sqlite3.connect(str(email_db_path))
            
            # 檢查 emails 表
            emails_schema = conn.execute("PRAGMA table_info(emails)").fetchall()
            email_columns = [col[1] for col in emails_schema]
            
            required_email_columns = [
                'id', 'message_id', 'sender', 'subject', 'body', 
                'received_time', 'created_at', 'is_read', 'is_processed'
            ]
            
            for col in required_email_columns:
                assert col in email_columns, f"郵件表缺少必要欄位: {col}"
            
            # 檢查 senders 表
            senders_schema = conn.execute("PRAGMA table_info(senders)").fetchall()
            sender_columns = [col[1] for col in senders_schema]
            
            required_sender_columns = ['id', 'email_address', 'total_emails']
            for col in required_sender_columns:
                assert col in sender_columns, f"寄件者表缺少必要欄位: {col}"
            
            conn.close()
            
            # 測試任務數據庫模式
            from backend.shared.infrastructure.database.task_status_db import TaskStatusDB
            
            task_db = TaskStatusDB(str(task_db_path))
            
            conn = sqlite3.connect(str(task_db_path))
            
            # 檢查 eqc_task_execution 表
            execution_schema = conn.execute("PRAGMA table_info(eqc_task_execution)").fetchall()
            execution_columns = [col[1] for col in execution_schema]
            
            required_execution_columns = [
                'id', 'task_id', 'session_id', 'folder_path', 
                'status', 'progress', 'started_at'
            ]
            
            for col in required_execution_columns:
                assert col in execution_columns, f"任務執行表缺少必要欄位: {col}"
            
            # 檢查 eqc_task_status 表
            status_schema = conn.execute("PRAGMA table_info(eqc_task_status)").fetchall()
            status_columns = [col[1] for col in status_schema]
            
            required_status_columns = [
                'id', 'task_id', 'session_id', 'step_name', 
                'progress', 'status', 'created_at'
            ]
            
            for col in required_status_columns:
                assert col in status_columns, f"任務狀態表缺少必要欄位: {col}"
            
            conn.close()
            
            print("✅ 數據庫模式驗證測試通過")
            
        except Exception as e:
            pytest.fail(f"數據庫模式驗證測試失敗: {e}")


def run_integration_tests():
    """運行整合測試"""
    print("🧪 開始數據庫整合測試...")
    print("=" * 60)
    
    # 運行 pytest
    test_file = __file__
    exit_code = pytest.main([
        test_file,
        "-v",
        "--tb=short",
        "--color=yes"
    ])
    
    if exit_code == 0:
        print("\n✅ 所有數據庫整合測試通過")
    else:
        print("\n❌ 部分數據庫整合測試失敗")
    
    return exit_code


if __name__ == "__main__":
    exit_code = run_integration_tests()
    sys.exit(exit_code)