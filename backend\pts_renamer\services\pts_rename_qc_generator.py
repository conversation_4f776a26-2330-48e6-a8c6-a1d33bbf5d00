"""
PTS QC File Generation Service

This module implements the QC file generation functionality based on the desktop version.
It handles the complete QC processing logic including content modification and bin filtering.
"""

import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

from loguru import logger
from backend.pts_renamer.models.pts_rename_entities import PTSRenameResult


@dataclass
class QCPreview:
    """Preview result for QC file generation"""
    original_name: str
    qc_name: str
    will_generate: bool
    error_message: Optional[str] = None


class PTSQCGenerator:
    """
    PTS QC File Generator
    
    Implements the exact QC processing logic from the desktop version:
    1. Remove data between "Parameter," and "QA," sections
    2. Modify QCOnlySBinAlter=1,0
    3. Recalculate ParamCnt based on non-empty lines
    4. Filter [Bin Definition] to keep only bins 1 and 31
    5. Generate _QC suffix filename
    """
    
    def __init__(self):
        self.logger = logger
    
    def create_qc_file(self, pts_file_path: Path) -> Tuple[bool, str]:
        """
        Create QC file from PTS file (exact logic from desktop version)
        
        Args:
            pts_file_path: Path to the original PTS file
            
        Returns:
            Tuple of (success, qc_filename_or_error_message)
        """
        try:
            # Check if file already has _QC suffix
            if pts_file_path.stem.endswith("_QC"):
                return False, "檔案名稱已包含'_QC'，無需執行QC處理"
            
            # Read original PTS file content
            with open(pts_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Split content into lines
            lines = content.split('\n')
            
            # Find "Parameter," and "QA," line indices
            parameter_index = -1
            qa_index = -1
            
            for i, line in enumerate(lines):
                if line.strip().startswith("Parameter,"):
                    parameter_index = i
                elif line.strip().startswith("QA,"):
                    qa_index = i
                    break
            
            if parameter_index == -1:
                return False, "找不到 'Parameter,' 行"
            
            if qa_index == -1:
                return False, "找不到 'QA,' 行"
            
            # Keep content up to "Parameter," line, then skip to after "QA," line
            new_lines = lines[:parameter_index + 1] + lines[qa_index + 1:]
            
            # Generate QC filename
            qc_filename = pts_file_path.stem + "_QC" + pts_file_path.suffix
            qc_file_path = pts_file_path.parent / qc_filename
            
            # Delete existing QC file if it exists
            if qc_file_path.exists():
                qc_file_path.unlink()
            
            # Write QC file
            with open(qc_file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_lines))
            
            # Apply additional QC modifications
            self.modify_qc_content(qc_file_path)
            
            self.logger.info(f"Successfully created QC file: {qc_filename}")
            return True, qc_filename
            
        except Exception as e:
            error_msg = f"創建QC檔案時發生錯誤: {str(e)}"
            self.logger.error(error_msg)
            return False, str(e)
    
    def modify_qc_content(self, qc_file_path: Path) -> bool:
        """
        Apply QC-specific modifications (exact logic from desktop version)
        
        Args:
            qc_file_path: Path to the QC file to modify
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Read QC file content
            with open(qc_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            modified_lines = []
            
            # 1. Modify QCOnlySBinAlter line
            for line in lines:
                if line.strip().startswith("QCOnlySBinAlter="):
                    modified_lines.append("QCOnlySBinAlter=1,0")
                else:
                    modified_lines.append(line)
            
            # 2. Calculate ParamCnt and update
            param_count = 0
            parameter_start = -1
            end_index = -1
            
            for i, line in enumerate(modified_lines):
                if line.strip().startswith("Parameter,"):
                    parameter_start = i
                elif line.strip() == "END":
                    end_index = i
                    break
            
            if parameter_start != -1 and end_index != -1:
                # Count non-empty lines between Parameter and END
                for i in range(parameter_start + 1, end_index):
                    if modified_lines[i].strip():  # Non-empty line
                        param_count += 1
            
            # Update ParamCnt line
            for i, line in enumerate(modified_lines):
                if line.strip().startswith("ParamCnt"):
                    modified_lines[i] = f"ParamCnt={param_count}"
                    break
            
            # 3. Process [Bin Definition] section
            bin_def_start = -1
            for i, line in enumerate(modified_lines):
                if line.strip() == "[Bin Definition]":
                    bin_def_start = i
                    break
            
            if bin_def_start != -1:
                # Find first number line after [Bin Definition]
                bin_lines_start = -1
                for i in range(bin_def_start + 1, len(modified_lines)):
                    if modified_lines[i].strip() and modified_lines[i].strip()[0].isdigit():
                        bin_lines_start = i
                        break
                
                if bin_lines_start != -1:
                    # Keep all content before [Bin Definition]
                    new_lines = modified_lines[:bin_def_start + 1]
                    
                    # Process content after [Bin Definition]
                    for i in range(bin_lines_start, len(modified_lines)):
                        line = modified_lines[i]
                        if line.strip():
                            if line.strip()[0].isdigit():
                                # Check if line starts with 1 or 31
                                parts = line.strip().split(',')
                                if parts and parts[0].strip() in ['1', '31']:
                                    new_lines.append(line)
                            else:
                                # Non-digit starting line, keep it
                                new_lines.append(line)
                        else:
                            # Empty line, keep it
                            new_lines.append(line)
                    
                    modified_lines = new_lines
            
            # Write back modified content
            with open(qc_file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(modified_lines))
            
            self.logger.info(f"Successfully modified QC file content: {qc_file_path.name}")
            return True
            
        except Exception as e:
            error_msg = f"修改QC檔案內容時發生錯誤: {str(e)}"
            self.logger.error(error_msg)
            return False
    
    def preview_qc_generation(self, files: List[Path]) -> List[QCPreview]:
        """
        Preview QC file generation operations
        
        Args:
            files: List of PTS files to preview
            
        Returns:
            List of QC generation previews
        """
        previews = []
        
        for file_path in files:
            original_name = file_path.name
            
            # Check if already has _QC suffix
            if file_path.stem.endswith("_QC"):
                previews.append(QCPreview(
                    original_name=original_name,
                    qc_name=original_name,
                    will_generate=False,
                    error_message="檔案名稱已包含'_QC'，無需執行QC處理"
                ))
            else:
                qc_name = file_path.stem + "_QC" + file_path.suffix
                qc_path = file_path.parent / qc_name
                
                # Check if QC file would conflict
                if qc_path.exists():
                    previews.append(QCPreview(
                        original_name=original_name,
                        qc_name=qc_name,
                        will_generate=True,
                        error_message=f"將覆蓋現有的QC檔案: {qc_name}"
                    ))
                else:
                    previews.append(QCPreview(
                        original_name=original_name,
                        qc_name=qc_name,
                        will_generate=True
                    ))
        
        return previews
    
    def batch_generate_qc_files(self, files: List[Path]) -> List[PTSRenameResult]:
        """
        Batch generate QC files for multiple PTS files
        
        Args:
            files: List of PTS files to process
            
        Returns:
            List of QC generation results
        """
        results = []
        
        for file_path in files:
            success, qc_name_or_error = self.create_qc_file(file_path)
            
            result = PTSRenameResult(
                original_name=file_path.name,
                new_name=qc_name_or_error if success else "",
                operation="qc_generation",
                success=success,
                error_message=None if success else qc_name_or_error
            )
            results.append(result)
        
        return results
    
    def validate_pts_file_for_qc(self, pts_file_path: Path) -> Tuple[bool, str]:
        """
        Validate if PTS file can be processed for QC generation
        
        Args:
            pts_file_path: Path to the PTS file
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not pts_file_path.exists():
            return False, "檔案不存在"
        
        if not pts_file_path.suffix.lower() in ['.pts', '.cpts']:
            return False, "不是PTS或CPTS檔案"
        
        if pts_file_path.stem.endswith("_QC"):
            return False, "檔案名稱已包含'_QC'，無需執行QC處理"
        
        try:
            # Check if file contains required sections
            with open(pts_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "Parameter," not in content:
                return False, "檔案中找不到 'Parameter,' 行"
            
            if "QA," not in content:
                return False, "檔案中找不到 'QA,' 行"
            
            return True, ""
            
        except Exception as e:
            return False, f"讀取檔案時發生錯誤: {str(e)}"