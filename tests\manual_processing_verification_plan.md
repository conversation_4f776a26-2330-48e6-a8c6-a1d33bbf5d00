# 手動處理功能驗證計劃

## 概述
本文檔提供手動處理功能修復後的完整驗證計劃，確保功能正常運作且與自動處理流程保持一致。

## 驗證架構分析

### API 端點
- **手動輸入API**: `/email/api/parser/emails/<emailId>/manual-input` (POST)
- **重新解析API**: `/email/api/parser/emails/<emailId>/reparse` (POST)
- **批次處理API**: `/email/api/parser/emails/batch-process` (POST)

### 前端實現
- **對話框**: `showManualInputDialog(emailId)` in `email-parser-ui.js`
- **儲存功能**: `saveManualInput(emailId)` in `email-parser-ui.js`
- **UI增強**: 手動輸入按鈕整合在郵件列表中

## 驗證清單

### ✅ 1. API 連接測試
**目標**: 確認前端能正確調用後端 API

#### 測試項目
- [ ] API 端點可正常訪問
- [ ] API Key 認證正常運作
- [ ] 正確的 HTTP 方法和路徑
- [ ] Content-Type 標頭設定正確
- [ ] CORS 標頭配置無誤

#### 預期結果
- API 返回 200 狀態碼
- 正確的 JSON 響應格式
- 無認證錯誤

#### 測試方法
```bash
# 執行 API 連接測試
python tests/manual_processing/test_api_connection.py
```

### ✅ 2. 資料完整性測試
**目標**: 確認郵件資料正確傳遞和處理

#### 測試項目
- [ ] emailId 參數正確傳遞
- [ ] 必填欄位驗證 (vendor_code, pd, lot)
- [ ] 選填欄位處理 (mo, yield_value)
- [ ] 資料格式驗證
- [ ] 資料庫更新準確性

#### 預期結果
- 手動輸入資料正確儲存到資料庫
- extraction_method 設為 'manual'
- parse_status 更新為 'parsed'
- 時間戳正確記錄

#### 測試方法
```bash
# 執行資料完整性測試
python tests/manual_processing/test_data_integrity.py
```

### ✅ 3. 錯誤處理測試
**目標**: 測試各種錯誤情況的處理

#### 測試項目
- [ ] 無效的 emailId
- [ ] 缺少必填欄位
- [ ] 格式驗證錯誤
- [ ] 資料庫連接錯誤
- [ ] API Key 錯誤
- [ ] 網路連接問題

#### 預期結果
- 返回適當的錯誤碼和訊息
- 前端顯示友善的錯誤提示
- 不會導致系統崩潰
- 錯誤日誌正確記錄

#### 測試方法
```bash
# 執行錯誤處理測試
python tests/manual_processing/test_error_handling.py
```

### ✅ 4. UI 回饋測試
**目標**: 確認成功/失敗訊息正確顯示

#### 測試項目
- [ ] 成功儲存後的提示訊息
- [ ] 錯誤情況的提示訊息
- [ ] 載入狀態指示器
- [ ] 對話框開啟/關閉動作
- [ ] 表單驗證提示
- [ ] 郵件列表更新

#### 預期結果
- 提示訊息清晰易懂
- UI 狀態更新及時
- 使用者體驗流暢

#### 測試方法
```bash
# 執行 UI 測試
python tests/manual_processing/test_ui_feedback.py
```

### ✅ 5. 關鍵點驗證

#### 5.1 處理按鈕調用正確的 API 端點
- [ ] 按鈕點擊觸發正確的 JavaScript 函數
- [ ] API 路徑拼接正確 (`/email/api/parser/emails/${emailId}/manual-input`)
- [ ] HTTP 方法為 POST

#### 5.2 使用正確的 emailId 參數
- [ ] emailId 從郵件列表正確獲取
- [ ] 參數類型為整數
- [ ] 參數值有效且存在於資料庫

#### 5.3 與自動處理流程一致性
- [ ] 資料欄位結構一致
- [ ] 處理結果格式統一
- [ ] 後續處理流程相同
- [ ] 通知機制一致

#### 5.4 無回歸問題
- [ ] 不影響現有自動解析功能
- [ ] 不影響批次處理功能
- [ ] 不影響郵件同步功能
- [ ] 系統效能無明顯下降

## 測試環境設定

### 前置條件
1. 系統正常運行
2. 資料庫連接正常
3. 測試資料準備完成
4. API Key 設定正確

### 測試資料
```sql
-- 建立測試用郵件資料
INSERT INTO emails (subject, sender, body, parse_status) VALUES
('Test Email 1', '<EMAIL>', 'Test body 1', 'pending'),
('Test Email 2', '<EMAIL>', 'Test body 2', 'failed'),
('Test Email 3', '<EMAIL>', 'Test body 3', 'parsed');
```

## 執行順序

### 階段 1: 基礎功能驗證
1. API 連接測試
2. 資料完整性測試
3. 錯誤處理測試

### 階段 2: 使用者體驗驗證
4. UI 回饋測試
5. 整合測試

### 階段 3: 系統整合驗證
6. 回歸測試
7. 效能測試

## 驗證標準

### 通過條件
- 所有測試項目 100% 通過
- 無關鍵錯誤或警告
- 使用者體驗流暢自然
- 與現有功能完全相容

### 失敗條件
- 任何關鍵功能測試失敗
- 出現資料完整性問題
- 影響現有功能正常運作
- 使用者體驗明顯下降

## 報告格式

### 測試結果報告
```
測試項目: [項目名稱]
執行時間: [時間戳]
執行結果: ✅ 通過 / ❌ 失敗
詳細說明: [結果描述]
問題記錄: [問題列表]
建議措施: [改進建議]
```

## 後續維護

### 持續監控
- 定期執行回歸測試
- 監控錯誤日誌
- 收集使用者回饋
- 效能指標追蹤

### 更新機制
- 測試腳本版本控制
- 測試資料定期更新
- 驗證標準持續改進