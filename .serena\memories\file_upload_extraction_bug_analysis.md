# 檔案上傳與解壓縮系統 Bug 分析

## 問題概述
用戶報告檔案上傳和解壓縮系統存在多個問題，之前已經修改過幾次但仍然失敗。

## 核心問題
1. **路徑配置問題**: 
   - 上傳目錄: `D:\temp\uploads\pts_upload_e6e87325917d\GMT_G2514XX_CTAF4_F1_XX_extracted` 是空的
   - 實際解壓內容在: `D:\temp\extracted_4bf9ead6a4364b3184565d129ed1d9ca`
   - .env 配置: 
     - UPLOAD_TEMP_DIR=d:/temp/uploads
     - EXTRACT_TEMP_DIR=d:/temp/zip_temp

2. **7z 解壓超時錯誤**:
   ```
   ERROR | backend.pts_renamer.services.pts_rename_upload_service:_extract_compressed_files:404 - [EXTRACT] Task timeout or failed after 180s for GMT_G2514XX_CTAF4_F1_XX.7z
   WARNING | backend.pts_renamer.services.pts_rename_upload_service:_extract_compressed_files:445 - [EXTRACT] Failed: GMT_G2514XX_CTAF4_F1_XX.7z - Extraction task timeout or failed after 180 seconds
   ```

3. **JSON 序列化錯誤**:
   ```
   ERROR | backend.pts_renamer.services.pts_rename_upload_service:handle_compressed_upload:222 - [UPLOAD] Upload processing failed: Object of type datetime is not JSON serializable
   ```

## 需要修復的關鍵點
- 統一路徑配置和實際使用路徑
- 解決 7z 解壓縮超時問題
- 修復 datetime 對象的 JSON 序列化問題
- 驗證整個上傳解壓流程

## 技術要求
- 使用 venv_win_3_11_9 虛擬環境進行測試
- 需要實際檔案上傳測試驗證
- 確保路徑配置與 .env 設定一致