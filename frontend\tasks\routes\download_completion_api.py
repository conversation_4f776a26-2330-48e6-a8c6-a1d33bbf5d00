"""
下載完成處理 API
提供下載完成事件的管理和監控接口

🎯 功能：
  - 手動觸發 code comparison 任務
  - 查詢事件狀態和統計
  - 管理自動觸發設定
  - 監控處理進度
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from backend.shared.infrastructure.adapters.download_completion_handler import (
    get_download_completion_handler, 
    trigger_code_comparison_from_path,
    TriggerReason
)
from backend.shared.infrastructure.logging.logger_manager import LoggerManager

logger = LoggerManager().get_logger("DownloadCompletionAPI")
router = APIRouter(prefix="/api/download-completion", tags=["Download Completion"])


class TriggerRequest(BaseModel):
    """觸發請求模型"""
    file_path: str = Field(..., description="檔案路徑 (例: d:\\temp\\GTK123\\MO456789)")
    vendor_code: Optional[str] = Field(None, description="廠商代碼")
    mo_code: Optional[str] = Field(None, description="MO代碼") 
    force: bool = Field(False, description="強制觸發")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="附加元數據")


class ConfigUpdateRequest(BaseModel):
    """配置更新請求模型"""
    enable_auto_trigger: Optional[bool] = Field(None, description="啟用自動觸發")
    max_concurrent_tasks: Optional[int] = Field(None, description="最大並發任務數")


@router.post("/trigger", summary="手動觸發代碼比較")
async def trigger_code_comparison(request: TriggerRequest) -> Dict[str, Any]:
    """
    手動觸發代碼比較任務
    
    適用場景：
    - 測試觸發機制
    - 重試失敗的處理
    - 手動處理遺漏的檔案
    """
    try:
        logger.info(f"🎯 收到手動觸發請求: {request.file_path}")
        
        event_id = await trigger_code_comparison_from_path(
            file_path=request.file_path,
            vendor_code=request.vendor_code,
            mo_code=request.mo_code,
            force=request.force
        )
        
        return {
            'success': True,
            'event_id': event_id,
            'message': '代碼比較任務已觸發',
            'triggered_at': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ 手動觸發失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/events/{event_id}", summary="查詢事件狀態")
async def get_event_status(event_id: str) -> Dict[str, Any]:
    """查詢指定事件的狀態"""
    try:
        handler = get_download_completion_handler()
        event_status = handler.get_event_status(event_id)
        
        if not event_status:
            raise HTTPException(status_code=404, detail=f"事件不存在: {event_id}")
        
        return {
            'success': True,
            'event': event_status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 查詢事件狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/events", summary="列出最近事件")
async def list_recent_events(
    hours: int = Query(24, description="查詢最近幾小時的事件"),
    limit: int = Query(50, description="最大返回數量")
) -> Dict[str, Any]:
    """列出最近的事件"""
    try:
        handler = get_download_completion_handler()
        events = handler.list_recent_events(hours=hours)
        
        # 限制返回數量
        if limit > 0:
            events = events[:limit]
        
        return {
            'success': True,
            'events': events,
            'total_count': len(events),
            'query_params': {
                'hours': hours,
                'limit': limit
            }
        }
        
    except Exception as e:
        logger.error(f"❌ 列出事件失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", summary="獲取統計資料")
async def get_statistics() -> Dict[str, Any]:
    """獲取下載完成處理的統計資料"""
    try:
        handler = get_download_completion_handler()
        stats = handler.get_statistics()
        
        return {
            'success': True,
            'statistics': stats,
            'updated_at': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ 獲取統計資料失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config", summary="獲取當前配置")
async def get_config() -> Dict[str, Any]:
    """獲取當前處理器配置"""
    try:
        handler = get_download_completion_handler()
        
        config = {
            'enable_auto_trigger': handler.enable_auto_trigger,
            'max_concurrent_tasks': handler.max_concurrent_tasks,
            'temp_path_pattern': handler.temp_path_pattern.pattern,
            'min_wait_seconds': handler.min_wait_seconds,
            'current_processing_count': len(handler.processing_events)
        }
        
        return {
            'success': True,
            'config': config
        }
        
    except Exception as e:
        logger.error(f"❌ 獲取配置失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/config", summary="更新配置")
async def update_config(request: ConfigUpdateRequest) -> Dict[str, Any]:
    """更新處理器配置"""
    try:
        handler = get_download_completion_handler()
        updated_fields = []
        
        if request.enable_auto_trigger is not None:
            handler.enable_auto_trigger = request.enable_auto_trigger
            updated_fields.append('enable_auto_trigger')
        
        if request.max_concurrent_tasks is not None:
            if request.max_concurrent_tasks < 1 or request.max_concurrent_tasks > 10:
                raise ValueError("max_concurrent_tasks 必須在 1-10 之間")
            handler.max_concurrent_tasks = request.max_concurrent_tasks
            updated_fields.append('max_concurrent_tasks')
        
        logger.info(f"🔧 配置已更新: {', '.join(updated_fields)}")
        
        return {
            'success': True,
            'message': f'配置已更新: {", ".join(updated_fields)}',
            'updated_fields': updated_fields
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ 更新配置失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup", summary="清理舊事件")
async def cleanup_old_events(days: int = Body(7, description="清理幾天前的事件")) -> Dict[str, Any]:
    """清理舊的事件記錄"""
    try:
        if days < 1 or days > 30:
            raise ValueError("days 必須在 1-30 之間")
            
        handler = get_download_completion_handler()
        original_count = len(handler.events)
        
        handler.cleanup_old_events(days=days)
        
        cleaned_count = original_count - len(handler.events)
        
        logger.info(f"🧹 清理了 {cleaned_count} 個舊事件記錄")
        
        return {
            'success': True,
            'message': f'清理了 {cleaned_count} 個事件記錄',
            'cleaned_count': cleaned_count,
            'remaining_count': len(handler.events),
            'cleanup_days': days
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ 清理舊事件失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health", summary="健康檢查")
async def health_check() -> Dict[str, Any]:
    """下載完成處理器健康檢查"""
    try:
        handler = get_download_completion_handler()
        stats = handler.get_statistics()
        
        # 計算健康狀態
        is_healthy = (
            handler.enable_auto_trigger and
            len(handler.processing_events) < handler.max_concurrent_tasks and
            stats['success_rate'] >= 50.0  # 成功率至少 50%
        )
        
        health_status = "healthy" if is_healthy else "degraded"
        
        return {
            'success': True,
            'status': health_status,
            'details': {
                'auto_trigger_enabled': handler.enable_auto_trigger,
                'processing_events_count': len(handler.processing_events),
                'max_concurrent_tasks': handler.max_concurrent_tasks,
                'success_rate': stats['success_rate'],
                'total_events': stats['total_events']
            },
            'checked_at': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ 健康檢查失敗: {e}")
        return {
            'success': False,
            'status': 'unhealthy',
            'error': str(e),
            'checked_at': datetime.now().isoformat()
        }


@router.post("/test", summary="測試觸發機制")
async def test_trigger(test_path: str = Body(..., description="測試路徑")) -> Dict[str, Any]:
    """
    測試觸發機制（不實際執行任務）
    
    用於驗證路徑解析和觸發條件
    """
    try:
        handler = get_download_completion_handler()
        
        # 解析路徑
        parsed_codes = handler._parse_path_codes(test_path)
        
        # 模擬檢查觸發條件
        from pathlib import Path
        file_exists = Path(test_path).exists()
        
        # 檢查是否符合 temp 路徑模式
        import re
        normalized_path = str(Path(test_path)).lower().replace('/', '\\')
        is_temp_path = 'd:\\temp' in normalized_path or 'temp' in normalized_path
        
        test_result = {
            'test_path': test_path,
            'parsed_vendor_code': parsed_codes.get('vendor_code'),
            'parsed_mo_code': parsed_codes.get('mo_code'),
            'file_exists': file_exists,
            'is_temp_path': is_temp_path,
            'would_trigger': is_temp_path and handler.enable_auto_trigger,
            'auto_trigger_enabled': handler.enable_auto_trigger,
            'max_concurrent_not_reached': len(handler.processing_events) < handler.max_concurrent_tasks
        }
        
        return {
            'success': True,
            'test_result': test_result,
            'message': '觸發測試完成' if test_result['would_trigger'] else '不會觸發任務',
            'tested_at': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ 測試觸發機制失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))