<!-- 主導航列 -->
<nav class="navbar">
    <div class="navbar-container">
        <!-- 品牌標誌 -->
        <div class="navbar-brand">
            <a href="/" class="brand-link">
                <i class="fas fa-microchip"></i>
                <span class="brand-text">半導體郵件處理系統</span>
            </a>
        </div>
        
        <!-- 主要導航選單 -->
        <div class="navbar-nav">
            <div class="nav-item dropdown">
                <a href="/email" class="nav-link {% if request.endpoint and request.endpoint.startswith('email') %}active{% endif %}">
                    <i class="fas fa-envelope"></i>
                    <span>郵件管理</span>
                </a>
                <div class="dropdown-menu">
                    <a href="/email/inbox" class="dropdown-item">
                        <i class="fas fa-inbox"></i>
                        收件夾
                    </a>
                    <a href="/email/compose" class="dropdown-item">
                        <i class="fas fa-edit"></i>
                        撰寫郵件
                    </a>
                    <a href="/email/settings" class="dropdown-item">
                        <i class="fas fa-cog"></i>
                        郵件設定
                    </a>
                </div>
            </div>
            
            <div class="nav-item dropdown">
                <a href="/analytics" class="nav-link {% if request.endpoint and request.endpoint.startswith('analytics') %}active{% endif %}">
                    <i class="fas fa-chart-line"></i>
                    <span>分析統計</span>
                </a>
                <div class="dropdown-menu">
                    <a href="/analytics/dashboard" class="dropdown-item">
                        <i class="fas fa-tachometer-alt"></i>
                        統計儀表板
                    </a>
                    <a href="/analytics/reports" class="dropdown-item">
                        <i class="fas fa-file-alt"></i>
                        報表管理
                    </a>
                    <a href="/analytics/vendor-analysis" class="dropdown-item">
                        <i class="fas fa-industry"></i>
                        廠商分析
                    </a>
                </div>
            </div>
            
            <div class="nav-item dropdown">
                <a href="/files" class="nav-link {% if request.endpoint and request.endpoint.startswith('file_management') %}active{% endif %}">
                    <i class="fas fa-folder"></i>
                    <span>檔案管理</span>
                </a>
                <div class="dropdown-menu">
                    <a href="/files/manager" class="dropdown-item">
                        <i class="fas fa-folder-open"></i>
                        檔案管理器
                    </a>
                    <a href="/files/upload" class="dropdown-item">
                        <i class="fas fa-upload"></i>
                        檔案上傳
                    </a>
                    <a href="/files/network-browser" class="dropdown-item">
                        <i class="fas fa-network-wired"></i>
                        網路瀏覽器
                    </a>
                </div>
            </div>
            
            <div class="nav-item dropdown">
                <a href="/eqc" class="nav-link {% if request.endpoint and request.endpoint.startswith('eqc') %}active{% endif %}">
                    <i class="fas fa-check-circle"></i>
                    <span>EQC 品質</span>
                </a>
                <div class="dropdown-menu">
                    <a href="/eqc/dashboard" class="dropdown-item">
                        <i class="fas fa-tachometer-alt"></i>
                        EQC 儀表板
                    </a>
                    <a href="/eqc/quality-check" class="dropdown-item">
                        <i class="fas fa-search"></i>
                        品質檢查
                    </a>
                    <a href="/eqc/compliance" class="dropdown-item">
                        <i class="fas fa-clipboard-check"></i>
                        合規檢查
                    </a>
                    <a href="/eqc/ui" class="dropdown-item" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        FT-EQC 處理
                    </a>
                </div>
            </div>
            
            <div class="nav-item dropdown">
                <a href="/tasks" class="nav-link {% if request.endpoint and request.endpoint.startswith('task') %}active{% endif %}">
                    <i class="fas fa-tasks"></i>
                    <span>任務管理</span>
                </a>
                <div class="dropdown-menu">
                    <a href="/tasks/dashboard" class="dropdown-item">
                        <i class="fas fa-tachometer-alt"></i>
                        任務儀表板
                    </a>
                    <a href="/tasks/queue" class="dropdown-item">
                        <i class="fas fa-list"></i>
                        任務隊列
                    </a>
                    <a href="/tasks/scheduler-dashboard" class="dropdown-item">
                        <i class="fas fa-clock"></i>
                        排程管理
                    </a>
                    <a href="/tasks/concurrent-task-manager" class="dropdown-item">
                        <i class="fas fa-cogs"></i>
                        並行任務管理
                    </a>
                </div>
            </div>
            
            <div class="nav-item dropdown">
                <a href="/monitoring" class="nav-link {% if request.endpoint and request.endpoint.startswith('monitoring') %}active{% endif %}">
                    <i class="fas fa-monitor-heart-rate"></i>
                    <span>系統監控</span>
                </a>
                <div class="dropdown-menu">
                    <a href="/monitoring/dashboard" class="dropdown-item">
                        <i class="fas fa-chart-area"></i>
                        監控儀表板
                    </a>
                    <a href="/monitoring/database-manager" class="dropdown-item">
                        <i class="fas fa-database"></i>
                        資料庫管理
                    </a>
                    <a href="/monitoring/realtime-dashboard" class="dropdown-item">
                        <i class="fas fa-broadcast-tower"></i>
                        即時監控
                    </a>
                    <a href="/monitoring/health-check" class="dropdown-item">
                        <i class="fas fa-heartbeat"></i>
                        健康檢查
                    </a>
                </div>
            </div>
            
            <div class="nav-item">
                <a href="/pts-renamer" class="nav-link {% if request.endpoint and request.endpoint.startswith('pts_renamer') %}active{% endif %}">
                    <i class="fas fa-file-code"></i>
                    <span>PTS 重命名</span>
                </a>
            </div>
        </div>
        
        <!-- 右側工具列 -->
        <div class="navbar-tools">
            <!-- 系統狀態指示器 -->
            <div class="nav-item">
                <div class="system-status" id="system-status">
                    <div class="status-indicator" id="status-indicator">
                        <i class="fas fa-circle"></i>
                    </div>
                    <span class="status-text" id="status-text">系統正常</span>
                </div>
            </div>
            
            <!-- 通知中心 -->
            <div class="nav-item dropdown">
                <a href="#" class="nav-link notification-toggle" id="notification-toggle">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" id="notification-badge" style="display: none;">0</span>
                </a>
                <div class="dropdown-menu notification-dropdown" id="notification-dropdown">
                    <div class="dropdown-header">
                        <h6>通知中心</h6>
                        <button class="btn btn-sm btn-link" id="clear-notifications">清除全部</button>
                    </div>
                    <div class="notification-list" id="notification-list">
                        <div class="no-notifications">
                            <i class="fas fa-bell-slash"></i>
                            <p>暫無通知</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 使用者選單 -->
            <div class="nav-item dropdown">
                <a href="#" class="nav-link user-menu-toggle">
                    <i class="fas fa-user-circle"></i>
                    <span>系統管理員</span>
                </a>
                <div class="dropdown-menu">
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-user"></i>
                        個人設定
                    </a>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-cog"></i>
                        系統設定
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-question-circle"></i>
                        說明文件
                    </a>
                    <a href="#" class="dropdown-item">
                        <i class="fas fa-info-circle"></i>
                        關於系統
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 行動版選單切換 -->
        <div class="navbar-toggle">
            <button class="navbar-toggler" id="navbar-toggler">
                <span class="toggler-icon"></span>
                <span class="toggler-icon"></span>
                <span class="toggler-icon"></span>
            </button>
        </div>
    </div>
</nav>

<!-- 導航列 JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 下拉選單功能
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.nav-link, .dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // 關閉其他下拉選單
                dropdowns.forEach(otherDropdown => {
                    if (otherDropdown !== dropdown) {
                        otherDropdown.classList.remove('show');
                    }
                });
                
                // 切換當前下拉選單
                dropdown.classList.toggle('show');
            });
        }
    });
    
    // 點擊外部關閉下拉選單
    document.addEventListener('click', function() {
        dropdowns.forEach(dropdown => {
            dropdown.classList.remove('show');
        });
    });
    
    // 行動版選單切換
    const navbarToggler = document.getElementById('navbar-toggler');
    const navbarNav = document.querySelector('.navbar-nav');
    
    if (navbarToggler && navbarNav) {
        navbarToggler.addEventListener('click', function() {
            navbarNav.classList.toggle('show');
            navbarToggler.classList.toggle('active');
        });
    }
    
    // 系統狀態監控
    function updateSystemStatus() {
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        
        if (statusIndicator && statusText) {
            // 這裡可以添加實際的系統狀態檢查邏輯
            // 目前設為正常狀態
            statusIndicator.className = 'status-indicator status-normal';
            statusText.textContent = '系統正常';
        }
    }
    
    // 初始化系統狀態
    updateSystemStatus();
    
    // 定期更新系統狀態（每30秒）
    setInterval(updateSystemStatus, 30000);
});
</script>