# TDD Verification Checklist

## TDD 流程驗證檢查清單 (強制執行)

### Red Phase 驗證
**確保先寫測試，測試初始狀態為失敗**

#### 測試設計檢查
- [ ] 單元測試先於實現代碼編寫
- [ ] 整合測試先於實現代碼編寫
- [ ] API 測試先於實現代碼編寫
- [ ] 所有測試初始狀態為 FAIL (Red)
- [ ] 測試涵蓋所有 CRUD 操作
- [ ] 測試涵蓋所有業務規則
- [ ] 測試涵蓋所有錯誤情況
- [ ] 測試涵蓋所有邊界條件

#### 測試品質檢查
- [ ] 測試描述清晰且具體
- [ ] 測試獨立且可重複執行
- [ ] 測試資料準備充分
- [ ] 測試斷言明確且完整
- [ ] 測試清理機制完整

#### Red Phase 記錄檢查
- [ ] 記錄了所有失敗的測試
- [ ] 記錄了失敗原因
- [ ] 記錄了預期的實現目標
- [ ] 有失敗測試的執行截圖或日誌

### Green Phase 驗證
**確保實現最小代碼讓所有測試通過**

#### 實現策略檢查
- [ ] 實現了讓測試通過的最小代碼
- [ ] 沒有過度實現不必要的功能
- [ ] 所有 Red Phase 的測試現在都通過
- [ ] 沒有跳過任何測試
- [ ] 沒有修改測試來適應實現

#### 業務邏輯完整性檢查
- [ ] 所有 CRUD 操作都實現了
- [ ] 所有業務規則都實現了
- [ ] 所有錯誤處理都實現了
- [ ] 所有資料驗證都實現了
- [ ] 沒有使用硬編碼或假數據

#### 代碼品質基本檢查
- [ ] 代碼可讀性良好
- [ ] 沒有明顯的重複代碼
- [ ] 沒有明顯的代碼異味
- [ ] 基本的設計原則已遵循

#### Green Phase 記錄檢查
- [ ] 記錄了實現的具體功能
- [ ] 記錄了所有通過的測試
- [ ] 記錄了實現過程中的決策
- [ ] 有通過測試的執行截圖或日誌

### Refactor Phase 驗證
**確保代碼重構且測試持續通過**

#### 重構活動檢查
- [ ] 進行了代碼重構活動
- [ ] 消除了重複代碼
- [ ] 改善了代碼結構
- [ ] 提升了代碼可讀性
- [ ] 優化了性能（如需要）

#### 測試持續性檢查
- [ ] 重構後所有測試仍然通過
- [ ] 沒有因重構而修改測試
- [ ] 測試覆蓋率沒有降低
- [ ] 測試執行時間沒有顯著增加

#### 設計改善檢查
- [ ] 設計模式使用恰當
- [ ] 介面設計清晰
- [ ] 職責分離明確
- [ ] 依賴關係合理

#### Refactor Phase 記錄檢查
- [ ] 記錄了重構的具體活動
- [ ] 記錄了重構的原因和目標
- [ ] 記錄了重構後的改善
- [ ] 有重構前後的對比記錄

## TDD 循環完整性檢查

### 循環執行檢查
- [ ] 完整執行了 Red → Green → Refactor 循環
- [ ] 每個功能都經過了完整的 TDD 循環
- [ ] 沒有跳過任何階段
- [ ] 每個循環都有明確的開始和結束

### 循環記錄檢查
- [ ] 每個循環都有完整的記錄
- [ ] 記錄包含時間戳和執行者
- [ ] 記錄包含循環的具體內容
- [ ] 記錄包含循環的結果和決策

### 循環品質檢查
- [ ] 每個循環的範圍適中
- [ ] 每個循環都產生了可驗證的價值
- [ ] 循環之間有邏輯連接
- [ ] 整體進度按計劃推進

## 測試覆蓋率驗證

### 單元測試覆蓋率
- [ ] 代碼覆蓋率 ≥ 90%
- [ ] 分支覆蓋率 ≥ 85%
- [ ] 函數覆蓋率 = 100%
- [ ] 涵蓋所有公開方法
- [ ] 涵蓋所有業務邏輯

### 整合測試覆蓋率
- [ ] API 端點覆蓋率 = 100%
- [ ] 業務流程覆蓋率 = 100%
- [ ] 資料流轉覆蓋率 = 100%
- [ ] 系統整合點覆蓋率 = 100%

### 端到端測試覆蓋率
- [ ] 主要用戶旅程覆蓋率 = 100%
- [ ] 關鍵業務場景覆蓋率 = 100%
- [ ] 錯誤處理場景覆蓋率 ≥ 80%

## 測試品質驗證

### 測試代碼品質
- [ ] 測試代碼遵循編碼標準
- [ ] 測試名稱清晰描述測試意圖
- [ ] 測試結構遵循 AAA 模式 (Arrange-Act-Assert)
- [ ] 測試資料管理良好
- [ ] 測試清理機制完整

### 測試維護性
- [ ] 測試獨立且可並行執行
- [ ] 測試執行速度合理
- [ ] 測試易於理解和維護
- [ ] 測試資料準備自動化
- [ ] 測試環境配置自動化

### 測試可靠性
- [ ] 測試結果穩定可重複
- [ ] 沒有間歇性失敗的測試
- [ ] 測試錯誤訊息清晰明確
- [ ] 測試執行環境隔離
- [ ] 測試資料互不干擾

## 假 TDD 檢測 (自動識別)

### ❌ 禁止的假 TDD 模式
1. **後寫測試模式**
   - 先寫實現代碼，後補測試
   - 測試只為了通過而寫

2. **修改測試模式**
   - 為了讓實現通過而修改測試
   - 降低測試標準來適應實現

3. **跳過 Red Phase 模式**
   - 沒有先寫失敗的測試
   - 測試一開始就是通過的

4. **過度實現模式**
   - Green Phase 實現過多功能
   - 沒有遵循最小實現原則

5. **跳過 Refactor 模式**
   - 只有 Red-Green 沒有 Refactor
   - 代碼品質沒有改善

### ❌ 禁止的測試替代品
1. **Manual Testing 模式**
   - 用手動測試代替自動化測試
   - 沒有可重複的測試腳本

2. **Debug Testing 模式**
   - 用 Debug 代替正式測試
   - 沒有持久化的測試案例

3. **Console Testing 模式**
   - 用 Console 輸出代替斷言
   - 沒有自動化驗證機制

## TDD 品質指標

### 時間分配指標
- [ ] Red Phase 時間比例：30-40%
- [ ] Green Phase 時間比例：40-50%
- [ ] Refactor Phase 時間比例：20-30%
- [ ] 整體開發時間合理

### 循環效率指標
- [ ] 平均循環時間：10-30 分鐘
- [ ] 循環成功率：≥ 90%
- [ ] 回滾次數：≤ 3 次
- [ ] 測試穩定性：≥ 95%

### 程式碼品質指標
- [ ] 代碼重複率：≤ 5%
- [ ] 圈複雜度：≤ 10
- [ ] 技術債比例：≤ 10%
- [ ] 代碼異味：= 0

## 檢查失敗處理

### 自動回滾條件
如果發現以下任一假 TDD 情況，將自動觸發回滾：
- [ ] 沒有真實執行 Red-Green-Refactor 循環
- [ ] 測試覆蓋率不足
- [ ] 檢測到先寫實現後補測試
- [ ] 檢測到為實現修改測試
- [ ] 檢測到跳過任何 TDD 階段

### 改進要求
當檢查失敗時，必須：
1. 回滾到 TDD 規劃階段
2. 重新設計測試策略
3. 嚴格執行 TDD 流程
4. 提供 TDD 執行證據
5. 通過 TDD 流程驗證才能繼續
