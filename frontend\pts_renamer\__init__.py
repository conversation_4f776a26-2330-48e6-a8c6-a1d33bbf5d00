"""
PTS Renamer Frontend Module

This module provides the web interface for PTS file processing:
- Flask routes and blueprints
- HTML templates with modern UI
- JavaScript components for file upload and processing
- Integration with backend services via MVP presenter pattern

Web Access: http://localhost:5000/pts-renamer/
"""

from .routes.pts_rename_flask_routes import pts_renamer_bp

__all__ = [
    "pts_renamer_bp"
]