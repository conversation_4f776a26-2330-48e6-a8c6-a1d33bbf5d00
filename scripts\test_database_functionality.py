#!/usr/bin/env python3
"""
數據庫功能測試腳本
簡化版本，專注於核心數據庫功能驗證
"""

import os
import sys
import sqlite3
import tempfile
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 設定中文編碼環境變數（Windows 兼容性）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['LANG'] = 'zh_TW.UTF-8'


class SimpleDatabaseTester:
    """簡化的數據庫測試器"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'errors': [],
            'warnings': []
        }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """運行所有測試"""
        print("🧪 開始數據庫功能測試...")
        print("=" * 50)
        
        # 1. 測試 Flask 前端配置
        self._test_flask_config()
        
        # 2. 測試郵件數據庫基本功能
        self._test_email_database_basic()
        
        # 3. 測試任務數據庫基本功能
        self._test_task_database_basic()
        
        # 4. 測試現有數據庫文件
        self._test_existing_databases()
        
        # 5. 生成測試報告
        self._generate_test_report()
        
        return self.results
    
    def _test_flask_config(self):
        """測試 Flask 前端配置"""
        print("🌐 測試 Flask 前端配置...")
        
        try:
            from frontend.config import Config, config
            
            # 檢查配置類是否正確載入
            assert 'development' in config
            assert 'testing' in config
            assert 'production' in config
            
            # 檢查開發環境配置
            dev_config = config['development']
            assert hasattr(dev_config, 'DATABASE_URL')
            assert hasattr(dev_config, 'SQLALCHEMY_DATABASE_URI')
            
            # 檢查測試環境配置
            test_config = config['testing']
            assert hasattr(test_config, 'DATABASE_URL')
            assert test_config.DATABASE_URL == 'sqlite:///:memory:'
            
            self.results['tests']['flask_config'] = {
                'status': 'passed',
                'message': 'Flask 配置載入成功',
                'details': {
                    'development_db': dev_config.DATABASE_URL,
                    'testing_db': test_config.DATABASE_URL,
                    'production_db': config['production'].DATABASE_URL
                }
            }
            
            print("  ✅ Flask 前端配置測試通過")
            
        except Exception as e:
            self.results['tests']['flask_config'] = {
                'status': 'failed',
                'message': f'Flask 配置測試失敗: {e}',
                'error': str(e)
            }
            self.results['errors'].append(f"Flask 配置測試失敗: {e}")
            print(f"  ❌ Flask 前端配置測試失敗: {e}")
    
    def _test_email_database_basic(self):
        """測試郵件數據庫基本功能"""
        print("\n📧 測試郵件數據庫基本功能...")
        
        try:
            # 使用臨時數據庫進行測試
            with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as temp_db:
                temp_db_path = temp_db.name
            
            try:
                from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
                from backend.shared.infrastructure.adapters.database.models import db_engine
                from backend.email.models.email_models import EmailData
                
                # 設定測試數據庫
                test_db_url = f"sqlite:///{temp_db_path}"
                db_engine.database_url = test_db_url
                
                # 初始化數據庫
                email_db = EmailDatabase(test_db_url)
                
                # 創建測試郵件
                test_email = EmailData(
                    message_id="test_message_001",
                    sender="<EMAIL>",
                    subject="測試郵件",
                    body="測試內容",
                    received_time=datetime.now(),
                    attachments=[]
                )
                
                # 測試保存郵件
                email_id = email_db.save_email(test_email)
                assert email_id is not None
                
                # 測試查詢郵件
                retrieved_email = email_db.get_email_by_id(email_id)
                assert retrieved_email is not None
                assert retrieved_email['message_id'] == test_email.message_id
                
                # 測試統計功能
                stats = email_db.get_statistics()
                assert 'total_emails' in stats
                assert stats['total_emails'] >= 1
                
                self.results['tests']['email_database'] = {
                    'status': 'passed',
                    'message': '郵件數據庫基本功能正常',
                    'details': {
                        'email_saved': True,
                        'email_retrieved': True,
                        'statistics_working': True,
                        'total_emails': stats['total_emails']
                    }
                }
                
                print("  ✅ 郵件數據庫基本功能測試通過")
                
            finally:
                # 清理臨時文件
                if Path(temp_db_path).exists():
                    Path(temp_db_path).unlink()
                    
        except Exception as e:
            self.results['tests']['email_database'] = {
                'status': 'failed',
                'message': f'郵件數據庫測試失敗: {e}',
                'error': str(e)
            }
            self.results['errors'].append(f"郵件數據庫測試失敗: {e}")
            print(f"  ❌ 郵件數據庫測試失敗: {e}")
    
    def _test_task_database_basic(self):
        """測試任務數據庫基本功能"""
        print("\n📋 測試任務數據庫基本功能...")
        
        try:
            # 使用臨時數據庫進行測試
            with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as temp_db:
                temp_db_path = temp_db.name
            
            try:
                from backend.shared.infrastructure.database.task_status_db import TaskStatusDB
                
                # 創建測試任務數據庫
                task_db = TaskStatusDB(temp_db_path)
                
                # 測試基本連接
                conn = task_db._get_connection()
                result = conn.execute("SELECT 1").fetchone()
                assert result[0] == 1
                conn.close()
                
                # 測試任務操作
                task_id = f"test_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                session_id = f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # 測試任務開始（使用簡化版本）
                conn = task_db._get_connection()
                conn.execute("""
                    INSERT INTO eqc_task_execution 
                    (task_id, session_id, folder_path, status, started_at)
                    VALUES (?, ?, ?, 'started', ?)
                """, (task_id, session_id, "/test/path", datetime.now().isoformat()))
                conn.commit()
                conn.close()
                
                # 測試查詢
                task_status = task_db.get_task_status(task_id=task_id)
                assert task_status is not None
                assert task_status['task_id'] == task_id
                
                self.results['tests']['task_database'] = {
                    'status': 'passed',
                    'message': '任務數據庫基本功能正常',
                    'details': {
                        'connection_working': True,
                        'task_created': True,
                        'task_retrieved': True
                    }
                }
                
                print("  ✅ 任務數據庫基本功能測試通過")
                
            finally:
                # 清理臨時文件
                if Path(temp_db_path).exists():
                    Path(temp_db_path).unlink()
                    
        except Exception as e:
            self.results['tests']['task_database'] = {
                'status': 'failed',
                'message': f'任務數據庫測試失敗: {e}',
                'error': str(e)
            }
            self.results['errors'].append(f"任務數據庫測試失敗: {e}")
            print(f"  ❌ 任務數據庫測試失敗: {e}")
    
    def _test_existing_databases(self):
        """測試現有數據庫文件"""
        print("\n💾 測試現有數據庫文件...")
        
        # 檢查的數據庫文件
        db_files = {
            'email_inbox_main': 'data/email_inbox.db',
            'email_inbox_alt': 'email_inbox.db',
            'email_inbox_frontend': 'frontend/email_inbox.db',
            'outlook': 'outlook.db',
            'eqc_task_status': 'data/eqc_task_status.db'
        }
        
        existing_dbs = {}
        
        for db_name, db_path in db_files.items():
            if Path(db_path).exists():
                try:
                    # 測試連接
                    conn = sqlite3.connect(db_path, timeout=5.0)
                    
                    # 獲取表列表
                    tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
                    table_names = [table[0] for table in tables]
                    
                    # 檢查完整性
                    integrity_result = conn.execute("PRAGMA integrity_check").fetchone()
                    is_ok = integrity_result and integrity_result[0] == 'ok'
                    
                    # 獲取文件大小
                    file_size = Path(db_path).stat().st_size
                    
                    existing_dbs[db_name] = {
                        'path': db_path,
                        'exists': True,
                        'size_bytes': file_size,
                        'tables': table_names,
                        'table_count': len(table_names),
                        'integrity_ok': is_ok,
                        'connection_ok': True
                    }
                    
                    conn.close()
                    
                    print(f"  ✅ {db_name}: {db_path} ({file_size:,} bytes, {len(table_names)} 表)")
                    
                except Exception as e:
                    existing_dbs[db_name] = {
                        'path': db_path,
                        'exists': True,
                        'connection_ok': False,
                        'error': str(e)
                    }
                    print(f"  ❌ {db_name}: 連接失敗 - {e}")
                    self.results['errors'].append(f"數據庫連接失敗 {db_name}: {e}")
            else:
                existing_dbs[db_name] = {
                    'path': db_path,
                    'exists': False
                }
                print(f"  ⚠️  {db_name}: 文件不存在 - {db_path}")
                self.results['warnings'].append(f"數據庫文件不存在: {db_path}")
        
        self.results['tests']['existing_databases'] = {
            'status': 'completed',
            'message': f'檢查了 {len(db_files)} 個數據庫文件',
            'details': existing_dbs
        }
    
    def _generate_test_report(self):
        """生成測試報告"""
        print("\n" + "=" * 50)
        print("📋 數據庫功能測試報告")
        print("=" * 50)
        
        # 統計測試結果
        total_tests = len(self.results['tests'])
        passed_tests = sum(1 for test in self.results['tests'].values() 
                          if test.get('status') == 'passed')
        failed_tests = sum(1 for test in self.results['tests'].values() 
                          if test.get('status') == 'failed')
        
        print(f"測試總數: {total_tests}")
        print(f"通過: {passed_tests}")
        print(f"失敗: {failed_tests}")
        print(f"警告: {len(self.results['warnings'])}")
        print(f"錯誤: {len(self.results['errors'])}")
        
        # 詳細結果
        print(f"\n📊 詳細結果:")
        for test_name, test_result in self.results['tests'].items():
            status_emoji = {
                'passed': '✅',
                'failed': '❌',
                'completed': '✅'
            }.get(test_result['status'], '❓')
            
            print(f"  {status_emoji} {test_name}: {test_result['message']}")
        
        # 錯誤摘要
        if self.results['errors']:
            print(f"\n❌ 錯誤摘要:")
            for i, error in enumerate(self.results['errors'], 1):
                print(f"  {i}. {error}")
        
        # 警告摘要
        if self.results['warnings']:
            print(f"\n⚠️  警告摘要:")
            for i, warning in enumerate(self.results['warnings'], 1):
                print(f"  {i}. {warning}")
        
        # 總體評估
        if failed_tests == 0:
            if len(self.results['errors']) == 0:
                print(f"\n🎉 所有數據庫功能測試通過！")
                self.results['overall_status'] = 'success'
            else:
                print(f"\n⚠️  測試通過但有警告")
                self.results['overall_status'] = 'warning'
        else:
            print(f"\n❌ 部分測試失敗，需要修復")
            self.results['overall_status'] = 'failure'


def main():
    """主函數"""
    print("🧪 數據庫功能測試工具")
    print("=" * 50)
    
    tester = SimpleDatabaseTester()
    results = tester.run_all_tests()
    
    # 返回適當的退出碼
    if results['overall_status'] == 'success':
        sys.exit(0)
    elif results['overall_status'] == 'warning':
        sys.exit(0)  # 警告不算失敗
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()