"""
PTS Renamer Core Service

This module contains the main service orchestrator for PTS file processing.
It coordinates between different processing services and manages the overall
workflow according to MVP architecture principles.
"""

from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
import asyncio
# import logging  # Using UnifiedLogger instead

from ..models.pts_rename_entities import (
    PTSProcessingJob,
    PTSFile,
    PTSRenameResult,
    PTSOperationType,
    PTSQCFile,
    PTSDirectory
)
from ..models.pts_rename_models import (
    PTSRenameJobRequest,
    PTSRenameJobStatus,
    PTSRenamePreviewResponse,
    PTSFilePreview
)
from ..repositories.pts_rename_repository import IPTSRenameRepository
from .pts_rename_task_queue import PTSRenameTaskQueue
# Using loguru for logging (already imported in other services)


class PTSRenameService:
    """
    Core service for PTS file processing
    
    This service orchestrates the entire PTS processing workflow:
    - Coordinates file processing, QC generation, and directory creation
    - Manages batch operations and result finalization
    - Integrates with existing Dramatiq infrastructure
    - Handles error recovery and cleanup
    """
    
    def __init__(self,
                 repository: IPTSRenameRepository,
                 processor: 'PTSRenameProcessor',
                 qc_generator: 'PTSQCGenerator',
                 directory_manager: 'PTSDirectoryManager',
                 download_service: 'PTSRenameDownloadService',
                 task_queue: PTSRenameTaskQueue,
                 logger = None):
        """
        Initialize PTS Rename Service
        
        Args:
            repository: Data access repository
            processor: File renaming processor
            qc_generator: QC file generator
            directory_manager: Directory management service
            download_service: Download and compression service
            task_queue: PTS-specific task queue for async processing
            logger: Optional logger instance
        """
        self.repository = repository
        self.processor = processor
        self.qc_generator = qc_generator
        self.directory_manager = directory_manager
        self.download_service = download_service
        self.task_queue = task_queue
        # Using loguru logger (imported in other services)
        from loguru import logger as loguru_logger
        self.logger = logger or loguru_logger
    
    async def process_pts_files(self, job_request: PTSRenameJobRequest) -> str:
        """
        Process PTS files according to request
        
        This method orchestrates the entire processing workflow:
        1. Create processing job
        2. Queue processing tasks using Dramatiq
        3. Monitor progress and handle errors
        4. Finalize results with compression
        
        Args:
            job_request: Processing request with configuration
            
        Returns:
            Job ID for tracking progress
            
        Raises:
            ServiceError: If processing setup fails
        """
        try:
            # Log processing request details
            self.logger.info(f"[PROCESS] Starting PTS file processing for upload: {job_request.upload_id}")
            self.logger.info(f"[PROCESS] Requested operations: {[op.value for op in job_request.operations]}")
            
            # Get PTS files for the upload
            pts_files = await self.repository.get_pts_files(job_request.upload_id)
            if not pts_files:
                error_details = {
                    "upload_id": job_request.upload_id,
                    "operations": [op.value for op in job_request.operations],
                    "message": "No PTS files were found in the upload directory",
                    "suggestion": "Please verify that the upload contains .pts files and was extracted properly"
                }
                self.logger.error(f"[PROCESS] No PTS files found for upload {job_request.upload_id}")
                raise ServiceError(
                    f"No PTS files found for upload {job_request.upload_id}",
                    details=error_details
                )
            
            # Log found files for debugging
            self.logger.info(f"[PROCESS] Found {len(pts_files)} PTS files:")
            for pts_file in pts_files:
                self.logger.info(f"[PROCESS] - {pts_file.filename} ({pts_file.size} bytes) at: {pts_file.original_path}")
            
            # Convert rename_config to RenamePattern if provided
            rename_pattern = None
            if job_request.rename_config:
                from ..models.pts_rename_entities import RenamePattern
                rename_pattern = RenamePattern(
                    old_pattern=job_request.rename_config.get('old_pattern', ''),
                    new_pattern=job_request.rename_config.get('new_pattern', '')
                )
                self.logger.info(f"[PROCESS] Rename pattern: {rename_pattern.old_pattern} -> {rename_pattern.new_pattern}")
            
            # Create processing job
            job = PTSProcessingJob(
                job_id=self._generate_job_id(),
                upload_id=job_request.upload_id,
                pts_files=pts_files,
                operations=[PTSOperationType(op.value) for op in job_request.operations],
                rename_pattern=rename_pattern,  # Fixed: use rename_pattern instead of rename_config
                qc_enabled=job_request.qc_enabled,
                create_directories=job_request.create_directories
            )
            
            # Save job to repository
            job_id = await self.repository.save_job(job)
            
            # Queue processing task using existing Dramatiq infrastructure
            await self.task_queue.enqueue_task(
                "pts_rename_process_job",
                job_id=job_id,
                job_data=self._serialize_job_for_queue(job)
            )
            
            self.logger.info(f"Queued PTS processing job {job_id} with {len(pts_files)} files")
            return job_id
            
        except Exception as e:
            self.logger.error(f"Failed to process PTS files: {e}")
            raise ServiceError(f"Processing setup failed: {e}")
    
    async def generate_preview(self, upload_id: str, operations: List[str], 
                             rename_config: Optional[Dict[str, str]] = None) -> PTSRenamePreviewResponse:
        """
        Generate processing preview
        
        Args:
            upload_id: Upload identifier
            operations: List of operations to preview
            rename_config: Rename configuration if applicable
            
        Returns:
            Preview response with file-by-file preview
        """
        try:
            # Get PTS files
            pts_files = await self.repository.get_pts_files(upload_id)
            if not pts_files:
                return PTSRenamePreviewResponse(
                    upload_id=upload_id,
                    total_files=0,
                    files_preview=[],
                    operations_summary={},
                    estimated_processing_time=0,
                    errors=["No PTS files found for upload"]
                )
            
            # Generate preview for each file
            files_preview = []
            warnings = []
            errors = []
            
            for pts_file in pts_files:
                try:
                    preview = await self._generate_file_preview(
                        pts_file, operations, rename_config
                    )
                    files_preview.append(preview)
                    warnings.extend(preview.warnings)
                except Exception as e:
                    errors.append(f"Preview failed for {pts_file.filename}: {e}")
            
            # Calculate operations summary
            operations_summary = self._calculate_operations_summary(files_preview)
            
            # Estimate processing time (rough calculation)
            estimated_time = self._estimate_processing_time(len(pts_files), operations)
            
            return PTSRenamePreviewResponse(
                upload_id=upload_id,
                total_files=len(pts_files),
                files_preview=files_preview,
                operations_summary=operations_summary,
                estimated_processing_time=estimated_time,
                warnings=list(set(warnings)),  # Remove duplicates
                errors=errors
            )
            
        except Exception as e:
            self.logger.error(f"Preview generation failed: {e}")
            raise ServiceError(f"Preview generation failed: {e}")
    
    async def finalize_processing(self, job_id: str, processed_files: List[str]) -> str:
        """
        Finalize processing by compressing results and creating download URL
        
        Args:
            job_id: Job identifier
            processed_files: List of processed file paths
            
        Returns:
            Download URL for frontend
        """
        try:
            # Get job details
            job = await self.repository.get_job(job_id)
            if not job:
                raise ServiceError(f"Job {job_id} not found")
            
            # Create download package using existing compression infrastructure
            download_url = await self.download_service.create_download_package(
                job_id, processed_files
            )
            
            # Update job with download URL
            job.mark_as_completed(download_url=download_url)
            await self.repository.save_job(job)
            
            self.logger.info(f"Finalized processing for job {job_id}, download URL: {download_url}")
            return download_url
            
        except Exception as e:
            self.logger.error(f"Failed to finalize processing: {e}")
            raise ServiceError(f"Finalization failed: {e}")
    
    async def get_job_status(self, job_id: str) -> Optional[PTSRenameJobStatus]:
        """
        Get current job status
        
        Args:
            job_id: Job identifier
            
        Returns:
            Job status or None if not found
        """
        try:
            job = await self.repository.get_job(job_id)
            if not job:
                return None
            
            return PTSRenameJobStatus(
                job_id=job.job_id,
                status=job.status,
                progress=job.progress_percentage,
                files_processed=job.files_processed,
                total_files=job.total_files,
                error_message=job.error_message,
                result_download_url=job.download_url,
                download_expires_at=job.download_expires_at,
                created_at=job.created_at,
                updated_at=job.updated_at
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get job status: {e}")
            return None
    
    async def cleanup_expired_jobs(self, retention_hours: int = 24) -> int:
        """
        Clean up expired jobs and associated files
        
        Args:
            retention_hours: Hours after which jobs are considered expired
            
        Returns:
            Number of jobs cleaned up
        """
        try:
            deleted_count = await self.repository.delete_expired_jobs(retention_hours)
            self.logger.info(f"Cleaned up {deleted_count} expired PTS processing jobs")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"Cleanup failed: {e}")
            return 0
    
    # Private helper methods
    
    def _generate_job_id(self) -> str:
        """Generate unique job ID"""
        import uuid
        return f"pts_job_{uuid.uuid4().hex[:12]}"
    
    def _serialize_job_for_queue(self, job: PTSProcessingJob) -> Dict[str, Any]:
        """Serialize job for task queue"""
        return {
            "job_id": job.job_id,
            "upload_id": job.upload_id,
            "operations": [op.value for op in job.operations],
            "rename_config": job.rename_config,
            "qc_enabled": job.qc_enabled,
            "create_directories": job.create_directories,
            "total_files": job.total_files
        }
    
    async def _generate_file_preview(self, pts_file: PTSFile, operations: List[str], 
                                   rename_config: Optional[Dict[str, str]]) -> PTSFilePreview:
        """Generate preview for a single file"""
        warnings = []
        operations_applied = []
        
        # Preview rename operation
        new_name = None
        if "rename" in operations and rename_config:
            try:
                # Create a Path object for the processor
                file_path = Path(pts_file.original_path)
                new_name = self.processor.generate_new_name(
                    file_path, 
                    rename_config.get("old_pattern", ""),
                    rename_config.get("new_pattern", "")
                )
                if new_name != pts_file.filename:
                    operations_applied.append("rename")
                else:
                    warnings.append(f"Rename pattern does not match {pts_file.filename}")
            except Exception as e:
                warnings.append(f"Rename preview failed: {e}")
        
        # Preview QC generation
        qc_file_name = None
        if "qc_generation" in operations:
            file_path = Path(pts_file.filename)
            qc_file_name = f"{file_path.stem}_QC{file_path.suffix}"
            operations_applied.append("qc_generation")
        
        # Preview directory creation
        directory_name = None
        if "directory_creation" in operations:
            directory_name = Path(pts_file.filename).stem
            operations_applied.append("directory_creation")
        
        return PTSFilePreview(
            original_name=pts_file.filename,
            new_name=new_name,
            qc_file_name=qc_file_name,
            directory_name=directory_name,
            file_size=pts_file.size,
            operations_applied=operations_applied,
            warnings=warnings
        )
    
    def _calculate_operations_summary(self, files_preview: List[PTSFilePreview]) -> Dict[str, int]:
        """Calculate summary of operations to be performed"""
        summary = {
            "rename": 0,
            "qc_generation": 0,
            "directory_creation": 0
        }
        
        for preview in files_preview:
            for operation in preview.operations_applied:
                if operation in summary:
                    summary[operation] += 1
        
        return summary
    
    def _estimate_processing_time(self, file_count: int, operations: List[str]) -> int:
        """Estimate processing time in seconds"""
        # Base time per file
        base_time = 2  # seconds per file
        
        # Additional time per operation
        operation_time = {
            "rename": 1,
            "qc_generation": 3,
            "directory_creation": 5
        }
        
        total_time = file_count * base_time
        for operation in operations:
            if operation in operation_time:
                total_time += file_count * operation_time[operation]
        
        # Add compression time
        total_time += min(30, file_count * 0.5)
        
        return int(total_time)


class ServiceError(Exception):
    """Exception raised by service operations"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message)
        self.details = details or {}
        self.timestamp = datetime.now()