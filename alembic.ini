# Alembic 配置檔案 - Epic-01 Database Infrastructure
# 為 Outlook 郵件摘要工具設計的資料庫遷移管理

[alembic]
# Alembic 遷移腳本路徑
script_location = alembic

# 資料庫連接字串模板 (將由環境變數或程式碼動態設定)
sqlalchemy.url = sqlite:///data/email_inbox.db

# 日誌配置
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S

# Epic-01 特定配置
[epic01]
# 遷移腳本命名模式
file_template = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s
# 時區設定
timezone = Asia/Taipei
# 備份配置
backup_before_migration = true
backup_directory = backups/migrations
# 驗證配置
verify_after_migration = true