"""
分析統計模組路由
處理所有分析統計相關的路由和 API 端點
"""

from flask import Blueprint, render_template, jsonify, request
import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.shared.infrastructure.logging.logger_manager import LoggerManager

# 創建藍圖
analytics_bp = Blueprint('analytics', __name__,
                          template_folder='../templates',
                          static_folder='../static',
                          static_url_path='/static/analytics')

# 初始化日誌
logger = LoggerManager().get_logger("AnalyticsRoutes")


@analytics_bp.route('/')
def analytics_home():
    """Analytics 主頁"""
    try:
        return render_template('dashboard.html')
    except Exception as e:
        logger.error(f"載入 Analytics 主頁失敗: {e}")
        return jsonify({'error': '載入 Analytics 主頁失敗'}), 500


@analytics_bp.route('/FT_QC_SUMMARY')
@analytics_bp.route('/dashboard')
@analytics_bp.route('/ft-qc-summary')
def ft_qc_summary():
    """FT QC Summary 批量處理主頁"""
    try:
        return render_template('dashboard.html')
    except Exception as e:
        logger.error(f"載入 FT QC Summary 主頁失敗: {e}")
        return jsonify({'error': '載入 FT QC Summary 主頁失敗'}), 500


@analytics_bp.route('/ft-summary-ui')
def ft_summary_ui():
    """FT Summary 批量處理 UI (備用路由)"""
    try:
        return render_template('ft_summary_ui.html')
    except Exception as e:
        logger.error(f"載入 FT Summary UI 失敗: {e}")
        return jsonify({'error': '載入 FT Summary UI 失敗'}), 500


@analytics_bp.route('/launch-csv-to-summary')
def launch_csv_to_summary():
    """啟動 csv_to_summary.py 工具"""
    try:
        import subprocess
        import sys
        from pathlib import Path

        # 獲取專案根目錄
        project_root = Path(__file__).parent.parent.parent.parent
        launch_script = project_root / "launch_csv_to_summary.py"

        if not launch_script.exists():
            return jsonify({
                'success': False,
                'error': 'launch_csv_to_summary.py 腳本不存在'
            }), 404

        # 在背景啟動腳本
        subprocess.Popen([
            sys.executable,
            str(launch_script)
        ], cwd=str(project_root))

        return jsonify({
            'success': True,
            'message': 'FT-Summary 處理工具已啟動',
            'script': str(launch_script)
        })

    except Exception as e:
        logger.error(f"啟動 csv_to_summary.py 失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'啟動失敗: {str(e)}'
        }), 500


@analytics_bp.route('/reports')
def reports():
    """報表頁面"""
    try:
        # 提供模板所需的統計數據
        stats = {
            'total_emails': 0,
            'processed_emails': 0,
            'processing_rate': 0.0,
            'total_vendors': 0,
            'active_vendors': 0,
            'compliance_rate': 0.0,
            'total_checks': 0,
            'pass_rate': 0.0,
            'failed_checks': 0,
            'avg_processing_time': 0.0,
            'system_load': 0.0,
            'error_rate': 0.0
        }
        
        # 提供報表歷史記錄
        report_history = []
        
        return render_template('reports.html', stats=stats, report_history=report_history)
    except Exception as e:
        logger.error(f"載入報表頁面失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


@analytics_bp.route('/vendor-analysis')
def vendor_analysis():
    """廠商分析頁面"""
    try:
        # 提供模板所需的統計數據
        stats = {
            'total_vendors': 0,
            'new_vendors': 0,
            'active_vendors': 0,
            'active_rate': 0.0,
            'compliant_vendors': 0,
            'compliance_rate': 0.0,
            'risk_vendors': 0
        }
        
        # 提供廠商列表數據
        vendors = []
        
        return render_template('vendor_analysis.html', stats=stats, vendors=vendors)
    except Exception as e:
        logger.error(f"載入廠商分析頁面失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


# API 路由
@analytics_bp.route('/api/metrics')
def api_metrics():
    """獲取關鍵指標 API"""
    try:
        # TODO: 實作統計指標邏輯
        return jsonify({
            'status': 'success',
            'data': {
                'total_emails': 0,
                'processed_emails': 0,
                'success_rate': 0.0,
                'avg_processing_time': 0.0
            }
        })
    except Exception as e:
        logger.error(f"獲取統計指標失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@analytics_bp.route('/api/charts/<chart_type>')
def api_charts(chart_type: str):
    """獲取圖表數據 API"""
    try:
        # TODO: 實作圖表數據邏輯
        return jsonify({
            'status': 'success',
            'data': {
                'chart_type': chart_type,
                'labels': [],
                'datasets': []
            }
        })
    except Exception as e:
        logger.error(f"獲取圖表數據失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@analytics_bp.route('/api/reports')
def api_reports():
    """獲取報表列表 API"""
    try:
        # TODO: 實作報表列表邏輯
        return jsonify({
            'status': 'success',
            'data': []
        })
    except Exception as e:
        logger.error(f"獲取報表列表失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@analytics_bp.route('/api/status')
def api_status():
    """獲取分析模組狀態 API"""
    try:
        return jsonify({
            'success': True,
            'module': 'analytics',
            'status': 'healthy',
            'data': {
                'module_version': '1.0.0',
                'features': ['reports', 'vendor-analysis', 'search'],
                'last_update': '2025-08-13T22:50:00Z'
            }
        })
    except Exception as e:
        logger.error(f"獲取分析模組狀態失敗: {e}")
        return jsonify({
            'success': False,
            'module': 'analytics',
            'status': 'error',
            'error': str(e)
        }), 500


@analytics_bp.route('/api/summaries/process', methods=['POST'])
def api_process_summaries():
    """處理 FT-Summary 批量處理請求 - 使用 run_csv_summary_task"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '無效的請求數據'
            }), 400

        folder_path = data.get('folderPath') or data.get('folder_path')
        if not folder_path:
            return jsonify({
                'success': False,
                'error': '缺少資料夾路徑'
            }), 400

        logger.info(f"開始處理 FT-Summary 批量處理: {folder_path}")

        # 處理遠端路徑（如果需要）
        processed_folder_path = folder_path
        cleanup_paths_list = []

        try:
            from backend.shared.utils.remote_path_processor import (
                process_input_path, RemotePathProcessor
            )

            # 檢查是否為遠端路徑
            if RemotePathProcessor().is_remote_path(folder_path):
                logger.info(f"檢測到遠端路徑，開始複製: {folder_path}")
                processed_path, process_type, success = process_input_path(folder_path)

                if not success:
                    return jsonify({
                        'success': False,
                        'error': '遠端路徑複製失敗',
                        'details': f'無法複製遠端路徑: {folder_path}'
                    }), 400

                processed_folder_path = processed_path
                cleanup_paths_list.append(processed_path)
                logger.info(f"遠端路徑複製完成: {folder_path} -> {processed_folder_path}")

        except ImportError:
            logger.warning("遠端路徑處理器不可用，使用原始路徑")

        # 嘗試使用 Dramatiq 背景任務
        try:
            from backend.tasks.services.dramatiq_tasks import run_csv_summary_task

            # 提交背景任務（使用處理後的路徑）
            task = run_csv_summary_task.send(processed_folder_path)
            task_id = task.message_id

            logger.info(f"FT-Summary 背景任務已提交: {task_id}")

            # 判斷是否為需要自動下載的情況
            is_temp_processing = processed_folder_path.lower().replace('/', '\\').startswith('d:\\temp\\')

            return jsonify({
                'success': True,
                'async': True,
                'task_id': task_id,
                'message': 'FT-Summary 處理任務已開始，正在背景執行',
                'folder_path': folder_path,
                'processed_folder_path': processed_folder_path,
                'auto_download_enabled': is_temp_processing,
                'cleanup_paths': cleanup_paths_list if cleanup_paths_list else None
            })

        except ImportError as e:
            logger.warning(f"Dramatiq 任務不可用，回退到同步處理: {e}")

            # 回退到同步處理（如果需要）
            return jsonify({
                'success': False,
                'error': 'CSV 摘要處理服務暫時不可用',
                'details': '請稍後再試或聯繫管理員'
            }), 503

    except Exception as e:
        logger.error(f"處理 FT-Summary 請求失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'處理失敗: {str(e)}'
        }), 500


@analytics_bp.route('/api/summaries')
def api_summaries_list():
    """獲取現有摘要列表"""
    try:
        # TODO: 實作摘要列表邏輯
        # 這裡可以從檔案系統或資料庫中獲取已生成的摘要
        return jsonify({
            'success': True,
            'data': []  # 暫時返回空列表
        })
    except Exception as e:
        logger.error(f"獲取摘要列表失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@analytics_bp.route('/api/summaries/<summary_id>')
def api_summary_detail(summary_id: str):
    """獲取特定摘要的詳情"""
    try:
        # TODO: 實作摘要詳情邏輯
        return jsonify({
            'success': True,
            'data': {
                'id': summary_id,
                'status': 'completed',
                'message': '摘要詳情暫未實作'
            }
        })
    except Exception as e:
        logger.error(f"獲取摘要詳情失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@analytics_bp.route('/api/tasks/<task_id>/progress')
def api_task_progress(task_id: str):
    """獲取任務進度"""
    try:
        # TODO: 實作任務進度查詢邏輯
        # 這裡可以查詢 Dramatiq 任務狀態
        return jsonify({
            'success': True,
            'task_id': task_id,
            'status': 'running',
            'progress': 50,
            'message': '正在處理中...'
        })
    except Exception as e:
        logger.error(f"獲取任務進度失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@analytics_bp.route('/api/summaries/task-status/<task_id>', methods=['GET'])
def api_get_task_status(task_id):
    """獲取任務狀態和下載連結"""
    try:
        logger.info(f"檢查任務狀態: {task_id}")

        # 嘗試從 Dramatiq Results 獲取任務結果
        try:
            from dramatiq import get_broker
            from dramatiq.results import Results

            broker = get_broker()
            results_backend = None

            # 查找 Results 中間件
            for middleware in broker.middleware:
                if isinstance(middleware, Results):
                    results_backend = middleware.backend
                    break

            if results_backend:
                # 獲取任務結果
                result = results_backend.get_result(task_id, block=False)

                if result is not None:
                    # 任務已完成
                    if isinstance(result, dict):
                        task_result = result
                    else:
                        task_result = {'status': 'completed', 'result': str(result)}

                    response = {
                        'success': True,
                        'task_id': task_id,
                        'status': task_result.get('status', 'completed'),
                        'completed': True,
                        'result': task_result
                    }

                    # 檢查是否有自動壓縮任務
                    if task_result.get('archive_task_submitted') and task_result.get('archive_task_id'):
                        archive_task_id = task_result['archive_task_id']

                        # 檢查壓縮任務狀態
                        try:
                            archive_result = results_backend.get_result(archive_task_id, block=False)

                            if archive_result is not None and isinstance(archive_result, dict):
                                if archive_result.get('status') == 'completed' and archive_result.get('download_ready'):
                                    # 壓縮任務完成，添加下載連結
                                    archive_path = archive_result['archive_path']
                                    response['download_available'] = True
                                    response['download_url'] = f'/analytics/api/summaries/download?path={archive_path}'
                                    response['download_filename'] = archive_result.get('archive_filename', os.path.basename(archive_path))
                                    response['archive_status'] = 'completed'
                                elif archive_result.get('status') == 'failed':
                                    response['archive_status'] = 'failed'
                                    response['archive_error'] = archive_result.get('error', '壓縮失敗')
                                else:
                                    response['archive_status'] = 'processing'
                            else:
                                # 壓縮任務仍在執行中
                                response['archive_status'] = 'processing'

                        except Exception as e:
                            logger.warning(f"檢查壓縮任務狀態失敗: {e}")
                            response['archive_status'] = 'unknown'

                    return jsonify(response)
                else:
                    # 任務仍在執行中
                    return jsonify({
                        'success': True,
                        'task_id': task_id,
                        'status': 'running',
                        'completed': False,
                        'message': '任務正在執行中...'
                    })
            else:
                return jsonify({
                    'success': False,
                    'error': 'Results backend 不可用'
                }), 503

        except ImportError:
            return jsonify({
                'success': False,
                'error': 'Dramatiq Results 不可用'
            }), 503

    except Exception as e:
        logger.error(f"檢查任務狀態失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'檢查任務狀態失敗: {str(e)}'
        }), 500


@analytics_bp.route('/api/summaries/download', methods=['GET'])
def api_download_result():
    """下載處理結果壓縮檔"""
    try:
        archive_path = request.args.get('path')
        if not archive_path:
            return jsonify({
                'success': False,
                'error': '缺少下載路徑'
            }), 400

        logger.info(f"下載結果壓縮檔: {archive_path}")

        # 驗證檔案存在
        if not os.path.exists(archive_path):
            return jsonify({
                'success': False,
                'error': '下載檔案不存在'
            }), 404

        # 獲取檔案名稱
        filename = os.path.basename(archive_path)

        from flask import send_file
        return send_file(
            archive_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/zip'
        )

    except Exception as e:
        logger.error(f"下載結果失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'下載失敗: {str(e)}'
        }), 500