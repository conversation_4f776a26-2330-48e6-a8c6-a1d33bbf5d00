"""Epic-01 Story 1.3: 郵件下載重試記錄表建立

Revision ID: 002_epic01_retry_log
Revises: 001_epic01_emails_ext
Create Date: 2025-08-20 14:50:00
Epic: 01 - Database Infrastructure  
Story: 1.3 - EmailDownloadRetryLogDB Establishment

Migration Type: Table Creation + Enum Setup
Risk Level: MEDIUM
Rollback Safe: YES

Description:
創建完整的郵件下載重試記錄表，包含枚舉類型定義、表結構、索引和約束。
這是 Epic-01 重試機制的核心組件，提供詳細的重試追蹤和分析能力。

Changes:
- 創建 EmailDownloadRetryLogDB 表 (16個欄位)
- 新增 RetryStrategy 枚舉 (5種策略)
- 新增 RetryStatus 枚舉 (6種狀態)
- 建立 7個效能優化索引
- 設置外鍵約束和數據完整性檢查
- 新增業務規則驗證觸發器

Prerequisites:
- emails 表必須存在 (Story 1.1 已執行)
- email_download_status 表必須存在
- 確保資料庫備份已創建

Verification Steps:
- 檢查表結構是否正確創建
- 驗證枚舉值是否正確設置
- 確認索引和約束建立成功
- 測試外鍵關聯完整性
- 驗證業務規則觸發器

Performance Impact:
- 預計遷移時間: <30秒
- 新表初始大小: 最小
- 索引創建: 輕微性能影響
- 並發影響: 無 (新表)

Author: Database Admin
Reviewer: System Architect
"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '002_epic01_retry_log'
down_revision = '001_epic01_emails_ext'
branch_labels = None
depends_on = None


def upgrade():
    """
    Epic-01 Story 1.3: 創建郵件下載重試記錄表
    
    這個遷移創建完整的重試記錄系統，包括：
    - 枚舉類型定義 (跨資料庫兼容)
    - 表結構創建 (16個欄位)
    - 效能優化索引 (7個索引)
    - 數據完整性約束
    - 業務規則驗證
    
    操作序列:
    1. 檢查前置條件
    2. 創建枚舉類型 (如適用)
    3. 創建主表結構
    4. 建立索引和約束
    5. 創建業務規則觸發器
    6. 驗證表結構完整性
    """
    
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    print(f"[{datetime.now()}] 開始 Epic-01 Story 1.3 遷移...")
    
    # 步驟1: 檢查前置條件
    required_tables = ['emails', 'email_download_status']
    existing_tables = inspector.get_table_names()
    
    missing_tables = [table for table in required_tables if table not in existing_tables]
    if missing_tables:
        raise Exception(f"前置條件不滿足，缺失表: {missing_tables}")
    
    print("✓ 前置條件檢查通過")
    
    # 步驟2: 創建枚舉類型 (PostgreSQL)
    # 注意：SQLite 使用 CHECK 約束替代 ENUM
    database_type = conn.dialect.name
    
    if database_type == 'postgresql':
        try:
            # 創建 RetryStrategy 枚舉
            op.execute("""
                CREATE TYPE retrystrategy AS ENUM (
                    'linear', 'exponential', 'fixed_delay', 'custom', 'adaptive'
                )
            """)
            print("✓ 創建 RetryStrategy 枚舉類型")
            
            # 創建 RetryStatus 枚舉
            op.execute("""
                CREATE TYPE retrystatus AS ENUM (
                    'scheduled', 'running', 'success', 'failed', 'cancelled', 'timeout'
                )
            """)
            print("✓ 創建 RetryStatus 枚舉類型")
            
        except Exception as e:
            print(f"✓ 枚舉類型已存在或跳過: {e}")
    else:
        print("✓ SQLite 模式：將使用 CHECK 約束替代枚舉")
    
    # 步驟3: 創建主表結構
    try:
        # 根據資料庫類型選擇適當的欄位定義
        if database_type == 'postgresql':
            retry_strategy_type = sa.Enum(
                'linear', 'exponential', 'fixed_delay', 'custom', 'adaptive',
                name='retrystrategy'
            )
            retry_status_type = sa.Enum(
                'scheduled', 'running', 'success', 'failed', 'cancelled', 'timeout',
                name='retrystatus'
            )
        else:
            # SQLite 使用字串類型配合 CHECK 約束
            retry_strategy_type = sa.String(20)
            retry_status_type = sa.String(20)
        
        # 創建 email_download_retry_log 表
        op.create_table(
            'email_download_retry_log',
            
            # 主鍵和外鍵
            sa.Column('id', sa.Integer(), autoincrement=True, primary_key=True, 
                     comment="主鍵ID"),
            sa.Column('download_status_id', sa.Integer(), 
                     sa.ForeignKey('email_download_status.id'), nullable=False,
                     comment="關聯的下載狀態記錄ID"),
            sa.Column('email_id', sa.Integer(), 
                     sa.ForeignKey('emails.id'), nullable=False,
                     comment="關聯的郵件ID"),
            
            # 重試基本資訊
            sa.Column('retry_attempt', sa.Integer(), nullable=False,
                     comment="重試次數 (1-10)"),
            sa.Column('retry_strategy', retry_strategy_type, nullable=False,
                     comment="重試策略"),
            
            # 時間管理
            sa.Column('scheduled_at', sa.DateTime(), nullable=False,
                     comment="調度時間"),
            sa.Column('started_at', sa.DateTime(), nullable=True,
                     comment="開始執行時間"),
            sa.Column('completed_at', sa.DateTime(), nullable=True,
                     comment="完成時間"),
            
            # 狀態和結果
            sa.Column('status', retry_status_type, 
                     nullable=False, default='scheduled',
                     comment="重試狀態"),
            sa.Column('success', sa.Boolean(), default=False, nullable=False,
                     comment="是否成功"),
            
            # 錯誤資訊
            sa.Column('error_type', sa.String(100), nullable=True,
                     comment="錯誤類型"),
            sa.Column('error_message', sa.Text(), nullable=True,
                     comment="錯誤訊息"),
            
            # 性能資訊
            sa.Column('retry_delay_seconds', sa.Integer(), nullable=False,
                     comment="重試延遲秒數"),
            sa.Column('duration_ms', sa.Integer(), nullable=True,
                     comment="執行持續時間(毫秒)"),
            
            # 審計欄位
            sa.Column('created_at', sa.DateTime(), default=datetime.utcnow, 
                     nullable=False, comment="記錄創建時間"),
            sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow, 
                     onupdate=datetime.utcnow, nullable=False, comment="記錄更新時間"),
        )
        
        print("✓ 創建 email_download_retry_log 表")
        
    except Exception as e:
        print(f"✗ 表創建失敗: {e}")
        raise
    
    # 步驟4: 創建 CHECK 約束 (SQLite 模式)
    if database_type == 'sqlite':
        try:
            # RetryStrategy 約束
            op.create_check_constraint(
                'ck_retry_strategy',
                'email_download_retry_log',
                "retry_strategy IN ('linear', 'exponential', 'fixed_delay', 'custom', 'adaptive')"
            )
            
            # RetryStatus 約束
            op.create_check_constraint(
                'ck_retry_status',
                'email_download_retry_log',
                "status IN ('scheduled', 'running', 'success', 'failed', 'cancelled', 'timeout')"
            )
            
            # 重試次數約束
            op.create_check_constraint(
                'ck_retry_attempt_range',
                'email_download_retry_log',
                "retry_attempt >= 1 AND retry_attempt <= 10"
            )
            
            # 延遲時間約束
            op.create_check_constraint(
                'ck_retry_delay_range',
                'email_download_retry_log',
                "retry_delay_seconds >= 0 AND retry_delay_seconds <= 3600"
            )
            
            print("✓ 創建數據完整性 CHECK 約束")
            
        except Exception as e:
            print(f"✗ CHECK 約束創建失敗: {e}")
            raise
    
    # 步驟5: 建立效能優化索引
    try:
        # 主要查詢索引
        op.create_index('idx_retry_log_download_status_id', 
                       'email_download_retry_log', ['download_status_id'])
        print("✓ 創建 idx_retry_log_download_status_id 索引")
        
        op.create_index('idx_retry_log_email_id', 
                       'email_download_retry_log', ['email_id'])
        print("✓ 創建 idx_retry_log_email_id 索引")
        
        # 狀態查詢索引
        op.create_index('idx_retry_log_status', 
                       'email_download_retry_log', ['status'])
        print("✓ 創建 idx_retry_log_status 索引")
        
        # 時間查詢索引
        op.create_index('idx_retry_log_scheduled_at', 
                       'email_download_retry_log', ['scheduled_at'])
        print("✓ 創建 idx_retry_log_scheduled_at 索引")
        
        # 策略分析索引
        op.create_index('idx_retry_log_retry_strategy', 
                       'email_download_retry_log', ['retry_strategy'])
        print("✓ 創建 idx_retry_log_retry_strategy 索引")
        
        # 複合索引 - 郵件和重試次數
        op.create_index('idx_retry_log_composite', 
                       'email_download_retry_log', ['email_id', 'retry_attempt'])
        print("✓ 創建 idx_retry_log_composite 複合索引")
        
        # 錯誤分析索引
        op.create_index('idx_retry_log_error_type', 
                       'email_download_retry_log', ['error_type'])
        print("✓ 創建 idx_retry_log_error_type 索引")
        
    except Exception as e:
        print(f"✗ 索引創建失敗: {e}")
        raise
    
    # 步驟6: 驗證表結構完整性
    try:
        # 重新檢查表結構
        inspector = sa.inspect(conn)
        
        if 'email_download_retry_log' not in inspector.get_table_names():
            raise Exception("email_download_retry_log 表創建失敗")
        
        # 檢查欄位數量和類型
        columns = inspector.get_columns('email_download_retry_log')
        if len(columns) < 15:  # 應該有16個欄位
            raise Exception(f"表欄位數量不正確，預期16個，實際{len(columns)}個")
        
        # 檢查索引
        indexes = inspector.get_indexes('email_download_retry_log')
        if len(indexes) < 7:
            print(f"⚠ 警告：索引數量可能不完整，實際{len(indexes)}個")
        
        # 檢查外鍵約束
        foreign_keys = inspector.get_foreign_keys('email_download_retry_log')
        if len(foreign_keys) < 2:
            print(f"⚠ 警告：外鍵約束可能不完整，實際{len(foreign_keys)}個")
        
        print("✓ 表結構驗證通過")
        
    except Exception as e:
        print(f"✗ 表結構驗證失敗: {e}")
        raise
    
    # 步驟7: 初始化統計資訊 (可選)
    try:
        if database_type == 'postgresql':
            conn.execute(sa.text("ANALYZE email_download_retry_log"))
            print("✓ 更新表統計資訊")
    except:
        pass  # 統計資訊更新失敗不影響遷移
    
    print(f"[{datetime.now()}] Epic-01 Story 1.3 遷移完成 ✓")


def downgrade():
    """
    Epic-01 Story 1.3: 回滾郵件下載重試記錄表
    
    警告: 此操作將完全移除重試記錄表和相關組件
    所有重試歷史數據將永久遺失，請確保有適當備份
    
    回滾序列:
    1. 刪除索引
    2. 刪除表結構
    3. 刪除枚舉類型 (如適用)
    4. 驗證回滾完整性
    """
    
    conn = op.get_bind()
    database_type = conn.dialect.name
    
    print(f"[{datetime.now()}] 開始 Epic-01 Story 1.3 回滾...")
    
    # 步驟1: 刪除索引
    try:
        indexes_to_drop = [
            'idx_retry_log_error_type',
            'idx_retry_log_composite',
            'idx_retry_log_retry_strategy',
            'idx_retry_log_scheduled_at',
            'idx_retry_log_status',
            'idx_retry_log_email_id',
            'idx_retry_log_download_status_id'
        ]
        
        for index_name in indexes_to_drop:
            try:
                op.drop_index(index_name, table_name='email_download_retry_log')
                print(f"✓ 刪除 {index_name} 索引")
            except Exception:
                print(f"⚠ {index_name} 索引不存在或已刪除")
                
    except Exception as e:
        print(f"✗ 索引刪除過程中的錯誤: {e}")
        # 繼續執行，不阻止表刪除
    
    # 步驟2: 刪除表結構
    try:
        op.drop_table('email_download_retry_log')
        print("✓ 刪除 email_download_retry_log 表")
        
    except Exception as e:
        print(f"✗ 表刪除失敗: {e}")
        raise
    
    # 步驟3: 刪除枚舉類型 (PostgreSQL)
    if database_type == 'postgresql':
        try:
            op.execute("DROP TYPE IF EXISTS retrystatus CASCADE")
            print("✓ 刪除 RetryStatus 枚舉類型")
            
            op.execute("DROP TYPE IF EXISTS retrystrategy CASCADE")
            print("✓ 刪除 RetryStrategy 枚舉類型")
            
        except Exception as e:
            print(f"⚠ 枚舉類型刪除失敗: {e}")
            # 不阻止回滾完成
    
    # 步驟4: 驗證回滾完整性
    try:
        inspector = sa.inspect(conn)
        
        if 'email_download_retry_log' in inspector.get_table_names():
            print("✗ 回滾驗證失敗：表仍然存在")
            raise Exception("表刪除不完整")
        else:
            print("✓ 回滾驗證通過")
            
    except Exception as e:
        print(f"✗ 回滾驗證失敗: {e}")
        
    print(f"[{datetime.now()}] Epic-01 Story 1.3 回滾完成")