#!/usr/bin/env python3
"""
Task 3 Cleanup Script - Backend Architecture Refactor

This script helps complete Task 3 by:
1. Identifying duplicate files that need cleanup
2. Searching for import statements that need updating
3. Providing safe cleanup commands

Usage:
    python scripts/complete_task3_cleanup.py --check    # Check status only
    python scripts/complete_task3_cleanup.py --fix      # Apply fixes (with confirmation)
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
from typing import List, Dict, Tuple

class Task3Cleanup:
    def __init__(self):
        self.project_root = Path.cwd()
        self.duplicate_files = [
            ("src/dashboard_monitoring/", "backend/monitoring/"),
            ("src/services/scheduler.py", "backend/tasks/services/scheduler.py"),
            ("src/services/concurrent_task_manager.py", "backend/tasks/services/concurrent_task_manager.py"),
        ]
        
        self.import_patterns = [
            ("from backend.monitoring", "from backend.monitoring"),
            ("import backend.monitoring", "import backend.monitoring"),
            ("from backend.tasks.services.scheduler", "from backend.tasks.services.scheduler"),
            ("from backend.tasks.services.concurrent_task_manager", "from backend.tasks.services.concurrent_task_manager"),
        ]

    def check_duplicate_files(self) -> List[Tuple[str, str, bool, bool]]:
        """Check which duplicate files exist"""
        results = []
        for src_path, target_path in self.duplicate_files:
            src_exists = (self.project_root / src_path).exists()
            target_exists = (self.project_root / target_path).exists()
            results.append((src_path, target_path, src_exists, target_exists))
        return results

    def search_import_patterns(self) -> Dict[str, List[str]]:
        """Search for old import patterns in the codebase"""
        results = {}
        
        for old_pattern, new_pattern in self.import_patterns:
            try:
                # Use grep to search for the pattern
                cmd = ["grep", "-r", "--include=*.py", old_pattern, "."]
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
                
                if result.returncode == 0:
                    matches = result.stdout.strip().split('\n')
                    results[old_pattern] = [match for match in matches if match.strip()]
                else:
                    results[old_pattern] = []
                    
            except Exception as e:
                print(f"Error searching for pattern '{old_pattern}': {e}")
                results[old_pattern] = []
                
        return results

    def check_status(self):
        """Check current status of Task 3"""
        print("=" * 60)
        print("TASK 3 CLEANUP STATUS CHECK")
        print("=" * 60)
        
        # Check duplicate files
        print("\n1. DUPLICATE FILES ANALYSIS:")
        print("-" * 40)
        duplicates = self.check_duplicate_files()
        
        for src_path, target_path, src_exists, target_exists in duplicates:
            status = "❌ NEEDS CLEANUP" if src_exists and target_exists else "✅ OK"
            print(f"{status}")
            print(f"  Source: {src_path} ({'EXISTS' if src_exists else 'MISSING'})")
            print(f"  Target: {target_path} ({'EXISTS' if target_exists else 'MISSING'})")
            print()
        
        # Check import patterns
        print("2. IMPORT PATTERN ANALYSIS:")
        print("-" * 40)
        import_results = self.search_import_patterns()
        
        total_matches = 0
        for pattern, matches in import_results.items():
            if matches:
                print(f"❌ Found {len(matches)} matches for: {pattern}")
                for match in matches[:3]:  # Show first 3 matches
                    print(f"   {match}")
                if len(matches) > 3:
                    print(f"   ... and {len(matches) - 3} more")
                print()
                total_matches += len(matches)
            else:
                print(f"✅ No matches for: {pattern}")
        
        # Summary
        print("3. SUMMARY:")
        print("-" * 40)
        cleanup_needed = any(src_exists and target_exists for _, _, src_exists, target_exists in duplicates)
        
        if cleanup_needed or total_matches > 0:
            print("❌ Task 3 cleanup REQUIRED")
            print(f"   - Duplicate files: {'YES' if cleanup_needed else 'NO'}")
            print(f"   - Import updates needed: {total_matches} files")
        else:
            print("✅ Task 3 appears to be COMPLETE")
        
        print("\nNext steps:")
        if cleanup_needed:
            print("1. Run with --fix to apply cleanup (with confirmation)")
        if total_matches > 0:
            print("2. Update import statements in affected files")
        print("3. Run tests to verify functionality")
        print("4. Update Task 3 status to completed")

    def apply_fixes(self):
        """Apply fixes with user confirmation"""
        print("=" * 60)
        print("TASK 3 CLEANUP - APPLY FIXES")
        print("=" * 60)
        
        # Check status first
        duplicates = self.check_duplicate_files()
        import_results = self.search_import_patterns()
        
        cleanup_needed = any(src_exists and target_exists for _, _, src_exists, target_exists in duplicates)
        total_imports = sum(len(matches) for matches in import_results.values())
        
        if not cleanup_needed and total_imports == 0:
            print("✅ No cleanup needed. Task 3 appears to be complete!")
            return
        
        print("⚠️  WARNING: This will make changes to your codebase!")
        print("\nChanges to be made:")
        
        if cleanup_needed:
            print("\nFiles to be DELETED:")
            for src_path, target_path, src_exists, target_exists in duplicates:
                if src_exists and target_exists:
                    print(f"  - {src_path}")
        
        if total_imports > 0:
            print(f"\nImport statements to update: {total_imports} files")
        
        # Get user confirmation
        response = input("\nDo you want to proceed? (yes/no): ").lower().strip()
        
        if response != 'yes':
            print("❌ Cleanup cancelled by user")
            return
        
        # Apply file deletions
        if cleanup_needed:
            print("\n🗑️  Removing duplicate files...")
            for src_path, target_path, src_exists, target_exists in duplicates:
                if src_exists and target_exists:
                    try:
                        full_path = self.project_root / src_path
                        if full_path.is_dir():
                            import shutil
                            shutil.rmtree(full_path)
                        else:
                            full_path.unlink()
                        print(f"✅ Removed: {src_path}")
                    except Exception as e:
                        print(f"❌ Failed to remove {src_path}: {e}")
        
        print("\n📝 Import statement updates need to be done manually.")
        print("   Use your IDE's find-and-replace feature for:")
        for old_pattern, new_pattern in self.import_patterns:
            if import_results.get(old_pattern):
                print(f"   '{old_pattern}' → '{new_pattern}'")
        
        print("\n✅ Cleanup completed!")
        print("\nNext steps:")
        print("1. Update import statements manually")
        print("2. Run tests: pytest tests/ -v")
        print("3. Update Task 3 status to completed")

def main():
    parser = argparse.ArgumentParser(description="Task 3 Cleanup Helper")
    parser.add_argument("--check", action="store_true", help="Check status only")
    parser.add_argument("--fix", action="store_true", help="Apply fixes with confirmation")
    
    args = parser.parse_args()
    
    if not args.check and not args.fix:
        parser.print_help()
        return
    
    cleanup = Task3Cleanup()
    
    if args.check:
        cleanup.check_status()
    elif args.fix:
        cleanup.apply_fixes()

if __name__ == "__main__":
    main()