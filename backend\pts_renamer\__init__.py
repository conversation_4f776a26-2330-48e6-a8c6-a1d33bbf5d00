"""
PTS (Parametric Test System) File Renamer Module

This module provides comprehensive PTS file processing capabilities including:
- File renaming with pattern matching and regex support
- QC file generation with specialized processing logic
- Directory structure creation and management
- Integration with existing Dramatiq infrastructure
- MVP architecture for future Vue.js + FastAPI migration

The module follows hexagonal architecture patterns and integrates with
the existing shared infrastructure components.
"""

__version__ = "1.0.0"
__author__ = "Semiconductor Email Processing System"

# Module metadata
MODULE_NAME = "pts_renamer"
MODULE_DESCRIPTION = "PTS File Renamer Integration Service"
WEB_ACCESS_PATH = "/pts-renamer/"