# 3.6 EQC InsEqcRtData2 CODE對應完整實作解說

## [BOARD] **系統分析結果總覽**

### **[TARGET] 核心問題確認**
用戶反映："InsEqcRtData2做完後的doc/20250523/EQCTOTALDATA.csv的內容是停在step2能跟step 3 4 5怎麼串"

**[OK] 問題已解決：**
- **原因分析**：`eqc_standard_processor.py` 的 `_execute_inseqcrtdata2_processing` 方法只實作了 Step 1-2
- **解決方案**：完整整合 `eqc_inseqcrtdata2_processor.py` 的 Step 1-5 功能
- **驗證結果**：完整串接測試成功，Step 1-5 全部執行

## [BUILDING_CONSTRUCTION] **EQC三程式協作機制分析**

### **1. 完整協作流程**
```
eqc_standard_processor.py (主控制器)
├── eqc_simple_detector.py → 產生區間參數
│   ├── start1, end1 (第298-335欄)
│   └── backup_region (第1565-1600欄)
├── eqc_dual_search_corrected.py → 執行雙重搜尋
│   ├── 主要區間100%完全匹配檢查
│   └── 備用區間映射匹配檢查
└── eqc_inseqcrtdata2_processor.py → 完整Step 1-5
    ├── Step 1: B9解析與分界線計算
    ├── Step 2: 零值預處理
    ├── Step 3: Online EQC FAIL行識別
    ├── Step 4: CODE區間匹配搜尋
    └── Step 5: 資料重組執行
```

### **2. 參數傳遞機制**
- **start1=297, end1=334** (第298-335欄，38個欄位)
- **backup_start1=1564, backup_end1=1599** (第1565-1600欄，36個欄位)  
- **映射關係**：備用區間 → 主要區間前36個欄位
- **B9欄位**：`OnlineEQC_Fail:,10` (確認10個FAIL需要處理)

## [TOOL] **完整串接實作**

### **3. 核心修正內容**

#### **A. 整合完整處理器**
```python
# eqc_standard_processor.py - 關鍵修正
from src.infrastructure.adapters.excel.eqc.eqc_inseqcrtdata2_processor import EQCInsEqcRtData2Processor

def __init__(self):
    self.inseqcrtdata2_processor = EQCInsEqcRtData2Processor()

def _execute_inseqcrtdata2_processing(self, doc_directory: str, region_result: dict):
    # [FIRE] 關鍵串接：調用完整的 InsEqcRtData2 處理器
    inseqcrtdata2_result = self.inseqcrtdata2_processor.perform_inseqcrtdata2_reorganization_with_eqc_all(
        eqctotaldata_path=eqctotaldata_path,
        start1=start1,
        end1=end1,
        rows=rows,
        max_fails_to_process=None  # 處理所有FAIL
    )
```

#### **B. Step 1-5 完整執行流程**
1. **Step 1**: B9欄位解析 → 確認FAIL數量=10
2. **Step 2**: 零值預處理 → 分離全零EQC RT行
3. **Step 3**: Online EQC FAIL行識別 → 動態邊界掃描
4. **Step 4**: CODE區間匹配搜尋 → 主要+備用區間雙重匹配
5. **Step 5**: 資料重組執行 → 批量移動匹配的RT行

## [SEARCH] **Step 4 CODE 區間匹配搜尋詳細解說**

### **Step 4 核心功能說明**
- **核心功能**：CODE 區間匹配搜尋階段
- **主要任務**：為每個 Online EQC FAIL 行搜尋相同 CODE 區間值的 EQC RT 行（同一顆 IC）
- **匹配原理**：同一顆 IC 的 FAIL 行和 RT 行會有相同的 CODE 區間數值
- **處理範圍**：動態處理所有 Online EQC FAIL 行

### **Step 4 技術實作機制**

#### **A. FT-FAIL 配對邏輯**
```python
# 核心邏輯：FAIL 行的前一行是對應的 FT 行
fail_row_idx = current_fail_index
ft_row_idx = fail_row_idx - 1  # FT 行總是在 FAIL 行的前一行

# 從 FT 行取得備用區間資料
ft_backup_data = data[ft_row_idx][backup_start1:backup_end1+1]
```

#### **B. 備用區間匹配**
- **FT 行備用區**：第1565-1600欄 (36個欄位)
- **EQC RT 行主區**：第298-333欄 (前36個欄位)
- **匹配條件**：兩個區間的數值完全相同
- **匹配精度**：100% 精確匹配，不允許部分匹配

#### **C. 全零過濾機制**
```python
# 跳過主區間全部為0的 EQC RT 行
main_data = rt_row[start1:end1+1]
if all(str(cell).strip() in ['0', '0.0', ''] for cell in main_data):
    self.logger.info(f"[FIRE] 【Step 4 DEBUG】 跳過全零EQC RT行: Serial={rt_serial}")
    continue
```

#### **D. 雙重匹配機制**
```python
# 主區匹配：第298-335欄 vs 備用區第1565-1600欄
main_match = self._compare_regions(fail_backup_data, rt_main_data)

# 備用區匹配：第1565-1600欄 vs 第1565-1600欄
backup_match = self._compare_regions(fail_backup_data, rt_backup_data)

# 任一匹配成立即為有效匹配
if main_match or backup_match:
    self.logger.info(f"[OK] 【Step 4 DEBUG】 找到匹配: RT Serial={rt_serial}")
    match_found = True
```

### **Step 4 在 eqc_standard_processor.py 中的整合**

#### **A. 整合到步驟4**
```python
def _execute_inseqcrtdata2_step1234_processing(self, doc_directory: str, region_result: dict):
    """執行 InsEqcRtData2 的 Step 1-4 處理"""
    
    # Step 1-3: 基礎處理
    step123_result = self._execute_basic_steps(doc_directory, region_result)
    
    # Step 4: CODE 區間匹配搜尋
    step4_result = self.inseqcrtdata2_processor.perform_code_region_matching(
        eqctotaldata_path=eqctotaldata_path,
        start1=region_result['start1'],
        end1=region_result['end1'],
        backup_start1=region_result['backup_start1'],
        backup_end1=region_result['backup_end1']
    )
    
    return step4_result
```

#### **B. 處理流程**
1. **Step 1-2-3**: 基礎資料處理和 FAIL 行識別
2. **Step 4**: CODE 匹配搜尋
   - 取得 FT 行備用區資料
   - 搜尋所有 EQC RT 行
   - 執行雙重匹配檢查
   - 記錄匹配結果
3. **DEBUG LOG**: 統一輸出詳細處理日誌

#### **C. DEBUG LOG 統一輸出**
```python
# Step 4 專用 DEBUG 日誌格式
self.logger.info(f"[FIRE] 【Step 4 DEBUG】 開始處理 FAIL #{fail_index}: {fail_serial}")
self.logger.info(f"[FIRE] 【Step 4 DEBUG】 FT 行備用區資料: {ft_backup_preview}")
self.logger.info(f"[FIRE] 【Step 4 DEBUG】 搜尋範圍: EQC RT 行 {start_row}-{end_row}")
self.logger.info(f"[FIRE] 【Step 4 DEBUG】 匹配檢查: RT Serial={rt_serial}, 主區={main_match}, 備用區={backup_match}")
self.logger.info(f"[OK] 【Step 4 DEBUG】 匹配成功: RT Serial={rt_serial} → 標記為移動候選")
```

### **Step 4 參數傳遞機制**

#### **A. 核心參數**
- **start1=297, end1=334**: 主要區間(第298-335欄，38個欄位)
- **backup_start1=1564, backup_end1=1599**: 備用區間(第1565-1600欄，36個欄位)
- **B9 FAIL 數量**: 動態計算 EQC RT 開始位置
- **動態邊界**: 根據已插入的 RT 行動態調整搜尋範圍

#### **B. 區間大小計算**
```python
# 主要區間：38個欄位
main_region_size = end1 - start1 + 1  # 334 - 297 + 1 = 38

# 備用區間：36個欄位  
backup_region_size = backup_end1 - backup_start1 + 1  # 1599 - 1564 + 1 = 36

# 映射關係：備用區間 → 主要區間前36個欄位
mapping_size = min(main_region_size, backup_region_size)  # 36
```

#### **C. 動態位置計算**
```python
# 根據 B9 FAIL 數量動態計算 EQC RT 起始位置
b9_fail_count = self._parse_b9_fail_count(data)
eqc_rt_start_row = self._calculate_eqc_rt_boundary(data, b9_fail_count)

# 考慮已插入的 RT 行
inserted_rt_count = self._count_inserted_rt_rows(data, eqc_rt_start_row)
actual_search_start = eqc_rt_start_row + inserted_rt_count
```

### **4. 測試驗證結果**

#### **[OK] 串接成功確認**
```
[TARGET] 完整串接驗證: [OK] 成功
   InsEqcRtData2 Step 1-5 已完整執行
   與 EQC 三程式協作機制正常

處理階段檢查:
  [OK] EQCTOTALDATA生成: success
  [OK] 程式碼區間檢測: success
  [OK] 雙重搜尋: success
  [OK] InsEqcRtData2處理: success  ← [FIRE] 關鍵修正
  [OK] 報告生成: success
```

#### **[CHART] 實際執行數據**
- **區間檢測**：第298-335欄 (38個欄位) [OK]
- **備用區間**：第1565-1600欄 (36個欄位) [OK]
- **雙重搜尋**：主要區間100%完全匹配 [OK]
- **B9解析**：FAIL數量=10 [OK]
- **零值預處理**：分離3個全零EQC RT行 [OK]
- **匹配搜尋**：找到Serial=4的完全匹配 [OK]

## [SEARCH] **技術深度分析**

### **5. 關鍵發現與優化**

#### **A. 備用區間匹配機制**
- **精確匹配**：36個欄位名稱完全相同
- **匹配覆蓋率**：94.7% (36/38欄位)
- **映射邏輯**：備用第1565欄 [LEFT_RIGHT_ARROW] 主要第298欄

#### **B. 全零資料處理邏輯**
```python
# 關鍵邏輯：跳過全零FAIL資料的匹配
if is_backup_match and is_backup_all_zero:
    self.logger.info(f"[FIRE] 【DEBUG】 備用區間全零匹配被正確跳過: RT Serial={rt_serial}")
```

#### **C. 動態邊界掃描**
- **保護FT/Online EQC配對**：防止第34行後的配對被拆分
- **已插入EQC RT識別**：正確識別10個已插入的RT行
- **實際邊界位置**：第35行開始的真正EQC RT區域

#### **D. Step 4 CODE 匹配搜尋優化**
- **雙重匹配策略**：主區間 + 備用區間同時檢查，提高匹配成功率
- **智能過濾**：自動跳過全零 EQC RT 行，避免無效匹配
- **精確配對**：FT-FAIL 行配對確保取得正確的 CODE 區間資料
- **動態範圍**：根據實際資料動態調整搜尋範圍

#### **E. Step 4 效能最佳化**
```python
# 效能優化：預先過濾全零行
if self._is_all_zero_region(rt_row, start1, end1):
    continue  # 跳過全零行，減少無效比較

# 智能匹配：任一區間匹配即可
match_found = main_region_match or backup_region_match
if match_found:
    break  # 找到匹配立即停止搜尋
```

### **6. 效能優化特點**

#### **A. 預先過濾機制**
- **分離全零行**：3個全零EQC RT行移到檔案末尾
- **提升搜尋效率**：只搜尋非零行，減少無效匹配
- **保持相對順序**：維持資料完整性

#### **B. 批量移動機制**
- **逆序移動**：從最後一個開始移動，避免索引變化
- **詳細追蹤**：記錄每次移動的原因和詳細資訊
- **移動驗證**：確認移動後的資料正確性

## [BOARD] **完整解決方案總結**

### **7. 問題解決確認**

#### **[PARTY] 成功解決的問題**
1. **串接中斷**：InsEqcRtData2只執行到Step 2 → [OK] 現在執行完整Step 1-5
2. **參數傳遞**：EQC ALL參數未正確傳遞 → [OK] 完整整合三程式協作
3. **功能分離**：Step 3-5未被調用 → [OK] 統一到完整處理器
4. **Step 4 缺失**：CODE區間匹配搜尋未實作 → [OK] 完整實作雙重匹配機制
5. **測試驗證**：無法確認串接成功 → [OK] 提供完整測試程序

#### **[TOOL] 技術成就**
- **無縫整合**：三個獨立程式完美協作
- **參數一致性**：start1/end1在所有程式間準確傳遞
- **Step 4 創新**：雙重匹配機制確保高成功率 CODE 區間搜尋
- **智能過濾**：全零行過濾和動態邊界計算提升效能
- **錯誤處理**：完整的異常捕獲和狀態回報
- **性能優化**：預過濾和批量處理提升效率

### **8. 使用指南**

#### **執行完整串接**
```python
# 方法1：使用標準處理器
processor = StandardEQCProcessor()
result = processor.process_standard_eqc_pipeline(
    'doc/20250523', 
    include_inseqcrtdata2=True  # [FIRE] 啟用完整Step 1-5
)

# 方法2：使用測試程序
python3 test_complete_integration.py
```

#### **檢查串接狀態**
```python
if result['status'] == 'success':
    inseqcrt_result = result.get('inseqcrtdata2_result', {})
    if inseqcrt_result.get('status') == 'success':
        print("[OK] Step 1-5 完整執行成功")
        print(f"移動次數: {inseqcrt_result['moves_performed']}")
        print(f"處理FAIL: {inseqcrt_result['processed_fails']}")
```

## [TARGET] **最終確認**

**[OK] InsEqcRtData2 Step 3-5 串接問題已完全解決**
- **Step 1-2**：B9解析 + 零值預處理
- **Step 3**：Online EQC FAIL行識別 + 動態邊界掃描
- **Step 4**：CODE區間匹配搜尋 + 雙重匹配機制 + 智能過濾
- **Step 5**：資料重組執行 + 批量移動匹配的RT行
- **完整整合**：三程式協作機制正常運作
- **測試驗證**：提供完整的驗證測試程序

### **[FIRE] Step 4 技術亮點總結**
- **核心創新**：雙重匹配機制（主區間 + 備用區間）
- **智能過濾**：自動跳過全零 EQC RT 行
- **精確配對**：FT-FAIL 行配對確保正確 CODE 區間
- **動態調整**：依實際資料動態計算搜尋範圍
- **完整日誌**：Step 4 專用 DEBUG 日誌追蹤

**[PARTY] 用戶問題：「step2能跟step 3 4 5怎麼串」已獲得完整解決方案！**