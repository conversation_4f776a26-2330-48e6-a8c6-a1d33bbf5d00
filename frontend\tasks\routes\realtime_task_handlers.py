"""
即時任務處理器
整合 WebSocket 推送與異步任務管理系統
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger

# 導入任務管理器
try:
    from backend.tasks.services.concurrent_task_manager_enhanced import get_enhanced_task_manager, TaskPriority
except ImportError:
    # 當直接執行時使用相對導入
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root / "src"))
    from services.concurrent_task_manager_enhanced import get_enhanced_task_manager, TaskPriority

try:
    from .websocket_endpoints import connection_manager
except ImportError:
    # 當直接執行時，先設為 None
    connection_manager = None

class RealtimeTaskHandlers:
    """即時任務處理器集合"""
    
    def __init__(self):
        self.task_manager = get_enhanced_task_manager()
        self.active_processes = {}
        
    async def email_processing_handler(self, **kwargs):
        """郵件處理任務處理器"""
        process_id = f"email_process_{int(time.time())}"
        
        try:
            # 模擬郵件處理步驟
            steps = [
                "初始化郵件連接",
                "掃描收件匣",
                "過濾郵件",
                "提取內容",
                "生成摘要",
                "保存結果"
            ]
            
            total_steps = len(steps)
            
            for i, step_name in enumerate(steps):
                current_step = i + 1
                progress = (current_step / total_steps) * 100
                
                # 推送進度更新
                await connection_manager.broadcast_message({
                    'type': 'process_progress_update',
                    'payload': {
                        'process_id': process_id,
                        'step': current_step,
                        'total_steps': total_steps,
                        'progress': progress,
                        'message': f"正在執行: {step_name}",
                        'process_type': 'email_processing'
                    }
                }, 'process_progress')
                
                # 模擬處理時間
                await asyncio.sleep(2)
            
            # 完成處理
            result = {
                'processed_emails': 25,
                'summaries_generated': 15,
                'processing_time': total_steps * 2,
                'success': True
            }
            
            # 推送完成狀態
            await connection_manager.broadcast_message({
                'type': 'process_progress_update',
                'payload': {
                    'process_id': process_id,
                    'step': total_steps,
                    'total_steps': total_steps,
                    'progress': 100,
                    'message': "郵件處理完成",
                    'process_type': 'email_processing',
                    'result': result
                }
            }, 'process_progress')
            
            return result
            
        except Exception as e:
            logger.error(f"郵件處理失敗: {e}")
            
            # 推送錯誤狀態
            await connection_manager.broadcast_message({
                'type': 'process_progress_update',
                'payload': {
                    'process_id': process_id,
                    'progress': 0,
                    'message': f"處理失敗: {str(e)}",
                    'process_type': 'email_processing',
                    'error': str(e)
                }
            }, 'process_progress')
            
            raise e
    
    async def file_processing_handler(self, **kwargs):
        """檔案處理任務處理器"""
        process_id = f"file_process_{int(time.time())}"
        
        try:
            # 模擬檔案處理步驟
            steps = [
                "掃描檔案系統", 
                "驗證檔案格式",
                "讀取檔案內容",
                "數據轉換",
                "生成輸出"
            ]
            
            total_steps = len(steps)
            
            for i, step_name in enumerate(steps):
                current_step = i + 1
                progress = (current_step / total_steps) * 100
                
                # 推送進度更新
                await connection_manager.broadcast_message({
                    'type': 'process_progress_update',
                    'payload': {
                        'process_id': process_id,
                        'step': current_step,
                        'total_steps': total_steps,
                        'progress': progress,
                        'message': f"正在執行: {step_name}",
                        'process_type': 'file_processing'
                    }
                }, 'process_progress')
                
                # 模擬處理時間 (檔案處理相對較快)
                await asyncio.sleep(1.5)
            
            # 完成處理
            result = {
                'processed_files': 42,
                'total_size_mb': 156.7,
                'processing_time': total_steps * 1.5,
                'success': True
            }
            
            # 推送完成狀態
            await connection_manager.broadcast_message({
                'type': 'process_progress_update',
                'payload': {
                    'process_id': process_id,
                    'step': total_steps,
                    'total_steps': total_steps,
                    'progress': 100,
                    'message': "檔案處理完成",
                    'process_type': 'file_processing',
                    'result': result
                }
            }, 'process_progress')
            
            return result
            
        except Exception as e:
            logger.error(f"檔案處理失敗: {e}")
            
            # 推送錯誤狀態
            await connection_manager.broadcast_message({
                'type': 'process_progress_update',
                'payload': {
                    'process_id': process_id,
                    'progress': 0,
                    'message': f"處理失敗: {str(e)}",
                    'process_type': 'file_processing',
                    'error': str(e)
                }
            }, 'process_progress')
            
            raise e
    
    async def summary_generation_handler(self, **kwargs):
        """摘要生成任務處理器"""
        process_id = f"summary_process_{int(time.time())}"
        
        try:
            # 模擬摘要生成步驟
            steps = [
                "分析文本內容",
                "提取關鍵信息", 
                "生成摘要草稿",
                "優化摘要內容",
                "格式化輸出"
            ]
            
            total_steps = len(steps)
            
            for i, step_name in enumerate(steps):
                current_step = i + 1
                progress = (current_step / total_steps) * 100
                
                # 推送進度更新
                await connection_manager.broadcast_message({
                    'type': 'process_progress_update',
                    'payload': {
                        'process_id': process_id,
                        'step': current_step,
                        'total_steps': total_steps,
                        'progress': progress,
                        'message': f"正在執行: {step_name}",
                        'process_type': 'summary_generation'
                    }
                }, 'process_progress')
                
                # 模擬處理時間
                await asyncio.sleep(1.8)
            
            # 完成處理
            result = {
                'summaries_created': 8,
                'total_words': 1247,
                'processing_time': total_steps * 1.8,
                'success': True
            }
            
            # 推送完成狀態
            await connection_manager.broadcast_message({
                'type': 'process_progress_update',
                'payload': {
                    'process_id': process_id,
                    'step': total_steps,
                    'total_steps': total_steps,
                    'progress': 100,
                    'message': "摘要生成完成",
                    'process_type': 'summary_generation',
                    'result': result
                }
            }, 'process_progress')
            
            return result
            
        except Exception as e:
            logger.error(f"摘要生成失敗: {e}")
            
            # 推送錯誤狀態
            await connection_manager.broadcast_message({
                'type': 'process_progress_update',
                'payload': {
                    'process_id': process_id,
                    'progress': 0,
                    'message': f"處理失敗: {str(e)}",
                    'process_type': 'summary_generation',
                    'error': str(e)
                }
            }, 'process_progress')
            
            raise e
    
    async def system_cleanup_handler(self, **kwargs):
        """系統清理任務處理器"""
        process_id = f"cleanup_process_{int(time.time())}"
        
        try:
            # 模擬系統清理步驟
            steps = [
                "掃描臨時檔案",
                "清理快取目錄",
                "整理日誌檔案", 
                "優化數據庫",
                "完成清理"
            ]
            
            total_steps = len(steps)
            
            for i, step_name in enumerate(steps):
                current_step = i + 1
                progress = (current_step / total_steps) * 100
                
                # 推送進度更新
                await connection_manager.broadcast_message({
                    'type': 'process_progress_update',
                    'payload': {
                        'process_id': process_id,
                        'step': current_step,
                        'total_steps': total_steps,
                        'progress': progress,
                        'message': f"正在執行: {step_name}",
                        'process_type': 'system_cleanup'
                    }
                }, 'process_progress')
                
                # 模擬處理時間
                await asyncio.sleep(2.5)
            
            # 完成處理
            result = {
                'freed_space_mb': 892.3,
                'cleaned_files': 1547,
                'processing_time': total_steps * 2.5,
                'success': True
            }
            
            # 推送完成狀態
            await connection_manager.broadcast_message({
                'type': 'process_progress_update',
                'payload': {
                    'process_id': process_id,
                    'step': total_steps,
                    'total_steps': total_steps,
                    'progress': 100,
                    'message': "系統清理完成",
                    'process_type': 'system_cleanup',
                    'result': result
                }
            }, 'process_progress')
            
            return result
            
        except Exception as e:
            logger.error(f"系統清理失敗: {e}")
            
            # 推送錯誤狀態
            await connection_manager.broadcast_message({
                'type': 'process_progress_update',
                'payload': {
                    'process_id': process_id,
                    'progress': 0,
                    'message': f"處理失敗: {str(e)}",
                    'process_type': 'system_cleanup',
                    'error': str(e)
                }
            }, 'process_progress')
            
            raise e
    
    async def sample_process_handler(self, **kwargs):
        """示例進程處理器（用於測試）"""
        steps = kwargs.get('steps', 5)
        delay = kwargs.get('delay', 1000) / 1000  # 轉換為秒
        process_id = f"sample_process_{int(time.time())}"
        
        try:
            for i in range(1, steps + 1):
                progress = (i / steps) * 100
                
                # 推送進度更新
                await connection_manager.broadcast_message({
                    'type': 'process_progress_update',
                    'payload': {
                        'process_id': process_id,
                        'step': i,
                        'total_steps': steps,
                        'progress': progress,
                        'message': f"執行步驟 {i}/{steps}",
                        'process_type': 'sample_process'
                    }
                }, 'process_progress')
                
                # 等待指定延遲
                await asyncio.sleep(delay)
            
            # 完成處理
            result = {
                'completed_steps': steps,
                'total_time': steps * delay,
                'success': True
            }
            
            # 推送完成狀態
            await connection_manager.broadcast_message({
                'type': 'process_progress_update',
                'payload': {
                    'process_id': process_id,
                    'step': steps,
                    'total_steps': steps,
                    'progress': 100,
                    'message': "示例進程完成",
                    'process_type': 'sample_process',
                    'result': result
                }
            }, 'process_progress')
            
            return result
            
        except Exception as e:
            logger.error(f"示例進程失敗: {e}")
            
            # 推送錯誤狀態
            await connection_manager.broadcast_message({
                'type': 'process_progress_update',
                'payload': {
                    'process_id': process_id,
                    'progress': 0,
                    'message': f"處理失敗: {str(e)}",
                    'process_type': 'sample_process',
                    'error': str(e)
                }
            }, 'process_progress')
            
            raise e

# 創建全局實例
realtime_handlers = RealtimeTaskHandlers()

def register_realtime_handlers():
    """註冊即時任務處理器到任務管理器"""
    if connection_manager is None:
        logger.warning("連線管理器未初始化，跳過註冊")
        return
    
    task_manager = get_enhanced_task_manager()
    
    # 註冊處理器
    task_manager.register_handler('email_processing', realtime_handlers.email_processing_handler)
    task_manager.register_handler('file_processing', realtime_handlers.file_processing_handler)
    task_manager.register_handler('summary_generation', realtime_handlers.summary_generation_handler)
    task_manager.register_handler('system_cleanup', realtime_handlers.system_cleanup_handler)
    task_manager.register_handler('sample_process', realtime_handlers.sample_process_handler)
    
    logger.info("即時任務處理器已註冊")

# 任務狀態回調包裝器
def create_task_status_callback():
    """創建任務狀態變更回調"""
    async def task_status_callback(task_id: str, status_data: Dict[str, Any]):
        """任務狀態變更回調"""
        await connection_manager.broadcast_message({
            'type': 'task_status_update',
            'payload': {
                'task_id': task_id,
                'timestamp': datetime.now().isoformat(),
                **status_data
            }
        }, 'task_status')
    
    return task_status_callback

# 自動註冊處理器（在模塊載入時）
def _auto_register():
    """自動註冊處理器"""
    try:
        if connection_manager is not None:
            register_realtime_handlers()
    except Exception as e:
        logger.warning(f"自動註冊即時任務處理器失敗: {e}")

# 在模塊載入時自動註冊
# _auto_register()  # 暂時停用自動註冊，由應用啟動時手動註冊