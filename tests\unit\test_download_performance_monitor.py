"""
DownloadPerformanceMonitor TDD 測試文件
Epic-02 Story 2.4: 下載性能監控和分析

測試完整的性能監控功能
"""

import pytest
import time
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from backend.shared.services.download_performance_monitor import (
    DownloadPerformanceMonitor, PerformanceMetrics
)


class TestDownloadPerformanceMonitor:
    """
    DownloadPerformanceMonitor 完整測試類
    """
    
    @pytest.fixture
    def monitor(self):
        """創建性能監控器實例"""
        return DownloadPerformanceMonitor(max_history_size=100)
    
    def test_monitor_initialization(self, monitor):
        """測試監控器初始化"""
        assert monitor.global_stats['total_downloads'] == 0
        assert monitor.global_stats['current_concurrent_downloads'] == 0
        assert len(monitor.performance_history) == 0
        assert len(monitor.metrics) == 0
    
    def test_track_download_start(self, monitor):
        """測試追蹤下載開始"""
        email_id = 1
        monitor.track_download_start(email_id, expected_size=1024)
        
        assert email_id in monitor.metrics
        assert monitor.metrics[email_id].download_start_time is not None
        assert monitor.global_stats['current_concurrent_downloads'] == 1
        assert monitor.global_stats['peak_concurrent_downloads'] == 1
    
    def test_track_download_progress(self, monitor):
        """測試追蹤下載進度"""
        email_id = 1
        monitor.track_download_start(email_id)
        
        # 模擬進度更新
        monitor.track_download_progress(email_id, 512, 1024.0)
        monitor.track_download_progress(email_id, 1024, 2048.0)
        
        metrics = monitor.metrics[email_id]
        assert len(metrics.progress_updates) == 2
        assert metrics.peak_speed_bps == 2048.0
    
    def test_track_download_completion_success(self, monitor):
        """測試追蹤下載成功完成"""
        email_id = 1
        monitor.track_download_start(email_id)
        
        # 模擬一些進度
        monitor.track_download_progress(email_id, 1024, 1024.0)
        
        # 完成下載
        monitor.track_download_completion(email_id, 1024, success=True)
        
        # 檢查統計
        assert monitor.global_stats['total_downloads'] == 1
        assert monitor.global_stats['successful_downloads'] == 1
        assert monitor.global_stats['failed_downloads'] == 0
        assert monitor.global_stats['current_concurrent_downloads'] == 0
        assert email_id not in monitor.metrics  # 已清理
        assert len(monitor.performance_history) == 1
    
    def test_track_download_completion_failure(self, monitor):
        """測試追蹤下載失敗"""
        email_id = 1
        monitor.track_download_start(email_id)
        monitor.track_download_completion(email_id, 0, success=False)
        
        assert monitor.global_stats['total_downloads'] == 1
        assert monitor.global_stats['successful_downloads'] == 0
        assert monitor.global_stats['failed_downloads'] == 1
    
    def test_track_download_error(self, monitor):
        """測試追蹤下載錯誤"""
        email_id = 1
        monitor.track_download_start(email_id)
        monitor.track_download_error(email_id, "Network timeout")
        
        metrics = monitor.metrics[email_id]
        assert metrics.error_count == 1
    
    def test_track_download_retry(self, monitor):
        """測試追蹤下載重試"""
        email_id = 1
        monitor.track_download_start(email_id)
        monitor.track_download_retry(email_id)
        monitor.track_download_retry(email_id)
        
        metrics = monitor.metrics[email_id]
        assert metrics.retry_count == 2
    
    def test_get_performance_metrics(self, monitor):
        """測試獲取性能指標"""
        # 模擬一些下載
        monitor.track_download_start(1)
        monitor.track_download_completion(1, 1024, success=True)
        
        monitor.track_download_start(2)
        monitor.track_download_completion(2, 0, success=False)
        
        metrics = monitor.get_performance_metrics("daily")
        
        assert metrics['total_downloads'] == 2
        assert metrics['successful_downloads'] == 1
        assert metrics['failed_downloads'] == 1
        assert metrics['success_rate'] == 50.0
    
    def test_get_current_downloads(self, monitor):
        """測試獲取當前下載"""
        email_id1 = 1
        email_id2 = 2
        
        monitor.track_download_start(email_id1)
        monitor.track_download_start(email_id2)
        monitor.track_download_progress(email_id1, 512, 1024.0)
        
        current_downloads = monitor.get_current_downloads()
        assert len(current_downloads) == 2
        
        # 找到email_id1的記錄
        email1_record = next(d for d in current_downloads if d['email_id'] == email_id1)
        assert email1_record['current_speed'] == 1024.0
    
    def test_get_detailed_analytics(self, monitor):
        """測試獲取詳細分析"""
        # 模擬多個下載，使用patch來控制時間
        import time
        
        for i in range(5):
            # 模擬下載開始
            start_time = time.time() - 10 + i  # 使用過去的時間
            
            with patch('time.time', return_value=start_time):
                monitor.track_download_start(i)
            
            # 模擬進度更新
            monitor.track_download_progress(i, 1024, 1024.0 + i * 100)
            
            # 模擬下載完成（1-1.4秒後）
            end_time = start_time + 1.0 + i * 0.1
            with patch('time.time', return_value=end_time):
                monitor.track_download_completion(i, 1024, success=True)
        
        analytics = monitor.get_detailed_analytics(last_hours=1)
        
        assert analytics['sample_count'] == 5
        assert 'download_time_stats' in analytics
        assert 'download_speed_stats' in analytics
        assert 'file_size_stats' in analytics
    
    def test_concurrent_downloads(self, monitor):
        """測試併發下載追蹤"""
        # 模擬10個併發下載
        for i in range(10):
            monitor.track_download_start(i)
        
        assert monitor.global_stats['current_concurrent_downloads'] == 10
        assert monitor.global_stats['peak_concurrent_downloads'] == 10
        
        # 完成其中5個
        for i in range(5):
            monitor.track_download_completion(i, 1024, success=True)
        
        assert monitor.global_stats['current_concurrent_downloads'] == 5
        assert monitor.global_stats['peak_concurrent_downloads'] == 10  # 保持峰值
    
    def test_reset_statistics(self, monitor):
        """測試重置統計"""
        # 添加一些數據
        monitor.track_download_start(1)
        monitor.track_download_completion(1, 1024, success=True)
        
        assert monitor.global_stats['total_downloads'] == 1
        assert len(monitor.performance_history) == 1
        
        # 重置
        monitor.reset_statistics()
        
        assert monitor.global_stats['total_downloads'] == 0
        assert len(monitor.performance_history) == 0
    
    def test_get_monitor_health(self, monitor):
        """測試獲取監控器健康狀態"""
        health = monitor.get_monitor_health()
        
        assert health['status'] == 'healthy'
        assert 'active_downloads' in health
        assert 'history_records' in health
        assert 'performance_thresholds' in health


class TestPerformanceMetrics:
    """
    PerformanceMetrics 測試類
    """
    
    def test_performance_metrics_initialization(self):
        """測試性能指標初始化"""
        metrics = PerformanceMetrics()
        
        assert metrics.download_start_time is None
        assert metrics.download_end_time is None
        assert metrics.total_bytes == 0
        assert len(metrics.progress_updates) == 0
        assert metrics.error_count == 0
        assert metrics.retry_count == 0
        assert metrics.peak_speed_bps == 0.0
    
    def test_add_progress_update(self):
        """測試添加進度更新"""
        metrics = PerformanceMetrics()
        
        metrics.add_progress_update(time.time(), 1024, 2048.0)
        metrics.add_progress_update(time.time(), 2048, 1024.0)
        
        assert len(metrics.progress_updates) == 2
        assert metrics.peak_speed_bps == 2048.0
    
    def test_get_duration(self):
        """測試獲取持續時間"""
        metrics = PerformanceMetrics()
        
        # 沒有設置時間時應該返回None
        assert metrics.get_duration() is None
        
        # 設置開始和結束時間
        start_time = time.time()
        end_time = start_time + 10.0
        
        metrics.download_start_time = start_time
        metrics.download_end_time = end_time
        
        duration = metrics.get_duration()
        assert duration == 10.0
    
    def test_get_average_speed(self):
        """測試獲取平均速度"""
        metrics = PerformanceMetrics()
        
        # 沒有時間數據時應該返回0
        assert metrics.get_average_speed() == 0.0
        
        # 設置數據
        start_time = time.time()
        end_time = start_time + 10.0
        
        metrics.download_start_time = start_time
        metrics.download_end_time = end_time
        metrics.total_bytes = 1024
        
        average_speed = metrics.get_average_speed()
        assert average_speed == 102.4  # 1024 bytes / 10 seconds


class TestDownloadPerformanceMonitorIntegration:
    """
    DownloadPerformanceMonitor 整合測試
    """
    
    def test_complete_download_lifecycle(self):
        """測試完整的下載生命週期"""
        monitor = DownloadPerformanceMonitor()
        email_id = 1
        
        # 1. 開始下載
        monitor.track_download_start(email_id, expected_size=1024)
        assert email_id in monitor.metrics
        assert monitor.global_stats['current_concurrent_downloads'] == 1
        
        # 2. 進度更新
        monitor.track_download_progress(email_id, 256, 512.0)
        monitor.track_download_progress(email_id, 512, 1024.0)
        monitor.track_download_progress(email_id, 1024, 2048.0)
        
        metrics = monitor.metrics[email_id]
        assert len(metrics.progress_updates) == 3
        assert metrics.peak_speed_bps == 2048.0
        
        # 3. 重試（模擬）
        monitor.track_download_retry(email_id)
        assert metrics.retry_count == 1
        
        # 4. 完成下載
        monitor.track_download_completion(email_id, 1024, success=True)
        
        # 驗證最終狀態
        assert email_id not in monitor.metrics  # 已清理
        assert monitor.global_stats['total_downloads'] == 1
        assert monitor.global_stats['successful_downloads'] == 1
        assert monitor.global_stats['current_concurrent_downloads'] == 0
        assert len(monitor.performance_history) == 1
        
        # 檢查歷史記錄
        history_record = monitor.performance_history[0]
        assert history_record['email_id'] == email_id
        assert history_record['success'] is True
        assert history_record['total_bytes'] == 1024
        assert history_record['retry_count'] == 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])