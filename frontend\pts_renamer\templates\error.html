{% extends "base.html" %}

{% block title %}錯誤 - PTS 檔案重命名工具 - {{ super() }}{% endblock %}

{% block body_class %}pts-renamer-error-page{% endblock %}

{% block page_title %}系統錯誤{% endblock %}

{% block page_css %}
<style>
    /* PTS Renamer 錯誤頁面樣式 */
    .pts-renamer-error-page {
        background: var(--bg-light);
    }

    .error-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) var(--spacing-md);
    }

    .error-card {
        background: white;
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-lg);
        overflow: hidden;
    }

    .error-header {
        background: linear-gradient(135deg, var(--error-color) 0%, #c82333 100%);
        color: white;
        padding: var(--spacing-lg);
        text-align: center;
    }

    .error-icon {
        font-size: 64px;
        margin-bottom: var(--spacing-md);
        opacity: 0.9;
    }

    .error-title {
        font-size: var(--font-size-lg);
        font-weight: 700;
        margin-bottom: var(--spacing-sm);
    }

    .error-subtitle {
        font-size: var(--font-size-sm);
        opacity: 0.9;
        margin: 0;
    }

    .error-content {
        padding: var(--spacing-xl);
    }

    .error-message {
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .error-message-title {
        font-weight: 600;
        color: var(--error-color);
        margin-bottom: var(--spacing-xs);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .error-message-text {
        color: #742a2a;
        font-size: var(--font-size-sm);
        line-height: 1.6;
        margin: 0;
    }

    .error-details {
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .error-details-title {
        font-weight: 600;
        color: var(--secondary-color);
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        cursor: pointer;
        user-select: none;
    }

    .error-details-content {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: var(--radius-sm);
        padding: var(--spacing-sm);
        font-family: 'Courier New', monospace;
        font-size: var(--font-size-xs);
        color: #4a5568;
        overflow-x: auto;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 200px;
        overflow-y: auto;
    }

    .error-actions {
        display: flex;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
        justify-content: center;
        margin-top: var(--spacing-lg);
    }

    .error-btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        min-width: 140px;
        justify-content: center;
    }

    .error-btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .error-btn-primary:hover {
        background: #5a6fd8;
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    .error-btn-secondary {
        background: var(--text-muted);
        color: white;
    }

    .error-btn-secondary:hover {
        background: #545b62;
        transform: translateY(-1px);
    }

    .error-btn-outline {
        background: transparent;
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
    }

    .error-btn-outline:hover {
        background: var(--primary-color);
        color: white;
    }

    .error-suggestions {
        background: #f0fff4;
        border: 1px solid #9ae6b4;
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .error-suggestions-title {
        font-weight: 600;
        color: var(--success-color);
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .error-suggestions-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .error-suggestions-list li {
        padding: var(--spacing-xs) 0;
        color: #22543d;
        font-size: var(--font-size-sm);
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .error-suggestions-list li::before {
        content: "💡";
        font-size: 14px;
        margin-top: 2px;
    }

    .error-code {
        background: #2d3748;
        color: #e2e8f0;
        padding: 2px 8px;
        border-radius: var(--radius-xs);
        font-family: 'Courier New', monospace;
        font-size: var(--font-size-xs);
        font-weight: 500;
    }

    .error-timestamp {
        text-align: center;
        color: var(--text-muted);
        font-size: var(--font-size-xs);
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-color);
    }

    /* 響應式設計 */
    @media (max-width: 768px) {
        .error-container {
            padding: var(--spacing-md) var(--spacing-sm);
        }

        .error-header {
            padding: var(--spacing-md);
        }

        .error-icon {
            font-size: 48px;
        }

        .error-title {
            font-size: var(--font-size-md);
        }

        .error-content {
            padding: var(--spacing-md);
        }

        .error-actions {
            flex-direction: column;
            align-items: stretch;
        }

        .error-btn {
            min-width: auto;
        }
    }

    /* 動畫效果 */
    .error-card {
        animation: fadeInUp 0.5s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .collapsible-content {
        transition: max-height 0.3s ease-out;
        overflow: hidden;
    }

    .collapsible-content.collapsed {
        max-height: 0;
    }

    .collapsible-content.expanded {
        max-height: 1000px;
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-card">
        <!-- 錯誤標題區域 -->
        <div class="error-header">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h1 class="error-title">系統發生錯誤</h1>
            <p class="error-subtitle">PTS 檔案重命名工具遇到問題</p>
        </div>

        <!-- 錯誤內容區域 -->
        <div class="error-content">
            <!-- 錯誤訊息 -->
            <div class="error-message">
                <div class="error-message-title">
                    <i class="fas fa-times-circle"></i>
                    錯誤訊息
                </div>
                <p class="error-message-text">
                    {% if error_message %}
                        {{ error_message }}
                    {% else %}
                        抱歉，系統在處理您的請求時發生了未預期的錯誤。請稍後再試或聯繫系統管理員。
                    {% endif %}
                </p>
            </div>

            <!-- 錯誤建議 -->
            <div class="error-suggestions">
                <div class="error-suggestions-title">
                    <i class="fas fa-lightbulb"></i>
                    解決建議
                </div>
                <ul class="error-suggestions-list">
                    <li>檢查上傳的檔案格式是否正確（支援 ZIP、7Z、RAR）</li>
                    <li>確認檔案大小不超過系統限制</li>
                    <li>重新整理頁面後再次嘗試</li>
                    <li>檢查網路連線是否正常</li>
                    {% if error_code %}
                    <li>如問題持續，請提供錯誤代碼 <span class="error-code">{{ error_code }}</span> 給系統管理員</li>
                    {% endif %}
                </ul>
            </div>

            <!-- 錯誤詳情（可摺疊） -->
            {% if error_details or request.args.get('debug') %}
            <div class="error-details">
                <div class="error-details-title" onclick="toggleErrorDetails()">
                    <i class="fas fa-chevron-right" id="detailsToggleIcon"></i>
                    技術詳情
                </div>
                <div class="error-details-content collapsible-content collapsed" id="errorDetailsContent">
                    {% if error_details %}
{{ error_details }}
                    {% else %}
錯誤類型：未知錯誤
時間戳記：{{ moment().format('YYYY-MM-DD HH:mm:ss') }}
請求路徑：{{ request.path }}
請求方法：{{ request.method }}
User Agent：{{ request.headers.get('User-Agent', 'Unknown') }}
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- 操作按鈕 -->
            <div class="error-actions">
                <a href="/pts-renamer/" class="error-btn error-btn-primary">
                    <i class="fas fa-home"></i>
                    回到首頁
                </a>
                <button class="error-btn error-btn-secondary" onclick="window.history.back()">
                    <i class="fas fa-arrow-left"></i>
                    返回上頁
                </button>
                <button class="error-btn error-btn-outline" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt"></i>
                    重新整理
                </button>
            </div>

            <!-- 時間戳記 -->
            <div class="error-timestamp">
                錯誤發生時間：{{ moment().format('YYYY年MM月DD日 HH:mm:ss') }}
                {% if error_id %}
                | 錯誤ID：{{ error_id }}
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_js %}
<script>
// 錯誤詳情摺疊功能
function toggleErrorDetails() {
    const content = document.getElementById('errorDetailsContent');
    const icon = document.getElementById('detailsToggleIcon');
    
    if (content.classList.contains('collapsed')) {
        content.classList.remove('collapsed');
        content.classList.add('expanded');
        icon.classList.remove('fa-chevron-right');
        icon.classList.add('fa-chevron-down');
    } else {
        content.classList.remove('expanded');
        content.classList.add('collapsed');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-right');
    }
}

// 自動回報錯誤（可選）
function reportError() {
    const errorData = {
        message: "{{ error_message | default('Unknown error') }}",
        code: "{{ error_code | default('') }}",
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent
    };
    
    // 在這裡可以發送錯誤報告到後端
    console.log('錯誤報告:', errorData);
    
    // 可以添加實際的錯誤報告邏輯
    // fetch('/api/error-report', { method: 'POST', body: JSON.stringify(errorData) });
}

// 鍵盤快捷鍵
document.addEventListener('keydown', function(event) {
    // ESC 鍵回到首頁
    if (event.key === 'Escape') {
        window.location.href = '/pts-renamer/';
    }
    
    // Ctrl+R 或 F5 重新整理
    if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
        event.preventDefault();
        window.location.reload();
    }
});

// 頁面載入時的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 可選：自動報告錯誤
    if ({{ 'true' if auto_report_errors else 'false' }}) {
        reportError();
    }
    
    // 提供用戶友善的互動回饋
    const buttons = document.querySelectorAll('.error-btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // 添加點擊效果
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 100);
        });
    });
});
</script>
{% endblock %}