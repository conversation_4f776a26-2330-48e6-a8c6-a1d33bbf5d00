"""
EQC 異步 API 端點
提供異步 EQC 處理任務的提交和狀態查詢功能
"""

import time
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field
from loguru import logger

# 導入統一超時保護工具
from frontend.shared.utils.timeout_utils import (
    timeout_heavy, timeout_complex, timeout_standard, timeout_quick,
    APITimeoutConfig, log_timeout_stats
)

# 導入統一任務接口和會話管理器
try:
    from backend.tasks.unified_task_interface import get_task_manager
    from backend.services.eqc_session_manager import get_eqc_session_manager, SessionStatus
except ImportError:
    # 處理直接執行時的導入問題
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from backend.tasks.unified_task_interface import get_task_manager
    from backend.eqc.services.eqc_session_manager import get_eqc_session_manager, SessionStatus

# 創建路由器
router = APIRouter(prefix="/api/eqc/async", tags=["EQC Async"])

# 請求和響應模型
class EQCAsyncStartRequest(BaseModel):
    """EQC 異步處理開始請求"""
    folder_path: str = Field(..., description="EQC 資料夾路徑")
    user_id: Optional[str] = Field(None, description="用戶ID（可選）")
    options: Optional[Dict[str, Any]] = Field(default_factory=dict, description="處理選項")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "folder_path": "D:/EQC_Data/Project_A",
                "user_id": "user123",
                "options": {
                    "main_start": "A1",
                    "main_end": "Z100",
                    "backup_start": "AA1",
                    "backup_end": "ZZ100"
                }
            }
        }
    }

class EQCAsyncStartResponse(BaseModel):
    """EQC 異步處理開始響應"""
    status: str = Field(..., description="狀態")
    session_id: str = Field(..., description="會話ID")
    task_id: str = Field(..., description="任務ID")
    message: str = Field(..., description="訊息")
    estimated_duration: str = Field(..., description="預估處理時間")

class EQCAsyncStatusResponse(BaseModel):
    """EQC 異步處理狀態響應"""
    status: str = Field(..., description="狀態")
    session_id: str = Field(..., description="會話ID")
    task_id: Optional[str] = Field(None, description="任務ID")
    progress: int = Field(..., description="進度百分比")
    current_step: str = Field(..., description="當前步驟")
    created_at: str = Field(..., description="創建時間")
    updated_at: str = Field(..., description="更新時間")
    error_message: Optional[str] = Field(None, description="錯誤訊息")
    result: Optional[Dict[str, Any]] = Field(None, description="處理結果")

@router.post("/start", response_model=EQCAsyncStartResponse)
async def start_eqc_async_processing(request: EQCAsyncStartRequest):
    """
    開始異步 EQC 處理
    
    創建新的會話並提交 Dramatiq 任務進行背景處理
    """
    try:
        logger.info(f"🚀 收到異步 EQC 處理請求: {request.folder_path}")
        
        # 30秒超時保護 - EQC 任務提交
        async with timeout_heavy("EQC 異步任務提交"):
            # 獲取會話管理器和異步檔案鎖定管理器
            session_manager = get_eqc_session_manager()
            from backend.shared.infrastructure.adapters.filesystem.async_file_lock_manager import get_async_file_lock_manager
            async_file_lock_manager = await get_async_file_lock_manager()

            # 異步檢查檔案路徑衝突
            lock_result = await async_file_lock_manager.acquire_lock_async(
                folder_path=request.folder_path,
                session_id="temp",  # 臨時ID，稍後更新
                user_id=request.user_id or f"user_{int(time.time())}"
            )

            if not lock_result['success']:
                raise HTTPException(
                    status_code=409,
                    detail=f"路徑衝突: {lock_result['message']} (衝突會話: {lock_result.get('conflict_session', 'unknown')})"
                )

            # 創建新會話
            session_id = session_manager.create_session(
                folder_path=request.folder_path,
                user_id=request.user_id
            )

            # 異步更新檔案鎖定的會話ID
            await async_file_lock_manager.release_lock_async(request.folder_path, "temp")
            await async_file_lock_manager.acquire_lock_async(
                folder_path=request.folder_path,
                session_id=session_id,
                user_id=request.user_id or f"user_{int(time.time())}"
            )
            
            # 使用統一任務接口提交任務
            task_manager = get_task_manager()
            task_result = task_manager.submit_eqc_workflow(
                folder_path=request.folder_path,
                user_session_id=session_id,
                options=request.options
            )

            # 更新會話任務資訊
            session_manager.update_session_task(session_id, task_result.task_id)

            logger.success(f"✅ 異步 EQC 任務已提交 - 會話: {session_id}, 任務: {task_result.task_id}, 隊列: Dramatiq")

            return EQCAsyncStartResponse(
                status="submitted",
                session_id=session_id,
                task_id=task_result.task_id,
                message="EQC 處理任務已成功提交到 DRAMATIQ 背景佇列 (修復版)",
                estimated_duration="8-12 分鐘"
            )
        
    except Exception as e:
        import traceback
        error_details = str(e) if str(e) else f"{type(e).__name__}: {repr(e)}"
        stack_trace = traceback.format_exc()
        error_msg = f"提交異步 EQC 任務失敗: {error_details}"

        logger.error(f"❌ {error_msg}")
        logger.error(f"詳細錯誤堆棧:\n{stack_trace}")

        # 在開發環境中包含更多錯誤詳情
        if hasattr(e, '__traceback__'):
            detail_msg = f"{error_msg}\n詳細信息: {error_details}"
        else:
            detail_msg = error_msg

        raise HTTPException(status_code=500, detail=detail_msg)
    # TimeoutContext 會自動處理 asyncio.TimeoutError，這裡不需要重複處理

@router.get("/status/{session_id}", response_model=EQCAsyncStatusResponse)
async def get_eqc_async_status(session_id: str):
    """
    查詢異步 EQC 處理狀態
    
    根據會話ID查詢處理進度和結果
    """
    try:
        logger.debug(f"🔍 查詢 EQC 異步狀態: {session_id}")
        
        # 15秒超時保護
        async with asyncio.timeout(15):
            # 獲取會話管理器
            session_manager = get_eqc_session_manager()
            
            # 獲取會話資訊
            session = session_manager.get_session(session_id)
            if not session:
                raise HTTPException(status_code=404, detail=f"會話 {session_id} 不存在")
        
        # 🔧 從資料庫檢查任務實際狀態（解決跨進程同步問題）
        if session.task_id and session.status == SessionStatus.PROCESSING:
            try:
                from backend.shared.infrastructure.database.task_status_db import get_task_status_db
                task_db = get_task_status_db()

                # 🔧 檢查資料庫中的任務狀態（優先使用會話ID查詢）
                db_task_status = task_db.get_task_status(session_id=session_id)
                if not db_task_status:
                    # 備用：使用任務ID查詢
                    db_task_status = task_db.get_task_status(task_id=session.task_id)

                if db_task_status:
                    logger.debug(f"📊 資料庫任務狀態: {db_task_status}")

                    if db_task_status['status'] == 'completed':
                        logger.info(f"🎉 資料庫顯示任務完成，同步會話狀態: {session_id}")
                        session.update_status(SessionStatus.COMPLETED, "任務處理完成")
                        session.result = db_task_status.get('result', {})
                        session.progress = 100
                        session.current_step = "處理完成"

                    elif db_task_status['status'] == 'failed':
                        error_msg = db_task_status.get('error_message', '任務執行失敗')
                        logger.warning(f"❌ 資料庫顯示任務失敗，同步會話狀態: {session_id}")
                        session.update_status(SessionStatus.FAILED, error_msg)

                    elif db_task_status['status'] in ['started', 'processing']:
                        # 🔧 獲取最新步驟狀態
                        latest_step = task_db.get_latest_step_status(session.task_id)
                        if latest_step:
                            session.progress = latest_step['progress']
                            session.current_step = latest_step['step_name']
                            logger.debug(f"📈 更新進度: {latest_step['progress']}% - {latest_step['step_name']}")
                else:
                    # 🔧 備用：檢查 Dramatiq 狀態
                    from backend.tasks.dramatiq_integration import get_task_status
                    dramatiq_status = get_task_status(session.task_id)
                    logger.debug(f"🎭 Dramatiq 任務狀態: {dramatiq_status}")

                    if dramatiq_status.get('status') == 'completed':
                        logger.info(f"🔄 Dramatiq 顯示任務完成，同步會話狀態: {session_id}")
                        session.update_status(SessionStatus.COMPLETED, "任務處理完成")
                        session.result = dramatiq_status.get('result', {})
                        session.progress = 100
                        session.current_step = "處理完成"

            except Exception as e:
                logger.debug(f"📊 資料庫狀態檢查失敗: {e}")
                # 🔧 不因為檢查失敗就設置超時

        # 🔧 檢查會話超時（只有在長時間無更新時才檢查）
        from datetime import datetime, timedelta
        if session.status == SessionStatus.PROCESSING:
            task_timeout = timedelta(minutes=45)  # 任務執行超時：45分鐘
            if datetime.now() - session.updated_at > task_timeout:
                logger.warning(f"⏰ 任務執行超時: {session.session_id}")
                session.update_status(SessionStatus.FAILED, f"任務執行超時（超過{task_timeout.total_seconds()/60}分鐘）")
        elif session.status not in [SessionStatus.COMPLETED, SessionStatus.FAILED]:
            session_timeout = timedelta(hours=4)  # 會話超時：4小時
            if datetime.now() - session.updated_at > session_timeout:
                logger.warning(f"⏰ 會話超時: {session.session_id}")
                session.update_status(SessionStatus.FAILED, f"會話超時（超過{session_timeout.total_seconds()/3600}小時）")
        
        return EQCAsyncStatusResponse(
            status=session.status.value,
            session_id=session.session_id,
            task_id=session.task_id,
            progress=session.progress,
            current_step=session.current_step,
            created_at=session.created_at.isoformat(),
            updated_at=session.updated_at.isoformat(),
            error_message=session.error_message,
            result=session.result
        )
        
    except HTTPException:
        raise
    except asyncio.TimeoutError:
        logger.warning(f"查詢 EQC 異步狀態超時: {session_id}")
        raise HTTPException(
            status_code=408, 
            detail="查詢狀態超時，請稍後重試"
        )
    except Exception as e:
        error_msg = f"查詢異步 EQC 狀態失敗: {str(e)}"
        logger.error(f"❌ {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/sessions")
async def list_eqc_sessions(
    user_id: Optional[str] = Query(None, description="用戶ID篩選"),
    status: Optional[str] = Query(None, description="狀態篩選"),
    limit: int = Query(50, description="返回數量限制")
):
    """
    列出 EQC 會話
    
    支援按用戶ID和狀態篩選
    """
    try:
        # 10秒超時保護
        async with asyncio.timeout(10):
            session_manager = get_eqc_session_manager()
            
            if user_id:
                sessions = session_manager.get_user_sessions(user_id)
            else:
                sessions = list(session_manager.sessions.values())
            
            # 狀態篩選
            if status:
                sessions = [s for s in sessions if s.status.value == status]
            
            # 按更新時間排序並限制數量
            sessions.sort(key=lambda x: x.updated_at, reverse=True)
            sessions = sessions[:limit]
            
            return {
                "status": "success",
                "sessions": [session.to_dict() for session in sessions],
                "total": len(sessions)
            }
        
    except asyncio.TimeoutError:
        logger.warning("列出 EQC 會話超時")
        raise HTTPException(
            status_code=408, 
            detail="列出會話超時，請稍後重試"
        )
    except Exception as e:
        error_msg = f"列出 EQC 會話失敗: {str(e)}"
        logger.error(f"❌ {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/stats")
async def get_eqc_stats():
    """
    獲取 EQC 系統統計資訊
    """
    try:
        # 10秒超時保護
        async with asyncio.timeout(10):
            session_manager = get_eqc_session_manager()
            stats = session_manager.get_session_stats()
            
            return {
                "status": "success",
                "stats": stats,
                "timestamp": datetime.now().isoformat()
            }
        
    except asyncio.TimeoutError:
        logger.warning("獲取 EQC 統計資訊超時")
        raise HTTPException(
            status_code=408, 
            detail="獲取統計資訊超時，請稍後重試"
        )
    except Exception as e:
        error_msg = f"獲取 EQC 統計資訊失敗: {str(e)}"
        logger.error(f"❌ {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.delete("/session/{session_id}")
async def cancel_eqc_session(session_id: str):
    """
    取消 EQC 會話
    
    停止相關的 Dramatiq 任務並清理會話
    """
    try:
        # 15秒超時保護
        async with asyncio.timeout(15):
            session_manager = get_eqc_session_manager()
            session = session_manager.get_session(session_id)
            
            if not session:
                raise HTTPException(status_code=404, detail=f"會話 {session_id} 不存在")
        
        # 如果有任務ID，記錄任務取消請求
        if session.task_id:
            try:
                # Dramatiq 任務取消：標記會話狀態，Worker 會自動處理
                # 注意：Dramatiq 沒有像 Celery 那樣的強制終止功能
                logger.info(f"🛑 標記任務取消請求: {session.task_id}")
                logger.info("📝 Dramatiq 任務將在下次檢查時自動停止")
            except Exception as e:
                logger.warning(f"⚠️ 標記任務取消失敗: {e}")
        
        # 標記會話為失敗狀態
        session_manager.fail_session(session_id, "用戶取消")
        
        return {
            "status": "success",
            "message": f"會話 {session_id} 已取消",
            "session_id": session_id
        }
        
    except HTTPException:
        raise
    except asyncio.TimeoutError:
        logger.warning(f"取消 EQC 會話超時: {session_id}")
        raise HTTPException(
            status_code=408, 
            detail="取消會話超時，請稍後重試"
        )
    except Exception as e:
        error_msg = f"取消 EQC 會話失敗: {str(e)}"
        logger.error(f"❌ {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)


@router.get("/locks")
async def get_file_locks():
    """
    獲取檔案鎖定狀態

    返回所有活躍的檔案鎖定信息，用於並發監控
    """
    try:
        # 10秒超時保護
        async with asyncio.timeout(10):
            from backend.services.async_file_lock_manager import get_async_file_lock_manager
            async_file_lock_manager = await get_async_file_lock_manager()

            locks_data = await async_file_lock_manager.get_active_locks_async()

            return {
                "status": "success",
                "data": locks_data,
                "timestamp": datetime.now().isoformat()
            }

    except asyncio.TimeoutError:
        logger.warning("獲取檔案鎖定狀態超時")
        raise HTTPException(
            status_code=408, 
            detail="獲取檔案鎖定狀態超時，請稍後重試"
        )
    except Exception as e:
        logger.error(f"獲取檔案鎖定狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取鎖定狀態失敗: {str(e)}")


@router.delete("/locks/cleanup")
async def cleanup_expired_locks():
    """
    清理過期的檔案鎖定

    手動觸發清理過期的檔案鎖定，釋放被卡住的資源
    """
    try:
        # 15秒超時保護
        async with asyncio.timeout(15):
            from backend.services.async_file_lock_manager import get_async_file_lock_manager
            async_file_lock_manager = await get_async_file_lock_manager()

            # 獲取清理前的鎖定數量
            before_data = await async_file_lock_manager.get_active_locks_async()
            before_count = before_data['total_locks']

            # 執行異步清理
            cleaned_count = await async_file_lock_manager.cleanup_expired_locks_async()

            # 獲取清理後的鎖定數量
            after_data = await async_file_lock_manager.get_active_locks_async()
            after_count = after_data['total_locks']

        logger.info(f"🧹 手動異步清理檔案鎖定完成，清理了 {cleaned_count} 個過期鎖定")

        return {
            "status": "success",
            "message": f"異步清理完成，釋放了 {cleaned_count} 個過期鎖定",
            "before_count": before_count,
            "after_count": after_count,
            "cleaned_count": cleaned_count,
            "remaining_locks": after_data,
            "timestamp": datetime.now().isoformat()
        }

    except asyncio.TimeoutError:
        logger.warning("清理過期檔案鎖定超時")
        raise HTTPException(
            status_code=408, 
            detail="清理檔案鎖定超時，請稍後重試"
        )
    except Exception as e:
        logger.error(f"清理檔案鎖定失敗: {e}")
        raise HTTPException(status_code=500, detail=f"清理鎖定失敗: {str(e)}")


@router.delete("/locks/force/{session_id}")
async def force_release_session_lock(session_id: str):
    """
    強制釋放指定會話的檔案鎖定

    用於緊急情況下釋放被卡住的會話鎖定
    """
    try:
        # 15秒超時保護
        async with asyncio.timeout(15):
            from backend.services.async_file_lock_manager import get_async_file_lock_manager
            from backend.services.eqc_session_manager import get_eqc_session_manager

            async_file_lock_manager = await get_async_file_lock_manager()
            session_manager = get_eqc_session_manager()

            # 獲取會話信息
            session = session_manager.get_session(session_id)
            if not session:
                raise HTTPException(status_code=404, detail=f"會話 {session_id} 不存在")

            # 異步強制釋放檔案鎖定
            release_result = await async_file_lock_manager.release_lock_async(session.folder_path, session_id)

            # 標記會話為失敗狀態
            session_manager.fail_session(session_id, "管理員強制釋放")

        logger.warning(f"🔓 強制釋放會話鎖定: {session_id}, 路徑: {session.folder_path}")

        return {
            "status": "success",
            "message": f"已強制釋放會話 {session_id} 的檔案鎖定",
            "session_id": session_id,
            "folder_path": session.folder_path,
            "release_result": release_result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except asyncio.TimeoutError:
        logger.warning(f"強制釋放會話鎖定超時: {session_id}")
        raise HTTPException(
            status_code=408, 
            detail="強制釋放鎖定超時，請稍後重試"
        )
    except Exception as e:
        logger.error(f"強制釋放會話鎖定失敗: {e}")
        raise HTTPException(status_code=500, detail=f"強制釋放失敗: {str(e)}")


@router.get("/concurrent-status")
async def get_concurrent_status():
    """
    獲取並發處理狀態

    提供完整的並發監控信息
    """
    try:
        # 15秒超時保護
        async with asyncio.timeout(15):
            # 獲取會話管理器和異步檔案鎖定管理器
            session_manager = get_eqc_session_manager()
            from backend.services.async_file_lock_manager import get_async_file_lock_manager
            async_file_lock_manager = await get_async_file_lock_manager()

            # 獲取會話統計
            session_stats = session_manager.get_session_stats()

            # 獲取檔案鎖定信息
            locks_data = await async_file_lock_manager.get_active_locks_async()
            active_locks = locks_data.get('locks', {})

            # 計算並發指標
            concurrent_users = len(set(lock['user_id'] for lock in active_locks.values()))

        return {
            "status": "success",
            "concurrent_status": {
                "active_sessions": session_stats["active_sessions"],
                "processing_sessions": session_stats["processing_sessions"],
                "concurrent_users": concurrent_users,
                "file_locks": len(active_locks),
                "system_load": "normal"  # 可以擴展為實際系統負載監控
            },
            "session_stats": session_stats,
            "file_locks": active_locks,
            "timestamp": datetime.now().isoformat()
        }

    except asyncio.TimeoutError:
        logger.warning("獲取並發狀態超時")
        raise HTTPException(
            status_code=408, 
            detail="獲取並發狀態超時，請稍後重試"
        )
    except Exception as e:
        logger.error(f"獲取並發狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取並發狀態失敗: {str(e)}")
