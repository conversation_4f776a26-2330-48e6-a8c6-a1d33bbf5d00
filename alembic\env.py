"""
Epic-01 Database Infrastructure - Alembic 環境配置
為 Outlook 郵件摘要工具提供企業級資料庫遷移支援

功能特色:
- 跨資料庫兼容性 (SQLite, PostgreSQL)
- 自動備份機制
- 遷移前後驗證
- 企業級日誌記錄
- 安全的回滾支援
"""

import logging
import os
import shutil
from datetime import datetime
from pathlib import Path
from sqlalchemy import engine_from_config, pool
from alembic import context

# Import models for autogenerate support
from backend.shared.infrastructure.adapters.database.models import Base
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    EmailDownloadStatusDB, 
    EmailDownloadRetryLogDB
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('alembic.epic01')

# Alembic Config object
config = context.config

def get_database_url():
    """
    動態獲取資料庫連接字串
    支援環境變數覆蓋和多環境配置
    """
    # 優先使用環境變數
    db_url = os.getenv('DATABASE_URL')
    if db_url:
        logger.info(f"Using DATABASE_URL from environment")
        return db_url
    
    # 使用配置檔案設定
    db_url = config.get_main_option("sqlalchemy.url")
    logger.info(f"Using database URL from config: {db_url}")
    return db_url

def create_backup():
    """
    遷移前創建資料庫備份
    """
    db_url = get_database_url()
    if not db_url.startswith('sqlite'):
        logger.info("Backup skipped: Non-SQLite database")
        return None
        
    # 提取資料庫檔案路徑
    db_path = db_url.replace('sqlite:///', '')
    if not os.path.exists(db_path):
        logger.warning(f"Database file not found: {db_path}")
        return None
    
    # 創建備份目錄
    backup_dir = Path("backups/migrations")
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成備份檔案名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"epic01_migration_backup_{timestamp}.db"
    backup_path = backup_dir / backup_filename
    
    try:
        shutil.copy2(db_path, backup_path)
        logger.info(f"Database backup created: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Backup creation failed: {e}")
        return None

def verify_migration(connection):
    """
    遷移後驗證資料庫完整性
    """
    try:
        # 驗證關鍵表是否存在
        tables_to_verify = ['emails', 'email_download_status', 'email_download_retry_log']
        
        for table_name in tables_to_verify:
            result = connection.execute(
                f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"
            )
            if not result.fetchone():
                logger.error(f"Table {table_name} not found after migration")
                return False
        
        # 驗證 emails 表的新欄位 (Story 1.1)
        result = connection.execute("PRAGMA table_info(emails)")
        columns = [row[1] for row in result.fetchall()]
        
        required_columns = ['download_success', 'processing_success', 
                          'download_completed_at', 'processing_completed_at']
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            logger.error(f"Missing columns in emails table: {missing_columns}")
            return False
        
        logger.info("Migration verification completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Migration verification failed: {e}")
        return False

def run_migrations_offline():
    """
    離線模式運行遷移 (生成 SQL 腳本)
    """
    url = get_database_url()
    context.configure(
        url=url,
        target_metadata=Base.metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online():
    """
    線上模式運行遷移 (直接執行)
    """
    # 創建備份
    backup_path = create_backup()
    
    # 配置引擎
    configuration = config.get_section(config.config_ini_section)
    configuration['sqlalchemy.url'] = get_database_url()
    
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=Base.metadata,
            compare_type=True,
            compare_server_default=True
        )

        try:
            with context.begin_transaction():
                context.run_migrations()
            
            # 驗證遷移結果
            if not verify_migration(connection):
                logger.error("Migration verification failed!")
                raise Exception("Migration verification failed")
                
            logger.info("Migration completed successfully")
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            if backup_path:
                logger.info(f"Backup available at: {backup_path}")
            raise

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()