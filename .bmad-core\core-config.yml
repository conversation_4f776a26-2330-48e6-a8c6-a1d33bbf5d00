# BMAD Core Configuration V2.0
# 符合標準BMAD-METHOD規範的配置文件
# 基於現有 PTS Renamer 項目結構

version: "5.0"
project_type: "full-stack-web"
project_name: "PTS Renamer System"
project_root: "D:\\project\\python\\outlook_summary"

# 項目當前狀態
project_status: "development"
current_epic: "pts-renamer-epic"
active_stories: 
  - "story-pts-001-upload-infrastructure"
  - "story-pts-002-frontend-upload-interface" 
  - "story-pts-003-initial-processing-pipeline"

# Dev Agent Always Load Files (標準BMAD要求)
devLoadAlwaysFiles:
  - docs/architecture.md
  - docs/pts_system/pts-renamer-architecture.md
  - .bmad-core/data/technical-preferences.md
  - docs/epics/pts-renamer-epic.md

# 現有文檔結構映射
document_structure:
  standard_bmad:
    prd: "docs/prd.md"
    architecture: "docs/architecture.md"
    epics_dir: "docs/epics/"
    stories_dir: "docs/stories/"
  
  project_specific:
    detailed_architecture: "docs/pts_system/pts-renamer-architecture.md"
    project_tracking: "docs/pts_system/pts-renamer-project-tracking.md"
    sprint_planning: "docs/stories/pts-renamer-sprint-planning.md"
  
  extended_qa:
    assessments_dir: "docs/qa/assessments/"
    gates_dir: "docs/qa/gates/"
    qa_assessment: "docs/qa/assessments/pts-renamer-qa-assessment.md"
    quality_gates: "docs/qa/gates/pts-renamer-quality-gates.md"
  
  automation_results:
    flow_results: ".bmad/flow-results/"
    state_tracking: ".bmad/auto-flow-state.json"

# 標準BMAD Agent Dependencies
agent_dependencies:
  analyst:
    templates: 
      - project-brief-template.md
    tasks: 
      - market-analysis.md
      - feasibility-check.md
    data: 
      - technical-preferences.md
      - bmad-kb.md
    existing_docs:
      - "docs/prd.md (already exists)"
      - "docs/epics/pts-renamer-epic.md (already exists)"
  
  pm:
    templates: 
      - prd-template.md
    tasks: 
      - create-prd.md
    checklists: 
      - prd-completeness.md
    existing_docs:
      - "docs/prd.md (completed)"
    dependencies:
      - analyst_output: "Project Brief"
    
  architect:
    templates: 
      - architecture-template.md
    tasks: 
      - design-architecture.md
    checklists: 
      - arch-review.md
    existing_docs:
      - "docs/architecture.md (completed)"
      - "docs/pts_system/pts-renamer-architecture.md (detailed)"
    dependencies:
      - pm_output: "docs/prd.md"
    data:
      - technical-preferences.md
    
  po:
    tasks: 
      - validate-alignment.md
      - shard-doc.md
    checklists: 
      - po-validation.md
      - business-completeness.md  # 新增：業務完整性檢查
    templates: 
      - epic-template.md
    existing_docs:
      - "docs/epics/pts-renamer-epic.md (completed)"
    dependencies:
      - pm_output: "docs/prd.md"
      - architect_output: "docs/architecture.md"
    
  sm:
    templates: 
      - story-template.md
      - tdd-story-template.md  # 新增：TDD 故事模板
    tasks: 
      - create-story.md
    checklists: 
      - story-completeness.md
    existing_docs:
      - "docs/stories/story-pts-001-upload-infrastructure.md (completed)"
      - "docs/stories/story-pts-002-frontend-upload-interface.md (completed)"
      - "docs/stories/story-pts-003-initial-processing-pipeline.md (completed)"
      - "docs/stories/pts-renamer-sprint-planning.md (completed)"
    dependencies:
      - po_output: "docs/epics/"
      - architect_output: "docs/architecture.md"
    
  dev:
    tasks: 
      - implement-story.md
    checklists: 
      - dev-completion.md
      - tdd-implementation.md  # 新增：TDD 實施檢查
      - business-logic-completeness.md  # 新增：業務邏輯完整性檢查
    always_load: "devLoadAlwaysFiles"
    dependencies:
      - sm_output: "docs/stories/"
      - architect_output: "docs/architecture.md"
    
  qa:
    tasks: 
      - review-story.md
    checklists: 
      - qa-gates.md
      - business-completeness-validation.md  # 新增：業務完整性驗證
      - tdd-verification.md  # 新增：TDD 流程驗證
    existing_docs:
      - "docs/qa/assessments/pts-renamer-qa-assessment.md (completed)"
      - "docs/qa/gates/pts-renamer-quality-gates.md (completed)"
    dependencies:
      - dev_output: "story files with dev notes"

# 自動化增強配置（創新擴展）
automation:
  service_management:
    main_service: "start_integrated_services.py"
    work_dir: "D:\\project\\python\\outlook_summary"
    service_pattern: "python.*start_integrated_services.py"
    service_ports: [5000, 8010]
  
  flow_state:
    state_file: ".bmad/auto-flow-state.json"
    results_dir: ".bmad/flow-results/"
    error_dir: ".bmad/"
    
  qa_documentation:
    enabled: true
    assessments_dir: "docs/qa/assessments/"
    gates_dir: "docs/qa/gates/"

# 現有項目識別
existing_project_assets:
  documentation_complete: true
  planning_phase_complete: true
  stories_defined: true
  qa_framework_established: true
  
  completed_phases:
    - "analyst" # PRD exists
    - "pm"      # PRD completed
    - "architect" # Architecture completed  
    - "po"      # Epics sharded
    - "sm"      # Stories created
    # Ready for dev/qa phases
    
  next_actions:
    - "Begin development implementation"
    - "Execute story-pts-001 first"
    - "Set up QA validation pipeline"

# Windows環境配置
environment:
  platform: "windows"
  encoding: "utf-8"
  line_endings: "crlf"
  no_emoji: true
  powershell_compatible: true
  
# 品質標準（強化業務完整性要求）
quality_standards:
  code_coverage_minimum: 90  # 提升到90%以確保TDD品質
  documentation_required: true
  security_scan_required: true
  performance_testing_required: true
  
  # 新增：業務完整性強制要求
  business_completeness:
    crud_operations_mandatory: true  # 所有資料模型必須有完整CRUD
    partial_implementation_forbidden: true  # 禁止部分實現
    mock_data_forbidden: true  # 禁止使用假數據
    read_only_features_forbidden: true  # 禁止只讀功能
    
  # 新增：TDD流程強制要求  
  tdd_requirements:
    red_green_refactor_mandatory: true  # 必須執行完整TDD循環
    test_first_mandatory: true  # 必須先寫測試
    test_coverage_minimum: 90  # TDD測試覆蓋率要求
    fake_tdd_detection: true  # 啟用假TDD檢測
    
  # 新增：自動回滾條件
  rollback_triggers:
    incomplete_crud: true  # 不完整CRUD自動回滾
    business_logic_gaps: true  # 業務邏輯缺失自動回滾
    tdd_violations: true  # TDD違規自動回滾
    data_consistency_issues: true  # 資料一致性問題自動回滾
  
# 集成點
integration_points:
  existing_outlook_system: "D:\\project\\python\\outlook_summary"
  shared_components: true
  common_authentication: true
  unified_deployment: true