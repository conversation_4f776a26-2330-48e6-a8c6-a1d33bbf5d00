# 專案結構 - 半導體郵件處理系統
# Project Structure - Semiconductor Email Processing System

## 📋 **文檔概述**

### **文檔目的**
本文檔詳細描述半導體郵件處理系統的完整專案結構，包括目錄組織、模組劃分、文件職責和開發規範。為AI開發代理和團隊成員提供專案導航和開發指導。

### **適用範圍**
- 新加入團隊的開發人員
- AI 開發代理和自動化工具
- 系統架構師和技術負責人
- DevOps 和運維團隊

### **文檔版本**
| 版本 | 日期 | 更新內容 | 作者 |
|------|------|----------|------|
| 1.0 | 2025-08-19 | 初始版本，基於現有系統分析 | Development Team |

---

## 🏗️ **整體專案架構**

### **專案根目錄結構**
```
outlook_summary/                              # 專案根目錄
├── 📁 .bmad/                                # BMAD 自動化工作流程
├── 📁 .bmad-core/                           # BMAD 核心配置和數據
├── 📁 .claude/                              # Claude AI 助手配置
├── 📁 backend/                              # 後端應用程序
├── 📁 deployment/                           # 部署配置和腳本
├── 📁 docs/                                 # 專案文檔
├── 📁 frontend/                             # 前端應用程序
├── 📁 reNameCTAF/                          # PTS 重命名工具
├── 📁 tests/                               # 測試文件
├── 📄 README.md                            # 專案說明文檔
├── 📄 CHANGELOG.md                         # 變更日誌
├── 📄 requirements.txt                     # Python 依賴
├── 📄 requirements.pts.txt                 # PTS 特定依賴
├── 📄 pyproject.toml                       # Python 專案配置
├── 📄 start_integrated_services.py        # 集成服務啟動腳本
└── 📄 project_info.json                   # 專案元數據
```

---

## 🧠 **AI 自動化系統結構**

### **BMAD 工作流程系統**
```
.bmad/                                        # BMAD 工作流程結果
├── flow-results/                            # 流程執行結果
│   ├── architecture-*.md                   # 架構設計文檔
│   ├── delivery-report-*.md                # 交付報告
│   ├── execution-plan-*.md                 # 執行計劃
│   ├── implementation-*.md                 # 實施記錄
│   ├── task-analysis-*.md                  # 任務分析
│   ├── validation-*.md                     # 驗證報告
│   └── self-validation-*.md                # 自我驗證報告

.bmad-core/                                  # BMAD 核心系統
├── core-config.yml                         # 核心配置
├── data/                                   # BMAD 數據
│   ├── email-inbox-functional-specification.md
│   ├── email-processing-button-fix-record.md
│   └── technical-preferences.md
├── epics/                                  # 史詩級需求
├── progress/                               # 進度追蹤
├── sprints/                                # 敏捷衝刺
└── templates/                              # 文檔模板
    └── prd-template.md

.claude/                                     # Claude AI 助手
├── commands/BMad/                          # BMAD 命令
│   ├── agents/                            # 專業代理
│   ├── tasks/                             # 任務定義
│   ├── bmad-auto-flow.md                  # 自動化流程
│   ├── bmad-flow-start.md                 # 流程啟動
│   ├── bmad-flow-status.md                # 狀態查詢
│   └── bmad-rollback.md                   # 回滾機制
├── agents/                                # 50+ 專業代理
├── output-styles/                         # 輸出樣式
│   └── bmad-auto-flow-orchestrator.md    # 自動化協調器
├── hooks/                                 # 自動觸發鉤子
└── dev_log.txt                           # 開發日誌
```

---

## 🔧 **後端系統結構**

### **後端總體架構**
```
backend/                                     # 後端應用 (473+ Python 文件)
├── 📁 email/                              # 郵件處理子系統
├── 📁 shared/                             # 共享基礎設施
├── 📁 monitoring/                         # 監控和可觀測性
├── 📁 tasks/                              # 異步任務處理
├── 📁 pts_renamer/                        # PTS 重命名服務
└── 📄 tasks/pts_renamer_tasks.py          # PTS 任務定義
```

### **郵件處理子系統** (`backend/email/`)
```
email/                                       # 郵件處理核心 (50+ 文件)
├── adapters/                               # 外部適配器層
│   ├── outlook/                           # Outlook 集成
│   │   └── outlook_adapter.py             # Outlook API 適配器
│   └── pop3/                              # POP3 協議支援
│       └── pop3_email_reader.py           # POP3 郵件讀取器
├── models/                                # 領域模型
│   └── email_models.py                   # 郵件領域模型
├── parsers/                               # 解析器生態系統
│   ├── base/                             # 基礎解析器
│   │   ├── base_parser.py                # 解析器基類
│   │   └── parser_interface.py           # 解析器接口
│   ├── vendors/                          # 12個廠商解析器
│   │   ├── gtk_parser.py                 # GTK 廠商解析器
│   │   ├── etd_parser.py                 # ETD 廠商解析器
│   │   ├── jcet_parser.py                # JCET 廠商解析器
│   │   ├── lingsen_parser.py             # 靈森廠商解析器
│   │   ├── xaht_parser.py                # 西安匯通解析器
│   │   ├── chuzhou_parser.py             # 滁州廠商解析器
│   │   ├── msec_parser.py                # MSEC 廠商解析器
│   │   ├── nanotech_parser.py            # 納米技術解析器
│   │   ├── nfme_parser.py                # NFME 廠商解析器
│   │   ├── suqian_parser.py              # 宿遷廠商解析器
│   │   └── tsht_parser.py                # TSHT 廠商解析器
│   ├── ai/                               # AI 解析器
│   │   ├── llm_parser.py                 # LLM 智能解析器
│   │   ├── hybrid_llm_parser.py          # 混合解析器
│   │   └── grok_client.py                # Grok API 客戶端
│   └── parser_factory.py                # 解析器工廠
├── services/                             # 應用服務層
│   ├── email_processing_service.py       # 郵件處理服務
│   ├── email_classification_service.py   # 郵件分類服務
│   └── attachment_service.py             # 附件處理服務
└── utils/                                # 工具函數
    ├── email_utils.py                    # 郵件工具函數
    └── validation_utils.py               # 驗證工具函數
```

### **共享基礎設施** (`backend/shared/`)
```
shared/                                      # 共享基礎設施 (200+ 文件)
├── infrastructure/                         # 基礎設施層
│   ├── adapters/                          # 適配器實現
│   │   ├── database/                      # 數據庫適配器
│   │   │   ├── email_database.py          # 郵件數據庫操作
│   │   │   ├── connection_manager.py      # 連接管理器
│   │   │   └── migrations/                # 數據庫遷移
│   │   ├── email_inbox/                   # 郵件收件箱
│   │   │   ├── email_sync_service.py      # 郵件同步服務
│   │   │   └── inbox_monitor.py           # 收件箱監控
│   │   ├── web_api/                       # Web API 適配器
│   │   │   ├── email_web_service.py       # 郵件 Web 服務
│   │   │   └── api_middleware.py          # API 中間件
│   │   ├── file_storage/                  # 文件存儲
│   │   │   ├── local_file_storage.py      # 本地文件存儲
│   │   │   └── attachment_manager.py      # 附件管理器
│   │   └── notification/                  # 通知服務
│   │       ├── line_notification_service.py # Line 通知服務
│   │       └── email_notification_service.py # 郵件通知服務
│   ├── config/                            # 配置管理
│   │   ├── app_config.py                  # 應用配置
│   │   ├── database_config.py             # 數據庫配置
│   │   └── logging_config.py              # 日誌配置
│   └── core/                              # 核心服務
│       ├── dependency_injection.py        # 依賴注入
│       ├── event_bus.py                   # 事件總線
│       └── service_locator.py             # 服務定位器
├── domain/                                # 領域層
│   ├── entities/                          # 實體
│   ├── value_objects/                     # 值對象
│   ├── aggregates/                        # 聚合根
│   └── repositories/                      # 存儲庫接口
├── application/                           # 應用層
│   ├── use_cases/                         # 用例
│   ├── command_handlers/                  # 命令處理器
│   └── query_handlers/                    # 查詢處理器
└── utils/                                 # 共享工具
    ├── date_utils.py                      # 日期工具
    ├── string_utils.py                    # 字符串工具
    ├── validation_utils.py                # 驗證工具
    └── encryption_utils.py                # 加密工具
```

### **監控子系統** (`backend/monitoring/`)
```
monitoring/                                  # 監控和可觀測性 (50+ 文件)
├── collectors/                             # 數據收集器
│   ├── dashboard_dramatiq_collector.py    # Dramatiq 指標收集
│   ├── dashboard_email_collector.py       # 郵件指標收集
│   ├── dashboard_system_collector.py      # 系統指標收集
│   └── dashboard_vendor_file_collector.py # 廠商文件指標收集
├── core/                                  # 核心監控服務
│   ├── dashboard_cache_service.py         # 緩存服務
│   ├── dashboard_websocket_manager.py     # WebSocket 管理器
│   └── dashboard_alert_service.py         # 告警服務
├── api/                                   # 監控 API
│   ├── dashboard_monitoring_api.py        # 監控 API 端點
│   └── dashboard_websocket.py             # WebSocket 端點
├── config/                                # 監控配置
│   ├── dashboard_config.py                # 儀表板配置
│   └── monitoring_rules.py                # 監控規則
└── utils/                                 # 監控工具
    ├── metrics_utils.py                   # 指標工具
    └── alert_utils.py                     # 告警工具
```

### **異步任務系統** (`backend/tasks/`)
```
tasks/                                       # Dramatiq 異步任務系統
├── services/                               # 任務服務
│   └── dramatiq_tasks.py                  # Dramatiq 任務定義
├── pts_renamer_tasks.py                   # PTS 重命名任務
├── workers/                               # 工作者進程
│   ├── email_worker.py                    # 郵件處理工作者
│   ├── analysis_worker.py                 # 分析工作者
│   └── maintenance_worker.py              # 維護工作者
├── middleware/                            # 任務中間件
│   ├── retry_middleware.py                # 重試中間件
│   ├── logging_middleware.py              # 日誌中間件
│   └── monitoring_middleware.py           # 監控中間件
└── config/                                # 任務配置
    ├── broker_config.py                   # 消息代理配置
    └── worker_config.py                   # 工作者配置
```

---

## 🎨 **前端系統結構**

### **前端總體架構**
```
frontend/                                    # 前端應用程序
├── 📁 app.py                              # Flask 主應用
├── 📁 email/                              # 郵件模組
├── 📁 eqc/                                # EQC 工作流模組
├── 📁 monitoring/                         # 監控界面模組
├── 📁 pts_renamer/                        # PTS 重命名界面
└── 📁 shared/                             # 共享前端組件
```

### **郵件前端模組** (`frontend/email/`)
```
email/                                       # 郵件前端模組
├── routes/                                 # Flask 路由
│   ├── email_routes.py                    # 郵件路由
│   └── parser_api.py                      # 解析器 API
├── templates/                             # HTML 模板
│   ├── inbox.html                         # 收件箱頁面
│   ├── inbox_new.html                     # 新版收件箱
│   ├── email_detail.html                 # 郵件詳情頁面
│   └── components/                        # 模板組件
│       ├── email_list_item.html           # 郵件列表項
│       └── attachment_viewer.html         # 附件查看器
├── static/                                # 靜態資源
│   ├── css/                               # 樣式文件
│   │   ├── inbox.css                      # 收件箱樣式
│   │   ├── email_detail.css               # 詳情頁樣式
│   │   └── components/                    # 組件樣式
│   ├── js/                                # JavaScript 文件
│   │   ├── email/                         # 郵件相關 JS
│   │   │   ├── email-list-manager.js      # 郵件列表管理
│   │   │   ├── email-operations.js        # 郵件操作
│   │   │   └── email-parser-ui.js         # 解析器 UI
│   │   ├── utils/                         # 工具函數
│   │   │   ├── api-client.js              # API 客戶端
│   │   │   ├── date-utils.js              # 日期工具
│   │   │   └── validation.js              # 驗證工具
│   │   └── components/                    # JavaScript 組件
│   │       ├── email-filter.js            # 郵件過濾器
│   │       └── attachment-handler.js      # 附件處理器
│   └── images/                            # 圖片資源
│       ├── icons/                         # 圖標
│       └── vendor-logos/                  # 廠商標誌
└── forms/                                 # 表單處理
    ├── email_form.py                      # 郵件表單
    └── search_form.py                     # 搜索表單
```

### **Vue.js 遷移結構** (`frontend/vue/` - 規劃中)
```
vue/                                         # Vue.js 3 應用 (遷移目標)
├── src/                                    # 源代碼
│   ├── components/                         # Vue 組件
│   │   ├── email/                         # 郵件組件
│   │   │   ├── EmailList.vue              # 郵件列表
│   │   │   ├── EmailDetail.vue            # 郵件詳情
│   │   │   ├── EmailFilter.vue            # 郵件篩選
│   │   │   └── AttachmentViewer.vue       # 附件查看器
│   │   ├── common/                        # 公共組件
│   │   │   ├── BaseButton.vue             # 基礎按鈕
│   │   │   ├── BaseModal.vue              # 基礎彈窗
│   │   │   └── LoadingSpinner.vue         # 載入動畫
│   │   └── layout/                        # 布局組件
│   │       ├── AppHeader.vue              # 應用標頭
│   │       ├── AppSidebar.vue             # 側邊欄
│   │       └── AppFooter.vue              # 頁腳
│   ├── views/                             # 頁面組件
│   │   ├── EmailInboxView.vue             # 收件箱頁面
│   │   ├── EmailDetailView.vue            # 詳情頁面
│   │   └── AnalyticsView.vue              # 分析頁面
│   ├── stores/                            # Pinia 狀態管理
│   │   ├── emailStore.ts                  # 郵件狀態
│   │   ├── userStore.ts                   # 用戶狀態
│   │   └── settingsStore.ts               # 設置狀態
│   ├── composables/                       # 組合式函數
│   │   ├── useEmailOperations.ts          # 郵件操作
│   │   ├── useNotification.ts             # 通知系統
│   │   └── useWebSocket.ts                # WebSocket
│   ├── api/                               # API 服務
│   │   ├── emailAPI.ts                    # 郵件 API
│   │   ├── userAPI.ts                     # 用戶 API
│   │   └── baseAPI.ts                     # 基礎 API
│   ├── types/                             # TypeScript 類型
│   │   ├── email.ts                       # 郵件類型
│   │   ├── user.ts                        # 用戶類型
│   │   └── api.ts                         # API 類型
│   ├── utils/                             # 工具函數
│   │   ├── dateUtils.ts                   # 日期工具
│   │   ├── validationUtils.ts             # 驗證工具
│   │   └── formatUtils.ts                 # 格式化工具
│   ├── router/                            # Vue Router
│   │   └── index.ts                       # 路由配置
│   ├── assets/                            # 靜態資源
│   │   ├── styles/                        # 樣式文件
│   │   ├── images/                        # 圖片資源
│   │   └── fonts/                         # 字體資源
│   ├── App.vue                            # 根組件
│   └── main.ts                            # 入口文件
├── public/                                # 公共資源
│   ├── index.html                         # HTML 模板
│   └── favicon.ico                        # 網站圖標
├── tests/                                 # 前端測試
│   ├── unit/                              # 單元測試
│   ├── integration/                       # 集成測試
│   └── e2e/                               # 端到端測試
├── package.json                           # 依賴配置
├── vite.config.ts                         # Vite 配置
├── tsconfig.json                          # TypeScript 配置
└── .eslintrc.js                           # ESLint 配置
```

---

## 🧪 **測試系統結構**

### **測試目錄組織**
```
tests/                                       # 測試文件 (173+ 測試文件)
├── unit/                                   # 單元測試 (4800+ 測試用例)
│   ├── backend/                           # 後端單元測試
│   │   ├── email/                         # 郵件模組測試
│   │   │   ├── test_email_models.py       # 郵件模型測試
│   │   │   ├── test_outlook_adapter.py    # Outlook 適配器測試
│   │   │   └── parsers/                   # 解析器測試
│   │   │       ├── test_gtk_parser.py     # GTK 解析器測試
│   │   │       ├── test_etd_parser.py     # ETD 解析器測試
│   │   │       └── test_llm_parser.py     # LLM 解析器測試
│   │   ├── shared/                        # 共享模組測試
│   │   │   ├── test_database.py           # 數據庫測試
│   │   │   ├── test_email_sync_service.py # 同步服務測試
│   │   │   └── test_notification.py       # 通知服務測試
│   │   └── monitoring/                    # 監控模組測試
│   │       ├── test_collectors.py         # 收集器測試
│   │       └── test_dashboard.py          # 儀表板測試
│   └── frontend/                          # 前端單元測試
│       ├── components/                    # 組件測試
│       ├── utils/                         # 工具函數測試
│       └── api/                           # API 測試
├── integration/                           # 集成測試
│   ├── test_email_processing_flow.py     # 郵件處理流程測試
│   ├── test_api_endpoints.py             # API 端點測試
│   ├── test_database_operations.py       # 數據庫操作測試
│   └── test_task_queue.py                # 任務隊列測試
├── e2e/                                   # 端到端測試 (Playwright)
│   ├── test_email_inbox.py               # 收件箱測試
│   ├── test_email_processing.py          # 郵件處理測試
│   ├── test_user_workflows.py            # 用戶工作流測試
│   └── utils/                             # 測試工具
│       ├── page_objects/                  # 頁面對象模式
│       └── test_helpers.py                # 測試助手
├── fixtures/                              # 測試數據
│   ├── email_samples/                     # 範例郵件
│   ├── vendor_data/                       # 廠商數據
│   └── test_databases/                    # 測試數據庫
├── conftest.py                            # Pytest 配置
├── pytest.ini                            # Pytest 設置
└── coverage.xml                           # 覆蓋率報告
```

---

## 🚀 **部署系統結構**

### **部署配置和腳本**
```
deployment/                                  # 部署相關文件
├── docker/                                # Docker 配置
│   ├── Dockerfile                         # 主應用 Docker 文件
│   ├── docker-compose.yml                # 服務編排配置
│   ├── .dockerignore                      # Docker 忽略文件
│   └── config/                            # 容器配置
│       ├── app/                           # 應用配置
│       ├── nginx/                         # Nginx 配置
│       │   ├── nginx.conf                 # 主配置文件
│       │   └── conf.d/                    # 虛擬主機配置
│       └── monitoring/                    # 監控配置
│           ├── prometheus.yml             # Prometheus 配置
│           └── grafana/                   # Grafana 配置
│               ├── provisioning/          # 自動配置
│               └── dashboards/            # 儀表板定義
├── scripts/                               # 部署腳本
│   ├── deploy.sh                          # 部署腳本
│   ├── backup.sh                          # 備份腳本
│   ├── restore.sh                         # 恢復腳本
│   └── maintenance.sh                     # 維護腳本
├── kubernetes/                            # Kubernetes 配置 (規劃)
│   ├── namespace.yaml                     # 命名空間
│   ├── deployment.yaml                    # 部署配置
│   ├── service.yaml                       # 服務配置
│   ├── ingress.yaml                       # 入口配置
│   └── configmap.yaml                     # 配置映射
└── ansible/                               # Ansible 自動化 (規劃)
    ├── playbooks/                         # 劇本
    ├── roles/                             # 角色
    └── inventory/                         # 清單
```

---

## 📚 **文檔系統結構**

### **文檔組織架構**
```
docs/                                        # 專案文檔
├── architecture/                           # 架構文檔
│   ├── overall-system-architecture.md     # 整體系統架構
│   ├── coding-standards.md                # 編碼標準
│   ├── tech-stack.md                      # 技術堆疊
│   └── project-structure.md               # 專案結構 (本文檔)
├── prd/                                    # 產品需求文檔
│   ├── overall-product-roadmap.md         # 整體產品路線圖
│   └── email-download-tracking-prd.md     # 郵件下載追蹤 PRD
├── technical-specs/                       # 技術規格
│   └── email-download-tracking-spec.md    # 郵件下載追蹤技術規格
├── stories/                               # 用戶故事
│   ├── email-download-tracking-story-1-database-structure.md
│   ├── pts-renamer-sprint-planning.md
│   └── story-pts-*.md                     # PTS 相關故事
├── qa/                                     # 品質保證文檔
│   ├── test-plans/                        # 測試計劃
│   ├── test-cases/                        # 測試用例
│   └── bug-reports/                       # 缺陷報告
├── api/                                    # API 文檔
│   ├── email-api.md                       # 郵件 API
│   ├── monitoring-api.md                  # 監控 API
│   └── openapi.yaml                       # OpenAPI 規格
├── deployment/                            # 部署文檔
│   ├── installation-guide.md             # 安裝指南
│   ├── configuration-guide.md            # 配置指南
│   └── troubleshooting.md                 # 故障排除
├── user-guides/                           # 用戶指南
│   ├── email-processing-guide.md         # 郵件處理指南
│   ├── dashboard-guide.md                 # 儀表板指南
│   └── admin-guide.md                     # 管理員指南
└── old/                                   # 歷史文檔
    ├── legacy-docs/                       # 遺留文檔
    └── archived/                          # 歸檔文檔
```

---

## 🔧 **工具和配置文件**

### **專案配置文件**
```
根目錄配置文件:
├── 📄 .gitignore                          # Git 忽略規則
├── 📄 .env.example                        # 環境變數範例
├── 📄 .env.local                          # 本地環境變數
├── 📄 .pre-commit-config.yaml             # 預提交鉤子
├── 📄 pyproject.toml                      # Python 專案配置
├── 📄 requirements.txt                    # Python 依賴
├── 📄 requirements.pts.txt                # PTS 特定依賴
├── 📄 pytest.ini                          # Pytest 配置
├── 📄 mypy.ini                            # MyPy 類型檢查
├── 📄 .flake8                             # Flake8 配置
├── 📄 .black                              # Black 格式化配置
├── 📄 package.json                        # Node.js 依賴 (前端)
├── 📄 tsconfig.json                       # TypeScript 配置
├── 📄 vite.config.ts                      # Vite 構建配置
├── 📄 .eslintrc.js                        # ESLint 配置
└── 📄 .prettierrc                         # Prettier 配置
```

### **數據和緩存文件**
```
運行時文件:
├── 📁 logs/                               # 運行日誌
├── 📁 temp/                               # 臨時文件
├── 📁 data/                               # 數據文件
├── 📁 cache/                              # 緩存文件
├── 📄 app.db                              # SQLite 數據庫 (開發)
├── 📄 project_info.json                   # 專案元數據
└── 📄 line_notifications.json             # Line 通知配置
```

---

## 🎯 **模組依賴關係**

### **後端模組依賴圖**
```mermaid
graph TD
    A[frontend/app.py] --> B[backend/email/]
    A --> C[backend/shared/]
    A --> D[backend/monitoring/]
    
    B --> E[backend/email/adapters/]
    B --> F[backend/email/parsers/]
    B --> G[backend/email/models/]
    
    E --> H[backend/shared/infrastructure/]
    F --> H
    G --> H
    
    I[backend/tasks/] --> B
    I --> C
    I --> J[Redis]
    
    C --> K[Database]
    C --> J
    
    D --> C
    D --> L[Prometheus]
    D --> M[Grafana]
```

### **前端模組依賴圖**
```mermaid
graph TD
    A[Vue.js App] --> B[Components]
    A --> C[Stores]
    A --> D[Router]
    
    B --> E[Email Components]
    B --> F[Common Components]
    B --> G[Layout Components]
    
    C --> H[Email Store]
    C --> I[User Store]
    C --> J[Settings Store]
    
    K[API Services] --> C
    K --> L[Backend APIs]
    
    M[Composables] --> B
    M --> C
```

---

## 📋 **開發工作流程**

### **文件創建和修改流程**
```yaml
新功能開發:
  1. 創建功能分支: feature/new-feature
  2. 後端開發:
     - 在 backend/email/ 添加新服務
     - 在 backend/shared/ 添加共享邏輯
     - 在 tests/ 添加測試用例
  3. 前端開發:
     - 在 frontend/email/ 添加路由和模板
     - 在 frontend/vue/ 添加 Vue 組件 (遷移後)
  4. 文檔更新:
     - 更新 docs/api/ API 文檔
     - 更新 docs/user-guides/ 用戶指南
  5. 測試和驗證:
     - 運行單元測試和集成測試
     - 執行端到端測試
  6. 部署準備:
     - 更新 deployment/ 配置
     - 測試 Docker 構建

Bug 修復流程:
  1. 創建修復分支: fix/bug-description
  2. 定位問題文件:
     - 後端: backend/相關模組/
     - 前端: frontend/相關模組/
  3. 添加回歸測試:
     - 在 tests/ 相應目錄添加測試
  4. 修復問題並驗證
  5. 更新 CHANGELOG.md
```

### **文件命名規範**
```yaml
Python 文件:
  - 模組: snake_case.py
  - 測試: test_module_name.py
  - 配置: config_name.py

前端文件:
  - Vue 組件: PascalCase.vue
  - JavaScript: camelCase.js
  - CSS: kebab-case.css
  - HTML 模板: kebab-case.html

文檔文件:
  - Markdown: kebab-case.md
  - 配置: UPPERCASE.yml
```

---

## 🔍 **模組職責說明**

### **核心業務模組**
```yaml
backend/email/:
  職責: 郵件處理核心業務邏輯
  包含: 郵件解析、廠商識別、數據提取
  依賴: backend/shared/

backend/shared/:
  職責: 共享基礎設施和工具
  包含: 數據庫、緩存、通知、工具函數
  依賴: 外部服務 (Redis, Database)

backend/monitoring/:
  職責: 系統監控和可觀測性
  包含: 指標收集、告警、儀表板
  依賴: backend/shared/

backend/tasks/:
  職責: 異步任務處理
  包含: Dramatiq 任務、工作者、中間件
  依賴: Redis, backend/email/
```

### **界面和展示模組**
```yaml
frontend/email/:
  職責: 郵件相關用戶界面
  包含: 收件箱、詳情頁、操作界面
  技術: Flask + Jinja2 + JavaScript

frontend/monitoring/:
  職責: 監控界面和儀表板
  包含: 系統狀態、性能指標、告警
  技術: Flask + WebSocket + Chart.js

frontend/vue/:
  職責: 現代化前端界面 (遷移目標)
  包含: Vue 3 組件、TypeScript、Pinia
  技術: Vue 3 + TypeScript + Vite
```

### **支援系統模組**
```yaml
tests/:
  職責: 品質保證和自動化測試
  包含: 單元測試、集成測試、E2E 測試
  工具: Pytest, Playwright, Coverage

deployment/:
  職責: 部署和運維自動化
  包含: Docker 配置、K8s 部署、腳本
  工具: Docker, Kubernetes, Ansible

docs/:
  職責: 專案文檔和知識管理
  包含: 架構文檔、API 文檔、用戶指南
  格式: Markdown, OpenAPI, Mermaid
```

---

## 🛠️ **開發環境設置**

### **本地開發環境**
```bash
# 1. 克隆專案
git clone <repository-url>
cd outlook_summary

# 2. 設置 Python 環境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# 3. 設置前端環境 (Vue 遷移後)
cd frontend/vue
npm install

# 4. 配置環境變數
cp .env.example .env.local
# 編輯 .env.local 設置數據庫和 API 配置

# 5. 初始化數據庫
python -c "from backend.shared.infrastructure.adapters.database import init_db; init_db()"

# 6. 啟動開發服務器
python start_integrated_services.py
```

### **Docker 開發環境**
```bash
# 1. 構建和啟動服務
docker-compose up -d

# 2. 查看服務狀態
docker-compose ps

# 3. 查看日誌
docker-compose logs -f outlook-summary

# 4. 進入容器調試
docker-compose exec outlook-summary bash
```

---

## 📈 **專案統計和指標**

### **代碼統計**
```yaml
總體規模:
  - 總文件數: 1000+ 文件
  - Python 文件: 473+ 文件
  - 前端文件: 200+ 文件
  - 測試文件: 173+ 文件
  - 文檔文件: 50+ 文件

代碼行數:
  - Python 代碼: 50,000+ 行
  - JavaScript 代碼: 15,000+ 行
  - CSS 代碼: 5,000+ 行
  - 測試代碼: 25,000+ 行

測試覆蓋率:
  - 單元測試: 4,800+ 測試用例
  - 覆蓋率目標: ≥ 80%
  - 關鍵模組覆蓋率: ≥ 90%
```

### **模組複雜度**
```yaml
高複雜度模組:
  - backend/email/parsers/: 12個廠商解析器
  - backend/shared/infrastructure/: 基礎設施抽象
  - backend/monitoring/: 監控和指標收集

中複雜度模組:
  - frontend/email/: 用戶界面和互動
  - backend/tasks/: 異步任務處理
  - tests/: 測試案例和驗證

低複雜度模組:
  - docs/: 文檔和說明
  - deployment/: 配置和腳本
  - utils/: 工具函數和助手
```

---

## 🔮 **未來擴展規劃**

### **模組擴展計劃**
```yaml
短期 (6個月):
  - 完成 Vue.js 前端遷移
  - 擴展 AI 解析器功能
  - 增強監控和告警系統

中期 (12個月):
  - 微服務架構拆分
  - Kubernetes 生產部署
  - API 網關和服務網格

長期 (24個月):
  - 多租戶架構支援
  - 邊緣計算集成
  - 國際化和本地化
```

### **新模組規劃**
```yaml
計劃新增模組:
  - backend/analytics/: 數據分析和商業智能
  - backend/security/: 安全和審計模組
  - backend/integration/: 第三方集成模組
  - frontend/mobile/: 移動端應用
  - deployment/cloud/: 雲原生部署
```

---

## 📚 **參考資料**

### **相關文檔**
- [整體系統架構](./overall-system-architecture.md)
- [編碼標準](./coding-standards.md)
- [技術堆疊](./tech-stack.md)
- [產品路線圖](../prd/overall-product-roadmap.md)

### **開發指南**
- [Python 開發指南](https://docs.python.org/3/tutorial/)
- [Vue.js 開發指南](https://vuejs.org/guide/)
- [Docker 開發指南](https://docs.docker.com/get-started/)
- [測試最佳實踐](https://docs.pytest.org/en/stable/goodpractices.html)

---

**📅 最後更新**: 2025-08-19  
**👤 維護者**: Development Team  
**🔄 審核週期**: 每月  
**📧 聯絡**: <EMAIL>