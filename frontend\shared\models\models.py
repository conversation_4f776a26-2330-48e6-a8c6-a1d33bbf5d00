"""
FT-EQC 分組 API 資料模型
使用 Pydantic 進行資料驗證和序列化
"""

from typing import List, Tuple, Optional, Any, Dict, Union
from pydantic import BaseModel, Field, field_validator
from datetime import datetime


class FTEQCGroupingRequest(BaseModel):
    """FT-EQC 分組處理請求模型"""
    folder_path: str = Field(
        ...,
        description="要處理的資料夾路徑",
        min_length=1,
        example="/path/to/csv/files"
    )
    
    @field_validator('folder_path')
    @classmethod
    def validate_folder_path(cls, v):
        """驗證資料夾路徑不能為空"""
        if not v or v.strip() == "":
            raise ValueError("資料夾路徑不能為空")
        return v.strip()


class StatisticsData(BaseModel):
    """統計資料模型"""
    total_csv_files: int = Field(
        ...,
        description="總 CSV 檔案數",
        ge=0
    )
    ft_files_count: int = Field(
        ...,
        description="FT 檔案數量",
        ge=0
    )
    eqc_files_count: int = Field(
        ...,
        description="EQC 檔案數量",
        ge=0
    )
    successful_matches: int = Field(
        ...,
        description="成功配對數量",
        ge=0
    )
    eqc_rt_count: int = Field(
        ...,
        description="EQC RT 檔案數量 (未配對的 EQC 檔案)",
        ge=0
    )
    matching_rate: float = Field(
        ...,
        description="配對成功率",
        ge=0.0,
        le=1.0
    )
    processing_timestamp: str = Field(
        ...,
        description="處理時間戳記",
        example="2024-01-01T12:00:00"
    )


class FailDetail(BaseModel):
    """失敗檔案詳細資訊模型"""
    file_path: str = Field(
        ...,
        description="失敗檔案路徑"
    )
    fail_count: int = Field(
        ...,
        description="該檔案的失敗數量",
        ge=0
    )
    first_fail_row: int = Field(
        ...,
        description="第一個失敗行的A欄值",
        ge=0
    )


class EQCFailResult(BaseModel):
    """Online EQC 失敗檔案結果模型"""
    fail_count: int = Field(
        ...,
        description="失敗檔案數量",
        ge=0
    )
    fail_files: List[str] = Field(
        default_factory=list,
        description="失敗檔案列表"
    )
    analysis_files: List[str] = Field(
        default_factory=list,
        description="分析檔案列表"
    )
    fail_details: List[FailDetail] = Field(
        default_factory=list,
        description="失敗檔案詳細資訊列表"
    )
    processing_timestamp: str = Field(
        ...,
        description="處理時間戳記",
        example="2024-01-01T12:00:00"
    )


class MatchedPair(BaseModel):
    """配對檔案模型"""
    ft_file: str = Field(..., description="FT 檔案名稱")
    eqc_file: str = Field(..., description="EQC 檔案名稱")


class GroupingData(BaseModel):
    """分組資料模型"""
    matched_pairs: List[MatchedPair] = Field(
        ...,
        description="成功配對的檔案列表",
        example=[{"ft_file": "ft_file1.csv", "eqc_file": "eqc_file1.csv"}]
    )
    unmatched_eqc: List[str] = Field(
        ...,
        description="未配對的 EQC 檔案列表 (EQC RT)",
        example=["eqc_rt_file1.csv"]
    )
    statistics: StatisticsData = Field(
        ...,
        description="處理統計資料"
    )
    eqc_fail_result: Optional[EQCFailResult] = Field(
        None,
        description="Online EQC 失敗檔案分析結果"
    )


class FTEQCGroupingResponse(BaseModel):
    """FT-EQC 分組處理回應模型"""
    status: str = Field(
        ...,
        description="處理狀態",
        pattern="^(success|error)$",
        example="success"
    )
    message: str = Field(
        ...,
        description="回應訊息",
        example="FT-EQC 分組處理完成"
    )
    data: Optional[GroupingData] = Field(
        None,
        description="分組結果資料 (只有成功時才有)"
    )
    error_details: Optional[Dict[str, Any]] = Field(
        None,
        description="錯誤詳細資訊 (只有失敗時才有)"
    )


class HealthCheckResponse(BaseModel):
    """健康檢查回應模型"""
    status: str = Field(
        ...,
        description="服務狀態",
        example="healthy"
    )
    timestamp: str = Field(
        ...,
        description="檢查時間",
        example="2024-01-01T12:00:00"
    )
    version: str = Field(
        ...,
        description="API 版本",
        example="1.0.0"
    )


class ErrorResponse(BaseModel):
    """通用錯誤回應模型"""
    status: str = Field(
        default="error",
        description="狀態標識",
        pattern="^error$"
    )
    message: str = Field(
        ...,
        description="錯誤訊息",
        example="處理過程中發生錯誤"
    )
    error_code: Optional[str] = Field(
        None,
        description="錯誤代碼",
        example="FOLDER_NOT_FOUND"
    )
    details: Optional[Dict[str, Any]] = Field(
        None,
        description="錯誤詳細資訊"
    )


class EQCBin1ScanRequest(BaseModel):
    """EQC BIN=1 掃描請求模型"""
    folder_path: str = Field(
        ...,
        description="要掃描的資料夾路徑",
        min_length=1,
        example="/path/to/csv/files"
    )
    
    @field_validator('folder_path')
    @classmethod
    def validate_folder_path(cls, v):
        """驗證資料夾路徑不能為空"""
        if not v or v.strip() == "":
            raise ValueError("資料夾路徑不能為空")
        return v.strip()


class EQCBin1Info(BaseModel):
    """EQC BIN=1 詳細資訊模型"""
    source_file: str = Field(
        ...,
        description="來源檔案路徑"
    )
    total_lines: int = Field(
        ...,
        description="總行數",
        ge=0
    )
    has_header: bool = Field(
        ...,
        description="是否包含標頭"
    )
    bin1_line_content: Optional[str] = Field(
        None,
        description="BIN=1 資料行內容 (前100字元)"
    )


class EQCBin1ScanData(BaseModel):
    """EQC BIN=1 掃描結果資料模型"""
    has_bin1_data: bool = Field(
        ...,
        description="是否找到 BIN=1 資料"
    )
    total_eqc_files: int = Field(
        ...,
        description="掃描的 EQC 檔案總數",
        ge=0
    )
    bin1_info: Optional[EQCBin1Info] = Field(
        None,
        description="BIN=1 詳細資訊 (找到時才有)"
    )
    scan_timestamp: str = Field(
        ...,
        description="掃描時間戳記",
        example="2024-01-01T12:00:00"
    )


class EQCBin1ScanResponse(BaseModel):
    """EQC BIN=1 掃描回應模型"""
    status: str = Field(
        ...,
        description="掃描狀態",
        pattern="^(success|error)$",
        example="success"
    )
    message: str = Field(
        ...,
        description="回應訊息",
        example="EQC BIN=1 掃描完成"
    )
    data: Optional[EQCBin1ScanData] = Field(
        None,
        description="掃描結果資料 (只有成功時才有)"
    )
    error_details: Optional[Dict[str, Any]] = Field(
        None,
        description="錯誤詳細資訊 (只有失敗時才有)"
    )


class OnlineEQCProcessRequest(BaseModel):
    """Online EQC 完整處理請求模型"""
    folder_path: str = Field(
        ...,
        description="要處理的資料夾路徑",
        min_length=1,
        example="/path/to/csv/files"
    )
    processing_mode: str = Field(
        default="1",
        description="處理模式: 1=EQC BIN1統計, 2=EQCTOTALDATA生成, 3=同時執行",
        pattern="^[123]$"
    )
    
    @field_validator("folder_path")
    @classmethod
    def validate_folder_path(cls, v):
        """驗證資料夾路徑不能為空"""
        if not v or v.strip() == "":
            raise ValueError("資料夾路徑不能為空")
        return v.strip()


class OnlineEQCProcessData(BaseModel):
    """Online EQC 處理結果資料模型"""
    processing_mode: str = Field(
        ...,
        description="實際執行的處理模式"
    )
    eqc_bin1_file: Optional[str] = Field(
        None,
        description="EQC BIN1 統計檔案路徑"
    )
    eqc_total_file: Optional[str] = Field(
        None,
        description="EQCTOTALDATA.csv 檔案路徑"
    )
    eqc_raw_file: Optional[str] = Field(
        None,
        description="EQCTOTALDATA_RAW.csv 檔案路徑"
    )
    excel_file: Optional[str] = Field(
        None,
        description="Excel 超連結檔案路徑"
    )
    statistics: Dict[str, Any] = Field(
        default_factory=dict,
        description="處理統計資訊"
    )
    hyperlink_count: int = Field(
        default=0,
        description="生成的超連結數量",
        ge=0
    )
    processing_time_seconds: float = Field(
        default=0.0,
        description="處理時間（秒）",
        ge=0
    )
    eqctotaldata_download_path: Optional[str] = Field(
        None,
        description="EQCTOTALDATA.xlsx 完整下載路徑 (Windows格式)"
    )


class FTSummaryProcessRequest(BaseModel):
    """FT Summary 批量處理請求模型"""
    folder_path: str = Field(
        ...,
        description="要處理的資料夾路徑",
        min_length=1,
        example="D:/project/python/outlook_summary/doc/20250523"
    )
    force_overwrite: bool = Field(
        default=False,
        description="是否強制覆寫已存在的檔案"
    )
    processing_mode: str = Field(
        default="full",
        description="處理模式：'full'=完整模式(Excel+Summary), 'summary_only'=快速模式(僅Summary)",
        pattern="^(full|summary_only)$"
    )
    
    @field_validator('folder_path')
    @classmethod
    def validate_folder_path(cls, v):
        """驗證資料夾路徑不能為空"""
        if not v or v.strip() == "":
            raise ValueError("資料夾路徑不能為空")
        return v.strip()




class OnlineEQCProcessResponse(BaseModel):
    """Online EQC 完整處理回應模型"""
    status: str = Field(
        ...,
        description="處理狀態",
        pattern="^(success|error)$",
        example="success"
    )
    message: str = Field(
        ...,
        description="回應訊息",
        example="Online EQC 處理完成"
    )
    processing_time: float = Field(
        default=0.0,
        description="處理時間（秒）",
        ge=0.0
    )
    data: Optional[OnlineEQCProcessData] = Field(
        None,
        description="處理結果資料 (只有成功時才有)"
    )
    error_details: Optional[Dict[str, Any]] = Field(
        None,
        description="錯誤詳細資訊 (只有失敗時才有)"
    )


class EQCInsEqcRtData2Request(BaseModel):
    """EQC InsEqcRtData2 處理請求模型"""
    folder_path: str = Field(
        ...,
        description="要處理的資料夾路徑",
        min_length=1,
        example="/path/to/eqc/data"
    )
    
    @field_validator('folder_path')
    @classmethod
    def validate_folder_path(cls, v):
        """驗證資料夾路徑不能為空"""
        if not v or v.strip() == "":
            raise ValueError("資料夾路徑不能為空")
        return v.strip()


class EQCInsEqcRtData2Data(BaseModel):
    """EQC InsEqcRtData2 處理資料模型"""
    dual_search_success: bool = Field(
        ...,
        description="雙重搜尋是否成功"
    )
    search_method: str = Field(
        ...,
        description="使用的搜尋方法"
    )
    match_rate: str = Field(
        ...,
        description="匹配率"
    )
    code_positions_count: int = Field(
        ...,
        description="CODE 位置數量",
        ge=0
    )
    fail_rows_count: int = Field(
        ...,
        description="fail 資料行數",
        ge=0
    )
    rt_data_rows_inserted: int = Field(
        ...,
        description="插入的 RT 資料行數",
        ge=0
    )
    total_rows_before: int = Field(
        ...,
        description="處理前總行數",
        ge=0
    )
    total_rows_after: int = Field(
        ...,
        description="處理後總行數",
        ge=0
    )
    processing_timestamp: str = Field(
        ...,
        description="處理時間戳"
    )


class EQCInsEqcRtData2Response(BaseModel):
    """EQC InsEqcRtData2 處理回應模型"""
    success: bool = Field(
        ...,
        description="處理是否成功"
    )
    message: str = Field(
        ...,
        description="處理訊息"
    )
    data: Optional[EQCInsEqcRtData2Data] = Field(
        None,
        description="處理結果資料"
    )
    timestamp: str = Field(
        ...,
        description="回應時間戳"
    )
    processing_time_ms: int = Field(
        ...,
        description="處理時間（毫秒）",
        ge=0
    )


class CodeRegion(BaseModel):
    """CODE 區間模型"""
    start_column: int = Field(
        ...,
        description="起始欄位號碼",
        ge=1,
        example=298
    )
    end_column: int = Field(
        ...,
        description="結束欄位號碼", 
        ge=1,
        example=335
    )
    
    @field_validator('end_column')
    @classmethod
    def validate_end_after_start(cls, v, info):
        """驗證結束欄位必須大於等於起始欄位"""
        if hasattr(info, 'data') and 'start_column' in info.data and v < info.data['start_column']:
            raise ValueError("結束欄位必須大於等於起始欄位")
        return v


class EQCStandardProcessRequest(BaseModel):
    """EQC 標準處理請求模型 - Step 1-2-3"""
    folder_path: str = Field(
        ...,
        description="要處理的資料夾路徑（包含 EQCTOTALDATA.csv）",
        min_length=1,
        example="doc/20250523"
    )
    include_step123: bool = Field(
        default=True,
        description="是否執行 Step 1-2-3 (ALL0移動 + FAIL檢測)"
    )
    main_region: Optional[CodeRegion] = Field(
        None,
        description="使用者指定的主要 CODE 區間"
    )
    backup_region: Optional[CodeRegion] = Field(
        None,
        description="使用者指定的備用 CODE 區間"
    )
    
    @field_validator('folder_path')
    @classmethod
    def validate_folder_path(cls, v):
        """驗證資料夾路徑不能為空"""
        if not v or v.strip() == "":
            raise ValueError("資料夾路徑不能為空")
        return v.strip()


class EQCStandardProcessData(BaseModel):
    """EQC 標準處理結果資料模型"""
    # 區間檢測結果
    main_region_start: int = Field(
        description="主要區間起始欄位",
        example=298
    )
    main_region_end: int = Field(
        description="主要區間結束欄位",
        example=335
    )
    backup_region_start: Optional[int] = Field(
        None,
        description="備用區間起始欄位"
    )
    backup_region_end: Optional[int] = Field(
        None,
        description="備用區間結束欄位"
    )
    
    # 雙重搜尋結果
    search_method: str = Field(
        description="搜尋方法",
        example="main_region_complete"
    )
    match_rate: str = Field(
        description="匹配率",
        example="100%"
    )
    
    # Step 1-2 結果
    all0_moved_count: int = Field(
        default=0,
        description="ALL0 移動行數",
        ge=0
    )
    
    # Step 3 結果
    online_eqc_fail_count: int = Field(
        default=0,
        description="Online EQC FAIL 行數",
        ge=0
    )
    step3_debug_log_file: Optional[str] = Field(
        None,
        description="Step 3 DEBUG LOG 檔案路徑"
    )
    
    # 總體結果
    total_rows_after: int = Field(
        description="處理後總行數",
        ge=0
    )
    processing_stage: str = Field(
        description="處理階段",
        example="step_1_2_3_complete"
    )
    execution_time: float = Field(
        default=0.0,
        description="執行時間（秒）",
        ge=0.0
    )


class EQCStandardProcessResponse(BaseModel):
    """EQC 標準處理回應模型"""
    status: str = Field(
        ...,
        description="處理狀態",
        pattern="^(success|error)$"
    )
    message: str = Field(
        ...,
        description="處理訊息",
        example="EQC 標準處理完成"
    )
    data: Optional[EQCStandardProcessData] = Field(
        None,
        description="處理結果資料 (只有成功時才有)"
    )
    error_details: Optional[Dict[str, Any]] = Field(
        None,
        description="錯誤詳細資訊 (只有失敗時才有)"
    )


class EQCAdvancedProcessResponse(BaseModel):
    """EQC 進階處理回應模型 (雙階段處理)"""
    status: str = Field(
        ...,
        description="處理狀態",
        pattern="^(success|error)$"
    )
    message: str = Field(
        ...,
        description="處理訊息",
        example="EQC 進階處理完成 (雙階段)"
    )
    data: Optional[EQCStandardProcessData] = Field(
        None,
        description="處理結果資料 (與標準處理相同結構)"
    )
    processing_time: float = Field(
        ...,
        description="處理耗時 (秒)",
        ge=0.0
    )
    code_regions: Optional[Dict[str, Any]] = Field(
        None,
        description="使用者指定的 CODE 區間設定",
        example={"main_start": 298, "main_end": 335, "backup_start": 400, "backup_end": 450}
    )
    api_version: str = Field(
        default="2.0.0",
        description="API版本號"
    )
    timestamp: str = Field(
        ...,
        description="處理時間戳"
    )


class EQCRealDataAnalysisResponse(BaseModel):
    """EQC 真實數據分析回應模型"""
    status: str = Field(
        ...,
        description="分析狀態",
        pattern="^(success|error)$"
    )
    online_eqc_fail: int = Field(
        ...,
        description="Online EQC FAIL 數量",
        ge=0
    )
    eqc_rt_pass: int = Field(
        ...,
        description="EQC RT PASS 數量", 
        ge=0
    )
    match_rate: str = Field(
        ...,
        description="匹配率",
        example="100%"
    )
    total_matches: int = Field(
        ...,
        description="總匹配數",
        ge=0
    )
    total_rows: int = Field(
        ...,
        description="總行數",
        ge=0
    )
    matched_count: int = Field(
        ...,
        description="匹配成功數量",
        ge=0
    )
    search_method: str = Field(
        ...,
        description="搜尋方法",
        example="主要區間"
    )
    search_status: str = Field(
        ...,
        description="搜尋狀態",
        example="成功"
    )
    folder_path: str = Field(
        ...,
        description="分析的資料夾路徑"
    )
    summary_data: Optional[Dict[str, Any]] = Field(
        None,
        description="摘要統計數據"
    )
    api_version: str = Field(
        default="2.0.0",
        description="API版本號"
    )
    timestamp: str = Field(
        ...,
        description="分析時間戳"
    )


class ReportReadResponse(BaseModel):
    """報告讀取回應模型"""
    status: str = Field(
        ...,
        description="讀取狀態",
        pattern="^(success|error)$"
    )
    content: str = Field(
        ...,
        description="報告內容"
    )
    file_name: str = Field(
        ...,
        description="檔案名稱"
    )
    api_version: str = Field(
        default="2.0.0", 
        description="API版本號"
    )
    timestamp: str = Field(
        ...,
        description="讀取時間戳"
    )


class FileExistsCheckResponse(BaseModel):
    """檔案存在檢查回應模型"""
    exists: bool = Field(
        ...,
        description="檔案是否存在"
    )
    file_path: str = Field(
        ...,
        description="檢查的檔案路徑"
    )
    api_version: str = Field(
        default="2.0.0",
        description="API版本號"
    )
    timestamp: str = Field(
        ...,
        description="檢查時間戳"
    )
    report_file: Optional[str] = Field(
        None,
        description="處理報告檔案路徑"
    )


class EQCStep5TestFlowRequest(BaseModel):
    """EQC Step 5 測試流程生成請求模型"""
    doc_directory: str = Field(
        ...,
        description="資料目錄路徑",
        example="doc/20250523"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "doc_directory": "doc/20250523"
            }
        }
    }


class EQCStep5TestFlowResponse(BaseModel):
    """EQC Step 5 測試流程生成響應模型"""
    status: str = Field(
        ...,
        description="處理狀態",
        pattern="^(success|error)$"
    )
    message: str = Field(
        ...,
        description="處理訊息",
        example="Step 5 測試流程生成完成"
    )
    output_file: Optional[str] = Field(
        None,
        description="輸出檔案路徑"
    )
    total_rows: Optional[int] = Field(
        None,
        description="總行數",
        ge=0
    )
    reordered_rows: Optional[int] = Field(
        None,
        description="重新排列的行數",
        ge=0
    )
    fail_mappings_count: Optional[int] = Field(
        None,
        description="FAIL 對應關係數量",
        ge=0
    )
    processing_stage: Optional[str] = Field(
        None,
        description="處理階段",
        example="step5_testflow_generation_complete"
    )
    error_message: Optional[str] = Field(
        None,
        description="錯誤訊息"
    )


# ==========================================
# 檔案上傳功能模型
# ==========================================

class UploadConfigResponse(BaseModel):
    """檔案上傳配置回應模型"""
    max_upload_size_mb: int = Field(
        ...,
        description="檔案上傳大小限制（MB）",
        ge=1
    )
    max_upload_size_bytes: int = Field(
        ...,
        description="檔案上傳大小限制（位元組）",
        ge=1
    )
    supported_formats: List[str] = Field(
        ...,
        description="支援的壓縮檔格式列表"
    )
    upload_temp_dir: str = Field(
        ...,
        description="上傳暫存目錄"
    )
    extract_temp_dir: str = Field(
        ...,
        description="解壓縮暫存目錄"
    )
    auto_cleanup_hours: int = Field(
        ...,
        description="自動清理時間（小時）",
        ge=1
    )


class UploadResult(BaseModel):
    """檔案上傳結果模型"""
    success: bool = Field(
        ...,
        description="上傳是否成功"
    )
    message: str = Field(
        ...,
        description="上傳結果訊息"
    )
    original_filename: Optional[str] = Field(
        None,
        description="原始檔案名稱"
    )
    upload_path: Optional[str] = Field(
        None,
        description="上傳檔案路徑"
    )
    file_size: Optional[int] = Field(
        None,
        description="檔案大小（位元組）",
        ge=0
    )
    file_size_mb: Optional[float] = Field(
        None,
        description="檔案大小（MB）",
        ge=0.0
    )


class ArchiveInfo(BaseModel):
    """壓縮檔資訊模型"""
    filename: str = Field(
        ...,
        description="檔案名稱"
    )
    format: str = Field(
        ...,
        description="壓縮檔格式"
    )
    size: int = Field(
        ...,
        description="檔案大小（位元組）",
        ge=0
    )
    size_mb: float = Field(
        ...,
        description="檔案大小（MB）",
        ge=0.0
    )
    file_count: Optional[Any] = Field(
        None,
        description="壓縮檔內檔案數量"
    )
    files: List[str] = Field(
        default=[],
        description="壓縮檔內檔案列表（前10個）"
    )


class ExtractionResult(BaseModel):
    """解壓縮結果模型"""
    success: bool = Field(
        ...,
        description="解壓縮是否成功"
    )
    message: str = Field(
        ...,
        description="解壓縮結果訊息"
    )
    archive_path: Optional[str] = Field(
        None,
        description="原始壓縮檔路徑"
    )
    extract_dir: Optional[str] = Field(
        None,
        description="解壓縮目錄路徑"
    )
    extracted_files: List[str] = Field(
        default=[],
        description="解壓縮的檔案列表"
    )
    file_count: Optional[int] = Field(
        None,
        description="解壓縮檔案數量",
        ge=0
    )


class UploadAndProcessRequest(BaseModel):
    """上傳並處理請求模型"""
    auto_process: bool = Field(
        default=False,
        description="是否自動處理"
    )
    processing_mode: str = Field(
        default="3",
        description="處理模式（1=EQC BIN1統計, 2=EQCTOTALDATA生成, 3=同時執行）",
        pattern="^[123]$"
    )
    code_regions: Optional[Dict[str, Optional[str]]] = Field(
        default=None,
        description="CODE區間設定"
    )


class UploadAndProcessResponse(BaseModel):
    """上傳並處理回應模型"""
    status: str = Field(
        ...,
        description="處理狀態",
        pattern="^(success|error)$"
    )
    message: str = Field(
        ...,
        description="處理訊息"
    )
    upload_result: Optional[UploadResult] = Field(
        None,
        description="上傳結果"
    )
    extraction_result: Optional[ExtractionResult] = Field(
        None,
        description="解壓縮結果"
    )
    processing_result: Optional[Dict[str, Any]] = Field(
        None,
        description="EQC處理結果"
    )
    extracted_folder_path: Optional[str] = Field(
        None,
        description="解壓縮後的資料夾路徑"
    )
    total_processing_time: Optional[float] = Field(
        None,
        description="總處理時間（秒）",
        ge=0.0
    )
    eqctotaldata_download_path: Optional[str] = Field(
        None,
        description="EQCTOTALDATA.xlsx 完整下載路徑 (Windows格式)"
    )


class FTSummaryProcessRequest(BaseModel):
    """FT Summary 批量處理請求模型"""
    folder_path: str = Field(
        ...,
        description="要處理的資料夾路徑",
        min_length=1,
        example="D:/project/python/outlook_summary/doc/20250523"
    )
    force_overwrite: bool = Field(
        default=False,
        description="是否強制覆寫已存在的檔案"
    )
    processing_mode: str = Field(
        default="full",
        description="處理模式：'full'=完整模式(Excel+Summary), 'summary_only'=快速模式(僅Summary)",
        pattern="^(full|summary_only)$"
    )
    
    @field_validator('folder_path')
    @classmethod
    def validate_folder_path(cls, v):
        """驗證資料夾路徑不能為空"""
        if not v or v.strip() == "":
            raise ValueError("資料夾路徑不能為空")
        return v.strip()


class FTSummaryProcessResponse(BaseModel):
    """FT Summary 批量處理回應模型"""
    status: str = Field(
        ...,
        description="處理狀態",
        pattern="^(success|error)$"
    )
    message: str = Field(
        ...,
        description="處理訊息"
    )
    total_files: int = Field(
        default=0,
        description="總檔案數",
        ge=0
    )
    processed_files: int = Field(
        default=0,
        description="成功處理檔案數",
        ge=0
    )
    skipped_files: int = Field(
        default=0,
        description="跳過檔案數",
        ge=0
    )
    failed_files: int = Field(
        default=0,
        description="失敗檔案數",
        ge=0
    )
    ft_summary_files: int = Field(
        default=0,
        description="生成的 FT Summary 檔案數",
        ge=0
    )
    ft_summary_output_file: Optional[str] = Field(
        None,
        description="最終 FT_SUMMARY.csv 檔案路徑"
    )
    processing_time_seconds: float = Field(
        default=0.0,
        description="處理時間（秒）",
        ge=0
    )
    error_message: Optional[str] = Field(
        None,
        description="錯誤訊息"
    )
    # EQC Summary相關欄位
    eqc_summary_files: int = Field(
        default=0,
        description="生成的 EQC Summary 檔案數",
        ge=0
    )
    eqc_summary_output_file: Optional[str] = Field(
        None,
        description="最終 EQC_SUMMARY.csv 檔案路徑"
    )
    eqc_all_pass_file: Optional[str] = Field(
        None,
        description="EQC 全通過檔案路徑"
    )
    # 檔案清單（用於前端顯示）
    ft_file_list: List[str] = Field(
        default_factory=list,
        description="FT 檔案清單"
    )
    eqc_file_list: List[str] = Field(
        default_factory=list,
        description="EQC 檔案清單"
    )
    # 下載相關欄位
    download_url: Optional[str] = Field(
        None,
        description="處理結果下載 URL（用於遠端路徑處理）"
    )
