#!/usr/bin/env python3
"""
手動處理功能 - API 連接測試
測試前端與後端 API 的連接是否正常
"""

import pytest
import requests
import json
import os
from datetime import datetime
from typing import Dict, Any


class TestAPIConnection:
    """API 連接測試類"""
    
    def __init__(self):
        self.base_url = os.getenv('API_BASE_URL', 'http://localhost:5000')
        self.api_key = os.getenv('PARSER_API_KEY', 'dev-parser-key-12345')
        self.headers = {
            'Content-Type': 'application/json',
            'X-API-Key': self.api_key
        }
        self.test_results = []
    
    def log_test_result(self, test_name: str, passed: bool, details: str = "", error: str = ""):
        """記錄測試結果"""
        result = {
            'test_name': test_name,
            'passed': passed,
            'timestamp': datetime.now().isoformat(),
            'details': details,
            'error': error
        }
        self.test_results.append(result)
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"[{status}] {test_name}")
        if details:
            print(f"    詳細: {details}")
        if error:
            print(f"    錯誤: {error}")
    
    def test_parser_api_endpoints(self):
        """測試解析器 API 端點可用性"""
        endpoints = [
            '/api/parser/debug/auth',
            '/api/parser/statistics'
        ]
        
        for endpoint in endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                response = requests.get(url, headers=self.headers, timeout=10)
                
                if response.status_code == 200:
                    self.log_test_result(
                        f"API 端點連通性: {endpoint}",
                        True,
                        f"狀態碼: {response.status_code}, 響應時間: {response.elapsed.total_seconds():.2f}s"
                    )
                else:
                    self.log_test_result(
                        f"API 端點連通性: {endpoint}",
                        False,
                        f"狀態碼: {response.status_code}",
                        response.text[:200]
                    )
            except requests.exceptions.RequestException as e:
                self.log_test_result(
                    f"API 端點連通性: {endpoint}",
                    False,
                    "",
                    str(e)
                )
    
    def test_manual_input_endpoint_structure(self):
        """測試手動輸入端點結構"""
        test_email_id = 999999  # 使用不存在的 ID 測試端點結構
        endpoint = f"/api/parser/emails/{test_email_id}/manual-input"
        url = f"{self.base_url}{endpoint}"
        
        test_data = {
            'vendor_code': 'TEST',
            'pd': 'TEST_PD',
            'lot': 'TEST_LOT'
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=test_data, timeout=10)
            
            # 端點存在但資料不存在應該返回 404 或適當錯誤
            if response.status_code in [404, 400, 500]:
                try:
                    error_data = response.json()
                    if 'success' in error_data:
                        self.log_test_result(
                            "手動輸入端點結構",
                            True,
                            f"端點正確回應錯誤格式，狀態碼: {response.status_code}"
                        )
                    else:
                        self.log_test_result(
                            "手動輸入端點結構",
                            False,
                            f"響應格式不正確，狀態碼: {response.status_code}",
                            response.text[:200]
                        )
                except json.JSONDecodeError:
                    self.log_test_result(
                        "手動輸入端點結構",
                        False,
                        f"響應不是有效 JSON，狀態碼: {response.status_code}",
                        response.text[:200]
                    )
            else:
                self.log_test_result(
                    "手動輸入端點結構",
                    False,
                    f"意外的狀態碼: {response.status_code}",
                    response.text[:200]
                )
                
        except requests.exceptions.RequestException as e:
            self.log_test_result(
                "手動輸入端點結構",
                False,
                "",
                str(e)
            )
    
    def test_api_key_authentication(self):
        """測試 API Key 認證"""
        endpoint = "/api/parser/debug/auth"
        url = f"{self.base_url}{endpoint}"
        
        # 測試有效 API Key
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log_test_result(
                        "API Key 認證 - 有效金鑰",
                        True,
                        "認證成功"
                    )
                else:
                    self.log_test_result(
                        "API Key 認證 - 有效金鑰",
                        False,
                        "認證失敗",
                        json.dumps(data, ensure_ascii=False)
                    )
            else:
                self.log_test_result(
                    "API Key 認證 - 有效金鑰",
                    False,
                    f"狀態碼: {response.status_code}",
                    response.text[:200]
                )
        except Exception as e:
            self.log_test_result(
                "API Key 認證 - 有效金鑰",
                False,
                "",
                str(e)
            )
        
        # 測試無效 API Key
        invalid_headers = {
            'Content-Type': 'application/json',
            'X-API-Key': 'invalid-key'
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/parser/emails/1/manual-input",
                headers=invalid_headers,
                json={'vendor_code': 'TEST', 'pd': 'TEST', 'lot': 'TEST'},
                timeout=10
            )
            
            if response.status_code == 401:
                self.log_test_result(
                    "API Key 認證 - 無效金鑰",
                    True,
                    "正確拒絕無效金鑰"
                )
            else:
                self.log_test_result(
                    "API Key 認證 - 無效金鑰",
                    False,
                    f"應該返回 401，實際: {response.status_code}",
                    response.text[:200]
                )
        except Exception as e:
            self.log_test_result(
                "API Key 認證 - 無效金鑰",
                False,
                "",
                str(e)
            )
    
    def test_cors_headers(self):
        """測試 CORS 標頭配置"""
        endpoint = "/api/parser/debug/auth"
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            
            cors_headers = [
                'Access-Control-Allow-Origin',
                'Access-Control-Allow-Headers',
                'Access-Control-Allow-Methods'
            ]
            
            missing_headers = []
            for header in cors_headers:
                if header not in response.headers:
                    missing_headers.append(header)
            
            if not missing_headers:
                self.log_test_result(
                    "CORS 標頭配置",
                    True,
                    "所有 CORS 標頭都存在"
                )
            else:
                self.log_test_result(
                    "CORS 標頭配置",
                    False,
                    f"缺少 CORS 標頭: {', '.join(missing_headers)}",
                    f"現有標頭: {dict(response.headers)}"
                )
                
        except Exception as e:
            self.log_test_result(
                "CORS 標頭配置",
                False,
                "",
                str(e)
            )
    
    def test_response_format(self):
        """測試響應格式"""
        endpoint = "/api/parser/statistics"
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # 檢查基本響應格式
                required_fields = ['success']
                missing_fields = [field for field in required_fields if field not in data]
                
                if not missing_fields:
                    self.log_test_result(
                        "響應格式驗證",
                        True,
                        "響應格式正確"
                    )
                else:
                    self.log_test_result(
                        "響應格式驗證",
                        False,
                        f"缺少必要欄位: {', '.join(missing_fields)}",
                        json.dumps(data, ensure_ascii=False)[:200]
                    )
            else:
                self.log_test_result(
                    "響應格式驗證",
                    False,
                    f"API 調用失敗，狀態碼: {response.status_code}",
                    response.text[:200]
                )
                
        except json.JSONDecodeError:
            self.log_test_result(
                "響應格式驗證",
                False,
                "響應不是有效的 JSON",
                response.text[:200]
            )
        except Exception as e:
            self.log_test_result(
                "響應格式驗證",
                False,
                "",
                str(e)
            )
    
    def run_all_tests(self):
        """執行所有測試"""
        print("=" * 60)
        print("手動處理功能 - API 連接測試")
        print("=" * 60)
        print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"基礎 URL: {self.base_url}")
        print(f"API Key: {self.api_key[:8]}...")
        print("-" * 60)
        
        # 執行所有測試
        self.test_parser_api_endpoints()
        self.test_manual_input_endpoint_structure()
        self.test_api_key_authentication()
        self.test_cors_headers()
        self.test_response_format()
        
        # 輸出測試總結
        print("-" * 60)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        total_tests = len(self.test_results)
        
        print(f"測試總結: {passed_tests}/{total_tests} 通過")
        
        if passed_tests == total_tests:
            print("🎉 所有 API 連接測試通過！")
            return True
        else:
            print("⚠️  部分測試失敗，請檢查詳細結果")
            return False
    
    def generate_report(self, output_file: str = None):
        """生成測試報告"""
        if output_file is None:
            output_file = f"api_connection_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            'test_type': 'API Connection Test',
            'timestamp': datetime.now().isoformat(),
            'base_url': self.base_url,
            'total_tests': len(self.test_results),
            'passed_tests': sum(1 for result in self.test_results if result['passed']),
            'failed_tests': sum(1 for result in self.test_results if not result['passed']),
            'test_results': self.test_results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"測試報告已儲存至: {output_file}")
        return output_file


if __name__ == "__main__":
    # 執行測試
    test_runner = TestAPIConnection()
    success = test_runner.run_all_tests()
    test_runner.generate_report()
    
    # 返回適當的退出碼
    exit(0 if success else 1)