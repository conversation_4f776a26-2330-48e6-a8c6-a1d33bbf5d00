D:\project\python\outlook_summary\
├── 📋 README.md                          # 專案總覽
├── 📋 MIGRATION_GUIDE.md                 # 重組指南
├── 🔧 .env.example
├── 🔧 docker-compose.yml
├── 🔧 requirements.txt
├── 🔧 pyproject.toml
│
├── 📁 frontend/                          # 前端主目錄（Flask + HTML/JS）
│   ├── 📁 email/                         # 郵件功能領域
│   │   ├── 📁 templates/                 # HTML 模板
│   │   │   ├── 📄 inbox.html            # 收件匣頁面
│   │   │   ├── 📄 email_detail.html     # 郵件詳情
│   │   │   ├── 📄 email_compose.html    # 撰寫郵件
│   │   │   └── 📄 email_settings.html   # 郵件設定
│   │   ├── 📁 static/                    # 靜態資源
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 email.css         # 郵件專用樣式
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 email-list.js     # 郵件列表邏輯
│   │   │   │   ├── 📄 email-detail.js   # 郵件詳情邏輯
│   │   │   │   └── 📄 email-api.js      # 郵件 API 調用
│   │   │   └── 📁 images/               # 郵件相關圖片
│   │   ├── 📁 components/                # 可重用組件（HTML片段）
│   │   │   ├── 📄 email-card.html       # 郵件卡片組件
│   │   │   ├── 📄 attachment-viewer.html # 附件查看器
│   │   │   └── 📄 email-toolbar.html    # 郵件工具列
│   │   ├── 📁 routes/                    # 路由處理
│   │   │   └── 📄 email_routes.py       # 郵件相關路由
│   │   └── 📋 README.md                  # 郵件模組說明
│   │
│   ├── 📁 analytics/                     # 分析統計功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 dashboard.html        # 統計儀表板
│   │   │   ├── 📄 reports.html          # 報表頁面
│   │   │   ├── 📄 vendor_analysis.html  # 廠商分析
│   │   │   └── 📄 csv_processor.html    # CSV 處理頁面
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 analytics.css     # 統計專用樣式
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 charts.js         # 圖表邏輯
│   │   │   │   ├── 📄 reports.js        # 報表邏輯
│   │   │   │   └── 📄 analytics-api.js  # 統計 API
│   │   │   └── 📁 lib/                  # 第三方庫（Chart.js等）
│   │   ├── 📁 components/
│   │   │   ├── 📄 chart-widget.html     # 圖表組件
│   │   │   ├── 📄 data-table.html       # 數據表格
│   │   │   └── 📄 export-button.html    # 導出按鈕
│   │   ├── 📁 routes/
│   │   │   └── 📄 analytics_routes.py   # 統計路由
│   │   └── 📋 README.md
│   │
│   ├── 📁 file-management/               # 檔案管理功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 file_manager.html     # 檔案管理器
│   │   │   ├── 📄 upload.html           # 檔案上傳
│   │   │   └── 📄 attachment_browser.html # 附件瀏覽器
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 file-manager.css  # 檔案管理樣式
│   │   │   └── 📁 js/
│   │   │       ├── 📄 file-upload.js    # 檔案上傳邏輯
│   │   │       ├── 📄 file-browser.js   # 檔案瀏覽邏輯
│   │   │       └── 📄 file-api.js       # 檔案 API
│   │   ├── 📁 components/
│   │   │   ├── 📄 file-uploader.html    # 上傳組件
│   │   │   └── 📄 file-list.html        # 檔案列表組件
│   │   ├── 📁 routes/
│   │   │   └── 📄 file_routes.py        # 檔案路由
│   │   └── 📋 README.md
│   │
│   ├── 📁 eqc/                           # EQC功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 eqc_dashboard.html    # EQC儀表板
│   │   │   ├── 📄 quality_check.html    # 品質檢查
│   │   │   └── 📄 compliance.html       # 合規檢查
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 eqc.css           # EQC專用樣式
│   │   │   └── 📁 js/
│   │   │       ├── 📄 eqc-dashboard.js  # EQC儀表板邏輯
│   │   │       └── 📄 eqc-api.js        # EQC API
│   │   ├── 📁 routes/
│   │   │   └── 📄 eqc_routes.py         # EQC路由
│   │   └── 📋 README.md
│   │
│   ├── 📁 tasks/                         # 任務管理功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 task_dashboard.html   # 任務儀表板
│   │   │   ├── 📄 task_queue.html       # 任務隊列
│   │   │   └── 📄 task_scheduler.html   # 任務調度
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 tasks.css         # 任務管理樣式
│   │   │   └── 📁 js/
│   │   │       ├── 📄 task-monitor.js   # 任務監控邏輯
│   │   │       └── 📄 tasks-api.js      # 任務 API
│   │   ├── 📁 routes/
│   │   │   └── 📄 task_routes.py        # 任務路由
│   │   └── 📋 README.md
│   │
│   ├── 📁 monitoring/                    # 監控功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 system_dashboard.html # 系統監控儀表板
│   │   │   └── 📄 health_check.html     # 健康檢查
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 monitoring.css    # 監控樣式
│   │   │   └── 📁 js/
│   │   │       ├── 📄 metrics.js        # 指標邏輯
│   │   │       └── 📄 monitoring-api.js # 監控 API
│   │   ├── 📁 routes/
│   │   │   └── 📄 monitoring_routes.py  # 監控路由
│   │   └── 📋 README.md
│   │
│   ├── 📁 shared/                        # 共享前端資源
│   │   ├── 📁 templates/
│   │   │   ├── 📄 base.html             # 基礎模板
│   │   │   ├── 📄 layout.html           # 主要佈局
│   │   │   └── 📄 components/           # 共享組件
│   │   │       ├── 📄 navbar.html       # 導航列
│   │   │       ├── 📄 sidebar.html      # 側邊欄
│   │   │       ├── 📄 modal.html        # 模態框
│   │   │       └── 📄 loading.html      # 載入動畫
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   ├── 📄 global.css        # 全域樣式
│   │   │   │   ├── 📄 components.css    # 共享組件樣式
│   │   │   │   └── 📄 theme.css         # 主題樣式
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 common.js         # 共用JavaScript
│   │   │   │   ├── 📄 api-client.js     # 統一API客戶端
│   │   │   │   ├── 📄 utils.js          # 工具函數
│   │   │   │   └── 📄 constants.js      # 前端常數
│   │   │   ├── 📁 lib/                  # 第三方函式庫
│   │   │   │   ├── 📄 jquery.min.js
│   │   │   │   ├── 📄 bootstrap.min.js
│   │   │   │   └── 📄 chart.min.js
│   │   │   └── 📁 images/               # 共享圖片
│   │   └── 📋 README.md
│   │
│   ├── 📄 app.py                         # Flask 主應用（重構後）
│   ├── 📄 config.py                      # Flask 配置
│   └── 📋 README.md                      # 前端開發指南
│
├── 📁 backend/                           # ✅ 後端主目錄（Task 3 完成）
│   │
│   ├── 📁 tasks/                         # ✅ 任務調度與管理服務 (Task 3 Phase 1)
│   │   ├── 📁 services/                 # 遷移自 src/services/
│   │   │   ├── 📄 scheduler.py          # ✅ 來自 src/services/scheduler.py
│   │   │   ├── 📄 concurrent_task_manager.py # ✅ 來自 src/services/concurrent_task_manager.py
│   │   │   └── 📄 dramatiq_tasks.py     # ✅ 來自 dramatiq_tasks.py
│   │   ├── 📁 models/                  # 模型定義
│   │   └── 📋 README.md              # 模組說明
│   │
│   ├── 📁 monitoring/                    # ✅ 監控系統 (Task 3 Phase 2)
│   │   ├── 📁 dashboard_monitoring/      # ✅ 完整監控仪表板 (75+ 檔案)
│   │   │   ├── 📄 api_endpoints.py      # API 端點監控
│   │   │   ├── 📄 core_services.py      # 核心監控服務
│   │   │   ├── 📄 dashboard_config.py   # 仪表板配置
│   │   │   └── 📄 monitoring_data_collector.py # 數據收集器
│   │   ├── 📁 monitoring_analysis/      # 監控分析
│   │   ├── 📁 performance_monitoring/   # 效能監控
│   │   ├── 📁 system_health/            # 系統健康監控
│   │   └── 📋 README.md              # 監控系統說明
│   │
│   ├── 📁 analytics/                     # ✅ 分析統計服務 (Task 3 Phase 3)
│   │   ├── 📁 services/                 # 遷移自 src/analytics_service/
│   │   │   ├── 📄 email_analytics.py     # ✅ 郵件分析服務
│   │   │   ├── 📄 performance_analytics.py # ✅ 效能分析服務
│   │   │   └── 📄 user_analytics.py      # ✅ 用戶分析服務
│   │   ├── 📁 models/                  # 分析模型
│   │   └── 📋 README.md              # 分析服務說明
│   │
│   ├── 📁 eqc/                           # ✅ EQC品質控制服務 (Task 3 Phase 4)
│   │   ├── 📁 services/                 # 遷移自 src/eqc_service/
│   │   │   ├── 📄 eqc_processor.py       # ✅ EQC 處理器
│   │   │   ├── 📄 quality_control.py     # ✅ 品質控制服務
│   │   │   └── 📄 validation_service.py  # ✅ 驗證服務
│   │   ├── 📁 models/                  # EQC 模型
│   │   └── 📋 README.md              # EQC 服務說明
│   │
│   │   # ⚠️ 以下模組尚未遷移（未來 Task 4+）
│   ├── 📁 email/                         # 🕰️ 待遷移 - 郵件服務
│   │   ├── 📁 api/
│   │   │   ├── 📄 routes.py             # 郵件 API 路由
│   │   │   ├── 📄 schemas.py            # 郵件 API 模式
│   │   │   └── 📄 dependencies.py       # 郵件依賴注入
│   │   ├── 📁 services/                 # 遷移現有服務
│   │   │   ├── 📄 outlook_service.py    # 來自 src/infrastructure/adapters/outlook/
│   │   │   ├── 📄 pop3_service.py       # 來自 src/infrastructure/adapters/pop3/
│   │   │   ├── 📄 email_parser.py       # 郵件解析服務
│   │   │   └── 📄 sync_service.py       # 郵件同步服務
│   │   ├── 📁 models/
│   │   │   ├── 📄 email.py              # 郵件數據模型
│   │   │   └── 📄 attachment.py         # 附件模型
│   │   ├── 📁 repositories/
│   │   │   └── 📄 email_repository.py   # 郵件數據訪問
│   │   ├── 📁 core/
│   │   │   ├── 📄 config.py             # 郵件服務配置
│   │   │   └── 📄 exceptions.py         # 郵件異常
│   │   └── 📋 README.md
│   │
│   ├── 📁 file-management/               # 🕰️ 待遷移 - 檔案管理服務
│   │   ├── 📁 api/
│   │   │   ├── 📄 routes.py             # 檔案 API 路由
│   │   │   └── 📄 schemas.py            # 檔案 API 模式
│   │   ├── 📁 services/                 # 遷移現有檔案服務
│   │   │   ├── 📄 attachment_service.py  # 來自 src/infrastructure/adapters/attachments/
│   │   │   ├── 📄 upload_service.py     # 來自 src/infrastructure/adapters/file_upload/
│   │   │   ├── 📄 file_monitor.py       # 來自 src/services/vendor_file_monitor.py
│   │   │   └── 📄 file_cleaner.py       # 來自 src/services/file_cleaner.py
│   │   ├── 📁 models/
│   │   │   └── 📄 file.py               # 檔案模型
│   │   └── 📋 README.md
│   │   ├── 📁 services/
│   │   │   ├── 📄 session_manager.py    # 來自 src/services/eqc_session_manager.py
│   │   │   ├── 📄 quality_checker.py    # 品質檢查服務
│   │   │   └── 📄 compliance_validator.py # 合規驗證服務
│   │   ├── 📁 models/
│   │   │   └── 📄 eqc_data.py           # EQC數據模型
│   │   └── 📋 README.md
│   │
│   ├── 📁 tasks/                         # 任務管理服務
│   │   ├── 📁 api/
│   │   │   ├── 📄 routes.py             # 任務 API 路由
│   │   │   └── 📄 schemas.py            # 任務 API 模式
│   │   ├── 📁 services/                 # 遷移現有任務服務
│   │   │   ├── 📄 scheduler.py          # 來自 src/services/scheduler.py
│   │   │   ├── 📄 task_manager.py       # 來自 src/services/concurrent_task_manager.py
│   │   │   ├── 📄 dramatiq_service.py   # 來自 dramatiq_tasks.py
│   │   │   └── 📄 task_monitor.py       # 任務監控服務
│   │   ├── 📁 models/
│   │   │   └── 📄 task.py               # 任務模型
│   │   └── 📋 README.md
│   │
│   ├── 📁 monitoring/                    # 監控服務
│   │   ├── 📁 api/
│   │   │   ├── 📄 routes.py             # 監控 API 路由
│   │   │   └── 📄 schemas.py            # 監控 API 模式
│   │   ├── 📁 services/
│   │   │   ├── 📄 health_checker.py     # 健康檢查服務
│   │   │   ├── 📄 metrics_collector.py  # 指標收集服務
│   │   │   └── 📄 alert_manager.py      # 告警管理服務
│   │   ├── 📁 models/
│   │   │   └── 📄 metrics.py            # 指標模型
│   │   └── 📋 README.md
│   │
│   ├── 📁 shared/                        # 後端共享資源
│   │   ├── 📁 database/                 # 遷移現有數據庫代碼
│   │   │   ├── 📄 base.py               # 來自 src/infrastructure/adapters/database/
│   │   │   ├── 📄 email_database.py    # 郵件數據庫
│   │   │   └── 📄 models.py             # 共享數據模型
│   │   ├── 📁 utils/                    # 遷移現有工具
│   │   │   ├── 📄 logger.py             # 來自 src/services/unified_logger.py
│   │   │   ├── 📄 config_manager.py     # 來自 src/services/config_manager.py
│   │   │   └── 📄 helpers.py            # 工具函數
│   │   ├── 📁 middleware/               # 共享中間件
│   │   ├── 📁 exceptions/               # 共享異常
│   │   └── 📁 constants/                # 後端常數
│   │
│   ├── 📄 main.py                        # 重構後的主入口
│   ├── 📄 config.py                      # 統一配置管理
│   └── 📋 README.md                      # 後端開發指南
│
├── 📁 infrastructure/                    # 基礎設施（保持現有）
│   ├── 📁 docker/
│   ├── 📁 scripts/
│   └── 📁 deployment/
│
├── 📁 docs/                              # 文檔系統
│   ├── 📋 README.md
│   ├── 📁 migration/                     # 遷移文檔
│   │   ├── 📋 restructure-guide.md      # 重構指南
│   │   ├── 📋 file-mapping.md           # 檔案映射表
│   │   └── 📋 team-responsibilities.md  # 團隊職責
│   ├── 📁 architecture/
│   ├── 📁 development/
│   └── 📁 api/
│
└── 📁 tests/                             # 測試（按模組組織）
    ├── 📁 frontend/
    │   ├── 📁 email/
    │   ├── 📁 analytics/
    │   └── 📁 shared/
    ├── 📁 backend/
    │   ├── 📁 email/
    │   ├── 📁 analytics/
    │   └── 📁 shared/
    └── 📁 integration/


🔄 具體遷移步驟
Step 1: 檔案映射和遷移（Week 1）
Frontend 重組
bash# 1. 建立新的前端結構
mkdir -p frontend/{email,analytics,file-management,eqc,tasks,monitoring,shared}/{templates,static/{css,js,images},components,routes}

# 2. 遷移現有前端檔案
# 從 src/presentation/web/templates/ 按功能分類移動到對應模組
# 從 src/presentation/web/static/ 按功能分類移動到對應模組

# 3. 重構 Flask 應用
# 將 email_inbox_app.py 重構為 frontend/app.py
# 按功能模組組織路由

Backend 重組
# 1. 建立新的後端結構  
mkdir -p backend/{email,analytics,file-management,eqc,tasks,monitoring,shared}/{api,services,models,repositories,core}

# 2. 映射現有服務
# src/infrastructure/adapters/outlook/ → backend/email/services/
# src/infrastructure/adapters/analytics/ → backend/analytics/services/
# src/services/eqc_session_manager.py → backend/eqc/services/
# dramatiq_tasks.py → backend/tasks/services/

Step 2: 統一命名規範（Week 1）
API 路由統一
# 舊的路由（不一致）
@app.route('/email_process')          # 混合命名
@app.route('/get-analytics-data')     # kebab-case
@app.route('/eqcProcessing')          # camelCase

# 新的路由（統一）
@email_bp.route('/api/email/process')        # 一致的REST風格
@analytics_bp.route('/api/analytics/data')   # 清晰的模組劃分  
@eqc_bp.route('/api/eqc/processing')         # 統一命名

檔案命名統一
python# 統一後端服務命名
email_service.py          # 不是 emailService.py 或 email_svc.py
analytics_service.py      # 不是 analyticsProcessor.py
file_service.py           # 不是 fileHandler.py or fileManager.py
Step 3: 模組邊界清晰化（Week 2）
依賴關係圖
python# frontend/email/routes/email_routes.py
from backend.email.api.routes import email_api_bp
from frontend.shared.utils import render_with_nav

# 清晰的模組依賴：
# email → shared（允許）
# email → analytics（需要通過API）
# email → 不直接依賴其他模組
API 介面統一
python# backend/shared/api/base.py
class APIResponse:
    """統一的API響應格式"""
    def __init__(self, data=None, message="", status="success"):
        self.data = data
        self.message = message
        self.status = status

# 每個模組都使用相同的響應格式
Step 4: 團隊協作改善（Week 2）
模組負責人制度
yaml團隊分工:
  email模組:
    前端負責人: A組
    後端負責人: E組  
    檔案位置: frontend/email/ + backend/email/
    
  analytics模組:
    前端負責人: B組
    後端負責人: F組
    檔案位置: frontend/analytics/ + backend/analytics/
開發規範文檔
markdown# docs/development/module-guidelines.md

## 模組開發規範
1. 每個模組必須有獨立的README.md
2. API介面必須遵循統一格式
3. 測試覆蓋率要求：前端>70%，後端>80%
4. 代碼審查：跨模組修改需要兩個模組負責人同意

📊 重組效益
解決現有問題
✅ 前端組件散亂 → 按功能領域清晰組織
✅ 命名不統一 → 統一REST API和檔案命名規範
✅ 模組邊界模糊 → 清晰的資料夾結構和依賴關係
✅ 協作困難 → 明確的模組負責人制度
為Vue遷移準備
✅ 邏輯已分離 → Vue組件可以直接對應現有模組
✅ API已標準化 → Vue可以直接使用現有API
✅ 團隊已熟悉 → 相同的目錄結構，降低學習成本