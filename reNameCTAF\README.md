# PTS文件批量处理工具需求说明

## 项目概述
PTS文件批量处理工具是一个基于Python Tkinter开发的图形化应用程序，用于批量处理、重命名和转换PTS（Parameter Test Specification）文件。该工具支持多种操作模式，包括文件重命名、QC文件生成和目录结构创建。

## 功能需求

### 1. 文件夹选择功能
- **多文件夹选择**：用户可以选择多个包含PTS文件的文件夹
- **文件夹列表显示**：在界面中显示已选择的文件夹列表
- **文件夹管理**：支持添加和移除文件夹

### 2. 重新命名功能
- **条件显示**：通过"重新命名"复选框控制显示/隐藏重命名相关字段
- **模式匹配**：支持正则表达式模式匹配进行文件名替换
- **输入字段**：
  - 替换前名称模式（old name pattern）
  - 替换后名称模式（new name pattern）
- **验证机制**：
  - 检查是否提供重命名模式
  - 验证模式是否匹配文件名
  - 检查新文件名是否已存在
- **结果反馈**：显示重命名成功/失败的文件列表

### 3. 增加QC功能
- **QC文件生成**：为每个PTS文件创建对应的QC版本
- **文件命名**：QC文件自动添加"_QC"后缀
- **内容处理**：
  - 保留"Parameter,"行
  - 删除"Parameter,"和"QA,"之间的所有行（包括"QA,"行）
  - 修改QCOnlySBinAlter参数为"1,0"
  - 重新计算ParamCnt（统计Parameter到END之间的非空行数）
  - 在[Bin Definition]部分只保留以"1"或"31"开头的行
- **智能处理**：
  - 跳过已包含"_QC"后缀的文件
  - 覆盖已存在的QC文件
- **结果反馈**：显示QC处理成功/失败的文件列表

### 4. 创建目录功能
- **目录结构**：为每个PTS文件创建独立的目录
- **目录命名**：目录名称为PTS文件名（不含扩展名）
- **文件复制**：
  - 复制整个原始文件夹内容
  - 只保留对应的PTS文件和其他非PTS文件
  - 删除所有*.ini文件
- **位置设置**：新目录创建在原始文件夹的父目录中
- **冲突处理**：
  - 如果PTS文件名与原始文件夹名相同，跳过创建并显示错误信息
  - 覆盖已存在的目录
- **结果反馈**：显示目录创建成功/失败的文件列表

### 5. 执行控制
- **操作顺序**：严格按照"重新命名 → 增加QC → 创建目录"的顺序执行
- **进度显示**：实时显示处理进度
- **批量处理**：支持同时处理多个文件夹中的多个PTS文件
- **错误处理**：完善的异常处理和错误信息显示

## 用户界面需求

### 1. 主界面布局
- **文件夹选择区域**：
  - 文件夹列表显示框（高度为4行）
  - 添加文件夹按钮
  - 移除文件夹按钮
- **操作选项区域**：
  - "重新命名"复选框
  - "增加QC"复选框
  - "创建目录"复选框
- **参数输入区域**：
  - 替换前名称输入框（条件显示）
  - 替换后名称输入框（条件显示）
- **执行控制区域**：
  - "执行"按钮（宽度为20）
  - 结果显示列表

### 2. 界面交互
- **动态显示**：根据复选框状态动态显示/隐藏相关输入字段
- **实时反馈**：处理过程中显示进度信息
- **结果展示**：在列表中显示详细的操作结果

## 技术规格

### 1. 开发环境
- **编程语言**：Python 3.x
- **GUI框架**：Tkinter
- **操作系统**：Windows 10/11

### 2. 文件处理
- **支持格式**：PTS文件（.pts扩展名）
- **编码处理**：UTF-8编码支持
- **文件操作**：
  - 文件重命名（os.rename）
  - 文件复制（shutil.copy2）
  - 目录操作（shutil.copytree, shutil.rmtree）
  - 文件删除（os.unlink）

### 3. 正则表达式
- **模式匹配**：使用Python re模块进行文件名模式匹配
- **替换操作**：支持复杂的字符串替换模式

### 4. 错误处理
- **异常捕获**：try-except块处理文件操作异常
- **用户提示**：messagebox显示错误和警告信息
- **日志记录**：控制台输出调试信息

## 使用场景

### 1. 测试文件管理
- 批量重命名测试规范文件
- 生成QC版本的测试文件
- 创建独立的测试目录结构

### 2. 质量控制
- 自动生成QC文件进行质量检查
- 标准化测试参数设置
- 清理和优化测试文件内容

### 3. 项目组织
- 为每个测试文件创建独立的工作目录
- 保持原始文件结构的同时创建新的组织方式
- 便于测试文件的版本管理和追踪

## 性能要求

### 1. 处理能力
- 支持同时处理多个文件夹
- 支持批量处理大量PTS文件
- 实时显示处理进度

### 2. 响应性
- 界面操作响应时间 < 1秒
- 文件处理过程中保持界面响应
- 支持处理过程中的用户交互

### 3. 稳定性
- 异常情况下程序不崩溃
- 提供详细的错误信息
- 支持操作回滚和恢复

## 安全要求

### 1. 文件安全
- 不删除原始文件
- 操作前验证文件存在性
- 防止文件覆盖冲突

### 2. 数据完整性
- 保持原始文件内容不变
- QC处理确保数据一致性
- 目录结构完整性验证

## 维护要求

### 1. 代码质量
- 模块化设计，便于维护
- 清晰的代码注释
- 统一的编码规范

### 2. 扩展性
- 支持新功能的添加
- 支持不同文件格式的扩展
- 支持自定义处理规则

### 3. 文档
- 完整的用户手册
- 详细的API文档
- 故障排除指南

## 测试要求

### 1. 功能测试
- 各种操作组合的测试
- 边界条件测试
- 异常情况测试

### 2. 性能测试
- 大量文件处理测试
- 内存使用测试
- 响应时间测试

### 3. 兼容性测试
- 不同Windows版本测试
- 不同Python版本测试
- 不同文件系统测试
