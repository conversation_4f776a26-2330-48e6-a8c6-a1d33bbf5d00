"""
Flask 全域錯誤處理器
"""

from flask import jsonify, request, render_template
from werkzeug.exceptions import HTTPException
import logging


class ErrorHandler:
    """
    統一錯誤處理類別
    """
    
    @staticmethod
    def register_error_handlers(app):
        """
        註冊全域錯誤處理器
        
        Args:
            app: Flask 應用程式實例
        """
        
        @app.errorhandler(404)
        def not_found(error):
            """處理 404 錯誤"""
            if request.path.startswith('/api/'):
                return jsonify({
                    "status": "error",
                    "message": "API 端點不存在",
                    "code": "NOT_FOUND"
                }), 404
            
            try:
                return render_template('errors/404.html'), 404
            except:
                return jsonify({
                    "status": "error",
                    "message": "頁面不存在",
                    "code": "NOT_FOUND"
                }), 404
        
        @app.errorhandler(500)
        def internal_error(error):
            """處理 500 錯誤"""
            logging.error(f"內部伺服器錯誤: {error}")
            
            if request.path.startswith('/api/'):
                return jsonify({
                    "status": "error",
                    "message": "內部伺服器錯誤",
                    "code": "INTERNAL_ERROR"
                }), 500
            
            try:
                return render_template('errors/500.html'), 500
            except:
                return jsonify({
                    "status": "error",
                    "message": "內部伺服器錯誤",
                    "code": "INTERNAL_ERROR"
                }), 500
        
        @app.errorhandler(Exception)
        def handle_exception(error):
            """處理未捕獲的異常"""
            logging.error(f"未處理的異常: {error}")
            
            if request.path.startswith('/api/'):
                return jsonify({
                    "status": "error",
                    "message": "發生未知錯誤",
                    "code": "UNKNOWN_ERROR"
                }), 500
            
            try:
                return render_template('errors/500.html'), 500
            except:
                return jsonify({
                    "status": "error",
                    "message": "發生未知錯誤",
                    "code": "UNKNOWN_ERROR"
                }), 500