"""
End-to-End Playwright tests for database field display improvements

These tests validate the actual browser behavior at http://localhost:5000/monitoring/database-manager
to ensure all three stories are working correctly in the live environment.

Target Stories:
- Story 1.1: is_remote_download_success → "下載成功" Chinese title
- Story 1.2: false values → red "失敗" visual tags  
- Story 1.3: Field category organization in control panel

Test Approach:
- Uses Playwright for real browser automation
- Tests actual user workflows and visual elements
- Validates cross-browser compatibility
- Captures screenshots for visual regression testing

Author: Test Automation Specialist
Date: 2025-08-19
"""

import pytest
from playwright.sync_api import Page, expect, Browser
import time
import os
from typing import List, Dict
import json


class TestDatabaseManagerPageLoad:
    """Test basic page loading and initialization"""
    
    def test_database_manager_page_loads(self, page: Page):
        """Test that database manager page loads correctly"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        
        # Wait for page to load
        expect(page).to_have_title("資料庫管理")
        
        # Check key UI elements are present
        expect(page.locator("#table-select")).to_be_visible()
        expect(page.locator("#refresh-btn")).to_be_visible() 
        expect(page.locator("#export-csv-btn")).to_be_visible()
        
        # Check that email_download_status option exists
        table_select = page.locator("#table-select")
        expect(table_select.locator("option[value='email_download_status']")).to_be_visible()
    
    def test_javascript_files_load(self, page: Page):
        """Test that required JavaScript files load without errors"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        
        # Check for JavaScript errors
        page.wait_for_load_state("networkidle")
        
        # Verify DatabaseManager class is available
        js_check = page.evaluate("""
            typeof window.databaseManager !== 'undefined' && 
            window.databaseManager.constructor.name === 'DatabaseManager'
        """)
        assert js_check, "DatabaseManager not properly initialized"
    
    def test_css_styles_load(self, page: Page):
        """Test that database.css styles are applied"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        
        # Check that key CSS classes exist
        table_selector = page.locator(".table-selector")
        expect(table_selector).to_be_visible()
        
        # Check for column visibility controller styling
        controller = page.locator(".column-visibility-controller")
        expect(controller).to_be_visible()


class TestStory1ChineseFieldTitles:
    """Test Story 1.1: Chinese field title display"""
    
    def test_select_email_download_status_table(self, page: Page):
        """Test selecting email_download_status table shows Chinese titles"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        
        # Select email_download_status table
        page.locator("#table-select").select_option("email_download_status")
        
        # Wait for table to load
        page.wait_for_selector("#data-table thead", state="visible", timeout=10000)
        
        # Check that Chinese column titles are displayed
        table_headers = page.locator("#data-table thead th").all_text_contents()
        
        # Key assertion from Story 1.1
        assert "下載成功" in table_headers, f"Chinese title '下載成功' not found in headers: {table_headers}"
        assert "處理成功" in table_headers, f"Chinese title '處理成功' not found in headers: {table_headers}"
        assert "郵件 ID" in table_headers, f"Chinese title '郵件 ID' not found in headers: {table_headers}"
    
    def test_chinese_titles_in_column_control_panel(self, page: Page):
        """Test Chinese titles appear in column visibility control panel"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table", state="visible")
        
        # Open column visibility panel
        page.locator("#column-visibility-toggle").click()
        page.wait_for_selector("#column-visibility-panel", state="visible")
        
        # Check Chinese titles in the control panel
        panel_text = page.locator("#column-visibility-panel").inner_text()
        
        assert "下載成功" in panel_text, "Chinese title '下載成功' not found in control panel"
        assert "處理成功" in panel_text, "Chinese title '處理成功' not found in control panel"
        assert "重試次數" in panel_text, "Chinese title '重試次數' not found in control panel"
    
    def test_chinese_titles_performance(self, page: Page):
        """Test that Chinese title display is fast (< 1ms requirement)"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        
        # Measure time to display table with Chinese titles
        start_time = time.time()
        
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table thead th", state="visible")
        
        end_time = time.time()
        load_time = end_time - start_time
        
        # Should load quickly (allowing for network/rendering time)
        assert load_time < 3.0, f"Table loading too slow: {load_time:.3f}s > 3.0s"
    
    def test_other_tables_unaffected(self, page: Page):
        """Test that other tables still use correct titles"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        
        # Test emails table first
        page.locator("#table-select").select_option("emails")
        page.wait_for_selector("#data-table thead", state="visible")
        
        emails_headers = page.locator("#data-table thead th").all_text_contents()
        
        # Should not have email_download_status specific Chinese titles
        assert "下載成功" not in emails_headers, "email_download_status titles appearing in emails table"
        
        # Should have emails-specific titles
        assert "寄件者" in emails_headers or "sender" in emails_headers
        
        # Switch back to email_download_status 
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table thead", state="visible")
        
        download_headers = page.locator("#data-table thead th").all_text_contents()
        assert "下載成功" in download_headers, "Chinese titles lost when switching tables"


class TestStory2BooleanVisualTags:
    """Test Story 1.2: Boolean values displayed as visual tags"""
    
    def test_false_values_show_red_failure_tags(self, page: Page):
        """Test that false boolean values show as red '失敗' tags"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table tbody tr", state="visible")
        
        # Look for failure tags - this is the key requirement from Story 1.2
        failure_tags = page.locator(".failed-status-tag").count()
        
        if failure_tags > 0:
            # Check first failure tag
            first_failure_tag = page.locator(".failed-status-tag").first
            expect(first_failure_tag).to_be_visible()
            
            # Verify tag content and styling
            tag_text = first_failure_tag.inner_text()
            assert tag_text == "失敗", f"Expected '失敗', got '{tag_text}'"
            
            # Check red styling
            bg_color = first_failure_tag.evaluate("el => getComputedStyle(el).backgroundColor")
            assert "248, 215, 218" in bg_color or "rgb(248, 215, 218)" in bg_color, f"Wrong background color: {bg_color}"
        else:
            # If no failure tags exist, create test data or skip with warning
            print("⚠️  No failure tags found - may need test data with false values")
    
    def test_true_values_show_green_success_tags(self, page: Page):
        """Test that true boolean values show as green '成功' tags"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table tbody tr", state="visible")
        
        # Look for success tags
        success_tags = page.locator(".success-status-tag").count()
        
        if success_tags > 0:
            first_success_tag = page.locator(".success-status-tag").first
            expect(first_success_tag).to_be_visible()
            
            # Verify tag content
            tag_text = first_success_tag.inner_text()
            assert tag_text == "成功", f"Expected '成功', got '{tag_text}'"
            
            # Check green styling
            bg_color = first_success_tag.evaluate("el => getComputedStyle(el).backgroundColor")
            assert "212, 237, 218" in bg_color or "rgb(212, 237, 218)" in bg_color, f"Wrong background color: {bg_color}"
        else:
            print("⚠️  No success tags found - may need test data with true values")
    
    def test_null_values_show_unknown_tags(self, page: Page):
        """Test that null boolean values show as grey '未知' tags"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table tbody tr", state="visible")
        
        # Look for unknown tags
        unknown_tags = page.locator(".unknown-status-tag").count()
        
        if unknown_tags > 0:
            first_unknown_tag = page.locator(".unknown-status-tag").first
            expect(first_unknown_tag).to_be_visible()
            
            # Verify tag content
            tag_text = first_unknown_tag.inner_text()
            assert tag_text == "未知", f"Expected '未知', got '{tag_text}'"
            
            # Check grey styling
            bg_color = first_unknown_tag.evaluate("el => getComputedStyle(el).backgroundColor")
            assert "245, 245, 245" in bg_color or "rgb(245, 245, 245)" in bg_color, f"Wrong background color: {bg_color}"
        else:
            print("⚠️  No unknown tags found - may need test data with null values")
    
    def test_boolean_tag_consistency(self, page: Page):
        """Test that boolean tags have consistent styling"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table tbody tr", state="visible")
        
        # Get all status tags
        all_tags = page.locator(".success-status-tag, .failed-status-tag, .unknown-status-tag")
        tag_count = all_tags.count()
        
        if tag_count > 0:
            for i in range(min(tag_count, 3)):  # Test first 3 tags
                tag = all_tags.nth(i)
                
                # Check consistent styling properties
                styles = tag.evaluate("""el => {
                    const cs = getComputedStyle(el);
                    return {
                        padding: cs.padding,
                        borderRadius: cs.borderRadius,
                        fontSize: cs.fontSize,
                        display: cs.display
                    };
                }""")
                
                # Check consistent padding and border radius
                assert "2px 8px" in styles['padding'] or "2px" in styles['padding']
                assert "12px" in styles['borderRadius']
                assert "inline" in styles['display']
    
    def test_boolean_fields_only_get_tags(self, page: Page):
        """Test that only boolean success fields get visual tags"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table tbody tr", state="visible")
        
        # Check that non-boolean fields don't have status tags
        first_row = page.locator("#data-table tbody tr").first
        cells = first_row.locator("td").all()
        
        # ID field should not have status tags
        id_cell = cells[1] if len(cells) > 1 else None  # Skip checkbox column
        if id_cell:
            id_status_tags = id_cell.locator(".success-status-tag, .failed-status-tag, .unknown-status-tag").count()
            assert id_status_tags == 0, "ID field should not have status tags"


class TestStory3FieldCategoryOrganization:
    """Test Story 1.3: Field category organization in control panel"""
    
    def test_field_categories_exist(self, page: Page):
        """Test that field categories are properly organized"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table", state="visible")
        
        # Open column control panel
        page.locator("#column-visibility-toggle").click()
        page.wait_for_selector("#column-visibility-panel", state="visible")
        
        # Check for category headers
        panel_text = page.locator("#column-visibility-panel").inner_text()
        
        # Expected categories from Story 1.3
        expected_categories = ["基本資訊", "處理狀態", "錯誤訊息"]
        
        for category in expected_categories:
            assert category in panel_text, f"Category '{category}' not found in control panel"
    
    def test_fields_grouped_in_correct_categories(self, page: Page):
        """Test that fields are grouped in logical categories"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table", state="visible")
        
        page.locator("#column-visibility-toggle").click()
        page.wait_for_selector("#column-visibility-panel", state="visible")
        
        # Get the full panel structure
        panel_html = page.locator("#column-visibility-panel").inner_html()
        
        # Check that status fields are under "處理狀態" category
        status_section_start = panel_html.find("處理狀態")
        if status_section_start > -1:
            # Look for status fields after the category header
            status_section = panel_html[status_section_start:status_section_start + 1000]
            assert "下載成功" in status_section, "'下載成功' not found in status category"
            assert "處理成功" in status_section, "'處理成功' not found in status category"
        
        # Check that error fields are under "錯誤訊息" category  
        error_section_start = panel_html.find("錯誤訊息")
        if error_section_start > -1:
            error_section = panel_html[error_section_start:error_section_start + 1000]
            assert "下載錯誤訊息" in error_section or "錯誤" in error_section
    
    def test_category_visual_distinction(self, page: Page):
        """Test that categories have proper visual styling"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table", state="visible")
        
        page.locator("#column-visibility-toggle").click()
        page.wait_for_selector("#column-visibility-panel", state="visible")
        
        # Check category headers have distinct styling
        category_headers = page.locator(".column-category-header")
        header_count = category_headers.count()
        
        if header_count > 0:
            first_header = category_headers.first
            
            # Check styling properties
            styles = first_header.evaluate("""el => {
                const cs = getComputedStyle(el);
                return {
                    fontWeight: cs.fontWeight,
                    borderBottom: cs.borderBottom,
                    color: cs.color
                };
            }""")
            
            assert "bold" in styles['fontWeight'] or int(styles['fontWeight']) >= 600
            assert "solid" in styles['borderBottom']
    
    def test_category_panel_functionality(self, page: Page):
        """Test that category panel opens, closes, and functions properly"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table", state="visible")
        
        # Panel should be closed initially
        panel = page.locator("#column-visibility-panel")
        expect(panel).to_have_class(re=".*collapsed.*")
        
        # Click to open
        page.locator("#column-visibility-toggle").click()
        page.wait_for_timeout(500)  # Animation time
        
        # Panel should be open
        expect(panel).not_to_have_class(re=".*collapsed.*")
        expect(panel).to_be_visible()
        
        # Click overlay to close
        page.locator("#column-visibility-overlay").click()
        page.wait_for_timeout(500)
        
        # Panel should be closed again
        expect(panel).to_have_class(re=".*collapsed.*")
    
    def test_field_checkbox_functionality(self, page: Page):
        """Test that field visibility checkboxes work correctly"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table", state="visible")
        
        # Get initial column count
        initial_headers = page.locator("#data-table thead th").count()
        
        # Open control panel
        page.locator("#column-visibility-toggle").click()
        page.wait_for_selector("#column-visibility-panel", state="visible")
        
        # Find a checkbox and uncheck it
        checkboxes = page.locator(".column-visibility-checkbox")
        if checkboxes.count() > 0:
            # Find a checked checkbox
            for i in range(checkboxes.count()):
                checkbox = checkboxes.nth(i)
                if checkbox.is_checked():
                    checkbox.uncheck()
                    page.wait_for_timeout(1000)  # Wait for table refresh
                    
                    # Check that column count decreased
                    new_headers = page.locator("#data-table thead th").count()
                    assert new_headers < initial_headers, "Column should have been hidden"
                    break


class TestCombinedFunctionality:
    """Test all three stories working together"""
    
    def test_complete_user_workflow(self, page: Page):
        """Test complete user workflow with all improvements"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        
        # Step 1: Select email_download_status table
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table tbody tr", state="visible")
        
        # Step 2: Verify Chinese titles (Story 1.1)
        headers = page.locator("#data-table thead th").all_text_contents()
        assert "下載成功" in headers, "Chinese titles not working"
        
        # Step 3: Verify visual tags (Story 1.2)
        status_tags = page.locator(".success-status-tag, .failed-status-tag, .unknown-status-tag").count()
        assert status_tags > 0, "Boolean visual tags not working"
        
        # Step 4: Test control panel organization (Story 1.3)
        page.locator("#column-visibility-toggle").click()
        page.wait_for_selector("#column-visibility-panel", state="visible")
        
        panel_text = page.locator("#column-visibility-panel").inner_text()
        assert "處理狀態" in panel_text, "Field categorization not working"
        assert "下載成功" in panel_text, "Chinese titles not in control panel"
        
        # Step 5: Test field visibility toggle
        download_success_checkbox = page.locator("input[value='is_remote_download_success']")
        if download_success_checkbox.count() > 0:
            initial_checked = download_success_checkbox.is_checked()
            
            # Toggle the checkbox
            download_success_checkbox.click()
            page.wait_for_timeout(1000)
            
            # Verify the column visibility changed
            headers_after = page.locator("#data-table thead th").all_text_contents()
            
            if initial_checked:
                # Was visible, now should be hidden
                assert "下載成功" not in headers_after, "Column should be hidden"
            else:
                # Was hidden, now should be visible
                assert "下載成功" in headers_after, "Column should be visible"
    
    def test_search_with_boolean_tags(self, page: Page):
        """Test search functionality works with boolean visual tags"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table tbody tr", state="visible")
        
        # Perform search
        page.locator("#search-input").fill("失敗")
        page.locator("#search-btn").click()
        page.wait_for_timeout(1000)
        
        # Check that search results still show proper tags
        failure_tags_after_search = page.locator(".failed-status-tag").count()
        
        # If there are failure tags, they should still be formatted correctly
        if failure_tags_after_search > 0:
            first_tag = page.locator(".failed-status-tag").first
            expect(first_tag).to_be_visible()
            tag_text = first_tag.inner_text()
            assert tag_text == "失敗", "Tags should remain formatted after search"
    
    def test_responsive_design(self, page: Page):
        """Test that improvements work on smaller screens"""
        # Test on tablet size
        page.set_viewport_size({"width": 768, "height": 1024})
        page.goto("http://localhost:5000/monitoring/database-manager")
        
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table", state="visible")
        
        # Check that key elements are still visible
        expect(page.locator("#column-visibility-toggle")).to_be_visible()
        
        # Check that status tags are still readable
        status_tags = page.locator(".success-status-tag, .failed-status-tag").count()
        if status_tags > 0:
            first_tag = page.locator(".success-status-tag, .failed-status-tag").first
            expect(first_tag).to_be_visible()
            
            # Check font size is appropriate for mobile
            font_size = first_tag.evaluate("el => getComputedStyle(el).fontSize")
            font_size_px = float(font_size.replace('px', ''))
            assert font_size_px >= 10, f"Font too small for mobile: {font_size_px}px"


class TestVisualRegressionAndScreenshots:
    """Test visual consistency and capture screenshots"""
    
    def test_capture_table_screenshots(self, page: Page):
        """Capture screenshots for visual regression testing"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table tbody tr", state="visible")
        
        # Create screenshots directory
        screenshot_dir = "tests/screenshots/database_field_display"
        os.makedirs(screenshot_dir, exist_ok=True)
        
        # Full page screenshot
        page.screenshot(path=f"{screenshot_dir}/full_table_view.png")
        
        # Table-only screenshot
        table = page.locator("#data-table")
        table.screenshot(path=f"{screenshot_dir}/table_only.png")
        
        # Status tags screenshot (if any exist)
        status_tags = page.locator(".success-status-tag, .failed-status-tag, .unknown-status-tag")
        if status_tags.count() > 0:
            first_row = page.locator("#data-table tbody tr").first
            first_row.screenshot(path=f"{screenshot_dir}/status_tags_row.png")
    
    def test_capture_control_panel_screenshots(self, page: Page):
        """Capture control panel screenshots"""
        page.goto("http://localhost:5000/monitoring/database-manager")
        page.locator("#table-select").select_option("email_download_status")
        page.wait_for_selector("#data-table", state="visible")
        
        # Open control panel
        page.locator("#column-visibility-toggle").click()
        page.wait_for_selector("#column-visibility-panel", state="visible")
        
        screenshot_dir = "tests/screenshots/database_field_display"
        os.makedirs(screenshot_dir, exist_ok=True)
        
        # Control panel screenshot
        panel = page.locator("#column-visibility-panel")
        panel.screenshot(path=f"{screenshot_dir}/control_panel.png")
        
        # Full page with panel open
        page.screenshot(path=f"{screenshot_dir}/full_page_with_panel.png")


# Playwright fixtures and configuration
@pytest.fixture(scope="session")
def browser_context_args(browser_context_args):
    """Configure browser context for testing"""
    return {
        **browser_context_args,
        "viewport": {"width": 1280, "height": 720},
        "ignore_https_errors": True,
    }


@pytest.fixture
def setup_test_data(page: Page):
    """Set up test data if needed"""
    # This could be used to ensure test data exists
    # For now, we'll assume the system has appropriate test data
    yield
    # Cleanup if needed


# Test configuration
@pytest.mark.slow
class TestCrossBrowserCompatibility:
    """Test improvements work across different browsers"""
    
    def test_firefox_compatibility(self, browser: Browser):
        """Test functionality in Firefox"""
        context = browser.new_context()
        page = context.new_page()
        
        try:
            page.goto("http://localhost:5000/monitoring/database-manager")
            page.locator("#table-select").select_option("email_download_status")
            page.wait_for_selector("#data-table", state="visible")
            
            # Test Chinese titles
            headers = page.locator("#data-table thead th").all_text_contents()
            assert "下載成功" in headers, "Chinese titles not working in Firefox"
            
            # Test visual tags
            tags = page.locator(".success-status-tag, .failed-status-tag").count()
            # At least some tags should exist if data is present
            
        finally:
            context.close()
    
    def test_webkit_compatibility(self, browser: Browser):
        """Test functionality in WebKit (Safari)"""
        if browser.browser_type.name != "webkit":
            pytest.skip("WebKit not available")
        
        context = browser.new_context()
        page = context.new_page()
        
        try:
            page.goto("http://localhost:5000/monitoring/database-manager")
            page.locator("#table-select").select_option("email_download_status")
            page.wait_for_selector("#data-table", state="visible")
            
            # Basic functionality should work
            expect(page.locator("#data-table")).to_be_visible()
            
        finally:
            context.close()


if __name__ == '__main__':
    # Quick test runner for development
    print("🎭 Database Field Display E2E Tests")
    print("   Target: http://localhost:5000/monitoring/database-manager")
    print("\n📋 Test Coverage:")
    print("   Story 1.1: ✅ Chinese field titles (is_remote_download_success → '下載成功')")
    print("   Story 1.2: ✅ Boolean visual tags (false → red '失敗' tag)")
    print("   Story 1.3: ✅ Field category organization panel")
    print("\n🧪 Test Types:")
    print("   • Page load and initialization")
    print("   • Chinese title display validation") 
    print("   • Boolean tag visual formatting")
    print("   • Control panel categorization")
    print("   • Complete user workflows")
    print("   • Cross-browser compatibility")
    print("   • Visual regression screenshots")
    print("\n⚠️  Prerequisites:")
    print("   • Server running at localhost:5000")
    print("   • Test data in email_download_status table")
    print("   • Playwright browsers installed")
    print("\nRun with: pytest tests/e2e/test_database_field_display_e2e.py -v")