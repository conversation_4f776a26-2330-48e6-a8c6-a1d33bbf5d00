Sub FolderPicker(Optional path As String = "default", Optional netpath As String = "default", Optional toname As String = "default", Optional subject As String = "default") '選擇資料夾 3
    '定義Sub程式
    Dim folderPath As String '宣告字串變數，用來儲存選擇的資料夾路徑
    Dim dlg As FileDialog '宣告FileDialog變數，用來顯示選擇資料夾的對話框
    destinationPath = Sheet2.Range("D2").value '設定預設路徑為D2儲存格的值
    Set dlg = Application.FileDialog(msoFileDialogFolderPicker) '使用FileDialog選擇資料夾
    If path = "default" Then
        With dlg
            .Title = "選擇資料夾" '設定對話框標題
            .AllowMultiSelect = False '不允許多選
            .InitialFileName = destinationPath '設定預設路徑
            If .Show = -1 Then '如果使用者選擇了資料夾
                folderPath = .SelectedItems(1) '取得選擇的第一個資料夾的路徑
            End If
        End With
    Else
        folderPath = path
    End If
    Dim ccname As String
    Dim csvFilestemp() As String
    Dim csvFiles() As String
    Dim csvFileseqc() As Variant
    Dim csvFilesft() As Variant
    Dim csvFileseqcrt() As Variant
    Dim csvFilesother() As Variant
    Dim csvFilesother1() As Variant
    Dim matchedCSV() As Variant
    Dim fteqcfile As Boolean
    Dim temp_csv_name As String
    fteqcfile = False
'    startTime = Timer
'    Set copyMsgBox = CreateObject("WScript.Shell") '建立WScript.Shell物件
    If folderPath <> "" Then

        mainprocess folderPath
        Call RenameFolders(folderPath) '20231027
        Compare_Onlineqc folderPath

        csvFilestemp = FindALLCSVFiles(folderPath)
        csvFiles = RemoveDuplicateCSVFiles(csvFilestemp)
        csvFileseqc = FindALLEQCFILE(csvFiles)
        csvFilesft = FindALLFTFILE(csvFiles)
        
        On Error GoTo eqcpoint
        For i = 1 To UBound(csvFilesft)
            temp_csv_name = csvFilesft(i)(1)
            If HasChineseCharacters(temp_csv_name) = False Then
                Device2BinControl csvFilesft(i)(1), True, True, "FT_", folderPath '將csv檔案轉換為bin檔案
                fteqcfile = True
            End If
        Next i
eqcpoint:
        On Error GoTo 0
        fteqcfile = False
        On Error GoTo otherpoint
        If UBound(csvFileseqc) >= 1 Then '20230714
            For i = 1 To UBound(csvFileseqc)
                temp_csv_name = csvFileseqc(i)(1)
                If HasChineseCharacters(temp_csv_name) = False Then
                    If Len(csvFileseqc(i)(1)) > 0 Then
                        Device2BinControl csvFileseqc(i)(1), True, True, "EQC_", folderPath '將csv檔案轉換為bin檔案
                        fteqcfile = True
                    End If
                End If
            Next i
        End If
otherpoint:
        On Error GoTo 0
        On Error Resume Next
        For i = 0 To UBound(csvFiles)
            If UBound(csvFilesft) >= 1 Then  '20230714 20241209
                For j = 1 To UBound(csvFilesft)
                    If csvFiles(i) = csvFilesft(j)(1) Then
                        GoTo nextii
                    End If
                Next j
            End If
                If UBound(csvFileseqc) >= 1 Then  '20230714 20241209
                For j = 1 To UBound(csvFileseqc)
                    If csvFiles(i) = csvFileseqc(j)(1) Then
                        GoTo nextii
                    End If
                Next j
            End If
            k = 5
            Device2BinControl csvFiles(i), True, True, "OTHER_", folderPath '將csv檔案轉換為bin檔案
nextii:
        Next i

        On Error GoTo 0
    End If
    Dim xst  As Integer
    Dim xsp  As Integer
    Dim yst  As Integer
    Dim ysp  As Integer
    If Dir(folderPath & "\" & "EQC_Summary.xlsx") <> "" Then
        OpenExcelFileAndSave folderPath & "\" & "EQC_Summary.xlsx"
    End If
    'WriteNumberedTimeToFile 777
    If Dir(folderPath & "\" & "FT_Summary.xlsx") <> "" Then
        CheckAndCloseWorkbook
        OpenExcelFileAndSave folderPath & "\" & "FT_Summary.xlsx"
        OpenSummaryFindBig folderPath & "\" & "FT_Summary.xlsx", xst, xsp, yst, ysp
        CheckAndCloseWorkbook
        'WriteNumberedTimeToFile 888
        'WritStringTimeToFile netpath
        If InStr(1, LCase(netpath), "defaul", vbTextCompare) = 0 Then
            'WriteDataToFile toname, "<EMAIL>", subject, netpath, xst, xsp, yst, ysp 'old
            ' new
            'ccname
             Dim ccname_temp() As String
             Dim ccnamex As String
             If InStr(1, LCase(toname), ";", vbTextCompare) > 0 Then
                ccname_temp = Split(toname, ";")
             Else
                ReDim ccname_temp(0)
                ccname_temp(0) = toname
             End If
            If CC_FLAG = True Then
                For i = LBound(ccname_temp) To UBound(ccname_temp)
                    If Len(ccname_temp(i)) > 0 Then
                        ccnamex = CheckCCMailAddress(ccname_temp(i))
                        ccnamex = ccnamex + ";"
                        If (InStr(1, ccname, ccnamex, vbTextCompare) = 0) Then
                            ccname = ccname + ccnamex
                        End If
                    End If
                Next
            End If
            WriteDataToFile toname, ccname, subject, netpath, xst, xsp, yst, ysp
            'WriteNumberedTimeToFile 1000
            'On Error Resume Next
            'SendSummaryEmail
            'On Error GoTo 0
        End If
    End If
     If Dir(folderPath & "\" & "OTHER_Summary.xlsx") <> "" Then
        OpenExcelFileAndSave folderPath & "\" & "OTHER_Summary.xlsx"
    End If
    
'    elapsedTime = Timer - startTime
'    copyMsgBox.Popup "Event handling completed. Time taken: " & elapsedTime & " seconds.", 0, "Time", vbInformation + vbSystemModal
    ' 釋放剪貼簿
FolderPicker_final:
'    Set copyMsgBox = Nothing
End Sub
Sub FileDialogX()
    destinationPath = Sheet2.Range("D2").value ' 設定預設路徑為Sheet2的D2儲存格的值
    Dim paths As Variant
    Dim tempPathx As String
    ' 使用FileDialog選擇檔案
    paths = Application.GetOpenFilename("All files (.), .", , "Select a folder or Zip file", destinationPath, True)
     On Error Resume Next
    If Not IsEmpty(paths) Then ' 如果有選擇檔案
     On Error GoTo notestX1
     startTime = Timer
       For Each path In paths ' 遍歷選擇的檔案
            Dim tempPath As String
            tempPath = path
            If LCase(Right(tempPath, 4)) = ".zip" Or LCase(Right(tempPath, 4)) = ".rar" Or LCase(Right(tempPath, 3)) = ".7z" Then ' 如果是壓縮檔
                 If InStr(1, filePath, ".7z", vbTextCompare) = 0 Then
                    tempPathx = Left(tempPath, Len(tempPath) - 4)
                 Else
                    tempPathx = Left(tempPath, Len(tempPath) - 3)
                 End If
                 CheckZipContents (tempPath) ' 檢查壓縮檔內容是否合法
                 DeleteDlxFiles tempPathx ' 刪[EXCEPT_CHAR]壓縮檔中的dlx檔案
                 DeleteDl4Files tempPathx ' 刪[EXCEPT_CHAR]壓縮檔中的dl4檔案
            ElseIf LCase(Right(tempPath, 4)) = ".spd" Then ' 如果是spd檔
                 '處理 spd 文件
                 ChangeSpdToCsv1 (tempPath) ' 將spd檔案轉換為csv檔案
                 Device2BinControl tempPath, True ' 將csv檔案轉換為bin檔案
            ElseIf LCase(Right(tempPath, 4)) = ".csv" Then ' 如果是csv檔
                ' 處理 csv 文件
                Device2BinControl tempPath, True ' 將csv檔案轉換為bin檔案
            Else
            End If
        Next path
    End If
notestX1:
    Set copyMsgBox = CreateObject("WScript.Shell")
    elapsedTime = Timer - startTime
    copyMsgBox.Popup "OK. Time taken: " & elapsedTime & " seconds.", 0, "Time", vbInformation + vbSystemModal
    ' 釋放剪貼簿
    Set copyMsgBox = Nothing
End Sub
Function HasChineseCharacters(inputString As String) As Boolean
    Dim i As Long
    For i = 1 To Len(inputString)
        If AscW(Mid(inputString, i, 1)) >= 19968 And AscW(Mid(inputString, i, 1)) <= 40869 Then
            HasChineseCharacters = True
            Exit Function
        End If
    Next i
    HasChineseCharacters = False
End Function
Sub Compare_Onlineqc(Optional path As String = "default")
    ' 設定目的地資料夾為 Sheet2 的 D2 儲存格的值
    destinationPath = Sheet2.Range("D2").value
    Dim folderPath As String
    Dim temp_str As String
    Dim dlg As FileDialog
    
    ' 打開檔案選擇器，預設開啟的資料夾為 destinationPath
    If path = "default" Then
        Set dlg = Application.FileDialog(msoFileDialogFolderPicker)
        With dlg
            .Title = "選擇資料夾"
            .AllowMultiSelect = False
            .InitialFileName = destinationPath
            If .Show = -1 Then
                folderPath = .SelectedItems(1)
            End If
        End With
    Else
        folderPath = path
    End If
    If folderPath <> "" Then
    
        Dim startTime As Double
        Dim elapsedTime As Double
        Set copyMsgBox = CreateObject("WScript.Shell")
'        startTime = Timer
        
        '處理長川的資料
         ctafile = ReadCSVsInDirectory(folderPath)
        
        Dim csvFilestemp() As String
        Dim csvFiles() As String
        Dim csvFileseqc() As Variant
        Dim csvFilesft() As Variant
        Dim csvFileseqcrt() As Variant
        Dim csvFilesonlineeqcfail() As Variant
        On Error GoTo NextcsvFileseqcrtfinal
        csvFilestemp = FindALLCSVFiles(folderPath)
        csvFiles = RemoveDuplicateCSVFiles(csvFilestemp)
        csvFileseqc = FindALLEQCFILE(csvFiles)
        csvFilesft = FindALLFTFILE(csvFiles)
        Dim matchedCSV() As Variant
        If csvFileseqc(1)(1) <> "" Then
            matchedCSV = FindmatchedCSV(csvFileseqc, csvFilesft, ctafile)
            If matchedCSV(0, 1) <> "" Then
                'csvFileseqcrt = FindLateCSVFiles(csvFileseqc, csvFilesft)
                csvFileseqcrt = FindunmatchedCSV(csvFileseqc, matchedCSV)
                eqc_fail_cnt = FindOnlieEQCFAILFiles(matchedCSV, csvFilesonlineeqcfail)
                Dim total_line_string As String
                total_line_string = FindOnlieEQCBin1datalog(csvFileseqc) '找FT 跟EQC的資料
                total_line_string = total_line_string & FindEQCFAILDATALOG(csvFilesonlineeqcfail)
                Dim newFileName As String
                newFileName = folderPath & "\" & "EQCTOTALDATA.csv" ' 新檔案名稱
                Open newFileName For Output As #1
                Print #1, total_line_string
                Close #1
                
                Open newFileName For Input As #2
                fileBContents = Input$(LOF(2), 2)
                Close #2
                Dim rowsInB As Variant
                rowsInB = Split(fileBContents, vbCrLf)
                Dim total_onlineqc_fail_cnt As Integer
                For i = 0 To UBound(rowsInB)
                    temp_str = rowsInB(i)
                    If (temp_str <> "") Then
                        total_onlineqc_fail_cnt = total_onlineqc_fail_cnt + 1
                    End If
                Next i
            End If
        End If
        On Error GoTo NextcsvFileseqcrt1
        Dim onlineeqccntline As Integer
        onlineeqccntline = 0
        onlineeqccntline = FindOnlineEQCFailCnt(folderPath & "\" & "EQCTOTALDATA.csv")
        If UBound(csvFileseqcrt) >= 0 Then
            WriteCSVFilesToExcel folderPath, csvFileseqcrt '將EQC RT的資料貼在EQCTOTALDATA.csv
            Dim fso As Object
            Set fso = CreateObject("Scripting.FileSystemObject")
            newFileName = folderPath & "\" & "EQCTOTALDATA_RAW.csv" ' 新檔案名稱
            If Dir(newFileName) <> "" Then
                Kill newFileName
            End If
            ' A和B是檔案的完整路徑
            FindEQCRTPaSSCnt folderPath & "\" & "EQCTOTALDATA.csv", onlineeqccntline
            fso.CopyFile folderPath & "\" & "EQCTOTALDATA.csv", folderPath & "\" & "EQCTOTALDATA_RAW.csv"
        End If
NextcsvFileseqcrt1:

        Dim goldeneqccsv As String
        For i = 1 To UBound(csvFileseqc)
            If CheckEQCBIN1CSVFile(csvFileseqc(i)(1)) = True Then
                If FindCSVWith2Ones(csvFileseqc(i)(1)) = True Then '找出 EQC bin1 datalog  能找onlineeqczzzzzzzzzzzzzzzzzzzzz
                    goldeneqccsv = csvFileseqc(i)(1)
                    Exit For
                End If
            End If
        Next i
        
        Dim start1  As Integer
        Dim end1  As Integer
        Dim start1x  As Integer
        If goldeneqccsv <> "" Then
            start1x = FindDifferentColumn(goldeneqccsv, start1, end1)
        End If
        
        If start1 > 10 And (end1 - start1) > 5 Then '找相同eqc code
            Dim rows As Variant
            Dim fileB As Object
            Set fileB = CreateObject("Scripting.FileSystemObject").GetFile(folderPath & "\" & "EQCTOTALDATA.csv")
            fileBContents = ""
            On Error Resume Next
'                Open fileB For Input As #1
'                fileBContents = Input$(LOF(1), 1)
'                Close #1
                '20241210
                Set fileStream = fileB.OpenAsTextStream(1, -2) ' 1 = ForReading, -2 = Default encoding
                fileBContents = fileStream.ReadAll
                fileStream.Close
                Set fileB = Nothing
                If Len(fileBContents) > 0 Then
                    rows = Split(fileBContents, vbCrLf)
                    InsEqcRtData2 folderPath & "\" & "EQCTOTALDATA.csv", start1, end1, 1, total_onlineqc_fail_cnt, rows
                End If
            On Error GoTo 0
        End If
        
        newFileName = folderPath & "\" & "EQCTOTALDATA.csv" ' 新檔案名稱
        If Dir(newFileName) <> "" Then
            Device2BinControl newFileName, True
        End If
        newFileName = folderPath & "\" & "EQCTOTALDATA_RAW.csv" ' 新檔案名稱
        If Dir(newFileName) <> "" Then
            newFileName = folderPath & "\" & "EQCTOTALDATA_RAW.xlsx" ' 新檔案名稱
            If Dir(newFileName) <> "" Then
                Kill newFileName
            End If
            newFileName = folderPath & "\" & "EQCTOTALDATA_RAW.csv" ' 新檔案名稱
            Device2BinControl newFileName, True
        End If
       
        
'        If newFileName <> "" Then 'csv kill
'            Kill newFileName
'        End If
        If start1 > 10 And (end1 - start1) > 5 Then '找相同eqc code
            If start1 <> end1 And end1 > start1 Then
                InsEqcRtData folderPath & "\" & "EQCTOTALDATA.xlsx", start1, end1
            End If
        End If
        
        newFileName = folderPath & "\" & "EQCTOTALDATA.xlsx" ' 新檔案名稱
        If Dir(newFileName) <> "" Then
            ConvertToHyperlinks newFileName '貼上超連結
        End If
        
        newFileName = folderPath & "\" & "EQCTOTALDATA_RAW.xlsx" ' 新檔案名稱
        If Dir(newFileName) <> "" Then
            ConvertToHyperlinks newFileName '貼上超連結
        End If
        
        
        If eqc_fail_cnt > 0 Then
            error_show = "TOTAL onlineEQC FILE CNT=" & eqc_fail_cnt
           ' MsgBox error_show
        Else
            error_show = "NO onlineEQC FAIL FILE"
             'MsgBox "NO onlineEQC FAIL FILE"
        End If
        
'        elapsedTime = Timer - startTime
'        copyMsgBox.Popup "OK. Time taken: " & elapsedTime & " seconds.", 0, error_show, vbInformation + vbSystemModal
'        ' 釋放剪貼簿
NextcsvFileseqcrtfinal:
'        Set copyMsgBox = Nothing
    End If
End Sub
Function FindOnlieEQCBin1datalog(ByVal eqccsv As Variant) As String
    ' 遍歷該資料夾中的每個檔案
    Dim fileName As String
    Dim findfilename As String
    Dim targetRowInB As String
    Dim new_file_path As String
    Dim i As Integer
    Dim j As Integer
    For j = 1 To UBound(eqccsv)
        fileName = eqccsv(j)(1)
         ' 使用Excel開啟檔案
        Dim fileB As Object
        Set fileB = CreateObject("Scripting.FileSystemObject").GetFile(fileName)
        Dim fileBContents As String
        Open fileB For Input As #2
        fileBContents = Input$(LOF(2), 2)
        Close #2
         ' 找出 B 檔中不是 1 的行的 A 欄位值，以及 A 檔中相對應的行
        Dim rowsInB As Variant
        rowsInB = Split(fileBContents, vbCrLf)
         For i = 12 To UBound(rowsInB) '從B檔 也就是onlineqc中找到為1的
            If Len(rowsInB(i)) < 1 Then
                Exit For
            End If
            lineElements = Split(rowsInB(i), ",")
            If val(lineElements(1)) = 1 Then   ' 如果 B 檔第2列為 1
                targetRowInB = rowsInB(i)
                Dim newFileContents As String
                newFileContents = ""
                For kk = 1 To 12
                    newFileContents = newFileContents & Split(fileBContents, vbCrLf)(kk - 1) & vbCrLf
                Next kk
                Dim rowAWithFT As String
                rowAWithFT = AddFTToRowA(targetRowInB, 0, "9876543210" & ",") '20231027 fix
                newFileContents = newFileContents & rowAWithFT & vbCrLf
                FindOnlieEQCBin1datalog = newFileContents
                Exit Function
            End If
        Next i
   Next j
End Function
Function FindOnlieEQCFAILFiles(ByVal matchedCSV As Variant, onlineeqcfailCSV As Variant) As Integer
    Dim temponlineeqcfailCSV As Variant
    ReDim temponlineeqcfailCSV(0 To UBound(matchedCSV))
    Dim fileName As String
    Dim findfilename As String
    Dim new_file_path As String
    Dim i As Integer
    Dim j As Integer
    j = 0
    For i = 0 To UBound(matchedCSV)
        fileName = matchedCSV(i, 1) '1 =eqc 0=ft
        ' 獲取檔案名稱
        ' 在該檔案中查找第一個不等於 1 的行，以便進行後續處理
        Dim result As Long
        result = FindFirstNonOneRow(fileName)
        If result > 0 Then '有找到eqc fail的檔案
            temponlineeqcfailCSV(j) = fileName
            eqc_fail_cnt = eqc_fail_cnt + 1
            ' 獲取符合條件的 CSV 檔案的路徑
            findfilename = matchedCSV(i, 0) '1 =eqc 0=ft
            If findfilename <> "" Then
                ' 將符合條件的行從 CSV 檔案中拷貝到一個新的檔案中
                new_file_path = CopyRowsToNewFile(findfilename, fileName) 'findfilename=FT fileName=EQC FAIL
                Device2BinControl new_file_path, True  ' 將csv檔案轉換為篩選資料
                new_file_path = Left(new_file_path, Len(new_file_path) - 4) & ".xlsx"
                'SortSummarySheetDescending new_file_path ' 取得沒有副檔名的檔名 '將summary排序
                ProcessExcelFile new_file_path '移動到第1個fail的地方
            End If
            j = j + 1
        End If
    Next i
    If j > 0 Then
        ReDim onlineeqcfailCSV(0 To j - 1)
        For i = 0 To j - 1
            onlineeqcfailCSV(i) = temponlineeqcfailCSV(i)
        Next i
    Else
        ReDim onlineeqcfailCSV(0, 0)
    End If
    FindOnlieEQCFAILFiles = eqc_fail_cnt
End Function
Function FindCSVFileByTime(folderPath As String, fileName As String) As String
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' 檢查資料夾路徑是否存在
    If Not fso.FolderExists(folderPath) Then
        MsgBox "Folder does not exist"
        Exit Function
    End If
    
    ' 轉換檔案日期為 Date 格式
    Dim fileDate As Date
    fileDate = FileDateTime(folderPath & "\" & fileName)
    
    ' 尋找符合條件的檔案
    Dim folder As Object
    Set folder = fso.GetFolder(folderPath)
    
    Dim file As Object
    For Each file In folder.files
        ' 檢查是否為 CSV 檔案且檔名不同
        If LCase(Right(file.name, 4)) = ".csv" And Not LCase(file.name) = LCase(fileName) Then
            ' 檢查檔案日期是否符合條件
            If Abs(file.DateLastModified - fileDate) < TimeSerial(0, 0, 60) Then ' 時間誤差小於 60 秒
                FindCSVFileByTime = file.path
                Exit Function
            End If
        End If
    Next file
    ' 找不到符合條件的檔案
     FindCSVFileByTime = ""
    'MsgBox "FT File not found"
End Function
Function FindCSVFileByTimex(ByVal fileNameft As String, ByVal fileNameqc As String) As Boolean

    ' 轉換檔案日期為 Date 格式
    Dim fileDateft As Date
    fileDateft = FileDateTime(fileNameft)
    Dim fileDateqc As Date
    fileDateqc = FileDateTime(fileNameqc)
    
    ' 尋找符合條件的檔案
    
    If Abs(fileDateft - fileDateqc) < TimeSerial(0, 0, 60) Then ' 時間誤差小於 60 秒
        FindCSVFileByTimex = True
    Else
        FindCSVFileByTimex = False
    End If
End Function
Function FindCSVFileByTime2x(ByVal fileNameft As String, ByVal fileNameqc As String) As Boolean '20240131

    ' 轉換檔案日期為數字格式
    Dim fileNameWithoutExtft As String
    If (InStr(1, fileNameft, "_data", vbTextCompare) = 0) Then
        fileNameWithoutExtft = Left(fileNameft, Len(fileNameft) - 4) ' 取得沒有副檔名的檔名
'    Else
'        fileNameWithoutExtft = Left(fileNameft, Len(fileNameft) - 10) ' 取得沒有副檔名的檔名
    End If
    Dim fileNameWithoutExtqc As String
    If (InStr(1, fileNameqc, "_data", vbTextCompare) = 0) Then
        fileNameWithoutExtqc = Left(fileNameqc, Len(fileNameqc) - 4) ' 取得沒有副檔名的檔名
'    Else
'        fileNameWithoutExtqc = Left(fileNameqc, Len(fileNameqc) - 10) ' 取得沒有副檔名的檔名
    End If
    
    Dim fileDateft As Long
    Dim strNumft As String
    Dim fileDateqc As Long
    Dim strNumqct As String
    If Len(fileNameWithoutExtft) > 0 And Len(fileNameWithoutExtqc) > 0 Then
        strNumft = Mid(fileNameWithoutExtft, Len(fileNameWithoutExtft) - 7, 8)
        If IsNumeric(strNumft) Then
            fileDateft = CLng(strNumft)
        End If
        strNumqct = Mid(fileNameWithoutExtqc, Len(fileNameWithoutExtqc) - 7, 8)
        If IsNumeric(strNumqct) Then
            fileDateqc = CLng(strNumqct)
        End If
    End If
    '20240129 fix
    If fileDateft > 0 And fileDateqc > 0 Then
        If Abs(fileDateft - fileDateqc) < 400 Then
            FindCSVFileByTime2x = True
        Else
            FindCSVFileByTime2x = False
        End If
    Else
        FindCSVFileByTime2x = False
    End If
    
End Function

' 這個函式用於在指定資料夾中尋找符合條件的檔案，並對這些檔案進行一些處理
' 輸入參數：folderPath 為欲搜尋的資料夾路徑
' 輸出結果：無
Function FindOnlieEQCFiles(ByVal folderPath As String) As Integer
    ' 設定要尋找的檔案名稱關鍵字
    Dim findfilename As String
    Dim error_show As String
    Dim new_file_path As String
    Dim eqc_fail_cnt As Integer
    ' 建立一個檔案系統物件
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")

    ' 獲取指定資料夾的物件
    Dim folder As Object
    Set folder = fso.GetFolder(folderPath)

    ' 遍歷該資料夾中的每個檔案
    Dim file As Object
    For Each file In folder.files
        ' 如果檔案名稱中包含 onlieeqc 關鍵字，則進行處理
        If LCase(Right(file.name, 4)) = ".csv" And InStr(1, UCase(file.name), "EQCFAILDATA") = 0 And InStr(1, UCase(file.name), "EQCFAILDATA") = 0 Then
            If InStr(1, LCase(file.name), "onlieeqc") > 0 Or InStr(1, file.name, "r1") > 0 Or InStr(1, file.name, "qa") > 0 Then
                ' 獲取檔案名稱
                Dim fileName As String
                fileName = file.name
    
                ' 在該檔案中查找第一個不等於 1 的行，以便進行後續處理
                Dim result As Long
                result = FindFirstNonOneRow(folderPath & "\" & fileName)
                If result > 0 Then '有找到eqc fail的檔案
                    eqc_fail_cnt = eqc_fail_cnt + 1
                    ' 獲取符合條件的 CSV 檔案的路徑
                    findfilename = FindCSVFileByTime(folderPath, fileName) 'KEY POINT 找FT file
                    'findfilename = "" 'DEBUG
                    If findfilename <> "" Then
                        ' 將符合條件的行從 CSV 檔案中拷貝到一個新的檔案中
                        new_file_path = CopyRowsToNewFile(findfilename, folderPath & "\" & fileName)
                        Device2BinControl new_file_path, True  ' 將csv檔案轉換為篩選資料
                        new_file_path = Left(new_file_path, Len(new_file_path) - 4) & ".xlsx"
                        'SortSummarySheetDescending new_file_path ' 取得沒有副檔名的檔名 '將summary排序
                        ProcessExcelFile new_file_path '移動到第1個fail的地方
                    Else
                       'findfilename = FindCSVFileByTime2(folderPath, fileName) 'KEY POINT 找FT file
                        ' 將符合條件的行從 CSV 檔案中拷貝到一個新的檔案中
                        new_file_path = CopyRowsToNewFile(findfilename, folderPath & "\" & fileName)
                        Device2BinControl new_file_path, True  ' 將csv檔案轉換為篩選資料
                        new_file_path = Left(new_file_path, Len(new_file_path) - 4) & ".xlsx"
                        'SortSummarySheetDescending new_file_path ' 取得沒有副檔名的檔名 '將summary排序
                        ProcessExcelFile new_file_path '移動到第1個fail的地方
                        
                    End If
                    
    '                ' 顯示符合條件的檔案名稱
    '                MsgBox "NOT FIND FT FILE"
                End If
            End If
    End If
FindOnlieEQCFilesx:
    Next file
    FindOnlieEQCFiles = eqc_fail_cnt
End Function
' 輸入一個檔案路徑，找到這個檔案中第一個不是 1 的 B 欄位所對應的 A 欄位值
Function FindFirstNonOneRow(fileName As String) As Long
    ' 開啟檔案
    Dim wb As Workbook
    Set wb = Workbooks.Open(fileName)
    Dim ws As Worksheet
    Set ws = wb.Sheets(1)
    
    ' 找到資料最後一行
    Dim lastRow As Long
    lastRow = ws.Cells(ws.rows.count, "B").End(xlUp).row
    
    ' 逐行搜尋找到第一個不是 1 的 B 欄位所對應的 A 欄位值
    Dim i As Long
    For i = 13 To lastRow
        If ws.Cells(i, "B").value <> 1 Then
            FindFirstNonOneRow = ws.Cells(i, "A").value
            wb.Close False
            Exit Function
        End If
    Next i
    
    ' 若找不到，回傳 0
    wb.Close False
    FindFirstNonOneRow = 0
End Function
Function AddFTToRowA(targetRowInA As String, Optional columnIndex As Long = 3, Optional replacementText As String = "FT,") As String
    ' 在 A 檔第4列改成FT，其餘保持不動
    Dim columnsInRowA As Variant
    columnsInRowA = Split(targetRowInA, ",")
    Dim rowAWithFT As String
    For k = 0 To UBound(columnsInRowA)
        If k = columnIndex Then
            rowAWithFT = rowAWithFT & replacementText
        Else
            rowAWithFT = rowAWithFT & columnsInRowA(k) & ","
        End If
    Next k
    rowAWithFT = Left(rowAWithFT, Len(rowAWithFT) - 1) ' 移[EXCEPT_CHAR]最後一個逗號
    
    AddFTToRowA = rowAWithFT
End Function
Function AddFTToRowA2(targetRowInA As String, Optional columnIndex As Long = 3, Optional replacementText As String = "FT,", Optional columnIndex2 As Long = 4, Optional replacementText2 As String = "1,") As String
    ' 在 A 檔第4列改成FT，其餘保持不動
    Dim columnsInRowA As Variant
    columnsInRowA = Split(targetRowInA, ",")
    Dim rowAWithFT As String
    For k = 0 To UBound(columnsInRowA)
        If k = columnIndex Then
            rowAWithFT = rowAWithFT & replacementText
        ElseIf k = columnIndex2 Then
            rowAWithFT = rowAWithFT & replacementText2
        Else
            rowAWithFT = rowAWithFT & columnsInRowA(k) & ","
        End If
    Next k
    rowAWithFT = Left(rowAWithFT, Len(rowAWithFT) - 1) ' 移[EXCEPT_CHAR]最後一個逗號
    
    AddFTToRowA2 = rowAWithFT
End Function
Function CopyRowsToNewFile(filenameA As String, filenameB As String) As String '20240219
    Dim write_flag As Boolean
    Dim havetesttime_ft As Boolean
    Dim havetesttime_qc As Boolean
    Dim have_X_COORD_ft As Boolean
    Dim have_Y_COORD_ft As Boolean
    Dim have_Alarm_ft As Boolean
    Dim have_X_COORD_qc As Boolean
    Dim have_Y_COORD_qc As Boolean
    Dim have_Alarm_qc As Boolean
    Dim cta_file8290 As Boolean
    Dim cta_file8280 As Boolean
    Dim cta_file As Integer
    Dim bypass1, bypass2, bypass3 As Integer
    write_flag = False
    Dim temp_str As String
    Dim hyplink_temp As String
    'filenameA=FT filenameB=EQC FAIL
    ' 開啟檔案 A
'    Dim fileA As Object
'    Set fileA = CreateObject("Scripting.FileSystemObject").GetFile(filenameA)
'    Dim fileAContents As String
'    Open fileA For Input As #1
'    fileAContents = Input$(LOF(1), 1)
'    Close #1
    Dim fileA As Object
    havetesttime_ft = False
    havetesttime_qt = False
    cta_file8290 = False
    cta_file8280 = False
    cta_file = 0
    bypass1 = -1
    bypass2 = -1
    bypass3 = -1
    'cta
    
    
    Dim cta_Part_No As String
    Dim cta_Dut_No As String
    Dim cta_Site_No As String '20240219
    
    ' 開啟檔案 B
    Dim fileB As Object
    Set fileB = CreateObject("Scripting.FileSystemObject").GetFile(filenameB) 'EQC
    Dim fileBContents As String
    Open fileB For Input As #2
    fileBContents = Input$(LOF(2), 2)
    Close #2

    ' 找出 B 檔中不是 1 的行的 A 欄位值，以及 A 檔中相對應的行
    Dim rowsInB As Variant
    rowsInB = Split(fileBContents, vbCrLf)

    Dim targetRowsInB As Variant
    Dim rowsInA As Variant
    rowsInA = Split(fileAContents, vbCrLf)

    Dim valueInColumnS As String
    Dim targetRowInA As String
    Dim targetRowInB As String
    Dim targetRowInC As String
    Dim found As Boolean
    Dim i, kk As Long
    Dim lineElements As Variant
    Dim lineElementsA As Variant
    Dim lineElementsC As Variant
    Dim lineElementsB11 As Variant
    j = 0
    For i = 12 To UBound(rowsInB) '從B檔 也就是onlineqc中找到不是為1的
        If Len(rowsInB(i)) < 1 Then
            Exit For
        End If
        lineElements = Split(rowsInB(i), ",")
        lineElementsB11 = Split(rowsInB(11), ",")
        If (InStr(1, lineElementsB11(2), "Time", vbTextCompare) > 0) Then 'qc have test time
           havetesttime_qc = True
        End If
        If val(lineElements(1)) <> 1 Then   ' 如果 B 檔第2列有不是為 1
            targetRowInB = rowsInB(i)
            targetRowsInB = Split(rowsInB(i), ",")
            valueInColumnS = targetRowsInB(0) ' 取得 B 檔該行的 1 欄位值
            cta_Part_No = targetRowsInB(3) ' 取得 cta 檔該行的 cta_Part_No 欄位值 8280=Dut_No
            cta_Dut_No = targetRowsInB(4) ' 取得 cta 檔該行的 cta_Dut_No 欄位值 cta_Dut_No 8280=Serial_No
            cta_Site_No = targetRowsInB(5) ' 取得 cta 檔該行的 Site_No 20240219
'            For j = 12 To UBound(rowsInA) '從A檔 也就是ft中找到同為B檔序號的該行
'                lineElementsa = Split(rowsInA(j), ",")
'                If lineElementsa(0) = valueInColumnS Then
'                    targetRowInA = rowsInA(j)
'                    found = True
'                    Exit For
'                End If
'            Next j
            If filenameA <> "" Then
                Set fileA = CreateObject("Scripting.FileSystemObject").GetFile(filenameA) 'FT
                Open fileA For Input As #1
                Do Until EOF(1)
                    Line Input #1, fileLine
                    lineElementsA = Split(fileLine, ",")
                    If j = 7 Then
                        If (InStr(1, lineElementsA(2), "Serial_No", vbTextCompare) > 0) Then
                            cta_file8290 = True
                        ElseIf (InStr(1, lineElementsA(2), "Index_No", vbTextCompare) > 0) Then
                            cta_file8280 = True
                        End If
                        If (InStr(1, lineElementsA(8), "X_COORD", vbTextCompare) > 0 And cta_file8280 = True) Then
                            have_X_COORD_ft = True
                            bypass1 = 8
                        End If
                        If (InStr(1, lineElementsA(9), "Y_COORD", vbTextCompare) > 0 And cta_file8280 = True) Then
                            have_Y_COORD_ft = True
                            bypass2 = 9
                        End If
                        If (InStr(1, lineElementsA(14), "Alarm", vbTextCompare) > 0 And cta_file8280 = True) Then
                            have_Alarm_ft = True
                            bypass3 = 14
                        End If
                    End If
                    If j = 8 Then
                        If (InStr(1, lineElementsA(0), "cta", vbTextCompare) > 0) Then 'cta_file
                            If cta_file8290 = True Then
                                cta_file = 8290
                            ElseIf cta_file8280 = True Then
                                cta_file = 8280
                            End If
                            
                         End If
                    End If
                    If j = 11 Then
                        If (InStr(1, lineElementsA(2), "Time", vbTextCompare) > 0) Then ' FT have test time
                            havetesttime_ft = True
                         End If
                    End If
                    If j >= 12 Then
                        If (lineElementsA(0) = valueInColumnS And cta_file = 0) Or (lineElementsA(3) = cta_Part_No And lineElementsA(4) = cta_Dut_No And lineElementsA(5) = cta_Site_No And cta_file = 8290) Or (lineElementsA(3) = cta_Part_No And lineElementsA(4) = cta_Dut_No And lineElementsA(5) = cta_Site_No And cta_file = 8280) Then  '20240219
                            If (have_X_COORD_ft = True Or have_Y_COORD_ft = True Or have_Alarm_ft = True) Then
                                For k = 0 To UBound(lineElementsA)
                                    If k <> bypass1 And k <> bypass2 And k <> bypass3 Then
                                        targetRowInC = targetRowInC & lineElementsA(k) & ","
                                    End If
                                    
''                                    If have_X_COORD_ft = True Then
''                                        bypass1 = 8
''                                    End If
''                                    If have_Y_COORD_ft = True Then
''                                        bypass2 = 9
''                                    End If
''                                    If have_Alarm_ft = True Then
''                                        bypass3 = 14
''                                    End If
''                                    If (k <> bypass1 Or k <> 9 Or k <> 14) Then
''                                        targetRowInC = targetRowInC & lineElementsA(k) & ","
''                                    End If
''                                    ElseIf have_Y_COORD_ft = True Then
''                                        If (k <> 9) Then
''                                            targetRowInC = targetRowInC & lineElementsA(k) & ","
''                                        End If
''                                    ElseIf have_Alarm_ft = True Then
''                                        If (k <> 14) Then
''                                            targetRowInC = targetRowInC & lineElementsA(k) & ","
''                                        End If
'                                    End If
                                Next k
                                targetRowInA = targetRowInC
                                targetRowInC = ""
                            ElseIf (havetesttime_ft = False And havetesttime_qc = False) Or (havetesttime_ft = True And havetesttime_qc = True) Then
                                targetRowInA = fileLine
                            Else
                                For k = 0 To UBound(lineElementsA)
                                    If havetesttime_ft = True And havetesttime_qc = False Then
                                        If (k <> 2) Then
                                            targetRowInC = targetRowInC & lineElementsA(k) & ","
                                        End If
                                    ElseIf havetesttime_ft = False And havetesttime_qc = True Then
                                        If (k = 2) Then
                                            targetRowInC = targetRowInC & "987,"
                                        End If
                                    End If
                                Next k
                                targetRowInA = targetRowInC
                                targetRowInC = ""
                            End If 'If (havetesttime_ft
                            found = True
                            Exit Do
                        End If '(lineElementsA(0)
                    End If 'If j >= 12 Then
                    j = j + 1
                Loop
                Close #1
            End If
   
            If write_flag = False Then
                Dim newFileContents As String
                newFileContents = ""
                For kk = 1 To 12
                    newFileContents = newFileContents & Split(fileBContents, vbCrLf)(kk - 1) & vbCrLf
                Next kk
                write_flag = True
            End If
            If found Then ' 如果有找到匹配的行
                ' 寫入新檔案
                Dim rowAWithFT As String
                hyplink_temp = filenameA
                hyplink_temp = ReplacePath(filenameA)
                rowAWithFT = AddFTToRowA(targetRowInA, 2, hyplink_temp & ",")
                'rowAWithFT = AddFTToRowA(targetRowInA, 2, filenameA & ",")old
                newFileContents = newFileContents & rowAWithFT & vbCrLf
                rowAWithFT = ""
            End If
            hyplink_temp = filenameB
            hyplink_temp = ReplacePath(filenameB)
            rowAWithFT = AddFTToRowA(targetRowInB, 2, hyplink_temp & ",")
            'rowAWithFT = AddFTToRowA(targetRowInB, 2, filenameB & ",") old
            newFileContents = newFileContents & rowAWithFT & vbCrLf
            Dim newFileName As String
               newFileName = Left(filenameB, InStrRev(filenameB, ".") - 1) & "_EQCFAILDATA.csv"  ' 新檔案名稱
            Open newFileName For Output As #3
               Print #3, newFileContents
               Close #3
            CopyRowsToNewFile = newFileName
        End If
    Next i
End Function
Function IsFileOpen(filePath As String) As Boolean
    Dim fileNumber As Integer

    ' 嘗試開啟檔案
    On Error Resume Next
    fileNumber = FreeFile
    Open filePath For Input Lock Read As #fileNumber
    If Err.Number <> 0 Then
        IsFileOpen = True ' 檔案已經開啟
        Close #fileNumber ' 關閉檔案
    Else
        IsFileOpen = False ' 檔案未開啟
    End If
    On Error GoTo 0
End Function
Function FindDifferentColumn(csvfile As String, start1 As Integer, end1 As Integer) As Integer
    Dim fileContent As String
    ' 判斷檔案是否已經開啟
    If IsFileOpen(csvfile) = False Then
        ' 如果檔案未開啟，則開啟並讀取檔案內容
        On Error Resume Next
        Dim fileB As Object
        Set fileB = CreateObject("Scripting.FileSystemObject").GetFile(csvfile)
        Dim fileBContents As String
        Open fileB For Input As #1
        fileContent = Input$(LOF(1), 1)
        Close #1
        On Error GoTo 0
    Else:
        FindDifferentColumn = 0
        Exit Function
    End If
   
    If Len(fileContent) > 0 Then
     Dim rows As Variant
     rows = Split(fileContent, vbCrLf)
     
     Dim numRows As Integer
     numRows = UBound(rows) - 12
     
     Dim ARow As Integer
     Dim BRow As Integer
     
     Dim AValues As Variant
     Dim BValues As Variant
     Dim lineElementsA As Variant
     Dim lineElementsB As Variant
     
     For i = 0 To numRows
         lineElementsA = Split(rows(i + 12), ",")
         If lineElementsA(1) = 1 Then '找到A行
             ARow = i + 12
             For j = i + 1 To numRows
                 lineElementsB = Split(rows(j + 12), ",")
                 If lineElementsB(1) = 1 Then  '找到B行
                     BRow = j + 12
                     Exit For
                 End If
             Next j
             Exit For
         End If
     Next i
     Dim maxIndex As Integer
    
     If ARow > 0 And BRow > 0 Then '如果找到了A行和B行
         AValues = Split(rows(ARow), ",")
         BValues = Split(rows(BRow), ",")
         maxIndex = UBound(AValues)
         Dim maxCnt As Integer
         Dim cnt As Integer
         Dim start2 As Integer
         Dim end2 As Integer
         Dim temp_a_val As Double
         Dim temp_b_val As Double
         For i = 10 To maxIndex 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz care int 從第幾欄開始找
             If IsNumeric(AValues(i)) Then
                 temp_a_val = CDbl(AValues(i))
             Else
                 temp_a_val = 999.9
             End If
             If IsNumeric(temp_a_val) And Int(temp_a_val) = temp_a_val Then  '找到A行連續的整數欄位
                 cnt = cnt + 1
                 If cnt > maxCnt Then  'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz
                     maxCnt = cnt
                     start2 = i - maxCnt + 1
                     end1 = i
                     If maxCnt >= 230 Then
                         Exit For
                     End If
                 End If
             Else
                 cnt = 0
             End If
         Next i
         
         Dim start3 As Integer
         For i = start2 + 3 To end1 '比較A行與B行的 start2 開始至end2 不一樣的數字
             start1 = end1
             temp_a_val = AValues(i)
             temp_b_val = BValues(i)
             If temp_a_val <> temp_b_val Then
                 start3 = i
                 start1 = i
                 Exit For
             End If
         Next i
         'start1x to end1x-------------------------------------------------------
         Dim start1x As Integer
         Dim end1x As Integer
         maxCnt = 0
         For i = end1 To maxIndex 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz care int 從第幾欄開始找
             If IsNumeric(AValues(i)) Then
                 temp_a_val = CDbl(AValues(i))
             Else
                 temp_a_val = 999.9
             End If
             If IsNumeric(temp_a_val) And Int(temp_a_val) = temp_a_val Then  '找到A行連續的整數欄位
                 cnt = cnt + 1
                 If cnt > maxCnt Then
                     maxCnt = cnt
                     start1x = i - maxCnt + 1
                     end1x = i
                 End If
             Else
                 cnt = 0
             End If
         Next i
         
         For i = start1x To end1x '比較A行與B行的 start2 開始至end2 不一樣的數字
             start1x = end1x
             temp_a_val = AValues(i)
             temp_b_val = BValues(i)
             If temp_a_val <> temp_b_val Then
                 start1x = i
                 Exit For
             End If
         Next i
         FindDifferentColumn = start1x '回傳start3
     Else
         FindDifferentColumn = 0 '找不到就回傳0
     End If
    End If
    FindDifferentColumn = 0 '找不到就回傳0
End Function
Function ReadCSVsInDirectory(directoryPath As String) As Boolean
    Dim fileSystem As Object
    Dim directory As Object
    Dim file As Object
    Dim csvText As String
    Dim lineElementsA As Variant
    Dim ctafile  As Boolean
    Dim csvRows As Variant
    Dim csvFilestempcta() As String
    Dim i As Long
    Dim k As Long
    ' 設定目標檔案路徑
    Dim destinationPathcsv As String
    destinationPathcsv = directoryPath & "\" & "CTAcsv\"
    
    Set fileSystem = CreateObject("Scripting.FileSystemObject")
    Set directory = fileSystem.GetFolder(directoryPath)
    
    csvFilestempcta = FindALLCSVFiles(directoryPath)

        k = UBound(csvFilestempcta)
        For i = 0 To UBound(csvFilestempcta)
            If Len(csvFilestempcta(i)) > 0 Then
                j = 0
                Set fileA = CreateObject("Scripting.FileSystemObject").GetFile(csvFilestempcta(i))
                ' 檢查檔案是否已經開啟
                On Error Resume Next
                fileNum = FreeFile()
                Open fileA For Input Lock Read Write As #fileNum
                If Err.Number <> 0 Then
                    ' 關閉檔案後再嘗試開啟
                    Close #fileNum
                    fileNum = FreeFile()
                    Open fileA For Input Lock Read Write As #fileNum
                End If
                On Error GoTo 0
        
                Do Until EOF(1)
                    Line Input #fileNum, fileLine
                    lineElementsA = Split(fileLine, ",")
                    If fileLine <> "" And UBound(lineElementsA) > 1 Then
                        If InStr(1, lineElementsA(1), "CTA8290", vbTextCompare) > 0 Or InStr(1, lineElementsA(0), "CTA8280", vbTextCompare) > 0 Then
                             '執行你想要執行的代碼 長川CSV TO ASL CSV
                             Close #fileNum
                             On Error Resume Next
                             ChangeCtaToAsl csvFilestempcta(i)
                              On Error GoTo 0
                             ReadCSVsInDirectory = True
                            ' 確認目標資料夾是否存在，若不存在就創建一個
                            If Dir(destinationPathcsv, vbDirectory) = "" Then
                                MkDir destinationPathcsv
                            End If
                            If Dir(csvFilestempcta(i)) <> "" Then
                                
                                On Error Resume Next
                                Name csvFilestempcta(i) As destinationPathcsv & Dir(csvFilestempcta(i))
                                On Error GoTo 0
                            End If
                            Exit Do
                        End If
                    End If
                    j = j + 1
                    If j > 10 Then
                        Exit Do
                    End If
                Loop
                Close #fileNum
            End If
        Next i

End Function


'Sub ReadWriteGoogleSheet()
'
'    Dim service As New SheetsService
'    Dim spreadsheetId As String
'    Dim rangeName As String
'    Dim values As Object
'
'    ' 設置 API 金鑰
'    service.Key = "AIzaSyCX0-SuF5bKC86RghQlV2jRQrv_op7G4yM"
'
'    ' 設置表格 ID 和範圍名稱
'    spreadsheetId = "11mmHUW96o3dn1HO-QlFUdTUQ-b_p1BJJ8MFCJfplimc"
'    rangeName = "Sheet1!A1:B2"
'
'    ' 讀取數據
'    Set values = service.spreadsheets.values.get(spreadsheetId, rangeName).Execute
'
'    ' 寫入數據
'    service.spreadsheets.values.Update spreadsheetId, rangeName, values
'
'End Sub

Function OpenFileAndPasteData(filePath As String)
    Dim xlApp As Object
    Set xlApp = CreateObject("Excel.Application")
    Dim xlBook As Object
    Dim firstfile As Boolean
    Dim i As Long
    firstfile = False
    If Dir(filePath) = "" Then
        Set xlBook = xlApp.Workbooks.Add
        xlBook.SaveAs fileName:=filePath
        xlBook.Close SaveChanges:=True
        xlApp.Quit
         Set xlBook = Nothing
         firstfile = True
    End If
    xlApp.Visible = True
    Set xlBook = xlApp.Workbooks.Open(filePath)
    Application.Wait Now + TimeValue("0:00:03") ' 等待5秒鐘
    Dim xlSheet As Object
    Set xlSheet = xlBook.Sheets(1)
    Dim lastCol As Integer
    Dim lastRow As Integer
    If firstfile = True Then
        lastCol = 1
    Else
        lastCol = xlSheet.Cells(6, xlSheet.Columns.count).End(xlToLeft).Column
        lastCol = lastCol + 1
    End If
    xlSheet.Range(xlSheet.Cells(1, lastCol), xlSheet.Cells(1, lastCol)).Select
    xlSheet.Paste
    Application.Wait Now + TimeValue("0:00:03") ' 等待2秒鐘
    DoEvents
    Dim maxLen As Integer
    maxLen = 0
    lastKRow = xlSheet.Cells(xlSheet.rows.count, lastCol).End(xlUp).row
    For i = 7 To lastKRow
        If Len(xlSheet.Cells(i, lastCol + 3).value) > maxLen Then
            maxLen = Len(xlSheet.Cells(i, lastCol + 3).value)
        End If
    Next i
    xlSheet.Cells(7, lastCol + 3).EntireColumn.ColumnWidth = maxLen + 4
    xlBook.Save
    Application.Wait Now + TimeValue("0:00:03") ' 等待2秒鐘
    DoEvents
    xlBook.Close False
    xlApp.Quit
    Set xlSheet = Nothing
    Set xlBook = Nothing
    Set xlApp = Nothing
End Function
Function FindOnlineEQCFailCnt(fileName As String) As Integer
    Dim lineElements As Variant
    Dim eqcfailcnt As Integer
    Dim eqcfailcntline As Integer
    Dim rowsInB As Variant
    Dim targetRowInB As String
    
    eqcfailcnt = 0
     ' 開啟檔案 B
    Dim fileB As Object
    Set fileB = CreateObject("Scripting.FileSystemObject").GetFile(fileName)
    Dim fileBContents As String
    Open fileB For Input As #1
    fileBContents = Input$(LOF(1), 1)
    Close #1
    rowsInB = Split(fileBContents, vbCrLf)
    For i = 12 To UBound(rowsInB) '從B檔 也就是onlineqc中找到不是為1的
        If Len(rowsInB(i)) < 1 Then
            Exit For
        End If
        lineElements = Split(rowsInB(i), ",")
        If val(lineElements(1)) <> 1 Then   ' 如果 B 檔第2列為 1
            eqcfailcnt = eqcfailcnt + 1
        End If
    Next i
    Dim newFileContents As String
    Dim rowAWithFT As String
    Dim rowAWithFT_temp As String
    newFileContents = ""
    For i = 0 To UBound(rowsInB)
        eqcfailcntline = eqcfailcntline + 1
        If Len(rowsInB(i)) < 1 Then
            Exit For
        End If
        targetRowInB = rowsInB(i)
        If i = 8 Then
            rowAWithFT = AddFTToRowA2(targetRowInB, 0, "OnlineEQC_Fail:" & ",", 1, eqcfailcnt & ",")
            newFileContents = newFileContents & rowAWithFT & vbCrLf
        Else
            newFileContents = newFileContents & Split(fileBContents, vbCrLf)(i) & vbCrLf
        End If
    Next i
    Dim newFileName As String
    Open fileName For Output As #2
    Print #2, newFileContents
    Close #2
    FindOnlineEQCFailCnt = eqcfailcntline
End Function
Function FindEQCRTPaSSCnt(fileName As String, ByVal eqcdataline As Integer)

    Dim lineElements As Variant
    Dim passcnt As Integer
    Dim rowsInB As Variant
    Dim targetRowInB As String
    
    passcnt = 0
     ' 開啟檔案 B
    Dim fileB As Object
    Set fileB = CreateObject("Scripting.FileSystemObject").GetFile(fileName)
    Dim fileBContents As String
    Open fileB For Input As #1
    'fileBContents = Input$(LOF(1), 1)
     Do While Not EOF(1)
        Line Input #1, fileLine
        fileLine = fileLine & vbCrLf
        fileBContents = fileBContents & fileLine
    Loop
    Close #1
    rowsInB = Split(fileBContents, vbCrLf)
    For i = eqcdataline - 1 To UBound(rowsInB) '從B檔 也就是onlineqc中找到不是為1的
        If Len(rowsInB(i)) < 1 Then
            Exit For
        End If
        lineElements = Split(rowsInB(i), ",")
        If val(lineElements(1)) = 1 Then  ' 如果 B 檔第2列為 1
            passcnt = passcnt + 1
        End If
    Next i
    
    Dim newFileContents As String
    Dim rowAWithFT As String
    Dim rowAWithFT_temp As String
    newFileContents = ""
    For i = 0 To UBound(rowsInB)
        If Len(rowsInB(i)) < 1 Then
            Exit For
        End If
        targetRowInB = rowsInB(i)
        If i = 9 Then
            rowAWithFT = AddFTToRowA2(targetRowInB, 0, "EQC_RT_FINAL_PASS:" & ",", 1, passcnt & ",")
            newFileContents = newFileContents & rowAWithFT & vbCrLf
        Else
            newFileContents = newFileContents & Split(fileBContents, vbCrLf)(i) & vbCrLf
        End If
    Next i
    Dim newFileName As String
    'newFileName = "E:\temp\YNT8722311381\123.csv" debug
    'Open newFileName For Output As #2 debug
    Open fileName For Output As #2
    Print #2, newFileContents
    Close #2
                
End Function
Function OpenExcelFileAndSave(filePath As String, Optional x As Integer = 1, Optional y As Integer = 1) As Boolean
    On Error GoTo ErrorHandler
    
    Dim xlApp As Object
    Dim xlBook As Object
    Dim xlSheet As Object
    
    Set xlApp = CreateObject("Excel.Application")
    Set xlBook = xlApp.Workbooks.Open(filePath)
    Set xlSheet = xlBook.Worksheets(1)
    
    xlSheet.Cells(x, y).Select
    'xlSheet.Range("A1").Select
    
    xlBook.Save
    xlBook.Close
    xlApp.Quit
    
    OpenExcelFileAndSave = True
    'Application.Wait Now + TimeValue("0:00:10") ' 等待10秒鐘
    Exit Function
    
ErrorHandler:
    OpenExcelFileAndSave = False
    MsgBox "Error " & Err.Number & ": " & Err.Description
End Function
Function OpenExcelFileAndSavex(xlSheet As Worksheet, Optional x As Integer = 1, Optional y As Integer = 1)
    xlSheet.Cells(x, y).Select
End Function
Function OpenSummaryFindBig(filePath As String, xst As Integer, xsp As Integer, yst As Integer, ysp As Integer)
    Dim xlApp As Object
    Dim xlBook As Object
    Dim xlSheet As Object
    Dim tempstr As String
    
    ' 開啟Excel
    Set xlApp = CreateObject("Excel.Application")
    xlApp.DisplayAlerts = False ' 禁用警告對話框
    xlApp.Visible = True
    Set xlBook = xlApp.Workbooks.Open(filePath)
    Application.Wait Now + TimeValue("0:00:10") ' 等待10秒鐘
    DoEvents
    ' 選擇第一個sheet
    Set xlSheet = xlBook.Sheets(1)
    
    ' 找出第1行中數字最大的，記錄位置和數字
    Dim maxNum As Double
    Dim maxCol As Integer
    Dim lastCol1 As Integer
    Dim lastCol5 As Integer
    Dim lastColst As Integer
    Dim lastColsp As Integer
    maxNum = 0
    maxCol = 1
    
    lastCol1 = xlSheet.Cells(1, xlSheet.Columns.count).End(xlToLeft).Column
    lastCol5 = xlSheet.Cells(5, xlSheet.Columns.count).End(xlToLeft).Column
    For i = 1 To lastCol1
        If IsNumeric(xlSheet.Cells(1, i)) Then
            If xlSheet.Cells(1, i) > maxNum Then
                maxNum = xlSheet.Cells(1, i)
                maxCol = i - 1
                lastColst = maxCol
            End If
        End If
    Next i
'
    ' 從該欄位數的第5行往右邊找到數字，數字加起來=上面找到的數字，則記錄該欄的位置
    Dim sumNum As Double
    Dim numValue As Double
    sumNum = 0
    For i = maxCol To lastCol5
        If Len(xlSheet.Cells(5, i).value) > 0 Then
            If IsNumeric(xlSheet.Cells(5, i).value) Then
                numValue = val(xlSheet.Cells(5, i).value)
                sumNum = sumNum + numValue
            End If
        Else
            sumNum = 0
        End If

        If sumNum = maxNum Then
            lastColsp = i
            Exit For
        End If
    Next i
    xst = 1
    yst = lastColst
    xsp = lastColsp
    ysp = xlSheet.Cells(xlSheet.rows.count, yst).End(xlUp).row
    If ysp >= 8 Then xlSheet.Cells(8, lastColst + 3).Interior.Color = RGB(255, 255, 10)
    If ysp >= 9 Then xlSheet.Cells(9, lastColst + 3).Interior.Color = RGB(255, 180, 20)
    If ysp >= 10 Then xlSheet.Cells(10, lastColst + 3).Interior.Color = RGB(255, 130, 30)
    If ysp >= 11 Then xlSheet.Cells(11, lastColst + 3).Interior.Color = RGB(255, 80, 140)
    If ysp >= 12 Then xlSheet.Cells(12, lastColst + 3).Interior.Color = RGB(255, 30, 50)
    If ysp >= 13 Then xlSheet.Cells(13, lastColst + 3).Interior.Color = RGB(135, 206, 235)
    If ysp >= 14 Then xlSheet.Cells(14, lastColst + 3).Interior.Color = RGB(200, 190, 70)
    If ysp >= 15 Then xlSheet.Cells(15, lastColst + 3).Interior.Color = RGB(150, 180, 80)
    If ysp >= 16 Then xlSheet.Cells(16, lastColst + 3).Interior.Color = RGB(100, 170, 90)
    If ysp >= 17 Then xlSheet.Cells(17, lastColst + 3).Interior.Color = RGB(50, 180, 150)
    
    Dim dataArr() As Variant
    lastRow = xlSheet.Cells(xlSheet.rows.count, lastColst + 3).End(xlUp).row
    If lastRow >= 9 Then
        dataArr = CheckData(xlSheet, lastColst + 3)
        ColorMatch xlSheet, lastColst + 3, dataArr
    End If
'    Application.Wait Now + TimeValue("0:00:03") ' 等待10秒鐘
'    DoEvents
    'summaryhyplink xlSheet
'    Application.Wait Now + TimeValue("0:00:03") ' 等待10秒鐘
'    DoEvents
    xlSheet.Cells(6, lastColst + 3).Select
    ' 關閉Excel
    xlBook.Close SaveChanges:=True  ' 51表示Excel 2010及以上版本的xlsx格式
    xlApp.Quit
    Set xlSheet = Nothing
    Set xlBook = Nothing
    Set xlApp = Nothing
    
    ' 找不到符合條件的位置時返回空值
    OpenSummaryFindBig = Null
End Function
Function CheckData(xlSheet As Worksheet, colNum As Integer) As Variant
    Dim lastRow As Long
    Dim arr() As Variant
    Dim i As Long
    lastRow = xlSheet.Cells(xlSheet.rows.count, colNum).End(xlUp).row
   If lastRow > 17 Then
       ReDim arr(8 To 17, 1 To 2) As Variant
        For i = 8 To 17
            arr(i, 1) = xlSheet.Cells(i, colNum).value
            arr(i, 2) = xlSheet.Cells(i, colNum).Interior.Color
        Next i
        CheckData = arr
    ElseIf lastRow >= 9 And lastRow <= 17 Then 'PFT5320112M
        ReDim arr(8 To lastRow, 1 To 2) As Variant
        For i = 8 To lastRow
            arr(i, 1) = xlSheet.Cells(i, colNum).value
            arr(i, 2) = xlSheet.Cells(i, colNum).Interior.Color
        Next i
        CheckData = arr
    End If
End Function
Function ColorMatch(xlSheet As Worksheet, colNum As Integer, dataArr As Variant) As String
    Dim lastCol As Long, lastRow As Long, i As Long, j As Long
    Dim cellValue As String, cellColor As Long
    
    lastCol = xlSheet.Cells(6, xlSheet.Columns.count).End(xlToLeft).Column
    lastRow = xlSheet.Cells(xlSheet.rows.count, colNum).End(xlUp).row
    
    For i = 4 To lastCol
        If i = colNum Then '跳過第 colNum 欄
            GoTo nextix
        End If
        
        If InStr(1, LCase(xlSheet.Cells(6, i).value), "definition", vbTextCompare) > 0 Then '找到 Definition 所在的欄位
            lastRow = xlSheet.Cells(xlSheet.rows.count, i).End(xlUp).row
            For j = 7 To lastRow
                cellValue = xlSheet.Cells(j, i).value
                For k = 8 To UBound(dataArr)
                    If cellValue = dataArr(k, 1) Then '若儲存格內的值等於 dataArr(1)
                        cellColor = dataArr(k, 2) '將儲存格的底色設定為 dataArr(2)
                        xlSheet.Cells(j, i).Interior.Color = cellColor
                    End If
                Next k
            Next j
        End If
nextix:
    Next i
    ColorMatch = "Finished!"
End Function
Function summaryhyplink(xlSheet As Worksheet)
    Dim lastCol As Long, lastRow As Long, i As Long, j As Long
    Dim cellValue As String, cellColor As Long
    Dim tempstr1 As String
    Dim tempstr2 As String
    Dim first_sheet_name As String
    Dim destinationPath As String
    lastCol = xlSheet.Cells(6, xlSheet.Columns.count).End(xlToLeft).Column
    For i = 1 To lastCol
        If InStr(1, LCase(xlSheet.Cells(6, i).value), "Bin", vbTextCompare) > 0 Then '找到 bin 所在的欄位
            lastRow = xlSheet.Cells(xlSheet.rows.count, i).End(xlUp).row
            destinationPath = xlSheet.Cells(3, i + 3).value
            first_sheet_name = GetFirstSheetName(destinationPath)
            For j = 7 To lastRow
                'hyplink
                'Set cell = xlSheet.Cells(j, i) '路徑到時要改成 在網路上
                'cell.Hyperlinks.Add Anchor:=cell, Address:=destinationPath, SubAddress:=Sheets(1).Cells(6, cell.value).Address(), TextToDisplay:=cell.value
                'cell.Hyperlinks.Add Anchor:=cell, Address:=destinationPath, SubAddress:=Sheets(1).Cells(6, cell.value).Address(), TextToDisplay:=cell.value
                'xlSheet.Hyperlinks.Add Anchor:=xlSheet.Cells(j, i), Address:="", SubAddress:="", TextToDisplay:=xlSheet.Cells(i, j).value '路徑到時要改成 在網路上
                 Set cell = xlSheet.Cells(j, i)
                 tempstr1 = cell.value
                 If cell.value = 1 Then
                    tempstr2 = "'" & first_sheet_name & "'!" & Cells(6, 1).Address() & ""
                 Else
                    tempstr2 = "'" & first_sheet_name & "'!" & Cells(6, cell.value - 2).Address() & ""
                 End If
                 'cell.Hyperlinks.Add Anchor:=cell, Address:=destinationPath, SubAddress:="'" & first_sheet_name & "'!A6", TextToDisplay:=tempstr1
                 cell.Hyperlinks.Add Anchor:=cell, Address:=destinationPath, SubAddress:=tempstr2, TextToDisplay:=tempstr1
            Next j
        End If
    Next i
End Function
Function GetFirstSheetName(destinationPath As String) As String
    Dim xlApp As Excel.Application
    Dim xlWorkbook As Excel.Workbook
    Dim firstSheetName As String

    ' Open Excel application
    Set xlApp = New Excel.Application

    ' Open workbook
    Set xlWorkbook = xlApp.Workbooks.Open(destinationPath)

    ' Get first worksheet name
    firstSheetName = xlWorkbook.Worksheets(1).name

    ' Close workbook and quit Excel
    xlWorkbook.Close SaveChanges:=False
    xlApp.Quit

    ' Release memory
    Set xlWorkbook = Nothing
    Set xlApp = Nothing

    ' Return first worksheet name
    GetFirstSheetName = firstSheetName
End Function
Function CreateFolders(folder1 As String, folder2 As String) As Boolean
    Dim folderPath1 As String
    Dim folderPath2 As String
    
    folderPath1 = tempPath & "\" & folder1
    folderPath2 = folderPath1 & "\" & folder2
    
    ' 檢查資料夾1是否存在，不存在則創建
    If Dir(folderPath1, vbDirectory) = "" Then
        MkDir folderPath1
    End If
    
    ' 檢查資料夾2是否存在，不存在則創建
    If Dir(folderPath2, vbDirectory) = "" Then
        MkDir folderPath2
    End If
    
    ' 檢查資料夾2是否成功創建
    If Dir(folderPath2, vbDirectory) <> "" Then
        CreateFolders = True
    Else
        CreateFolders = False
    End If
End Function
Function RemoveDuplicateCSVFiles(ByVal csvFilestemp As Variant) As Variant
    Dim result() As String
    Dim dict As Object
    Set dict = CreateObject("Scripting.Dictionary")
    
    Dim i As Long
    For i = LBound(csvFilestemp) To UBound(csvFilestemp)
        Dim filePath As String
        filePath = csvFilestemp(i)
        Dim fileName As String
        fileName = Mid(filePath, InStrRev(filePath, "\") + 1)
        
        If InStr(1, Right(fileName, 4), ".csv", vbTextCompare) > 0 And InStr(1, LCase(fileName), "eqcfaildata", vbTextCompare) = 0 And InStr(1, LCase(fileName), "eqctotaldata", vbTextCompare) = 0 Then
            If Not dict.exists(fileName) Then
                dict.Add fileName, filePath
            End If
        End If
    Next i
    
    If dict.count > 0 Then
        ReDim result(0 To dict.count - 1)
        i = 0
        Dim key As Variant
        For Each key In dict.keys
            result(i) = dict(key)
            i = i + 1
        Next key
    End If
    
    RemoveDuplicateCSVFiles = result
End Function
Function ReplacePath(ByVal inputStr As String) As String
    Dim startpos As Long
    Dim endpos As Long
    Dim replaceStr As String
    Dim findStr As String
    
    replaceStr = netpath & "\"
    findStr = tempPath & "\"
    startpos = InStr(1, inputStr, findStr)
    endpos = InStrRev(inputStr, "\", startpos + Len(findStr))
    If endpos > startpos Then
        ReplacePath = replaceStr & Mid(inputStr, endpos + 1)
    Else
        ReplacePath = ""
    End If
End Function

Function FindAllFiles(ByVal folderPath As String) As Variant
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    Dim folder As Object
    Set folder = fso.GetFolder(folderPath)
    Dim result() As String
    Dim i As Integer
    i = 0
    Dim file As Object
    For Each file In folder.files
        Dim fileName As String
        fileName = file.name
            ReDim Preserve result(i)
            result(i) = file.path
            i = i + 1
    Next file
    
    Dim subfolder As Object
    For Each subfolder In folder.Subfolders
        Dim subFolderPath As String
        subFolderPath = subfolder.path

        Dim subResult
        subResult = FindAllFiles(subFolderPath)
        If Not IsEmpty(subResult) Then
            Dim j As Integer
            On Error Resume Next
            For j = 0 To UBound(subResult)
                ReDim Preserve result(i)
                result(i) = subResult(j)
                i = i + 1
            Next j
            On Error GoTo FindAllFilesNext
        End If

    Next subfolder
FindAllFilesNext:
    FindAllFiles = result
End Function
Function moveilfe(ByVal folderPath As String)
    Dim fso As Object
    Dim parentPath As String
    Dim folderPathtemp As String
    Set fso = CreateObject("Scripting.FileSystemObject")
    folderPathtemp = UCase(Replace(LCase(folderPath), tempPath, netpath))
    parentPath = Left(folderPathtemp, InStrRev(folderPathtemp, "\") - 1)
     If Not fso.FolderExists(parentPath) Then
        fso.CreateFolder parentPath
    End If
    If Not fso.FolderExists(folderPathtemp) Then
        fso.CreateFolder folderPathtemp
    End If
    On Error Resume Next
    fso.CopyFolder folderPath, folderPathtemp, True
    fso.DeleteFolder folderPath
    On Error GoTo 0
End Function
Function FindthisfolderALLFiles(ByVal folderPath As String, Optional ByRef file_cnt As Integer = 0) As Variant
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    Dim folder As Object
    Set folder = fso.GetFolder(folderPath)
    Dim result() As String
    Dim i As Integer
    i = 0
    Dim file As Object
    For Each file In folder.files
        Dim fileName As String
        fileName = file.name
            ReDim Preserve result(i)
            result(i) = file.path
            i = i + 1
    Next file
    FindthisfolderALLFiles = result
    file_cnt = i
End Function
