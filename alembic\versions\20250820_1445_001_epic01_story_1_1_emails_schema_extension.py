"""Epic-01 Story 1.1: Emails表狀態追蹤欄位擴展

Revision ID: 001_epic01_emails_ext
Revises: 
Create Date: 2025-08-20 14:45:00
Epic: 01 - Database Infrastructure
Story: 1.1 - EmailDB Schema Extension

Migration Type: Schema Extension
Risk Level: LOW
Rollback Safe: YES

Description:
為 emails 表新增狀態追蹤欄位，支援郵件下載和處理狀態的精確管理。
這是 Epic-01 的核心遷移，為後續的下載追蹤功能提供基礎架構。

Changes:
- 新增 download_success 欄位: Boolean, 預設 False
- 新增 processing_success 欄位: Boolean, 預設 False  
- 新增 download_completed_at 欄位: DateTime, 可為空
- 新增 processing_completed_at 欄位: DateTime, 可為空
- 新增對應的效能優化索引
- 更新現有郵件記錄的預設狀態

Prerequisites:
- 確保資料庫備份已創建
- 確認 emails 表存在且可訪問
- 驗證無活動的寫入事務

Verification Steps:
- 檢查新欄位是否正確創建
- 驗證索引是否建立成功
- 確認現有數據完整性
- 測試新欄位的讀寫操作

Performance Impact:
- 預計遷移時間: <1分鐘 (10萬筆記錄以下)
- 索引創建: 輕微性能影響
- 儲存空間增加: 約16位元組/記錄

Author: Database Admin
Reviewer: System Architect
"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '001_epic01_emails_ext'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """
    Epic-01 Story 1.1: 新增 emails 表狀態追蹤欄位
    
    這個遷移為 emails 表添加必要的狀態追蹤欄位，
    為郵件下載和處理流程提供精確的狀態管理。
    
    操作序列:
    1. 新增狀態追蹤欄位 (安全的 ALTER TABLE 操作)
    2. 創建效能優化索引
    3. 更新現有記錄的預設狀態
    4. 驗證資料一致性
    """
    
    # 檢查表是否存在
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    if 'emails' not in inspector.get_table_names():
        raise Exception("emails 表不存在，無法執行遷移")
    
    print(f"[{datetime.now()}] 開始 Epic-01 Story 1.1 遷移...")
    
    # 步驟1: 新增狀態追蹤欄位
    try:
        # 新增 download_success 欄位
        op.add_column('emails', 
            sa.Column('download_success', sa.Boolean(), 
                     nullable=False, default=False,
                     comment="郵件下載成功標誌"))
        print("✓ 新增 download_success 欄位")
        
        # 新增 processing_success 欄位  
        op.add_column('emails',
            sa.Column('processing_success', sa.Boolean(), 
                     nullable=False, default=False,
                     comment="郵件處理成功標誌"))
        print("✓ 新增 processing_success 欄位")
        
        # 新增 download_completed_at 欄位
        op.add_column('emails',
            sa.Column('download_completed_at', sa.DateTime(),
                     nullable=True,
                     comment="下載完成時間"))
        print("✓ 新增 download_completed_at 欄位")
        
        # 新增 processing_completed_at 欄位
        op.add_column('emails',
            sa.Column('processing_completed_at', sa.DateTime(),
                     nullable=True, 
                     comment="處理完成時間"))
        print("✓ 新增 processing_completed_at 欄位")
        
    except Exception as e:
        print(f"✗ 欄位新增失敗: {e}")
        raise
    
    # 步驟2: 創建效能優化索引
    try:
        # 下載成功狀態索引
        op.create_index('idx_email_download_success', 'emails', ['download_success'])
        print("✓ 創建 idx_email_download_success 索引")
        
        # 處理成功狀態索引
        op.create_index('idx_email_processing_success', 'emails', ['processing_success'])
        print("✓ 創建 idx_email_processing_success 索引")
        
        # 下載完成時間索引
        op.create_index('idx_email_download_completed', 'emails', ['download_completed_at'])
        print("✓ 創建 idx_email_download_completed 索引")
        
        # 處理完成時間索引
        op.create_index('idx_email_processing_completed', 'emails', ['processing_completed_at'])
        print("✓ 創建 idx_email_processing_completed 索引")
        
        # 複合索引 - 下載和處理狀態
        op.create_index('idx_email_status_composite', 'emails', 
                       ['download_success', 'processing_success'])
        print("✓ 創建 idx_email_status_composite 複合索引")
        
    except Exception as e:
        print(f"✗ 索引創建失敗: {e}")
        raise
    
    # 步驟3: 更新現有記錄的預設狀態
    try:
        # 將現有郵件標記為已完成下載和處理
        # 這是安全的假設，因為現有郵件已經在系統中
        conn.execute(
            sa.text("""
                UPDATE emails 
                SET download_success = :download_success,
                    processing_success = :processing_success,
                    download_completed_at = created_at,
                    processing_completed_at = created_at
                WHERE download_success IS NULL
            """),
            {
                'download_success': True,
                'processing_success': True
            }
        )
        
        # 獲取更新計數
        result = conn.execute(sa.text("SELECT COUNT(*) FROM emails"))
        total_count = result.scalar()
        print(f"✓ 更新 {total_count} 封現有郵件的狀態")
        
    except Exception as e:
        print(f"✗ 現有數據更新失敗: {e}")
        raise
    
    # 步驟4: 驗證遷移結果
    try:
        # 檢查新欄位是否存在
        columns = inspector.get_columns('emails')
        column_names = [col['name'] for col in columns]
        
        required_columns = ['download_success', 'processing_success', 
                          'download_completed_at', 'processing_completed_at']
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            raise Exception(f"遷移驗證失敗，缺失欄位: {missing_columns}")
        
        # 檢查數據完整性
        result = conn.execute(sa.text(
            "SELECT COUNT(*) FROM emails WHERE download_success IS NULL"
        ))
        null_count = result.scalar()
        
        if null_count > 0:
            raise Exception(f"數據完整性檢查失敗，發現 {null_count} 筆 NULL 狀態記錄")
        
        print("✓ 遷移驗證通過")
        
    except Exception as e:
        print(f"✗ 遷移驗證失敗: {e}")
        raise
    
    print(f"[{datetime.now()}] Epic-01 Story 1.1 遷移完成 ✓")


def downgrade():
    """
    Epic-01 Story 1.1: 回滾 emails 表狀態追蹤欄位
    
    警告: 此操作將移除所有 Story 1.1 相關的欄位和索引
    請確保您有適當的備份，因為這會導致資料永久遺失
    
    回滾序列:
    1. 刪除效能優化索引
    2. 移除狀態追蹤欄位
    3. 驗證回滾完整性
    """
    
    print(f"[{datetime.now()}] 開始 Epic-01 Story 1.1 回滾...")
    
    # 步驟1: 刪除索引
    try:
        # 刪除複合索引
        op.drop_index('idx_email_status_composite', table_name='emails')
        print("✓ 刪除 idx_email_status_composite 索引")
        
        # 刪除單一欄位索引
        op.drop_index('idx_email_processing_completed', table_name='emails')
        print("✓ 刪除 idx_email_processing_completed 索引")
        
        op.drop_index('idx_email_download_completed', table_name='emails')
        print("✓ 刪除 idx_email_download_completed 索引")
        
        op.drop_index('idx_email_processing_success', table_name='emails')
        print("✓ 刪除 idx_email_processing_success 索引")
        
        op.drop_index('idx_email_download_success', table_name='emails')
        print("✓ 刪除 idx_email_download_success 索引")
        
    except Exception as e:
        print(f"✗ 索引刪除失敗: {e}")
        # 繼續執行，某些索引可能不存在
    
    # 步驟2: 移除欄位
    try:
        # 注意：SQLite 不支援 DROP COLUMN，需要重建表
        # 對於 SQLite，建議使用備份恢復而非此回滾
        op.drop_column('emails', 'processing_completed_at')
        print("✓ 移除 processing_completed_at 欄位")
        
        op.drop_column('emails', 'download_completed_at')
        print("✓ 移除 download_completed_at 欄位")
        
        op.drop_column('emails', 'processing_success')
        print("✓ 移除 processing_success 欄位")
        
        op.drop_column('emails', 'download_success')
        print("✓ 移除 download_success 欄位")
        
    except Exception as e:
        print(f"✗ 欄位移除失敗: {e}")
        print("注意：SQLite 不支援 DROP COLUMN，建議使用備份恢復")
        raise
    
    # 步驟3: 驗證回滾
    try:
        conn = op.get_bind()
        inspector = sa.inspect(conn)
        columns = inspector.get_columns('emails')
        column_names = [col['name'] for col in columns]
        
        removed_columns = ['download_success', 'processing_success', 
                          'download_completed_at', 'processing_completed_at']
        remaining_columns = [col for col in removed_columns if col in column_names]
        
        if remaining_columns:
            print(f"⚠ 警告：以下欄位未能完全移除: {remaining_columns}")
        else:
            print("✓ 回滾驗證通過")
            
    except Exception as e:
        print(f"✗ 回滾驗證失敗: {e}")
        
    print(f"[{datetime.now()}] Epic-01 Story 1.1 回滾完成")