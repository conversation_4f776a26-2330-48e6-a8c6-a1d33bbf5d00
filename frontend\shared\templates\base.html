<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}半導體郵件處理系統{% endblock %}</title>
    
    <!-- 共享 CSS 資源 -->
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/variables.css') }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/base.css') }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/components.css') }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/layout.css') }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/responsive.css') }}">
    
    <!-- 模組特定 CSS -->
    {% block module_css %}{% endblock %}
    
    <!-- 頁面特定 CSS -->
    {% block page_css %}{% endblock %}
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('shared.static', filename='images/favicon.ico') }}">
    
    <!-- 第三方 CSS -->
    {% block external_css %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- 載入中遮罩 -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-spinner large"></div>
            <p>處理中...</p>
        </div>
    </div>

    <!-- 主要內容區域 -->
    <div class="app-container">
        <!-- 導航列 -->
        {% block navigation %}
            {% include 'components/navbar.html' %}
        {% endblock %}
        
        <!-- 側邊欄 -->
        {% if show_sidebar|default(true) %}
            {% block sidebar %}
                {% include 'components/sidebar.html' %}
            {% endblock %}
        {% endif %}
        
        <!-- 主要內容 -->
        <main class="main-content {% if not show_sidebar|default(true) %}full-width{% endif %}">
            <!-- 頁面標題 -->
            {% block page_header %}
                <div class="page-header">
                    <h1 class="page-title">{% block page_title %}{% endblock %}</h1>
                    {% block page_actions %}{% endblock %}
                </div>
            {% endblock %}
            
            <!-- 通知區域 -->
            {% block notifications %}
                <div id="notification-container" class="notification-container"></div>
            {% endblock %}
            
            <!-- 頁面內容 -->
            <div class="page-content">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>

    <!-- 全域模態框 -->
    {% block modals %}
        {% include 'components/modal.html' %}
        {% include 'components/confirm-dialog.html' %}
    {% endblock %}

    <!-- 通知訊息 -->
    {% include 'components/notification.html' %}

    <!-- 共享 JavaScript 資源 -->
    <script src="{{ url_for('shared.static', filename='js/core/utils.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('shared.static', filename='js/core/api-client.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('shared.static', filename='js/core/dom-manager.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('shared.static', filename='js/core/status-manager.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('shared.static', filename='js/utils/url-config.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('shared.static', filename='js/components/notification.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('shared.static', filename='js/ui-components.js') }}?v={{ range(1000, 9999) | random }}"></script>
    
    <!-- 第三方 JavaScript -->
    {% block external_js %}{% endblock %}
    
    <!-- 模組特定 JavaScript -->
    {% block module_js %}{% endblock %}
    
    <!-- 頁面特定 JavaScript -->
    {% block page_js %}{% endblock %}
    
    <!-- 初始化腳本 -->
    <script>
        // 全域初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化錯誤處理
            if (typeof ErrorHandler !== 'undefined') {
                ErrorHandler.init();
            }
            
            // 初始化通知系統
            if (typeof NotificationManager !== 'undefined') {
                window.notificationManager = new NotificationManager();
            }
            
            // 初始化 URL 配置
            if (typeof UrlConfig !== 'undefined') {
                try {
                    UrlConfig.init();
                } catch (error) {
                    console.warn('URL 配置初始化失敗:', error);
                }
            }
            
            // 頁面特定初始化
            {% block page_init %}{% endblock %}
        });
    </script>
</body>
</html>