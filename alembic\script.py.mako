"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}
Epic: 01 - Database Infrastructure
Story: ${story if story else 'N/A'}

Migration Type: ${migration_type if migration_type else 'Schema Update'}
Risk Level: ${risk_level if risk_level else 'LOW'}
Rollback Safe: ${rollback_safe if rollback_safe else 'YES'}

Description:
${description if description else 'Database schema migration for Epic-01'}

Changes:
${changes if changes else '- Schema updates for email download tracking'}

Prerequisites:
${prerequisites if prerequisites else '- Ensure database backup is created'}

Verification Steps:
${verification if verification else '- Verify all tables and indexes created successfully'}

Author: ${author if author else 'Database Admin'}
Reviewer: ${reviewer if reviewer else 'TBD'}
"""
from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade():
    """
    Epic-01: Apply database schema changes
    
    This migration implements the database changes required for
    Email Download Tracking functionality as specified in Epic-01.
    
    IMPORTANT: This migration includes:
    - Schema extensions for email status tracking
    - New download status management tables
    - Retry mechanism implementation
    - Performance optimization indexes
    
    Rollback: Use downgrade() function if needed
    """
    ${upgrades if upgrades else "pass"}


def downgrade():
    """
    Epic-01: Rollback database schema changes
    
    CAUTION: This will remove all Epic-01 related schema changes.
    Ensure you have a proper backup before proceeding.
    
    Data Loss Warning: This operation may result in data loss for:
    - Email download status information
    - Retry attempt logs
    - Performance tracking data
    """
    ${downgrades if downgrades else "pass"}