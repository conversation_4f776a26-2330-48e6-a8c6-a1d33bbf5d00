{% extends "base.html" %}

{% block title %}PTS 檔案重命名工具 - {{ super() }}{% endblock %}

{% block body_class %}pts-renamer-page{% endblock %}

{% block page_title %}PTS 檔案重命名工具{% endblock %}

{% block module_css %}
<link rel="stylesheet" href="{{ url_for('pts_renamer.static', filename='css/pts_renamer.css') }}">
{% endblock %}

{% block page_css %}
<style>
    /* PTS Renamer 專用樣式 */
    .pts-renamer-page {
        background: var(--bg-light);
    }

    .pts-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 var(--spacing-md);
    }

    .pts-header {
        text-align: center;
        margin-bottom: var(--spacing-xl);
        padding: var(--spacing-lg);
        background: white;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-sm);
    }

    .pts-header h1 {
        color: var(--secondary-color);
        font-size: var(--font-size-lg);
        font-weight: 700;
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .pts-header .subtitle {
        color: var(--text-muted);
        font-size: var(--font-size-sm);
        margin: 0;
    }

    .pts-section {
        background: white;
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-color);
        margin-bottom: var(--spacing-lg);
        box-shadow: var(--shadow-sm);
        transition: var(--transition);
    }

    .pts-section:hover {
        box-shadow: var(--shadow-md);
    }

    .section-header {
        padding: var(--spacing-md) var(--spacing-lg);
        border-bottom: 1px solid var(--border-color);
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }

    .section-title {
        font-size: var(--font-size-md);
        font-weight: 600;
        color: var(--secondary-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .section-title i {
        color: var(--primary-color);
        font-size: 18px;
    }

    .section-content {
        padding: var(--spacing-lg);
    }

    /* 檔案上傳區域 */
    .upload-area {
        border: 2px dashed #dee2e6;
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        text-align: center;
        background: #f8f9fa;
        transition: var(--transition);
        cursor: pointer;
        position: relative;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .upload-area:hover,
    .upload-area.dragover {
        border-color: var(--primary-color);
        background: #f0f4ff;
        transform: scale(1.01);
    }

    .upload-area.dragover {
        border-color: var(--success-color);
        background: #f0fff4;
    }

    .upload-icon {
        font-size: 48px;
        color: var(--text-muted);
        margin-bottom: var(--spacing-md);
        transition: var(--transition);
    }

    .upload-area:hover .upload-icon {
        color: var(--primary-color);
        transform: scale(1.1);
    }

    .upload-text {
        font-size: var(--font-size-md);
        color: var(--secondary-color);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
    }

    .upload-hint {
        font-size: var(--font-size-sm);
        color: var(--text-muted);
        margin: 0;
    }

    .file-input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    /* 上傳檔案列表 */
    .uploaded-files {
        margin-top: var(--spacing-md);
    }

    .file-item {
        background: #f8f9fa;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: var(--transition);
    }

    .file-item:hover {
        background: #e9ecef;
        border-color: var(--primary-color);
    }

    .file-info {
        display: flex;
        align-items: center;
        flex-grow: 1;
        gap: var(--spacing-md);
    }

    .file-icon {
        font-size: 24px;
        color: var(--primary-color);
    }

    .file-details {
        flex-grow: 1;
    }

    .file-name {
        font-weight: 600;
        color: var(--secondary-color);
        margin-bottom: 2px;
        font-size: var(--font-size-sm);
    }

    .file-meta {
        color: var(--text-muted);
        font-size: var(--font-size-xs);
    }

    .remove-file-btn {
        background: var(--error-color);
        color: white;
        border: none;
        border-radius: var(--radius-sm);
        padding: 6px 12px;
        font-size: var(--font-size-xs);
        cursor: pointer;
        transition: var(--transition);
    }

    .remove-file-btn:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    /* 處理選項 */
    .options-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }

    .option-card {
        background: #f8f9fa;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        transition: var(--transition);
    }

    .option-card:hover {
        background: #e9ecef;
        border-color: var(--primary-color);
    }

    .checkbox-group {
        display: flex;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
        flex-wrap: wrap;
    }

    .checkbox-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        cursor: pointer;
        padding: var(--spacing-sm);
        border-radius: var(--radius-sm);
        transition: var(--transition);
    }

    .checkbox-item:hover {
        background: rgba(102, 126, 234, 0.1);
    }

    .checkbox-item input[type="checkbox"] {
        width: 18px;
        height: 18px;
        accent-color: var(--primary-color);
        cursor: pointer;
    }

    .checkbox-item label {
        font-size: var(--font-size-sm);
        font-weight: 500;
        color: var(--secondary-color);
        cursor: pointer;
        margin: 0;
    }

    .form-group {
        margin-bottom: var(--spacing-md);
    }

    .form-group label {
        display: block;
        font-size: var(--font-size-sm);
        font-weight: 500;
        color: var(--secondary-color);
        margin-bottom: var(--spacing-xs);
    }

    .form-group input[type="text"] {
        width: 100%;
        padding: var(--spacing-sm);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-sm);
        transition: var(--transition);
    }

    .form-group input[type="text"]:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-group .help-text {
        font-size: var(--font-size-xs);
        color: var(--text-muted);
        margin-top: var(--spacing-xxs);
    }

    /* 預覽區域 */
    .preview-container {
        background: #f8f9fa;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        min-height: 200px;
        position: relative;
    }

    .preview-list {
        padding: var(--spacing-md);
        max-height: 300px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: var(--font-size-xs);
        line-height: 1.6;
    }

    .preview-item {
        padding: 4px 0;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .preview-item:last-child {
        border-bottom: none;
    }

    .preview-item .status-icon {
        font-size: 12px;
        width: 16px;
    }

    .preview-item.success .status-icon {
        color: var(--success-color);
    }

    .preview-item.error .status-icon {
        color: var(--error-color);
    }

    .preview-item.info .status-icon {
        color: var(--info-color);
    }

    .preview-item.warning .status-icon {
        color: var(--warning-color);
    }

    .preview-empty {
        text-align: center;
        padding: var(--spacing-xl);
        color: var(--text-muted);
        font-style: italic;
    }

    /* 按鈕樣式 */
    .btn-group {
        display: flex;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
        margin-top: var(--spacing-md);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        border: none;
        border-radius: var(--radius-sm);
        font-size: var(--font-size-sm);
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition);
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        text-decoration: none;
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #5a6fd8;
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    .btn-secondary {
        background: var(--text-muted);
        color: white;
    }

    .btn-secondary:hover {
        background: #545b62;
        transform: translateY(-1px);
    }

    .btn-success {
        background: var(--success-color);
        color: white;
    }

    .btn-success:hover {
        background: #1e7e34;
        transform: translateY(-1px);
    }

    .btn-danger {
        background: var(--error-color);
        color: white;
    }

    .btn-danger:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    /* 執行區域 */
    .execute-section {
        text-align: center;
        padding: var(--spacing-xl);
        border-top: 2px solid var(--border-color);
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .execute-btn {
        background: var(--gradient-primary);
        color: white;
        border: none;
        border-radius: var(--radius-lg);
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: var(--font-size-md);
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        min-width: 200px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        box-shadow: var(--shadow-sm);
    }

    .execute-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .execute-btn:disabled {
        background: var(--text-muted);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: var(--spacing-xs);
    }

    .status-ready {
        background: var(--success-color);
    }

    .status-processing {
        background: var(--warning-color);
        animation: pulse 1.5s infinite;
    }

    .status-error {
        background: var(--error-color);
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    /* 進度條 */
    .progress-section {
        margin-top: var(--spacing-md);
    }

    .progress-bar {
        width: 100%;
        height: 24px;
        background: #e9ecef;
        border-radius: var(--radius-sm);
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
    }

    .progress-fill {
        height: 100%;
        background: var(--gradient-primary);
        transition: width 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: var(--font-size-xs);
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .progress-text {
        margin-top: var(--spacing-xs);
        font-size: var(--font-size-xs);
        color: var(--text-muted);
        text-align: center;
    }

    /* 下載區域 */
    .download-section {
        background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
        color: white;
        border-color: #1e7e34;
    }

    .download-section .section-header {
        background: rgba(255, 255, 255, 0.1);
        border-bottom-color: rgba(255, 255, 255, 0.2);
    }

    .download-section .section-title {
        color: white;
    }

    .download-item {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: var(--transition);
    }

    .download-item:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
    }

    .download-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        flex-grow: 1;
    }

    .download-icon {
        font-size: 24px;
        color: white;
    }

    .download-details {
        flex-grow: 1;
    }

    .download-name {
        font-weight: 600;
        margin-bottom: 2px;
        font-size: var(--font-size-sm);
    }

    .download-meta {
        font-size: var(--font-size-xs);
        opacity: 0.8;
    }

    .download-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--radius-sm);
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: var(--font-size-sm);
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .download-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
    }

    /* 工具樣式 */
    .hidden {
        display: none !important;
    }

    .text-center {
        text-align: center;
    }

    .mb-0 { margin-bottom: 0; }
    .mt-2 { margin-top: var(--spacing-sm); }
    .mb-2 { margin-bottom: var(--spacing-sm); }

    /* 響應式設計 */
    @media (max-width: 768px) {
        .pts-container {
            padding: 0 var(--spacing-sm);
        }

        .pts-header {
            padding: var(--spacing-md);
        }

        .pts-header h1 {
            font-size: var(--font-size-md);
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .section-content {
            padding: var(--spacing-md);
        }

        .upload-area {
            padding: var(--spacing-lg);
            min-height: 150px;
        }

        .upload-icon {
            font-size: 36px;
        }

        .options-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }

        .checkbox-group {
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .file-item {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-sm);
        }

        .file-info {
            width: 100%;
        }

        .btn-group {
            flex-direction: column;
        }

        .btn {
            justify-content: center;
        }

        .execute-btn {
            width: 100%;
        }

        .download-item {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-sm);
        }

        .download-info {
            width: 100%;
        }
    }

    @media (max-width: 480px) {
        .pts-header h1 {
            font-size: var(--font-size-sm);
        }

        .upload-area {
            padding: var(--spacing-md);
        }

        .upload-icon {
            font-size: 24px;
        }

        .upload-text {
            font-size: var(--font-size-sm);
        }

        .execute-section {
            padding: var(--spacing-md);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="pts-container">
    <!-- 頁面標題 -->
    <div class="pts-header">
        <h1>
            <i class="fas fa-file-code"></i>
            PTS 檔案批量重命名工具
        </h1>
        <p class="subtitle">專業的半導體測試檔案管理系統 - 支援批量處理與自動化重命名</p>
    </div>

    <!-- 檔案上傳區域 -->
    <div class="pts-section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-cloud-upload-alt"></i>
                檔案上傳
            </h2>
        </div>
        <div class="section-content">
            <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                <input type="file" id="fileInput" class="file-input" accept=".zip,.7z,.rar" multiple>
                <div class="upload-icon">
                    <i class="fas fa-file-archive"></i>
                </div>
                <div class="upload-text">點擊或拖拽檔案到此處上傳</div>
                <p class="upload-hint">支援格式：ZIP、7Z、RAR（包含 .pts 檔案）</p>
            </div>
            
            <div class="uploaded-files" id="uploadedFiles">
                <!-- 上傳的檔案列表會在這裡顯示 -->
            </div>
            
            <div class="btn-group">
                <button class="btn btn-danger hidden" id="clearFilesBtn">
                    <i class="fas fa-trash-alt"></i>
                    清空所有檔案
                </button>
            </div>
        </div>
    </div>

    <!-- 處理選項區域 -->
    <div class="pts-section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-cogs"></i>
                處理選項
            </h2>
        </div>
        <div class="section-content">
            <div class="checkbox-group">
                <div class="checkbox-item">
                    <input type="checkbox" id="renameEnabled">
                    <label for="renameEnabled">重新命名檔案</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="qcEnabled">
                    <label for="qcEnabled">生成 QC 檔案</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="createDirectories">
                    <label for="createDirectories">創建組織目錄</label>
                </div>
            </div>

            <div class="options-grid">
                <!-- 重命名設定 -->
                <div class="option-card hidden" id="renameOptions">
                    <h3 class="mb-2">
                        <i class="fas fa-edit"></i>
                        重命名設定
                    </h3>
                    <div class="form-group">
                        <label for="oldPattern">原檔名模式：</label>
                        <input type="text" id="oldPattern" placeholder="例如：old_pattern 或使用正則表達式">
                        <div class="help-text">支援變數：{old}, {ext}, {num}</div>
                    </div>
                    <div class="form-group">
                        <label for="newPattern">新檔名模式：</label>
                        <input type="text" id="newPattern" placeholder="例如：new_pattern 或 new_{old}_{num}">
                        <div class="help-text">支援變數替換和序號生成</div>
                    </div>
                </div>

                <!-- QC 設定 -->
                <div class="option-card hidden" id="qcOptions">
                    <h3 class="mb-2">
                        <i class="fas fa-check-circle"></i>
                        QC 檔案設定
                    </h3>
                    <div class="form-group">
                        <label for="qcSuffix">QC 後綴：</label>
                        <input type="text" id="qcSuffix" value="_QC" placeholder="QC 檔案後綴">
                        <div class="help-text">將在原檔名後添加指定後綴</div>
                    </div>
                </div>

                <!-- 目錄設定 -->
                <div class="option-card hidden" id="directoryOptions">
                    <h3 class="mb-2">
                        <i class="fas fa-folder"></i>
                        目錄創建設定
                    </h3>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="groupByType" checked>
                            <label for="groupByType">按檔案類型分組</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="groupByDate">
                            <label for="groupByDate">按日期分組</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 預覽區域 -->
    <div class="pts-section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-eye"></i>
                處理預覽
            </h2>
        </div>
        <div class="section-content">
            <div class="preview-container">
                <div class="preview-list" id="previewList">
                    <div class="preview-empty">
                        <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                        請上傳檔案並選擇處理選項後，點擊「預覽處理結果」查看效果
                    </div>
                </div>
            </div>
            <div class="btn-group">
                <button class="btn btn-secondary" id="previewBtn">
                    <i class="fas fa-search"></i>
                    預覽處理結果
                </button>
                <button class="btn btn-danger" id="clearPreviewBtn">
                    <i class="fas fa-times"></i>
                    清除預覽
                </button>
            </div>
        </div>
    </div>

    <!-- 執行處理 -->
    <div class="pts-section">
        <div class="execute-section">
            <button class="execute-btn" id="executeBtn" disabled>
                <span class="status-indicator status-ready"></span>
                <i class="fas fa-play"></i>
                開始處理
            </button>
            
            <div class="progress-section hidden" id="progressSection">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%">0%</div>
                </div>
                <div class="progress-text" id="progressText">準備中...</div>
            </div>
        </div>
    </div>

    <!-- 下載結果區域 -->
    <div class="pts-section download-section hidden" id="downloadSection">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-download"></i>
                處理完成 - 下載結果
            </h2>
        </div>
        <div class="section-content">
            <div id="downloadList">
                <!-- 下載連結會在這裡顯示 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script src="{{ url_for('pts_renamer.static', filename='js/pts_renamer.js') }}"></script>
{% endblock %}

{% block page_js %}
<script>
// 頁面特定初始化邏輯（如果需要）
document.addEventListener('DOMContentLoaded', function() {
    // 可以在這裡添加頁面特定的初始化邏輯
    console.log('PTS Renamer 頁面已載入');
});
</script>
{% endblock %}