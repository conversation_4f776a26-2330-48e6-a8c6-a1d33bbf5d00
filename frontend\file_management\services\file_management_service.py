"""
檔案管理服務模組
提供檔案上傳、下載、存在檢查、今日處理檔案管理等功能
"""

import os
import json
import shutil
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
from loguru import logger
from fastapi import HTTPException, UploadFile, Form, File
from fastapi.responses import JSONResponse, FileResponse

from backend.shared.infrastructure.adapters.api_utils import (
    APIUtils, 
    UploadProcessorManager, 
    ResponseFormatter, 
    LoggingUtils, 
    FileOperations,
    SystemConfig
)
from frontend.shared.models.models import (
    UploadConfigResponse,
    UploadResult,
    ArchiveInfo,
    ExtractionResult,
    UploadAndProcessRequest,
    UploadAndProcessResponse,
    OnlineEQCProcessData
)


class FileManagementService:
    """檔案管理服務類別"""
    
    def __init__(self):
        pass  # 現在使用 SystemConfig 統一配置
    
    @staticmethod
    def normalize_windows_path(file_path: str) -> str:
        """
        統一路徑格式化為 Windows 格式，避免雙反斜線問題
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            str: 格式化後的 Windows 路徑
        """
        # 先統一使用正斜線
        normalized = str(file_path).replace("\\", "/")
        
        # 轉換 /mnt/d/ 為 D:/
        if normalized.startswith("/mnt/d/"):
            normalized = "D:/" + normalized[7:]  # 移[EXCEPT_CHAR] /mnt/d/
        
        # 轉換為 Windows 反斜線格式
        windows_path = normalized.replace("/", "\\")
        
        # 確保磁碟機代號後只有一個反斜線
        if windows_path.startswith("D:\\"):
            # 移[EXCEPT_CHAR]重複的反斜線
            windows_path = windows_path.replace("\\\\", "\\")
            return windows_path
        elif windows_path.startswith("D:"):
            return "D:\\" + windows_path[2:].lstrip("\\")
        
        # 最終清理：移[EXCEPT_CHAR]任何重複的反斜線
        return windows_path.replace("\\\\", "\\")
    
    async def get_upload_config(self) -> UploadConfigResponse:
        """
        取得檔案上傳配置資訊
        
        Returns:
            UploadConfigResponse: 上傳配置資訊
        """
        try:
            LoggingUtils.log_api_start("get_upload_config")
            
            processor = UploadProcessorManager.get_global_upload_processor()
            config_info = processor.get_upload_info()
            
            result = UploadConfigResponse(**config_info)
            
            LoggingUtils.log_api_success("get_upload_config", "配置資訊獲取成功")
            return result
            
        except Exception as e:
            LoggingUtils.log_api_error("get_upload_config", e)
            raise HTTPException(
                status_code=500,
                detail=f"取得上傳配置失敗: {str(e)}"
            )
    
    async def upload_archive(self, file: UploadFile) -> Dict[str, Any]:
        """
        檔案上傳端點（單純上傳+解壓縮）
        
        Args:
            file: 上傳的壓縮檔
            
        Returns:
            Dict[str, Any]: 上傳和解壓縮結果
        """
        try:
            import time
            start_time = time.time()
            
            LoggingUtils.log_api_start("upload_archive", {"filename": file.filename})
            
            # 匯入檔案上傳相關模組
            from backend.file_management.adapters.file_upload import (
                UploadProcessor,
                ArchiveExtractor
            )
            
            # 第一步：檔案上傳（使用全局實例維持重複防護狀態）
            upload_processor = UploadProcessorManager.get_global_upload_processor()
            upload_result_dict = await upload_processor.process_upload(file)
            upload_result = UploadResult(**upload_result_dict)
            
            if not upload_result.success:
                return ResponseFormatter.create_error_response(
                    status_code=400,
                    message=upload_result.message,
                    error_code="UPLOAD_FAILED",
                    details={"upload_result": upload_result.dict()}
                )
            
            # 第二步：解壓縮
            extractor = ArchiveExtractor()
            extraction_result_dict = extractor.extract_archive(upload_result.upload_path)
            extraction_result = ExtractionResult(**extraction_result_dict)
            
            # 清理上傳的原始檔案
            upload_processor.cleanup_upload_file(upload_result.upload_path)

            if not extraction_result.success:
                return ResponseFormatter.create_error_response(
                    status_code=400,
                    message=extraction_result.message,
                    error_code="EXTRACTION_FAILED",
                    details={"extraction_result": extraction_result.dict()}
                )

            # 自動提交 CSV 摘要處理任務（支援多人多工）
            csv_task_id = None
            csv_task_error = None

            try:
                from backend.tasks.services.dramatiq_tasks import run_csv_summary_task

                # 提交背景任務處理解壓縮後的資料夾
                task = run_csv_summary_task.send(extraction_result.extract_dir)
                csv_task_id = task.message_id

                logger.info(f"檔案上傳後自動提交 CSV 摘要任務: {csv_task_id}")

            except ImportError as e:
                csv_task_error = f"CSV 摘要任務不可用: {e}"
                logger.warning(csv_task_error)
            except Exception as e:
                csv_task_error = f"提交 CSV 摘要任務失敗: {e}"
                logger.error(csv_task_error)

            processing_time = time.time() - start_time

            result = {
                "status": "success",
                "message": "檔案上傳、解壓縮和 CSV 摘要處理已開始",
                "upload_result": upload_result.dict(),
                "extraction_result": extraction_result.dict(),
                "extracted_folder_path": extraction_result.extract_dir,
                "csv_summary_task_id": csv_task_id,
                "csv_summary_status": "submitted" if csv_task_id else "failed",
                "csv_summary_error": csv_task_error,
                "processing_time": round(processing_time, 2),
                "auto_download_enabled": True  # 標記啟用自動下載
            }
            
            LoggingUtils.log_api_success("upload_archive", f"處理時間: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            LoggingUtils.log_api_error("upload_archive", e)
            return ResponseFormatter.create_error_response(
                status_code=500,
                message=f"檔案上傳失敗: {str(e)}",
                error_code="UPLOAD_PROCESSING_FAILED"
            )
    
    # NOTE: upload_and_process 功能將在 EQC 處理服務完成後整合
    
    async def get_archive_info(self, archive_path: str) -> Dict[str, Any]:
        """
        取得壓縮檔資訊（不解壓縮）
        
        Args:
            archive_path: 壓縮檔路徑
            
        Returns:
            Dict[str, Any]: 壓縮檔資訊
        """
        try:
            LoggingUtils.log_api_start("get_archive_info", {"archive_path": archive_path})
            
            from infrastructure.adapters.file_upload import ArchiveExtractor
            
            # 轉換路徑
            _, converted_path = APIUtils.process_folder_path(archive_path)
            
            # 驗證檔案存在
            is_valid, error_msg = APIUtils.validate_file_path(converted_path)
            if not is_valid:
                raise HTTPException(status_code=404, detail=error_msg)
            
            extractor = ArchiveExtractor()
            info = extractor.get_archive_info(converted_path)
            
            if info["success"]:
                archive_info = ArchiveInfo(**info)
                result = {
                    "status": "success",
                    "message": "成功取得壓縮檔資訊",
                    "archive_info": archive_info.dict()
                }
            else:
                result = ResponseFormatter.create_error_response(
                    status_code=400,
                    message=info["message"],
                    error_code="ARCHIVE_INFO_FAILED"
                )
            
            LoggingUtils.log_api_success("get_archive_info", "壓縮檔資訊獲取成功")
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            LoggingUtils.log_api_error("get_archive_info", e)
            raise HTTPException(
                status_code=500,
                detail=f"取得壓縮檔資訊失敗: {str(e)}"
            )
    
    async def check_file_exists(self, file_path: str) -> Dict[str, Any]:
        """
        檢查檔案是否存在
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            Dict[str, Any]: 檔案存在狀態
        """
        try:
            LoggingUtils.log_api_start("check_file_exists", {"file_path": file_path})
            
            # 轉換路徑
            _, converted_path = APIUtils.process_folder_path(file_path)
            
            exists = FileOperations.safe_file_exists(converted_path)
            file_size = FileOperations.get_file_size(converted_path) if exists else 0
            
            result = {
                "status": "success",
                "message": "檔案檢查完成",
                "file_exists": exists,
                "file_path": file_path,
                "converted_path": converted_path,
                "file_size": file_size,
                "file_size_formatted": FileOperations.format_file_size(file_size)
            }
            
            LoggingUtils.log_api_success("check_file_exists", f"檔案存在: {exists}")
            return result
            
        except Exception as e:
            LoggingUtils.log_api_error("check_file_exists", e)
            raise HTTPException(
                status_code=500, 
                detail=f"檔案檢查失敗: {str(e)}"
            )
    
    async def get_today_processed_files(self) -> Dict[str, Any]:
        """
        獲取今日處理過的檔案清單
        
        Returns:
            dict: 今日處理檔案的詳細資訊
        """
        try:
            LoggingUtils.log_api_start("get_today_processed_files")
            
            # 獲取今天的日期範圍
            today = datetime.now().date()
            today_start = datetime.combine(today, datetime.min.time())
            today_end = datetime.combine(today, datetime.max.time())
            
            logger.info(f"掃描範圍: {today_start} 到 {today_end}")
            
            processed_files = []
            
            # 掃描解壓縮目錄
            extract_base_dir = SystemConfig.TEMP_BASE_DIR.resolve()
            self._scan_extracted_directories(extract_base_dir, today_start, today_end, processed_files)
            
            # 掃描 doc 目錄中的直接處理檔案
            doc_base_dir = Path("doc").resolve()
            self._scan_doc_directories(doc_base_dir, today_start, today_end, processed_files)
            
            # 按處理時間倒序排列（最新在前）
            processed_files.sort(key=lambda x: x["process_time"], reverse=True)
            
            result = {
                "status": "success",
                "data": {
                    "date": today.isoformat(),
                    "total_count": len(processed_files),
                    "processed_files": processed_files
                }
            }
            
            LoggingUtils.log_api_success("get_today_processed_files", f"找到 {len(processed_files)} 個記錄")
            return result
            
        except Exception as e:
            LoggingUtils.log_api_error("get_today_processed_files", e)
            raise HTTPException(
                status_code=500,
                detail=f"獲取今日處理檔案失敗: {str(e)}"
            )
    
    def _scan_extracted_directories(self, extract_base_dir: Path, today_start: datetime, today_end: datetime, processed_files: List[Dict[str, Any]]) -> None:
        """掃描解壓縮目錄中的處理檔案"""
        if not extract_base_dir.exists():
            return
            
        for item in extract_base_dir.iterdir():
            if item.is_dir():
                # 檢查目錄修改時間是否在今天
                mtime = datetime.fromtimestamp(item.stat().st_mtime)
                
                if today_start <= mtime <= today_end:
                    result_files = self._scan_result_files(item)
                    
                    if result_files:
                        windows_dir_path = self.normalize_windows_path(str(item))
                        
                        # 讀取 metadata 獲取原始資料夾名稱
                        original_folder_name = None
                        metadata_file = item / "metadata.json"
                        if metadata_file.exists():
                            try:
                                with open(metadata_file, 'r', encoding='utf-8') as f:
                                    metadata = json.load(f)
                                    original_folder_name = metadata.get('original_folder_name')
                            except Exception:
                                pass
                        
                        display_name = original_folder_name if original_folder_name else item.name
                        
                        processed_files.append({
                            "extract_id": item.name,
                            "original_folder_name": original_folder_name,
                            "display_name": display_name,
                            "process_time": mtime.isoformat(),
                            "process_time_display": mtime.strftime("%H:%M"),
                            "directory_path": windows_dir_path,
                            "result_files": result_files,
                            "total_files": len(result_files),
                            "source": "extracted"  # 標識來源
                        })
    
    def _scan_doc_directories(self, doc_base_dir: Path, today_start: datetime, today_end: datetime, processed_files: List[Dict[str, Any]]) -> None:
        """掃描 doc 目錄中的直接處理檔案"""
        if not doc_base_dir.exists():
            return
            
        for item in doc_base_dir.iterdir():
            if item.is_dir():
                result_files = self._scan_result_files(item)
                
                if result_files:
                    # 檢查是否有今天修改的檔案
                    has_today_files = False
                    for file_info in result_files:
                        file_mtime = datetime.fromisoformat(file_info["modified"].replace('Z', '+00:00'))
                        if file_mtime.date() == today_start.date():
                            has_today_files = True
                            break
                    
                    if has_today_files:
                        windows_dir_path = self.normalize_windows_path(str(item))
                        
                        # 使用目錄名稱作為顯示名稱
                        display_name = item.name
                        
                        # 使用最新檔案的時間作為處理時間
                        latest_mtime = max(
                            datetime.fromisoformat(f["modified"].replace('Z', '+00:00')) 
                            for f in result_files
                        )
                        
                        processed_files.append({
                            "extract_id": f"doc_{item.name}",
                            "original_folder_name": item.name,
                            "display_name": display_name,
                            "process_time": latest_mtime.isoformat(),
                            "process_time_display": latest_mtime.strftime("%H:%M"),
                            "directory_path": windows_dir_path,
                            "result_files": result_files,
                            "total_files": len(result_files),
                            "source": "doc"  # 標識來源
                        })
    
    def _scan_result_files(self, directory: Path) -> List[Dict[str, Any]]:
        """掃描目錄中的結果檔案，包括子目錄"""
        result_files = []
        
        # 檢查標準結果檔案 - 首先檢查直接在目錄下的檔案
        for filename in ["EQCTOTALDATA.xlsx", "EQCTOTALDATA_RAW.csv", "EQCTOTALDATA.csv"]:
            file_path = directory / filename
            if file_path.exists():
                file_size = file_path.stat().st_size
                file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                windows_path = self.normalize_windows_path(str(file_path))
                
                result_files.append({
                    "filename": filename,
                    "size": file_size,
                    "size_mb": round(file_size / (1024 * 1024), 1),
                    "modified": file_mtime.isoformat(),
                    "path": windows_path
                })
        
        # 如果直接目錄下沒有找到檔案，搜尋子目錄（最多2層深度）
        if not result_files:
            for sub_item in directory.iterdir():
                if sub_item.is_dir():
                    for filename in ["EQCTOTALDATA.xlsx", "EQCTOTALDATA_RAW.csv", "EQCTOTALDATA.csv"]:
                        file_path = sub_item / filename
                        if file_path.exists():
                            file_size = file_path.stat().st_size
                            file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                            windows_path = self.normalize_windows_path(str(file_path))
                            
                            result_files.append({
                                "filename": filename,
                                "size": file_size,
                                "size_mb": round(file_size / (1024 * 1024), 1),
                                "modified": file_mtime.isoformat(),
                                "path": windows_path
                            })
                    
                    # 如果在第一個子目錄找到檔案，就不再檢查其他子目錄
                    if result_files:
                        break
        
        return result_files
    
    
    async def download_report(self, report_path: str):
        """
        下載報告檔案
        
        Args:
            report_path: 報告檔案路徑
            
        Returns:
            FileResponse: 檔案下載回應
        """
        try:
            LoggingUtils.log_api_start("download_report", {"report_path": report_path})
            
            # 轉換路徑
            _, converted_path = APIUtils.process_folder_path(report_path)
            
            # 驗證檔案存在
            is_valid, error_msg = APIUtils.validate_file_path(converted_path)
            if not is_valid:
                raise HTTPException(status_code=404, detail=error_msg)
            
            # 獲取檔案名稱
            filename = os.path.basename(converted_path)
            
            LoggingUtils.log_api_success("download_report", f"下載檔案: {filename}")
            
            return FileResponse(
                path=converted_path,
                filename=filename,
                media_type='application/octet-stream'
            )
            
        except HTTPException:
            raise
        except Exception as e:
            LoggingUtils.log_api_error("download_report", e)
            raise HTTPException(
                status_code=500,
                detail=f"下載報告失敗: {str(e)}"
            )
    
    async def download_file(self, file_path: str):
        """
        下載一般檔案

        Args:
            file_path: 檔案路徑

        Returns:
            FileResponse: 檔案下載回應
        """
        try:
            LoggingUtils.log_api_start("download_file", {"file_path": file_path})

            # 轉換路徑
            _, converted_path = APIUtils.process_folder_path(file_path)

            # 驗證檔案存在
            is_valid, error_msg = APIUtils.validate_file_path(converted_path)
            if not is_valid:
                raise HTTPException(status_code=404, detail=error_msg)

            # 獲取檔案名稱
            filename = os.path.basename(converted_path)

            LoggingUtils.log_api_success("download_file", f"下載檔案: {filename}")

            return FileResponse(
                path=converted_path,
                filename=filename,
                media_type='application/octet-stream'
            )

        except HTTPException:
            raise
        except Exception as e:
            LoggingUtils.log_api_error("download_file", e)
            raise HTTPException(
                status_code=500,
                detail=f"下載檔案失敗: {str(e)}"
            )

    async def process_download_path(self, file_path: str) -> str:
        """處理下載路徑（Windows路徑轉換為Linux路徑）"""
        _, converted_path = APIUtils.process_folder_path(file_path)
        return converted_path

    async def read_report_content(self, report_path: str) -> str:
        """讀取報告檔案內容"""
        processed_path = await self.process_download_path(report_path)

        if not os.path.exists(processed_path):
            raise FileNotFoundError(f"報告檔案不存在: {processed_path}")

        try:
            with open(processed_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            with open(processed_path, 'r', encoding='big5') as f:
                return f.read()

    async def upload_and_process(self, request: UploadAndProcessRequest) -> UploadAndProcessResponse:
        """
        上傳並處理檔案
        
        Args:
            request: 上傳並處理請求
            
        Returns:
            處理結果
        """
        try:
            LoggingUtils.log_api_start("upload_and_process")
            
            # 解析請求數據
            if hasattr(request, 'dict'):
                request_dict = request.dict()
            elif isinstance(request, dict):
                request_dict = request
            else:
                # 嘗試轉換為字典
                request_dict = dict(request) if hasattr(request, 'items') else {}
            
            file_path = request_dict.get('file_path', '')
            auto_process = request_dict.get('auto_process', False)
            
            logger.info(f"[INBOX_TRAY] 上傳處理請求: file_path={file_path}, auto_process={auto_process}")
            
            if not file_path:
                raise HTTPException(status_code=400, detail="檔案路徑不能為空")
            
            # 檢查檔案是否存在
            if not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail=f"檔案不存在: {file_path}")
            
            # 基本的處理邏輯
            result = {
                "status": "success",
                "message": "檔案上傳並處理完成",
                "file_path": file_path,
                "auto_process": auto_process,
                "processed": auto_process
            }
            
            LoggingUtils.log_api_success("upload_and_process", f"處理檔案: {file_path}")
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            LoggingUtils.log_api_error("upload_and_process", e)
            raise HTTPException(status_code=500, detail=f"上傳並處理失敗: {str(e)}")
    
    async def upload_and_extract_archive(self, file: Any) -> Dict[str, Any]:
        """上傳並解壓縮檔案"""
        # TODO: 實現具體的上傳解壓縮邏輯
        return {
            "status": "success",
            "filename": file.filename,
            "extracted_path": "/tmp/extracted",
            "file_count": 0
        }

    async def clear_duplicate_cache(self) -> Dict[str, Any]:
        """清[EXCEPT_CHAR]重複上傳快取"""
        # TODO: 實現具體的快取清[EXCEPT_CHAR]邏輯
        return {
            "cleared_count": 0,
            "cache_size_freed": 0
        }

    async def get_archive_info(self, archive_path: str) -> Dict[str, Any]:
        """
        取得壓縮檔資訊（不解壓縮）

        Args:
            archive_path: 壓縮檔路徑

        Returns:
            Dict[str, Any]: 壓縮檔資訊
        """
        try:
            LoggingUtils.log_api_start("get_archive_info", {"archive_path": archive_path})

            # 處理路徑
            processed_path = await self.process_download_path(archive_path)

            # 檢查檔案是否存在
            if not os.path.exists(processed_path):
                raise FileNotFoundError(f"壓縮檔不存在: {processed_path}")

            # 獲取檔案基本資訊
            file_size = os.path.getsize(processed_path)
            file_name = os.path.basename(processed_path)

            # TODO: 實現具體的壓縮檔分析邏輯
            # 這裡可以使用 zipfile, tarfile 等模組來分析壓縮檔內容

            result = {
                "file_name": file_name,
                "file_path": archive_path,
                "file_size": file_size,
                "file_size_formatted": self._format_file_size(file_size),
                "file_type": self._get_file_extension(file_name),
                "is_valid_archive": self._is_valid_archive(processed_path),
                "estimated_files": 0,  # TODO: 實際計算檔案數量
                "supported_formats": [".zip", ".7z", ".rar", ".tar", ".gz"]
            }

            LoggingUtils.log_api_success("get_archive_info", f"分析完成: {file_name}")
            return result

        except Exception as e:
            LoggingUtils.log_api_error("get_archive_info", e)
            raise HTTPException(
                status_code=500,
                detail=f"取得壓縮檔資訊失敗: {str(e)}"
            )

    def _format_file_size(self, size_bytes: int) -> str:
        """格式化檔案大小"""
        if size_bytes == 0:
            return "0B"

        size_units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)

        while size >= 1024.0 and unit_index < len(size_units) - 1:
            size /= 1024.0
            unit_index += 1

        return f"{size:.1f}{size_units[unit_index]}"

    def _get_file_extension(self, filename: str) -> str:
        """獲取檔案副檔名"""
        return os.path.splitext(filename)[1].lower()

    def _is_valid_archive(self, file_path: str) -> bool:
        """檢查是否為有效的壓縮檔"""
        try:
            extension = self._get_file_extension(file_path)
            return extension in ['.zip', '.7z', '.rar', '.tar', '.gz']
        except Exception:
            return False
    


# 全域檔案管理服務實例
file_management_service = FileManagementService()


# 依賴注入函數
def get_file_management_service() -> FileManagementService:
    """取得檔案管理服務實例（用於 FastAPI 依賴注入）"""
    return file_management_service
