# PTS Renamer Task 7 Implementation Context

## Current Task: Task 7 - Flask Web Interface Implementation

### Task 7 Overview
Implement Flask web interface (current implementation) with complete user interface:

#### 7.1 Create Flask routes and API endpoints
- Implement `pts_rename_flask_routes.py` with complete API
- Support upload, processing, status, preview, and download endpoints
- Ensure route accessible at `http://localhost:5000/pts-renamer/`
- Requirements: 3.1, 3.2, 3.3, 3.4, 3.5

#### 7.2 Create HTML templates and user interface
- Implement `pts_rename_main.html` with modern web interface
- Support drag-and-drop upload, real-time progress, and preview
- Include all processing options (rename, QC, directories)
- Requirements: 3.1, 3.2, 3.3, 3.4, 3.5

#### 7.3 Implement JavaScript frontend functionality
- Create `pts_rename_upload.js` for file upload and progress tracking
- Implement `pts_rename_processor.js` for processing configuration
- Add real-time status updates and result display
- Requirements: 3.2, 3.3, 3.4, 3.5

## Prerequisites Completed (Tasks 1-6)
- ✅ Module structure and core interfaces (Task 1)
- ✅ Data models and entities (Task 2)
- ✅ File processing core services (Task 3)
- ✅ Upload and file handling services (Task 4)
- ⏳ Dramatiq integration (Task 5) - In Progress
- ⏳ MVP presenter layer (Task 6) - Ready for Task 7

## Integration Points
- Uses existing backend PTS renamer modules
- Integrates with existing Flask app structure
- Leverages existing Dramatiq task infrastructure
- Follows existing frontend patterns from email module

## Expected Deliverables
1. Complete Flask route implementation with all endpoints
2. Modern HTML template with drag-and-drop interface
3. JavaScript modules for upload and processing management
4. Integration with existing backend services
5. Real-time progress tracking and status updates

## Critical Requirements
- Route must be accessible at `/pts-renamer/` endpoint
- Must support all PTS processing options (rename, QC, directories)
- Must integrate with existing authentication and security
- Must follow existing frontend patterns and styling