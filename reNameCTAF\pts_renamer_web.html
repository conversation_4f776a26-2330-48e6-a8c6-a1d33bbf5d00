<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PTS檔案批量重命名工具 - Web版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #2b2b2b;
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #3a3a3a;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #555;
            padding-bottom: 20px;
        }

        .header h1 {
            color: #ffffff;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .header .subtitle {
            color: #aaaaaa;
            font-size: 14px;
        }

        .section {
            background-color: #404040;
            border: 1px solid #555;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .section-title {
            color: #ffffff;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #666;
        }

        .upload-section {
            min-height: 200px;
        }

        .upload-area {
            background-color: #2b2b2b;
            border: 2px dashed #666;
            border-radius: 8px;
            min-height: 150px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            margin-bottom: 15px;
        }

        .upload-area:hover {
            border-color: #007acc;
            background-color: #353535;
        }

        .upload-area.dragover {
            border-color: #28a745;
            background-color: #353535;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 48px;
            color: #666;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #cccccc;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #888;
            font-size: 14px;
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .uploaded-files {
            margin-top: 15px;
        }

        .file-item {
            background-color: #2b2b2b;
            border: 1px solid #555;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .file-info {
            display: flex;
            align-items: center;
            flex-grow: 1;
        }

        .file-icon {
            font-size: 24px;
            margin-right: 12px;
            color: #007acc;
        }

        .file-details {
            flex-grow: 1;
        }

        .file-name {
            color: #ffffff;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .file-size {
            color: #aaa;
            font-size: 12px;
        }

        .remove-file-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
        }

        .remove-file-btn:hover {
            background-color: #c82333;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            background-color: #007acc;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #005a9e;
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        .btn-success {
            background-color: #28a745;
        }

        .btn-success:hover {
            background-color: #1e7e34;
        }

        .btn-danger {
            background-color: #dc3545;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .checkbox-group {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #007acc;
        }

        .checkbox-item label {
            color: #ffffff;
            font-size: 14px;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .form-group input[type="text"] {
            width: 100%;
            background-color: #2b2b2b;
            color: white;
            border: 1px solid #666;
            border-radius: 4px;
            padding: 10px;
            font-size: 14px;
        }

        .form-group input[type="text"]:focus {
            outline: none;
            border-color: #007acc;
            box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
        }

        .preview-section {
            min-height: 180px;
        }

        .preview-list {
            background-color: #2b2b2b;
            border: 1px solid #666;
            border-radius: 6px;
            min-height: 150px;
            padding: 10px;
            overflow-y: auto;
            max-height: 200px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .preview-item {
            padding: 4px 0;
            border-bottom: 1px solid #444;
            color: #cccccc;
        }

        .preview-item:last-child {
            border-bottom: none;
        }

        .preview-item.success {
            color: #28a745;
        }

        .preview-item.error {
            color: #dc3545;
        }

        .preview-item.info {
            color: #17a2b8;
        }

        .execute-section {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #555;
        }

        .execute-btn {
            background-color: #007acc;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px 40px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 200px;
        }

        .execute-btn:hover {
            background-color: #005a9e;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 122, 204, 0.3);
        }

        .execute-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .hidden {
            display: none;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-ready {
            background-color: #28a745;
        }

        .status-processing {
            background-color: #ffc107;
            animation: pulse 1.5s infinite;
        }

        .status-error {
            background-color: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .progress-bar {
            width: 100%;
            background-color: #444;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 20px;
            background-color: #007acc;
            transition: width 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .download-section {
            background-color: #28a745;
            border: 1px solid #1e7e34;
        }

        .download-section .section-title {
            color: #ffffff;
        }

        .download-item {
            background-color: #2b2b2b;
            border: 1px solid #555;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .download-info {
            display: flex;
            align-items: center;
            flex-grow: 1;
        }

        .download-icon {
            font-size: 24px;
            margin-right: 15px;
            color: #28a745;
        }

        .download-details {
            flex-grow: 1;
        }

        .download-name {
            color: #ffffff;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .download-meta {
            color: #aaa;
            font-size: 12px;
        }

        .download-btn {
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-size: 14px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .download-btn:hover {
            background-color: #1e7e34;
        }

        .clear-files-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 10px;
        }

        .clear-files-btn:hover {
            background-color: #545b62;
        }

        .hidden {
            display: none !important;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .checkbox-group {
                flex-direction: column;
                gap: 10px;
            }

            .file-item, .download-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .file-info, .download-info {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PTS檔案批量重命名工具</h1>
            <div class="subtitle">專業的半導體測試檔案管理系統 - 上傳版本</div>
        </div>

        <!-- 檔案上傳區域 -->
        <div class="section upload-section">
            <div class="section-title">📤 上傳壓縮檔案</div>
            <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                <input type="file" id="fileInput" class="file-input" accept=".zip,.7z,.rar" multiple onchange="handleFileSelect(event)">
                <div class="upload-icon">📦</div>
                <div class="upload-text">點擊或拖拽檔案到此處上傳</div>
                <div class="upload-hint">支援格式: ZIP, 7Z, RAR (包含 .pts 檔案)</div>
            </div>
            
            <div class="uploaded-files" id="uploadedFiles">
                <!-- 上傳的檔案列表會在這裡顯示 -->
            </div>
            
            <button class="clear-files-btn hidden" id="clearFilesBtn" onclick="clearUploadedFiles()">清空所有檔案</button>
        </div>

        <!-- 重命名規則區域 -->
        <div class="section">
            <div class="section-title">⚙️ 處理選項</div>
            
            <div class="checkbox-group">
                <div class="checkbox-item">
                    <input type="checkbox" id="renameEnabled" onchange="toggleRenameFields()">
                    <label for="renameEnabled">重新命名</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="qcEnabled">
                    <label for="qcEnabled">增加QC</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="createDirectories">
                    <label for="createDirectories">創建目錄</label>
                </div>
            </div>

            <div id="renameFields" class="hidden">
                <div class="form-group">
                    <label for="oldPattern">替換前的檔案名稱模式:</label>
                    <input type="text" id="oldPattern" placeholder="例如: old_ 或 file_(\d+)">
                </div>
                <div class="form-group">
                    <label for="newPattern">替換後的檔案名稱模式:</label>
                    <input type="text" id="newPattern" placeholder="例如: new_ 或 newfile_$1">
                </div>
            </div>
        </div>

        <!-- 預覽結果區域 -->
        <div class="section preview-section">
            <div class="section-title">👁️ 處理預覽</div>
            <div class="preview-list" id="previewList">
                <div style="color: #888; text-align: center; padding: 20px;">
                    請上傳壓縮檔案後點擊「預覽處理結果」查看效果
                </div>
            </div>
            <div class="button-group" style="margin-top: 15px;">
                <button class="btn btn-secondary" onclick="previewProcessing()">預覽處理結果</button>
            </div>
        </div>

        <!-- 執行按鈕 -->
        <div class="execute-section">
            <button class="execute-btn" id="executeBtn" onclick="executeProcessing()">
                <span class="status-indicator status-ready"></span>
                開始處理
            </button>
            <div class="progress-bar hidden" id="progressBar">
                <div class="progress-fill" id="progressFill" style="width: 0%">0%</div>
            </div>
        </div>

        <!-- 下載區域 -->
        <div class="section download-section hidden" id="downloadSection">
            <div class="section-title">⬇️ 處理完成 - 下載結果</div>
            <div id="downloadList">
                <!-- 下載連結會在這裡顯示 -->
            </div>
        </div>
    </div>

    <script>
        let uploadedFiles = [];
        let isProcessing = false;

        // 檔案上傳處理
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            files.forEach(file => {
                if (file.type === 'application/zip' || file.name.endsWith('.zip') || 
                    file.name.endsWith('.7z') || file.name.endsWith('.rar')) {
                    addUploadedFile(file);
                } else {
                    alert(`檔案 "${file.name}" 不是支援的壓縮格式！`);
                }
            });
        }

        function addUploadedFile(file) {
            if (!uploadedFiles.find(f => f.name === file.name && f.size === file.size)) {
                uploadedFiles.push({
                    file: file,
                    name: file.name,
                    size: file.size,
                    type: getFileType(file.name),
                    uploadTime: new Date()
                });
                updateUploadedFilesList();
                document.getElementById('clearFilesBtn').classList.remove('hidden');
            }
        }

        function getFileType(fileName) {
            if (fileName.endsWith('.zip')) return 'ZIP';
            if (fileName.endsWith('.7z')) return '7Z';
            if (fileName.endsWith('.rar')) return 'RAR';
            return 'UNKNOWN';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function updateUploadedFilesList() {
            const uploadedFilesContainer = document.getElementById('uploadedFiles');
            if (uploadedFiles.length === 0) {
                uploadedFilesContainer.innerHTML = '';
                document.getElementById('clearFilesBtn').classList.add('hidden');
            } else {
                uploadedFilesContainer.innerHTML = uploadedFiles.map((fileData, index) => 
                    `<div class="file-item">
                        <div class="file-info">
                            <div class="file-icon">${getFileIcon(fileData.type)}</div>
                            <div class="file-details">
                                <div class="file-name">${fileData.name}</div>
                                <div class="file-size">${formatFileSize(fileData.size)} • ${fileData.type} • ${fileData.uploadTime.toLocaleTimeString()}</div>
                            </div>
                        </div>
                        <button class="remove-file-btn" onclick="removeUploadedFile(${index})">移除</button>
                    </div>`
                ).join('');
            }
        }

        function getFileIcon(type) {
            switch(type) {
                case 'ZIP': return '📦';
                case '7Z': return '🗜️';
                case 'RAR': return '📚';
                default: return '📄';
            }
        }

        function removeUploadedFile(index) {
            uploadedFiles.splice(index, 1);
            updateUploadedFilesList();
            clearPreview();
        }

        function clearUploadedFiles() {
            uploadedFiles = [];
            updateUploadedFilesList();
            clearPreview();
            hideDownloadSection();
        }

        // 拖拽上傳功能
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = Array.from(e.dataTransfer.files);
            files.forEach(file => {
                if (file.type === 'application/zip' || file.name.endsWith('.zip') || 
                    file.name.endsWith('.7z') || file.name.endsWith('.rar')) {
                    addUploadedFile(file);
                } else {
                    alert(`檔案 "${file.name}" 不是支援的壓縮格式！`);
                }
            });
        });

        function toggleRenameFields() {
            const renameFields = document.getElementById('renameFields');
            const renameEnabled = document.getElementById('renameEnabled').checked;
            
            if (renameEnabled) {
                renameFields.classList.remove('hidden');
            } else {
                renameFields.classList.add('hidden');
            }
        }

        function previewProcessing() {
            const previewList = document.getElementById('previewList');
            
            if (uploadedFiles.length === 0) {
                previewList.innerHTML = '<div class="preview-item error">❌ 錯誤：請先上傳至少一個壓縮檔案</div>';
                return;
            }

            const renameEnabled = document.getElementById('renameEnabled').checked;
            const qcEnabled = document.getElementById('qcEnabled').checked;
            const createDirectories = document.getElementById('createDirectories').checked;

            if (!renameEnabled && !qcEnabled && !createDirectories) {
                previewList.innerHTML = '<div class="preview-item error">❌ 錯誤：請至少選擇一個處理選項</div>';
                return;
            }

            // 模擬預覽結果
            let previewHtml = '<div class="preview-item info">=== 處理預覽 ===</div>';
            
            uploadedFiles.forEach((fileData, index) => {
                previewHtml += `<div class="preview-item info">📦 處理壓縮檔: ${fileData.name}</div>`;
                
                // 模擬壓縮檔內的 .pts 檔案
                const samplePtsFiles = [
                    `test_${index+1}_001.pts`,
                    `device_data_${index+1}_002.pts`,
                    `quality_check_${index+1}_003.pts`
                ];

                samplePtsFiles.forEach(file => {
                    if (renameEnabled) {
                        const oldPattern = document.getElementById('oldPattern').value;
                        const newPattern = document.getElementById('newPattern').value;
                        
                        if (oldPattern && newPattern) {
                            const newName = file.replace(oldPattern, newPattern);
                            previewHtml += `<div class="preview-item success">　✓ 重命名: ${file} → ${newName}</div>`;
                        } else {
                            previewHtml += `<div class="preview-item">　📄 無變更: ${file} (未設定模式)</div>`;
                        }
                    }
                    
                    if (qcEnabled) {
                        const qcName = file.replace('.pts', '_QC.pts');
                        previewHtml += `<div class="preview-item success">　✓ QC檔案: ${file} → ${qcName}</div>`;
                    }
                    
                    if (createDirectories) {
                        const dirName = file.replace('.pts', '');
                        previewHtml += `<div class="preview-item success">　✓ 創建目錄: ${dirName}/</div>`;
                    }
                });
                
                previewHtml += '<div class="preview-item">&nbsp;</div>';
            });

            previewList.innerHTML = previewHtml;
        }

        function clearPreview() {
            const previewList = document.getElementById('previewList');
            previewList.innerHTML = '<div style="color: #888; text-align: center; padding: 20px;">請上傳壓縮檔案後點擊「預覽處理結果」查看效果</div>';
        }

        function executeProcessing() {
            if (isProcessing) return;

            if (uploadedFiles.length === 0) {
                alert('請先上傳至少一個壓縮檔案！');
                return;
            }

            const renameEnabled = document.getElementById('renameEnabled').checked;
            const qcEnabled = document.getElementById('qcEnabled').checked;
            const createDirectories = document.getElementById('createDirectories').checked;

            if (!renameEnabled && !qcEnabled && !createDirectories) {
                alert('請至少選擇一個處理選項！');
                return;
            }

            if (renameEnabled) {
                const oldPattern = document.getElementById('oldPattern').value;
                const newPattern = document.getElementById('newPattern').value;
                
                if (!oldPattern || !newPattern) {
                    alert('啟用重命名時，請輸入替換前和替換後的檔案名稱模式！');
                    return;
                }
            }

            // 開始處理
            startProcessing();
        }

        function startProcessing() {
            isProcessing = true;
            const executeBtn = document.getElementById('executeBtn');
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            
            executeBtn.innerHTML = '<span class="status-indicator status-processing"></span>處理中...';
            executeBtn.disabled = true;
            progressBar.classList.remove('hidden');
            
            // 模擬處理進度
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    finishProcessing();
                }
                
                progressFill.style.width = progress + '%';
                progressFill.textContent = Math.round(progress) + '%';
            }, 800);
        }

        function finishProcessing() {
            const executeBtn = document.getElementById('executeBtn');
            const progressBar = document.getElementById('progressBar');
            const previewList = document.getElementById('previewList');
            
            // 顯示完成結果
            executeBtn.innerHTML = '<span class="status-indicator status-ready"></span>處理完成！';
            executeBtn.disabled = false;
            
            setTimeout(() => {
                executeBtn.innerHTML = '<span class="status-indicator status-ready"></span>開始處理';
                progressBar.classList.add('hidden');
                isProcessing = false;
            }, 2000);

            // 顯示結果
            const totalFiles = uploadedFiles.length * 3; // 每個壓縮檔模擬包含3個.pts檔案
            const resultHtml = `
                <div class="preview-item info">=== 處理完成 ===</div>
                <div class="preview-item success">✓ 成功處理: ${totalFiles} 個 PTS 檔案</div>
                <div class="preview-item error">✗ 處理失敗: 0 個檔案</div>
                <div class="preview-item">&nbsp;</div>
                <div class="preview-item info">=== 詳細結果 ===</div>
                <div class="preview-item success">✓ 重命名操作: ${document.getElementById('renameEnabled').checked ? '已執行' : '已跳過'}</div>
                <div class="preview-item success">✓ QC 檔案生成: ${document.getElementById('qcEnabled').checked ? '已執行' : '已跳過'}</div>
                <div class="preview-item success">✓ 目錄創建: ${document.getElementById('createDirectories').checked ? '已執行' : '已跳過'}</div>
            `;
            
            previewList.innerHTML = resultHtml;
            
            // 顯示下載區域
            showDownloadSection();
            
            alert(`處理完成！\n成功處理: ${totalFiles} 個 PTS 檔案\n失敗: 0 個檔案\n\n請至下載區域獲取處理結果！`);
        }

        function showDownloadSection() {
            const downloadSection = document.getElementById('downloadSection');
            const downloadList = document.getElementById('downloadList');
            
            downloadSection.classList.remove('hidden');
            
            let downloadHtml = '';
            uploadedFiles.forEach((fileData, index) => {
                const processedFileName = fileData.name.replace(/\.(zip|7z|rar)$/i, '_processed.zip');
                const fileSize = Math.round(fileData.size * 1.2); // 模擬處理後檔案可能稍大
                const downloadUrl = `#download-${index}`; // 實際應用中這裡應該是真實的下載連結
                
                downloadHtml += `
                    <div class="download-item">
                        <div class="download-info">
                            <div class="download-icon">⬇️</div>
                            <div class="download-details">
                                <div class="download-name">${processedFileName}</div>
                                <div class="download-meta">處理時間: ${new Date().toLocaleString()} • 大小: ${formatFileSize(fileSize)}</div>
                            </div>
                        </div>
                        <a href="${downloadUrl}" class="download-btn" onclick="simulateDownload('${processedFileName}')">下載</a>
                    </div>
                `;
            });
            
            downloadList.innerHTML = downloadHtml;
            
            // 自動滾動到下載區域
            downloadSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        function hideDownloadSection() {
            const downloadSection = document.getElementById('downloadSection');
            downloadSection.classList.add('hidden');
        }

        function simulateDownload(fileName) {
            // 實際應用中這裡應該觸發真實的檔案下載
            alert(`開始下載檔案: ${fileName}\n\n在實際應用中，這裡會下載處理完成的檔案。`);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化界面狀態
            clearPreview();
        });
    </script>
</body>
</html>