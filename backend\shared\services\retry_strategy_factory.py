"""
Epic-04 Story 4.2: 智能重試策略工廠實現
TDD GREEN PHASE: 最小實現通過測試

實現5種重試策略：
- LINEAR: 線性間隔 (1s, 2s, 3s, 4s...)
- EXPONENTIAL: 指數退避 (1s, 2s, 4s, 8s, 16s...)
- FIXED_DELAY: 固定間隔 (5s, 5s, 5s, 5s...)
- CUSTOM: 自定義間隔 ([1, 5, 10, 30, 60])
- ADAPTIVE: 自適應策略 (根據錯誤類型動態調整)
"""

import math
import random
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from backend.shared.infrastructure.adapters.database.download_tracking_models import RetryStrategy
from backend.shared.infrastructure.logging.logger_manager import LoggerManager


@dataclass
class RetryDelayInfo:
    """重試延遲信息"""
    delay_seconds: int
    strategy_used: RetryStrategy
    attempt_number: int
    calculated_at: datetime
    has_jitter: bool = False
    jitter_amount: int = 0


class BaseRetryStrategy(ABC):
    """重試策略基類"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = LoggerManager().get_logger(f"RetryStrategy.{name}")
    
    @abstractmethod
    def calculate_delay(self, attempt: int) -> int:
        """
        計算重試延遲時間
        
        Args:
            attempt: 重試次數 (1-based)
            
        Returns:
            延遲時間（秒）
        """
        pass
    
    def get_delay_info(self, attempt: int) -> RetryDelayInfo:
        """
        獲取詳細的延遲信息
        
        Args:
            attempt: 重試次數
            
        Returns:
            重試延遲信息
        """
        delay = self.calculate_delay(attempt)
        
        return RetryDelayInfo(
            delay_seconds=delay,
            strategy_used=RetryStrategy(self.name.lower()),
            attempt_number=attempt,
            calculated_at=datetime.utcnow(),
            has_jitter=hasattr(self, '_has_jitter') and self._has_jitter,
            jitter_amount=getattr(self, '_last_jitter', 0)
        )


class LinearRetryStrategy(BaseRetryStrategy):
    """線性重試策略: base_delay * attempt"""
    
    def __init__(self, base_delay: int = 60, max_delay: int = 3600):
        super().__init__("LINEAR")
        self.base_delay = base_delay
        self.max_delay = max_delay
    
    def calculate_delay(self, attempt: int) -> int:
        """計算線性延遲: base_delay * attempt"""
        if attempt <= 0:
            return self.base_delay
        
        delay = self.base_delay * attempt
        return min(delay, self.max_delay)


class ExponentialBackoffStrategy(BaseRetryStrategy):
    """指數退避策略: base_delay * (2 ^ (attempt - 1))"""
    
    def __init__(self, base_delay: int = 60, max_delay: int = 3600, multiplier: float = 2.0):
        super().__init__("EXPONENTIAL")
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.multiplier = multiplier
    
    def calculate_delay(self, attempt: int) -> int:
        """計算指數退避延遲: base_delay * (multiplier ^ (attempt - 1))"""
        if attempt <= 0:
            return self.base_delay
        
        delay = self.base_delay * (self.multiplier ** (attempt - 1))
        return min(int(delay), self.max_delay)


class FixedDelayStrategy(BaseRetryStrategy):
    """固定延遲策略: 固定間隔"""
    
    def __init__(self, delay: int = 300):
        super().__init__("FIXED_DELAY")
        self.delay = delay
    
    def calculate_delay(self, attempt: int) -> int:
        """計算固定延遲: 總是返回相同的延遲時間"""
        return self.delay


class CustomRetryStrategy(BaseRetryStrategy):
    """自定義重試策略: 使用預定義的延遲序列"""
    
    def __init__(self, delays: List[int]):
        super().__init__("CUSTOM")
        if not delays:
            raise ValueError("自定義延遲列表不能為空")
        self.delays = delays
    
    def calculate_delay(self, attempt: int) -> int:
        """計算自定義延遲: 使用預定義序列，超出範圍時使用最後一個值"""
        if attempt <= 0:
            return self.delays[0]
        
        # 使用 attempt-1 因為 attempt 是 1-based
        index = min(attempt - 1, len(self.delays) - 1)
        return self.delays[index]


class AdaptiveRetryStrategy(BaseRetryStrategy):
    """自適應重試策略: 結合線性和指數，加入隨機抖動"""
    
    def __init__(self, base_delay: int = 60, max_delay: int = 3600, 
                 linear_threshold: int = 3, jitter_percentage: float = 0.2):
        super().__init__("ADAPTIVE")
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.linear_threshold = linear_threshold
        self.jitter_percentage = jitter_percentage
        self._has_jitter = True
        self._last_jitter = 0
    
    def calculate_delay(self, attempt: int) -> int:
        """
        計算自適應延遲:
        - 前 linear_threshold 次使用線性策略
        - 之後使用指數退避策略
        - 添加隨機抖動避免雷鳴群效應
        """
        if attempt <= 0:
            return self.base_delay
        
        # 基礎延遲計算
        if attempt <= self.linear_threshold:
            # 線性策略
            base_delay = self.base_delay * attempt
        else:
            # 指數策略
            exponential_attempt = attempt - self.linear_threshold
            base_delay = self.base_delay * self.linear_threshold * (2 ** exponential_attempt)
        
        # 添加隨機抖動 (±jitter_percentage)
        jitter_range = base_delay * self.jitter_percentage
        jitter = random.uniform(-jitter_range, jitter_range)
        self._last_jitter = int(jitter)
        
        final_delay = int(base_delay + jitter)
        
        # 確保延遲在合理範圍內
        return max(1, min(final_delay, self.max_delay))


class RetryStrategyFactory:
    """
    重試策略工廠 - GREEN PHASE 實現
    
    負責創建和管理不同類型的重試策略：
    - 策略創建和配置
    - 智能策略選擇
    - 歷史數據分析
    """
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("RetryStrategyFactory")
        
        # 策略映射表
        self._strategy_classes = {
            RetryStrategy.LINEAR: LinearRetryStrategy,
            RetryStrategy.EXPONENTIAL: ExponentialBackoffStrategy,
            RetryStrategy.FIXED_DELAY: FixedDelayStrategy,
            RetryStrategy.CUSTOM: CustomRetryStrategy,
            RetryStrategy.ADAPTIVE: AdaptiveRetryStrategy
        }
        
        # 預設參數
        self._default_params = {
            RetryStrategy.LINEAR: {'base_delay': 60, 'max_delay': 3600},
            RetryStrategy.EXPONENTIAL: {'base_delay': 60, 'max_delay': 3600, 'multiplier': 2.0},
            RetryStrategy.FIXED_DELAY: {'delay': 300},
            RetryStrategy.CUSTOM: {'delays': [1, 5, 10, 30, 60]},
            RetryStrategy.ADAPTIVE: {'base_delay': 60, 'max_delay': 3600, 'linear_threshold': 3}
        }
    
    def create_strategy(self, strategy_type: RetryStrategy, **params) -> BaseRetryStrategy:
        """
        創建重試策略實例 - GREEN PHASE 實現
        
        Args:
            strategy_type: 策略類型
            **params: 策略參數
            
        Returns:
            重試策略實例
            
        Raises:
            ValueError: 無效的策略類型
            TypeError: 參數錯誤
        """
        try:
            # 驗證策略類型
            if strategy_type not in self._strategy_classes:
                raise ValueError(f"不支持的重試策略: {strategy_type}")
            
            # 獲取策略類
            strategy_class = self._strategy_classes[strategy_type]
            
            # 合併預設參數和自定義參數
            default_params = self._default_params[strategy_type].copy()
            default_params.update(params)
            
            # 創建策略實例
            strategy = strategy_class(**default_params)
            
            self.logger.debug(f"創建重試策略: {strategy_type}, 參數: {default_params}")
            return strategy
            
        except Exception as e:
            self.logger.error(f"創建重試策略失敗: {e}")
            raise
    
    def determine_optimal_strategy(self, error_type: str, 
                                 historical_data: Dict[str, Any]) -> RetryStrategy:
        """
        基於錯誤類型和歷史數據決定最佳策略 - GREEN PHASE 實現
        
        Args:
            error_type: 錯誤類型
            historical_data: 歷史數據
            
        Returns:
            推薦的重試策略
        """
        try:
            # GREEN PHASE: 簡單的策略選擇邏輯
            
            # 分析歷史成功率
            success_rates = historical_data.get('success_rates', {})
            
            if success_rates:
                # 選擇成功率最高的策略
                best_strategy = max(success_rates.items(), key=lambda x: x[1])
                strategy_name = best_strategy[0]
                
                # 轉換為 RetryStrategy 枚舉
                strategy_mapping = {
                    'linear': RetryStrategy.LINEAR,
                    'exponential': RetryStrategy.EXPONENTIAL,
                    'fixed': RetryStrategy.FIXED_DELAY,
                    'custom': RetryStrategy.CUSTOM,
                    'adaptive': RetryStrategy.ADAPTIVE
                }
                
                if strategy_name in strategy_mapping:
                    selected_strategy = strategy_mapping[strategy_name]
                    self.logger.info(f"基於歷史數據選擇策略: {selected_strategy}")
                    return selected_strategy
            
            # 基於錯誤類型的預設策略選擇
            error_strategy_mapping = {
                'network_error': RetryStrategy.EXPONENTIAL,
                'timeout_error': RetryStrategy.LINEAR,
                'rate_limit_error': RetryStrategy.FIXED_DELAY,
                'system_error': RetryStrategy.ADAPTIVE,
                'unknown_error': RetryStrategy.EXPONENTIAL
            }
            
            selected_strategy = error_strategy_mapping.get(error_type, RetryStrategy.EXPONENTIAL)
            self.logger.info(f"基於錯誤類型選擇策略: {error_type} -> {selected_strategy}")
            
            return selected_strategy
            
        except Exception as e:
            self.logger.error(f"決定最佳策略失敗: {e}")
            # 失敗時返回預設策略
            return RetryStrategy.EXPONENTIAL
    
    def get_strategy_recommendations(self, error_patterns: Dict[str, Any]) -> Dict[str, RetryStrategy]:
        """
        獲取策略推薦 - GREEN PHASE 實現
        
        Args:
            error_patterns: 錯誤模式分析
            
        Returns:
            策略推薦字典
        """
        recommendations = {}
        
        try:
            # GREEN PHASE: 基本推薦邏輯
            for error_type, pattern_info in error_patterns.items():
                frequency = pattern_info.get('frequency', 0)
                avg_duration = pattern_info.get('avg_duration', 0)
                
                if frequency > 10:  # 高頻錯誤
                    if avg_duration > 30:  # 持續時間長
                        recommendations[error_type] = RetryStrategy.ADAPTIVE
                    else:
                        recommendations[error_type] = RetryStrategy.LINEAR
                elif frequency > 5:  # 中頻錯誤
                    recommendations[error_type] = RetryStrategy.EXPONENTIAL
                else:  # 低頻錯誤
                    recommendations[error_type] = RetryStrategy.FIXED_DELAY
            
            self.logger.debug(f"生成策略推薦: {recommendations}")
            return recommendations
            
        except Exception as e:
            self.logger.error(f"獲取策略推薦失敗: {e}")
            return {}
    
    def validate_strategy_config(self, strategy_type: RetryStrategy, 
                                config: Dict[str, Any]) -> bool:
        """
        驗證策略配置 - GREEN PHASE 實現
        
        Args:
            strategy_type: 策略類型
            config: 配置參數
            
        Returns:
            配置是否有效
        """
        try:
            # GREEN PHASE: 基本配置驗證
            if strategy_type == RetryStrategy.LINEAR:
                base_delay = config.get('base_delay', 60)
                max_delay = config.get('max_delay', 3600)
                return 0 < base_delay <= max_delay
            
            elif strategy_type == RetryStrategy.EXPONENTIAL:
                base_delay = config.get('base_delay', 60)
                max_delay = config.get('max_delay', 3600)
                multiplier = config.get('multiplier', 2.0)
                return 0 < base_delay <= max_delay and multiplier > 1.0
            
            elif strategy_type == RetryStrategy.FIXED_DELAY:
                delay = config.get('delay', 300)
                return delay > 0
            
            elif strategy_type == RetryStrategy.CUSTOM:
                delays = config.get('delays', [])
                return len(delays) > 0 and all(d > 0 for d in delays)
            
            elif strategy_type == RetryStrategy.ADAPTIVE:
                base_delay = config.get('base_delay', 60)
                max_delay = config.get('max_delay', 3600)
                linear_threshold = config.get('linear_threshold', 3)
                return 0 < base_delay <= max_delay and linear_threshold > 0
            
            return False
            
        except Exception as e:
            self.logger.error(f"驗證策略配置失敗: {e}")
            return False
    
    def get_all_supported_strategies(self) -> List[RetryStrategy]:
        """
        獲取所有支持的策略類型
        
        Returns:
            支持的策略類型列表
        """
        return list(self._strategy_classes.keys())
    
    def estimate_total_delay(self, strategy_type: RetryStrategy, 
                           max_attempts: int, **params) -> int:
        """
        估算總延遲時間 - GREEN PHASE 實現
        
        Args:
            strategy_type: 策略類型
            max_attempts: 最大重試次數
            **params: 策略參數
            
        Returns:
            預估總延遲時間（秒）
        """
        try:
            strategy = self.create_strategy(strategy_type, **params)
            
            total_delay = 0
            for attempt in range(1, max_attempts + 1):
                total_delay += strategy.calculate_delay(attempt)
            
            self.logger.debug(f"估算總延遲時間: {strategy_type}, {max_attempts}次, {total_delay}秒")
            return total_delay
            
        except Exception as e:
            self.logger.error(f"估算總延遲時間失敗: {e}")
            return 0