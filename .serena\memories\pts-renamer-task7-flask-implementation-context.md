# PTS Renamer Task 7 Flask Web Interface Implementation Context

## Task 7 詳細要求
基於 .kiro/specs/pts-file-renamer-integration/tasks.md 第126-143行：

### 7.1 Flask routes and API endpoints (pts_rename_flask_routes.py)
- 實作完整的 API 支援: upload, processing, status, preview, download endpoints
- 確保路由可在 `http://localhost:5000/pts-renamer/` 存取
- 需求: 3.1, 3.2, 3.3, 3.4, 3.5

### 7.2 HTML templates and user interface (pts_rename_main.html)
- 實作現代網頁介面，支援拖放上傳、即時進度、預覽功能
- 包含所有處理選項 (rename, QC, directories)
- 需求: 3.1, 3.2, 3.3, 3.4, 3.5

### 7.3 JavaScript frontend functionality
- `pts_rename_upload.js`: 檔案上傳和進度追蹤
- `pts_rename_processor.js`: 處理配置
- 即時狀態更新和結果顯示
- 需求: 3.2, 3.3, 3.4, 3.5

## 必要先決條件 (Tasks 1-6)
- ✅ Task 1: 模組結構和核心接口
- ✅ Task 2: 資料模型和實體
- ✅ Task 3: 檔案處理核心服務
- ✅ Task 4: 上傳和檔案處理服務
- ⏳ Task 5: Dramatiq 整合 (進行中)
- ⏳ Task 6: MVP presenter layer (準備中)

## 整合點
- 使用現有後端 PTS renamer 模組
- 與現有 Flask 應用結構整合
- 利用現有 Dramatiq 任務基礎設施
- 遵循現有電子郵件模組的前端模式

## 關鍵架構決策
- 使用統一的 `outlook.db` 資料庫
- MVP (Model-View-Presenter) 架構模式
- 為未來 Vue.js + FastAPI 遷移做準備
- 所有檔案使用 `pts_rename` 前綴

## 當前環境狀態
- 虛擬環境: venv_win_3_11_9 已激活
- Python 版本: 3.11.9
- 工作目錄: D:\project\python\outlook_summary

## Agent 委派策略
1. backend-architect: 檢查 Tasks 1-6 的實際實作狀態
2. python-pro: 檢查 Dramatiq 整合和 MVP presenter 層
3. frontend-developer: 實作 Flask routes 和 HTML 模板
4. javascript-pro: 實作前端 JavaScript 功能
5. test-automator: 建立完整的測試套件

## 品質要求
- 所有實作必須在虛擬環境中驗證
- 必須與現有系統完全整合
- 必須遵循現有的編碼標準和模式
- 必須通過完整的功能測試