"""
Unit tests for PTS Rename Repository

Tests the repository implementation for data persistence operations
following the established testing patterns.
"""

import pytest
import tempfile
import os
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch

from backend.pts_renamer.repositories.pts_rename_sql_repository import PTSRenameSQLRepository
from backend.pts_renamer.repositories.pts_rename_repository import RepositoryError
from backend.pts_renamer.models.pts_rename_entities import (
    PTSProcessingJob,
    PTSFile,
    PTSRenameResult,
    PTSOperationType,
    JobId,
    UploadId,
    FileChecksum
)


class TestPTSRenameSQLRepository:
    """Test cases for PTS Rename SQL Repository"""
    
    @pytest.fixture
    def mock_db_connection(self):
        """Mock database connection"""
        mock_connection = Mock()
        mock_session = Mock()
        mock_connection.get_session.return_value.__enter__.return_value = mock_session
        mock_connection.get_session.return_value.__exit__.return_value = None
        return mock_connection, mock_session
    
    @pytest.fixture
    def repository(self, mock_db_connection):
        """Create repository instance with mocked database"""
        db_connection, _ = mock_db_connection
        return PTSRenameSQLRepository(db_connection)
    
    @pytest.fixture
    def sample_pts_file(self):
        """Create sample PTS file for testing"""
        with tempfile.NamedTemporaryFile(suffix='.pts', delete=False) as tmp_file:
            tmp_file.write(b"Parameter,QA,Test data")
            tmp_path = Path(tmp_file.name)
        
        pts_file = PTSFile.create_from_path(
            file_path=tmp_path,
            upload_id="test_upload_123"
        )
        
        yield pts_file
        
        # Cleanup
        if tmp_path.exists():
            os.unlink(tmp_path)
    
    @pytest.fixture
    def sample_job(self, sample_pts_file):
        """Create sample processing job for testing"""
        return PTSProcessingJob.create_job(
            job_id="test_job_123",
            upload_id="test_upload_123",
            pts_files=[sample_pts_file],
            operations=[PTSOperationType.RENAME, PTSOperationType.QC_GENERATION]
        )
    
    @pytest.mark.asyncio
    async def test_save_job_success(self, repository, sample_job, mock_db_connection):
        """Test successful job save"""
        _, mock_session = mock_db_connection
        
        # Mock successful database operations
        mock_session.add.return_value = None
        mock_session.commit.return_value = None
        
        # Execute
        result = await repository.save_job(sample_job)
        
        # Verify
        assert result == sample_job.job_id.value
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_save_job_database_error(self, repository, sample_job, mock_db_connection):
        """Test job save with database error"""
        _, mock_session = mock_db_connection
        
        # Mock database error
        from sqlalchemy.exc import SQLAlchemyError
        mock_session.commit.side_effect = SQLAlchemyError("Database error")
        
        # Execute and verify exception
        with pytest.raises(RepositoryError) as exc_info:
            await repository.save_job(sample_job)
        
        assert "Failed to save job" in str(exc_info.value)
        assert exc_info.value.operation == "save_job"
    
    @pytest.mark.asyncio
    async def test_get_job_found(self, repository, mock_db_connection):
        """Test successful job retrieval"""
        _, mock_session = mock_db_connection
        
        # Mock database model
        from backend.pts_renamer.repositories.pts_rename_database import PTSRenameJobModel
        mock_job_model = Mock(spec=PTSRenameJobModel)
        mock_job_model.id = "test_job_123"
        mock_job_model.upload_id = "test_upload_123"
        mock_job_model.status = "pending"
        mock_job_model.operations_list = ["rename", "qc_generation"]
        mock_job_model.qc_enabled = False
        mock_job_model.create_directories = False
        mock_job_model.files_processed = 0
        mock_job_model.total_files = 1
        mock_job_model.error_message = None
        mock_job_model.created_at = datetime.now()
        mock_job_model.updated_at = datetime.now()
        mock_job_model.started_at = None
        mock_job_model.completed_at = None
        mock_job_model.rename_config_dict = {}
        
        # Mock query result
        mock_session.query.return_value.filter.return_value.first.return_value = mock_job_model
        mock_session.query.return_value.filter.return_value.all.return_value = []  # No files or results
        
        # Execute
        result = await repository.get_job("test_job_123")
        
        # Verify
        assert result is not None
        assert result.job_id.value == "test_job_123"
        assert result.upload_id.value == "test_upload_123"
        assert result.status == "pending"
    
    @pytest.mark.asyncio
    async def test_get_job_not_found(self, repository, mock_db_connection):
        """Test job retrieval when job doesn't exist"""
        _, mock_session = mock_db_connection
        
        # Mock empty query result
        mock_session.query.return_value.filter.return_value.first.return_value = None
        
        # Execute
        result = await repository.get_job("nonexistent_job")
        
        # Verify
        assert result is None
    
    @pytest.mark.asyncio
    async def test_update_job_status_success(self, repository, mock_db_connection):
        """Test successful job status update"""
        _, mock_session = mock_db_connection
        
        # Mock existing job
        from backend.pts_renamer.repositories.pts_rename_database import PTSRenameJobModel
        mock_job_model = Mock(spec=PTSRenameJobModel)
        mock_job_model.status = "pending"
        mock_job_model.started_at = None
        mock_job_model.completed_at = None
        
        mock_session.query.return_value.filter.return_value.first.return_value = mock_job_model
        
        # Execute
        result = await repository.update_job_status("test_job_123", "processing")
        
        # Verify
        assert result is True
        assert mock_job_model.status == "processing"
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_job_progress_success(self, repository, mock_db_connection):
        """Test successful job progress update"""
        _, mock_session = mock_db_connection
        
        # Mock existing job
        from backend.pts_renamer.repositories.pts_rename_database import PTSRenameJobModel
        mock_job_model = Mock(spec=PTSRenameJobModel)
        mock_job_model.files_processed = 0
        mock_job_model.progress = 0
        
        mock_session.query.return_value.filter.return_value.first.return_value = mock_job_model
        
        # Execute
        result = await repository.update_job_progress("test_job_123", 5, 50)
        
        # Verify
        assert result is True
        assert mock_job_model.files_processed == 5
        assert mock_job_model.progress == 50
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_add_job_result_success(self, repository, mock_db_connection):
        """Test successful job result addition"""
        _, mock_session = mock_db_connection
        
        # Mock existing file
        from backend.pts_renamer.repositories.pts_rename_database import PTSRenameFileModel
        mock_file_model = Mock(spec=PTSRenameFileModel)
        mock_file_model.id = "file_123"
        mock_file_model.mark_as_processed = Mock()
        
        mock_session.query.return_value.filter.return_value.first.return_value = mock_file_model
        
        # Create test result
        result = PTSRenameResult.success_result(
            original_name="test.pts",
            new_name="renamed_test.pts",
            operation=PTSOperationType.RENAME,
            processing_time=1.5,
            file_size=1024
        )
        
        # Execute
        success = await repository.add_job_result("test_job_123", result)
        
        # Verify
        assert success is True
        mock_session.add.assert_called()
        mock_session.commit.assert_called_once()
        mock_file_model.mark_as_processed.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_pts_files_success(self, repository, mock_db_connection):
        """Test successful PTS files retrieval"""
        _, mock_session = mock_db_connection
        
        # Mock file models
        from backend.pts_renamer.repositories.pts_rename_database import PTSRenameFileModel
        mock_file_model = Mock(spec=PTSRenameFileModel)
        mock_file_model.original_path = "/tmp/test.pts"
        mock_file_model.filename = "test.pts"
        mock_file_model.size = 1024
        mock_file_model.checksum = "abc123"
        mock_file_model.extracted_from = None
        mock_file_model.created_at = datetime.now()
        
        mock_session.query.return_value.join.return_value.filter.return_value.all.return_value = [mock_file_model]
        
        # Execute
        files = await repository.get_pts_files("test_upload_123")
        
        # Verify
        assert len(files) == 1
        assert files[0].filename == "test.pts"
        assert files[0].size == 1024
    
    @pytest.mark.asyncio
    async def test_get_job_statistics_success(self, repository, mock_db_connection):
        """Test successful job statistics retrieval"""
        _, mock_session = mock_db_connection
        
        # Mock statistics function
        with patch('backend.pts_renamer.repositories.pts_rename_sql_repository.get_job_statistics') as mock_stats:
            mock_stats.return_value = {
                'total_jobs': 10,
                'completed_jobs': 8,
                'failed_jobs': 2,
                'success_rate': 80.0
            }
            
            # Execute
            stats = await repository.get_job_statistics(7)
            
            # Verify
            assert stats['total_jobs'] == 10
            assert stats['completed_jobs'] == 8
            assert stats['success_rate'] == 80.0
            mock_stats.assert_called_once_with(mock_session, 7)
    
    def test_job_entity_to_model_conversion(self, repository, sample_job):
        """Test conversion from job entity to database model"""
        model = repository._job_entity_to_model(sample_job)
        
        assert model.id == sample_job.job_id.value
        assert model.upload_id == sample_job.upload_id.value
        assert model.status == sample_job.status
        assert model.total_files == len(sample_job.pts_files)
    
    def test_result_entity_to_model_conversion(self, repository):
        """Test conversion from result entity to database model"""
        result = PTSRenameResult.success_result(
            original_name="test.pts",
            new_name="renamed_test.pts",
            operation=PTSOperationType.RENAME
        )
        
        model = repository._result_entity_to_model(result, "job_123", "file_123")
        
        assert model.job_id == "job_123"
        assert model.file_id == "file_123"
        assert model.operation == "rename"
        assert model.original_name == "test.pts"
        assert model.new_name == "renamed_test.pts"
        assert model.success is True


if __name__ == "__main__":
    pytest.main([__file__])