"""
Epic-03 Story 3.4: 處理性能監控和分析實現 (性能追蹤)
高級處理性能分析和監控系統
"""

import time
import statistics
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from threading import Lock
import json

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.shared.services.process_status_enums import ProcessStatus


@dataclass
class ProcessingStageMetrics:
    """處理階段性能指標"""
    stage_name: str
    total_count: int = 0
    success_count: int = 0
    failed_count: int = 0
    total_duration: float = 0.0
    min_duration: float = float('inf')
    max_duration: float = 0.0
    durations: deque = field(default_factory=lambda: deque(maxlen=1000))  # 保留最近1000次記錄
    
    @property
    def average_duration(self) -> float:
        """平均處理時間"""
        return self.total_duration / self.total_count if self.total_count > 0 else 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.success_count / self.total_count if self.total_count > 0 else 0.0
    
    @property
    def median_duration(self) -> float:
        """中位數處理時間"""
        return statistics.median(self.durations) if self.durations else 0.0
    
    @property
    def percentile_95_duration(self) -> float:
        """95%分位數處理時間"""
        if not self.durations:
            return 0.0
        sorted_durations = sorted(self.durations)
        index = int(0.95 * len(sorted_durations))
        return sorted_durations[min(index, len(sorted_durations) - 1)]


@dataclass
class PerformanceAlert:
    """性能警報"""
    alert_type: str
    severity: str  # low, medium, high, critical
    message: str
    timestamp: datetime
    metric_value: float
    threshold_value: float
    stage_name: Optional[str] = None
    email_id: Optional[int] = None


@dataclass
class BottleneckAnalysis:
    """瓶頸分析結果"""
    bottleneck_stage: str
    impact_level: str  # low, medium, high
    average_duration: float
    expected_duration: float
    slowdown_factor: float
    affected_emails: int
    recommendations: List[str]


class ProcessingPerformanceAnalyzer:
    """
    處理性能監控和分析器 - Epic-03 Story 3.4 核心實現
    
    功能：
    1. 實時性能監控
    2. 瓶頸分析和識別
    3. 性能趨勢分析
    4. 自動化性能警報
    5. 優化建議生成
    """
    
    def __init__(self, alert_thresholds: Optional[Dict[str, float]] = None):
        """
        初始化性能分析器
        
        Args:
            alert_thresholds: 性能警報閾值配置
        """
        self.logger = LoggerManager().get_logger("ProcessingPerformanceAnalyzer")
        
        # 性能數據存儲
        self.stage_metrics: Dict[str, ProcessingStageMetrics] = {}
        self.email_processing_records: Dict[int, Dict[str, Any]] = {}
        self.performance_alerts: deque = deque(maxlen=1000)  # 保留最近1000個警報
        self.hourly_stats: Dict[str, Dict[str, float]] = defaultdict(dict)
        
        # 線程安全鎖
        self._lock = Lock()
        
        # 性能警報閾值
        self.alert_thresholds = alert_thresholds or {
            'avg_processing_time': 300.0,  # 5分鐘
            'success_rate': 0.85,  # 85%
            'stage_duration_multiplier': 3.0,  # 3倍正常時間
            'error_rate': 0.15,  # 15%
            'queue_depth': 100  # 佇列深度
        }
        
        # 基準性能指標 (用於瓶頸檢測)
        self.baseline_metrics = {
            'email_parsing': 5.0,      # 5秒
            'attachment_processing': 15.0,  # 15秒
            'vendor_files_processing': 30.0,  # 30秒
            'database_update': 2.0,    # 2秒
            'notification_sending': 3.0  # 3秒
        }
        
        self.logger.info("處理性能分析器已初始化")
    
    def start_email_processing(self, email_id: int, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        開始追蹤郵件處理
        
        Args:
            email_id: 郵件ID
            metadata: 可選的元數據
        """
        with self._lock:
            self.email_processing_records[email_id] = {
                'start_time': time.time(),
                'stages': {},
                'metadata': metadata or {},
                'status': 'processing'
            }
        
        self.logger.debug(f"開始追蹤郵件處理: email_id={email_id}")
    
    def track_stage_start(self, email_id: int, stage_name: str) -> None:
        """
        開始追蹤處理階段
        
        Args:
            email_id: 郵件ID
            stage_name: 階段名稱
        """
        with self._lock:
            if email_id in self.email_processing_records:
                self.email_processing_records[email_id]['stages'][stage_name] = {
                    'start_time': time.time(),
                    'status': 'running'
                }
        
        self.logger.debug(f"開始追蹤處理階段: email_id={email_id}, stage={stage_name}")
    
    def track_stage_completion(self, email_id: int, stage_name: str, success: bool = True, 
                              error_message: Optional[str] = None) -> float:
        """
        完成處理階段追蹤
        
        Args:
            email_id: 郵件ID
            stage_name: 階段名稱
            success: 是否成功
            error_message: 錯誤訊息
            
        Returns:
            float: 階段處理時間
        """
        duration = 0.0
        
        with self._lock:
            if (email_id in self.email_processing_records and 
                stage_name in self.email_processing_records[email_id]['stages']):
                
                stage_info = self.email_processing_records[email_id]['stages'][stage_name]
                duration = time.time() - stage_info['start_time']
                
                # 更新階段信息
                stage_info.update({
                    'end_time': time.time(),
                    'duration': duration,
                    'success': success,
                    'error_message': error_message,
                    'status': 'completed' if success else 'failed'
                })
                
                # 更新階段指標
                self._update_stage_metrics(stage_name, duration, success)
                
                # 檢查性能警報
                self._check_performance_alerts(stage_name, duration, success, email_id)
        
        self.logger.debug(f"完成處理階段追蹤: email_id={email_id}, stage={stage_name}, "
                         f"duration={duration:.2f}s, success={success}")
        return duration
    
    def complete_email_processing(self, email_id: int, overall_success: bool = True,
                                 final_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        完成郵件處理追蹤
        
        Args:
            email_id: 郵件ID
            overall_success: 整體是否成功
            final_metadata: 最終元數據
            
        Returns:
            Dict[str, Any]: 處理性能摘要
        """
        summary = {}
        
        with self._lock:
            if email_id in self.email_processing_records:
                record = self.email_processing_records[email_id]
                total_duration = time.time() - record['start_time']
                
                # 更新記錄
                record.update({
                    'end_time': time.time(),
                    'total_duration': total_duration,
                    'overall_success': overall_success,
                    'final_metadata': final_metadata or {},
                    'status': 'completed' if overall_success else 'failed'
                })
                
                # 生成性能摘要
                summary = self._generate_processing_summary(email_id, record)
                
                # 檢查整體性能警報
                self._check_overall_performance_alerts(email_id, total_duration, overall_success)
                
                # 更新小時統計
                self._update_hourly_stats(total_duration, overall_success)
        
        self.logger.info(f"完成郵件處理追蹤: email_id={email_id}, "
                        f"total_duration={summary.get('total_duration', 0):.2f}s, "
                        f"success={overall_success}")
        return summary
    
    def _update_stage_metrics(self, stage_name: str, duration: float, success: bool) -> None:
        """更新階段性能指標"""
        if stage_name not in self.stage_metrics:
            self.stage_metrics[stage_name] = ProcessingStageMetrics(stage_name=stage_name)
        
        metrics = self.stage_metrics[stage_name]
        metrics.total_count += 1
        metrics.total_duration += duration
        metrics.durations.append(duration)
        
        if success:
            metrics.success_count += 1
        else:
            metrics.failed_count += 1
        
        # 更新最小最大值
        metrics.min_duration = min(metrics.min_duration, duration)
        metrics.max_duration = max(metrics.max_duration, duration)
    
    def _check_performance_alerts(self, stage_name: str, duration: float, 
                                 success: bool, email_id: int) -> None:
        """檢查性能警報"""
        alerts = []
        
        # 檢查階段持續時間警報
        baseline_duration = self.baseline_metrics.get(stage_name, 10.0)
        threshold_multiplier = self.alert_thresholds['stage_duration_multiplier']
        
        if duration > baseline_duration * threshold_multiplier:
            severity = 'high' if duration > baseline_duration * 5 else 'medium'
            alerts.append(PerformanceAlert(
                alert_type='slow_stage_processing',
                severity=severity,
                message=f"階段 {stage_name} 處理時間異常: {duration:.2f}s "
                       f"(基準: {baseline_duration}s)",
                timestamp=datetime.now(timezone.utc),
                metric_value=duration,
                threshold_value=baseline_duration * threshold_multiplier,
                stage_name=stage_name,
                email_id=email_id
            ))
        
        # 檢查失敗率警報
        if not success:
            stage_metrics = self.stage_metrics.get(stage_name)
            if stage_metrics and stage_metrics.total_count >= 10:  # 至少10次記錄
                error_rate = stage_metrics.failed_count / stage_metrics.total_count
                if error_rate > self.alert_thresholds['error_rate']:
                    alerts.append(PerformanceAlert(
                        alert_type='high_error_rate',
                        severity='high',
                        message=f"階段 {stage_name} 錯誤率過高: {error_rate:.2%}",
                        timestamp=datetime.now(timezone.utc),
                        metric_value=error_rate,
                        threshold_value=self.alert_thresholds['error_rate'],
                        stage_name=stage_name,
                        email_id=email_id
                    ))
        
        # 添加警報到佇列
        for alert in alerts:
            self.performance_alerts.append(alert)
            self.logger.warning(f"性能警報: {alert.message}")
    
    def _check_overall_performance_alerts(self, email_id: int, total_duration: float, 
                                        success: bool) -> None:
        """檢查整體性能警報"""
        # 檢查總處理時間警報
        if total_duration > self.alert_thresholds['avg_processing_time']:
            severity = 'critical' if total_duration > self.alert_thresholds['avg_processing_time'] * 2 else 'high'
            alert = PerformanceAlert(
                alert_type='slow_overall_processing',
                severity=severity,
                message=f"郵件整體處理時間過長: {total_duration:.2f}s "
                       f"(閾值: {self.alert_thresholds['avg_processing_time']}s)",
                timestamp=datetime.now(timezone.utc),
                metric_value=total_duration,
                threshold_value=self.alert_thresholds['avg_processing_time'],
                email_id=email_id
            )
            self.performance_alerts.append(alert)
            self.logger.warning(f"整體性能警報: {alert.message}")
    
    def _update_hourly_stats(self, total_duration: float, success: bool) -> None:
        """更新小時統計"""
        current_hour = datetime.now(timezone.utc).strftime('%Y-%m-%d-%H')
        
        if current_hour not in self.hourly_stats:
            self.hourly_stats[current_hour] = {
                'total_processed': 0,
                'successful_count': 0,
                'failed_count': 0,
                'total_duration': 0.0,
                'min_duration': float('inf'),
                'max_duration': 0.0
            }
        
        stats = self.hourly_stats[current_hour]
        stats['total_processed'] += 1
        stats['total_duration'] += total_duration
        stats['min_duration'] = min(stats['min_duration'], total_duration)
        stats['max_duration'] = max(stats['max_duration'], total_duration)
        
        if success:
            stats['successful_count'] += 1
        else:
            stats['failed_count'] += 1
    
    def _generate_processing_summary(self, email_id: int, record: Dict[str, Any]) -> Dict[str, Any]:
        """生成處理性能摘要"""
        stages_summary = {}
        total_stage_time = 0.0
        
        for stage_name, stage_info in record['stages'].items():
            if 'duration' in stage_info:
                stages_summary[stage_name] = {
                    'duration': stage_info['duration'],
                    'success': stage_info.get('success', True),
                    'error_message': stage_info.get('error_message')
                }
                total_stage_time += stage_info['duration']
        
        overhead_time = record['total_duration'] - total_stage_time
        
        return {
            'email_id': email_id,
            'total_duration': record['total_duration'],
            'stages_duration': total_stage_time,
            'overhead_duration': max(0, overhead_time),
            'overall_success': record['overall_success'],
            'stages_summary': stages_summary,
            'stage_count': len(record['stages']),
            'successful_stages': sum(1 for s in record['stages'].values() 
                                   if s.get('success', True)),
            'processing_efficiency': (total_stage_time / record['total_duration']) 
                                   if record['total_duration'] > 0 else 0.0
        }
    
    # ===========================================
    # 性能分析和報告方法
    # ===========================================
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """獲取實時性能指標"""
        with self._lock:
            # 當前處理中的郵件數量
            active_processing = sum(1 for record in self.email_processing_records.values() 
                                  if record['status'] == 'processing')
            
            # 最近一小時的統計
            current_hour = datetime.now(timezone.utc).strftime('%Y-%m-%d-%H')
            hourly_stats = self.hourly_stats.get(current_hour, {})
            
            # 階段性能摘要
            stage_summary = {}
            for stage_name, metrics in self.stage_metrics.items():
                stage_summary[stage_name] = {
                    'total_count': metrics.total_count,
                    'average_duration': metrics.average_duration,
                    'success_rate': metrics.success_rate,
                    'median_duration': metrics.median_duration,
                    'p95_duration': metrics.percentile_95_duration
                }
            
            # 最近的警報
            recent_alerts = [{
                'type': alert.alert_type,
                'severity': alert.severity,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'stage': alert.stage_name
            } for alert in list(self.performance_alerts)[-10:]]  # 最近10個警報
            
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'active_processing_count': active_processing,
                'hourly_stats': hourly_stats,
                'stage_performance': stage_summary,
                'recent_alerts': recent_alerts,
                'total_alerts_count': len(self.performance_alerts)
            }
    
    def analyze_bottlenecks(self, time_window_hours: int = 24) -> List[BottleneckAnalysis]:
        """分析性能瓶頸"""
        bottlenecks = []
        
        with self._lock:
            for stage_name, metrics in self.stage_metrics.items():
                if metrics.total_count < 5:  # 樣本數太少，跳過
                    continue
                
                baseline_duration = self.baseline_metrics.get(stage_name, metrics.average_duration)
                slowdown_factor = metrics.average_duration / baseline_duration
                
                # 識別瓶頸
                if slowdown_factor > 2.0:  # 超過2倍基準時間
                    impact_level = 'high' if slowdown_factor > 4.0 else 'medium'
                    
                    # 生成優化建議
                    recommendations = self._generate_optimization_recommendations(
                        stage_name, slowdown_factor, metrics)
                    
                    bottlenecks.append(BottleneckAnalysis(
                        bottleneck_stage=stage_name,
                        impact_level=impact_level,
                        average_duration=metrics.average_duration,
                        expected_duration=baseline_duration,
                        slowdown_factor=slowdown_factor,
                        affected_emails=metrics.total_count,
                        recommendations=recommendations
                    ))
            
            # 按影響程度排序
            bottlenecks.sort(key=lambda x: x.slowdown_factor, reverse=True)
            
        return bottlenecks
    
    def _generate_optimization_recommendations(self, stage_name: str, slowdown_factor: float,
                                             metrics: ProcessingStageMetrics) -> List[str]:
        """生成優化建議"""
        recommendations = []
        
        # 基於階段類型的建議
        if stage_name == 'email_parsing':
            recommendations.extend([
                "考慮優化郵件解析算法，減少複雜度",
                "檢查是否存在過多的正則表達式匹配",
                "考慮使用更高效的文本處理庫"
            ])
        elif stage_name == 'attachment_processing':
            recommendations.extend([
                "檢查附件處理的並行度設置",
                "考慮增加磁盤I/O性能",
                "實施附件大小限制和預檢"
            ])
        elif stage_name == 'vendor_files_processing':
            recommendations.extend([
                "優化文件複製和網路傳輸",
                "檢查網路連接穩定性",
                "考慮實施文件處理快取"
            ])
        elif stage_name == 'database_update':
            recommendations.extend([
                "檢查資料庫連接池配置",
                "優化SQL查詢和索引",
                "考慮批量更新策略"
            ])
        
        # 基於錯誤率的建議
        if metrics.success_rate < 0.9:
            recommendations.append("錯誤率較高，建議加強錯誤處理和重試機制")
        
        # 基於慢化程度的建議
        if slowdown_factor > 5.0:
            recommendations.append("嚴重性能問題，建議立即檢查系統資源和配置")
        
        return recommendations
    
    def get_performance_trends(self, hours: int = 24) -> Dict[str, Any]:
        """獲取性能趨勢分析"""
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=hours)
        
        # 收集時間範圍內的數據
        hourly_trends = []
        for i in range(hours):
            hour_time = start_time + timedelta(hours=i)
            hour_key = hour_time.strftime('%Y-%m-%d-%H')
            
            hour_stats = self.hourly_stats.get(hour_key, {
                'total_processed': 0,
                'successful_count': 0,
                'failed_count': 0,
                'total_duration': 0.0,
                'min_duration': 0.0,
                'max_duration': 0.0
            })
            
            # 計算衍生指標
            avg_duration = (hour_stats['total_duration'] / hour_stats['total_processed'] 
                          if hour_stats['total_processed'] > 0 else 0.0)
            success_rate = (hour_stats['successful_count'] / hour_stats['total_processed'] 
                          if hour_stats['total_processed'] > 0 else 0.0)
            
            hourly_trends.append({
                'hour': hour_time.isoformat(),
                'total_processed': hour_stats['total_processed'],
                'success_rate': success_rate,
                'average_duration': avg_duration,
                'min_duration': hour_stats['min_duration'],
                'max_duration': hour_stats['max_duration']
            })
        
        # 計算趨勢指標
        recent_hours = hourly_trends[-6:]  # 最近6小時
        earlier_hours = hourly_trends[-12:-6]  # 之前6小時
        
        # 計算最近時間的平均處理時間
        recent_durations = [h['average_duration'] for h in recent_hours if h['total_processed'] > 0]
        recent_avg_duration = statistics.mean(recent_durations) if recent_durations else 0
        
        # 計算較早時間的平均處理時間
        earlier_durations = [h['average_duration'] for h in earlier_hours if h['total_processed'] > 0]
        earlier_avg_duration = statistics.mean(earlier_durations) if earlier_durations else 0
        
        duration_trend = 'improving' if recent_avg_duration < earlier_avg_duration else \
                        'degrading' if recent_avg_duration > earlier_avg_duration else 'stable'
        
        return {
            'time_range': {
                'start': start_time.isoformat(),
                'end': end_time.isoformat(),
                'hours': hours
            },
            'hourly_trends': hourly_trends,
            'trend_analysis': {
                'duration_trend': duration_trend,
                'recent_avg_duration': recent_avg_duration,
                'earlier_avg_duration': earlier_avg_duration,
                'performance_change_percent': (
                    ((recent_avg_duration - earlier_avg_duration) / earlier_avg_duration * 100)
                    if earlier_avg_duration > 0 else 0
                )
            }
        }
    
    def get_performance_alerts(self, severity_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """獲取性能警報"""
        alerts = list(self.performance_alerts)
        
        if severity_filter:
            alerts = [alert for alert in alerts if alert.severity == severity_filter]
        
        return [{
            'type': alert.alert_type,
            'severity': alert.severity,
            'message': alert.message,
            'timestamp': alert.timestamp.isoformat(),
            'metric_value': alert.metric_value,
            'threshold_value': alert.threshold_value,
            'stage_name': alert.stage_name,
            'email_id': alert.email_id
        } for alert in alerts]
    
    def clear_old_data(self, days_to_keep: int = 7) -> Dict[str, int]:
        """清理舊數據"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
        cutoff_hour = cutoff_time.strftime('%Y-%m-%d-%H')
        
        with self._lock:
            # 清理舊的小時統計
            old_hours = [hour for hour in self.hourly_stats.keys() if hour < cutoff_hour]
            for hour in old_hours:
                del self.hourly_stats[hour]
            
            # 清理舊的處理記錄
            old_records = [email_id for email_id, record in self.email_processing_records.items()
                          if datetime.fromtimestamp(record['start_time']) < cutoff_time]
            for email_id in old_records:
                del self.email_processing_records[email_id]
            
            # 清理舊的警報
            cutoff_timestamp = cutoff_time
            old_alerts = [alert for alert in self.performance_alerts 
                         if alert.timestamp < cutoff_timestamp]
            for alert in old_alerts:
                self.performance_alerts.remove(alert)
        
        cleaned_counts = {
            'hourly_stats_cleaned': len(old_hours),
            'processing_records_cleaned': len(old_records),
            'alerts_cleaned': len(old_alerts)
        }
        
        self.logger.info(f"清理舊數據完成: {cleaned_counts}")
        return cleaned_counts
    
    def export_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """導出性能報告"""
        return {
            'report_metadata': {
                'generated_at': datetime.now(timezone.utc).isoformat(),
                'time_window_hours': hours,
                'analyzer_version': '1.0.0'
            },
            'real_time_metrics': self.get_real_time_metrics(),
            'bottleneck_analysis': [{
                'stage': b.bottleneck_stage,
                'impact_level': b.impact_level,
                'average_duration': b.average_duration,
                'expected_duration': b.expected_duration,
                'slowdown_factor': b.slowdown_factor,
                'affected_emails': b.affected_emails,
                'recommendations': b.recommendations
            } for b in self.analyze_bottlenecks(hours)],
            'performance_trends': self.get_performance_trends(hours),
            'performance_alerts': self.get_performance_alerts(),
            'stage_details': {
                stage_name: {
                    'total_count': metrics.total_count,
                    'success_count': metrics.success_count,
                    'failed_count': metrics.failed_count,
                    'average_duration': metrics.average_duration,
                    'min_duration': metrics.min_duration,
                    'max_duration': metrics.max_duration,
                    'median_duration': metrics.median_duration,
                    'p95_duration': metrics.percentile_95_duration,
                    'success_rate': metrics.success_rate
                } for stage_name, metrics in self.stage_metrics.items()
            }
        }