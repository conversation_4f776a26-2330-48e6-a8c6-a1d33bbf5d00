# -*- coding: utf-8 -*-
"""
FT-EQC 分組處理 FastAPI 應用程式
結構化、邏輯性、可讀性、維護性提升的模組化架構
完全採用 FastAPI 依賴注入機制
"""

# 優先設置 Unicode 環境
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))
try:
    from unicode_fix_global import setup_unicode_environment, apply_unicode_patches
    setup_unicode_environment()
    apply_unicode_patches()
except ImportError as e:
    print(f"警告：無法導入 unicode_fix_global：{e}")
    # 設置基本 Unicode 環境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
import asyncio
import subprocess
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

from fastapi import FastAPI, HTTPException, Request, UploadFile, File, Form, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from loguru import logger

# 模型導入 - 修復：使用正確的共享模型路徑
from frontend.shared.models.models import (
    # 核心模型
    FTEQCGroupingRequest, FTEQCGroupingResponse, GroupingData, StatisticsData,
    EQCFailResult, HealthCheckResponse, ErrorResponse,
    # EQC 處理模型
    EQCBin1ScanRequest, EQCBin1ScanResponse, EQCBin1ScanData, EQCBin1Info,
    EQCStandardProcessRequest, EQCStandardProcessResponse, EQCStandardProcessData,
    EQCAdvancedProcessResponse,  # 新增進階處理回應模型
    EQCRealDataAnalysisResponse, ReportReadResponse, FileExistsCheckResponse,  # 新增回應模型
    OnlineEQCProcessRequest, OnlineEQCProcessResponse, OnlineEQCProcessData,
    EQCStep5TestFlowRequest, EQCStep5TestFlowResponse,
    # FT Summary 模型
    FTSummaryProcessRequest, FTSummaryProcessResponse,
    # 檔案上傳模型
    UploadConfigResponse, UploadResult, ArchiveInfo, ExtractionResult,
    UploadAndProcessRequest, UploadAndProcessResponse
)

# 服務模組導入 - 修復：使用正確的服務路徑
from frontend.eqc.services.eqc_processing_service import EQCProcessingService
from frontend.file_management.services.file_management_service import FileManagementService
from frontend.shared.utils.cleanup_service import CleanupService
from backend.shared.infrastructure.adapters.api_utils import (
    SystemConfig, DataParser, ResponseFormatter, ErrorHandler, APIUtils
)

# 遠端路徑處理器導入
try:
    from backend.shared.utils.remote_path_processor import (
        process_input_path, create_result_archive, cleanup_paths, RemotePathProcessor
    )
    REMOTE_PATH_AVAILABLE = True
except ImportError as e:
    logger.warning(f"遠端路徑處理器無法使用: {e}")
    REMOTE_PATH_AVAILABLE = False

# 舊版本處理器保留 (用於向下相容) 
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
from backend.shared.infrastructure.adapters.excel.ft_eqc_grouping_processor import (
    FTEQCGroupingProcessor, GroupingResult, OnlineEQCFailProcessor, CSVFileDiscovery
)

# FT Summary 批量處理器匯入
try:
    import sys
    import os
    
    # 確保項目根目錄在 Python 路徑中
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    from batch_csv_to_excel_processor import BatchCsvToExcelProcessor
    FT_SUMMARY_AVAILABLE = True
    logger.info("[OK] FT Summary 處理器載入成功")
except ImportError as e:
    logger.error(f"FT Summary 處理器無法匯入: {e}")
    logger.error(f"當前工作目錄: {os.getcwd()}")
    logger.error(f"Python 路徑: {sys.path[:3]}")  # 顯示前3個路徑
    FT_SUMMARY_AVAILABLE = False
    BatchCsvToExcelProcessor = None

# ================================
# FastAPI 應用程式設定
# ================================

app = FastAPI(
    title="FT-EQC 分組處理 API",
    description="結構化、邏輯性、可讀性、維護性提升的模組化 API",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",  # 明確指定 OpenAPI JSON 端點
    # 暫時禁用自動生成的 OpenAPI schema 以避免 Pydantic 問題
    generate_unique_id_function=lambda route: f"{route.tags[0] if route.tags else 'default'}-{route.name}"
)

# CORS 中介軟體設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 自定義 OpenAPI 生成器 - 修復 Pydantic 問題
@app.get("/openapi.json", include_in_schema=False)
async def custom_openapi():
    """自定義 OpenAPI JSON 端點，避免 Pydantic 模型問題"""
    try:
        from fastapi.openapi.utils import get_openapi

        if app.openapi_schema:
            return app.openapi_schema

        # 生成簡化的 OpenAPI schema
        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )

        # 移除可能有問題的 schema 定義
        if "components" in openapi_schema and "schemas" in openapi_schema["components"]:
            schemas = openapi_schema["components"]["schemas"]
            # 移除有問題的 Tuple 相關 schema
            problematic_schemas = [k for k in schemas.keys() if "Tuple" in str(schemas[k])]
            for schema_name in problematic_schemas:
                del schemas[schema_name]

        app.openapi_schema = openapi_schema
        return openapi_schema
    except Exception as e:
        logger.error(f"生成 OpenAPI schema 失敗: {e}")
        return {
            "openapi": "3.0.2",
            "info": {
                "title": app.title,
                "version": app.version,
                "description": "API 文檔生成中遇到問題，請稍後再試"
            },
            "paths": {},
            "components": {"schemas": {}}
        }

# 全局異常處理器 - 確保總是返回 JSON
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """處理 HTTP 異常，確保返回 JSON 格式"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "status": "error",
            "message": str(exc.detail),
            "error_code": exc.status_code
        }
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """處理請求驗證錯誤"""
    return JSONResponse(
        status_code=422,
        content={
            "status": "error",
            "message": "請求參數驗證失敗",
            "details": exc.errors(),
            "error_code": 422
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """處理所有其他異常"""
    logger.error(f"未處理的異常: {str(exc)}")
    logger.exception("詳細錯誤信息:")
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "message": f"內部服務器錯誤: {str(exc)}",
            "error_code": 500
        }
    )

def cleanup_after_download(zip_file_path: str, source_folder_path: str):
    """
    下載完成後清理相關文件和資料夾

    Args:
        zip_file_path: 要刪除的 ZIP 檔案路徑
        source_folder_path: 要刪除的來源資料夾路徑
    """
    import time
    import shutil

    try:
        # 等待一段時間確保下載完成
        time.sleep(2)

        # 刪除 ZIP 檔案
        if os.path.exists(zip_file_path):
            os.remove(zip_file_path)
            logger.info(f"已刪除下載檔案: {zip_file_path}")

        # 刪除來源資料夾
        if os.path.exists(source_folder_path):
            shutil.rmtree(source_folder_path)
            logger.info(f"已刪除處理資料夾: {source_folder_path}")

    except Exception as e:
        logger.error(f"清理檔案失敗: {e}")
        logger.error(f"ZIP 檔案: {zip_file_path}")
        logger.error(f"來源資料夾: {source_folder_path}")

# 靜態檔案服務設定 - 指向前端靜態文件
project_root = Path(__file__).parent.parent.parent.parent
shared_static_dir = project_root / "frontend" / "shared" / "static"
analytics_static_dir = project_root / "frontend" / "analytics" / "static"

# 創建自定義的靜態文件處理器，確保正確的編碼
class UTF8StaticFiles(StaticFiles):
    async def get_response(self, path: str, scope):
        response = await super().get_response(path, scope)
        # 為 JavaScript 和 CSS 文件設置正確的編碼
        if path.endswith(('.js', '.css')):
            response.headers['content-type'] = f"{response.headers.get('content-type', 'text/plain')}; charset=utf-8"
        return response

# 掛載共享靜態檔案
if shared_static_dir.exists():
    app.mount("/static", UTF8StaticFiles(directory=str(shared_static_dir)), name="static")
    logger.info(f"[OK] 共享靜態檔案服務已啟用 (UTF-8): {shared_static_dir}")
else:
    logger.warning(f"[WARNING] 共享靜態檔案目錄不存在: {shared_static_dir}")

# 掛載 analytics 模組靜態檔案
if analytics_static_dir.exists():
    app.mount("/static/analytics", UTF8StaticFiles(directory=str(analytics_static_dir)), name="analytics_static")
    logger.info(f"[OK] Analytics 靜態檔案服務已啟用 (UTF-8): {analytics_static_dir}")
else:
    logger.warning(f"[WARNING] Analytics 靜態檔案目錄不存在: {analytics_static_dir}")

# ================================
# 並發任務管理模組整合
# ================================

# 導入並發任務管理器路由
from frontend.tasks.routes.concurrent_tasks_api import router as concurrent_tasks_router
from frontend.tasks.routes.concurrent_tasks_api import initialize_concurrent_task_manager, shutdown_concurrent_task_manager

# 導入 EQC 異步 API 路由
from frontend.eqc.routes.eqc_async_api import router as eqc_async_router

# 導入下載完成處理 API 路由
from frontend.tasks.routes.download_completion_api import router as download_completion_router

# 註冊並發任務管理器路由
app.include_router(concurrent_tasks_router)

# 註冊 EQC 異步 API 路由
app.include_router(eqc_async_router)

# 註冊下載完成處理 API 路由
app.include_router(download_completion_router)

# EQC 監控 API 路由的註冊被移至 startup_event 中以避免循環依賴
EQC_MONITORING_AVAILABLE = False

# 根路由 - 提供 API 基本信息
@app.get("/")
async def root():
    """根路徑端點 - 提供 API 基本信息"""
    return {
        "message": "FT-EQC 分組處理 API 服務正在運行",
        "title": "FT-EQC 分組處理 API",
        "version": "2.0.0",
        "status": "healthy",
        "endpoints": {
            "ui": "/ui",
            "docs": "/docs",
            "redoc": "/redoc",
            "health": "/health",
            "openapi": "/openapi.json"
        },
        "features": [
            "FT-EQC 檔案分組處理",
            "Online EQC 失效分析",
            "EQC BIN=1 最終處理",
            "檔案上傳與處理",
            "報告生成與下載"
        ]
    }

# favicon 路由 (與 main 一致的呈現方式：使用共享靜態資源的 /static/images/favicon.ico)
@app.get("/favicon.ico")
async def favicon():
    try:
        # 優先使用共享靜態資源目錄
        candidate_paths = [
            shared_static_dir / "images" / "favicon.ico",
            analytics_static_dir / "images" / "favicon.ico",
        ]
        for path in candidate_paths:
            if path.exists():
                return FileResponse(str(path))
        # 若檔案不存在，回傳 404
        raise HTTPException(status_code=404, detail="Favicon not found")
    except Exception as e:
        # 保險：避免再度觸發未定義例外
        logger.error(f"提供 favicon 時發生錯誤: {e}")
        raise HTTPException(status_code=500, detail="Failed to serve favicon")

# ================================
# 依賴注入提供器 (Dependency Providers)
# ================================

# 全域單例實例
_cleanup_service_instance = None
_system_config_instance = None

def get_system_config() -> SystemConfig:
    """取得系統配置 (單例模式)"""
    global _system_config_instance
    if _system_config_instance is None:
        _system_config_instance = SystemConfig()
    return _system_config_instance

def get_eqc_processing_service() -> EQCProcessingService:
    """取得 EQC 處理服務"""
    from frontend.eqc.services.eqc_processing_service import get_eqc_processing_service as get_service
    return get_service()

def get_file_management_service() -> FileManagementService:
    """取得檔案管理服務"""
    return FileManagementService()

def get_cleanup_service() -> CleanupService:
    """取得清理服務 (單例模式)"""
    global _cleanup_service_instance
    if _cleanup_service_instance is None:
        _cleanup_service_instance = CleanupService()
    return _cleanup_service_instance

def get_data_parser() -> DataParser:
    """取得資料解析器"""
    return DataParser()

def get_response_formatter() -> ResponseFormatter:
    """取得回應格式化器"""
    return ResponseFormatter()

def get_error_handler() -> ErrorHandler:
    """取得錯誤處理器"""
    return ErrorHandler()

# 向下相容處理器 (漸進式移轉)
def get_legacy_grouping_processor() -> FTEQCGroupingProcessor:
    """取得舊版本分組處理器 (向下相容)"""
    return FTEQCGroupingProcessor()

def get_legacy_online_fail_processor() -> OnlineEQCFailProcessor:
    """取得舊版本線上失敗處理器 (向下相容)"""
    return OnlineEQCFailProcessor()

def get_csv_file_discovery() -> CSVFileDiscovery:
    """取得 CSV 檔案探索器 (向下相容)"""
    return CSVFileDiscovery()

# ================================
# 全域異常處理器
# ================================

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """全域異常處理器 - 統一錯誤處理"""
    logger.error(f"API 請求發生未預期錯誤: {str(exc)}")
    logger.error(f"錯誤追蹤: {traceback.format_exc()}")
    
    return JSONResponse(
        status_code=500,
        content={
            "error": str(exc), 
            "type": "internal_error",
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP 異常處理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail, 
            "type": "http_error",
            "timestamp": datetime.now().isoformat()
        }
    )

# ================================
# 核心 API 端點
# ================================

@app.get("/health", response_model=HealthCheckResponse)
async def health_check(
    config: SystemConfig = Depends(get_system_config),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> HealthCheckResponse:
    """健康檢查端點"""
    return formatter.format_health_response(config)

@app.get("/api/health", response_model=HealthCheckResponse)
async def health_check_legacy(
    config: SystemConfig = Depends(get_system_config),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> HealthCheckResponse:
    """健康檢查端點 (向下相容)"""
    return formatter.format_health_response(config)

@app.get("/")
async def root():
    """根路由"""
    return {"message": "FT-EQC 分組處理 API 服務正常運行", "version": "2.0.0"}

@app.get("/ui")
async def get_ui():
    """提供 FT-EQC 分組 UI 介面 (修復版本)"""
    import os
    from pathlib import Path

    # 修復：使用正確的專案結構路徑
    current_file = Path(__file__)
    project_root = current_file.parent.parent.parent.parent  # 回到專案根目錄

    # 優先使用 src/presentation/web/templates/ 下的模組化版本
    ui_file = project_root / "src" / "presentation" / "web" / "templates" / "ft_eqc_grouping_ui_modular.html"

    logger.info(f"[SEARCH] 檢查模組化 UI 檔案路徑: {ui_file}")
    logger.info(f"[SEARCH] 檔案是否存在: {ui_file.exists()}")

    if not ui_file.exists():
        # 回退到 REF 目錄下的原始版本
        ui_file = project_root / "REF" / "ft_eqc_grouping_ui.html"
        logger.info(f"[SEARCH] 回退到 REF 版本路徑: {ui_file}")
        logger.info(f"[SEARCH] REF 版本是否存在: {ui_file.exists()}")

        if not ui_file.exists():
            # 最後回退：使用 frontend/eqc/templates 下的簡單版本
            ui_file = current_file.parent.parent / "templates" / "eqc_dashboard.html"
            logger.info(f"[SEARCH] 最終回退路徑: {ui_file}")

            if not ui_file.exists():
                raise HTTPException(status_code=404, detail="UI 檔案未找到")
            logger.warning("[WARNING] 使用 EQC 儀表板作為回退")
        else:
            logger.warning("[WARNING] 模組化 UI 檔案不存在，使用 REF 版本")
    else:
        logger.info("[OK] 使用模組化 UI 版本")

    logger.info(f"[PAGE_FACING_UP] 最終使用的檔案: {ui_file}")

    # 讀取文件內容並返回 HTMLResponse
    try:
        with open(ui_file, 'r', encoding='utf-8') as f:
            content = f.read()
        return HTMLResponse(content=content)
    except Exception as e:
        logger.error(f"讀取 UI 檔案失敗: {e}")
        raise HTTPException(status_code=500, detail=f"無法載入 UI 檔案: {e}")

# ================================
# EQC 處理相關端點
# ================================

@app.post("/api/scan_eqc_bin1", response_model=EQCBin1ScanResponse)
async def scan_eqc_bin1(
    request: EQCBin1ScanRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service)
) -> EQCBin1ScanResponse:
    """掃描 EQC BIN1 檔案"""
    try:
        result = await eqc_service.scan_eqc_bin1(request)
        return result
    except Exception as e:
        logger.error(f"EQC BIN1 掃描失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/process_eqc_standard", response_model=EQCStandardProcessResponse)
async def process_eqc_standard(
    request: EQCStandardProcessRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> EQCStandardProcessResponse:
    """處理 EQC 標準流程"""
    try:
        # 將 Pydantic 模型轉換為 dict
        result = await eqc_service.process_eqc_standard(request.dict())
        return formatter.format_eqc_standard_response(result)
    except Exception as e:
        logger.error(f"EQC 標準處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/process_eqc_advanced", response_model=EQCAdvancedProcessResponse)
async def process_eqc_advanced(
    request: EQCStandardProcessRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> EQCAdvancedProcessResponse:
    """處理 EQC 進階流程 (雙階段處理)"""
    try:
        # 將 Pydantic 模型轉換為 dict
        result = await eqc_service.process_eqc_advanced(request.dict())

        # 檢查處理是否成功
        if result.get('status') != 'success':
            error_message = f"EQC 進階處理失敗: {result.get('message', '未知錯誤')}"
            logger.error(error_message)
            raise HTTPException(status_code=500, detail=error_message)

        # 進階流程會回傳一個包含多個階段結果的複雜物件
        integrated_result = result.get('integrated_result', {})
        results_list = integrated_result.get('results', [])

        # 檢查是否存在第二階段的成功結果
        if len(results_list) > 1 and results_list[1].get('status') == 'success':
            stage2_data = results_list[1].get('data', {})
            # 使用第二階段的資料來格式化回應
            formatted_response = formatter.format_eqc_standard_response(stage2_data)

            # 構建符合EQCAdvancedProcessResponse模型的回應
            advanced_response = EQCAdvancedProcessResponse(
                status=formatted_response.get("status", "success"),
                message=formatted_response.get("message", "EQC 進階處理完成 (雙階段)"),
                data=formatted_response.get("data"),
                processing_time=result.get('processing_time', 0.0),
                code_regions=result.get('code_regions'),
                api_version="2.0.0",
                timestamp=ResponseFormatter.get_current_timestamp()
            )

            return advanced_response
        else:
            # 如果找不到預期的第二階段資料或處理失敗
            error_message = f"EQC 進階處理的第二階段失敗或結果格式不符預期。Result: {result}"
            logger.error(error_message)
            raise HTTPException(status_code=500, detail=error_message)

    except Exception as e:
        logger.error(f"EQC 進階處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/process_online_eqc", response_model=OnlineEQCProcessResponse)
async def process_online_eqc(
    request: OnlineEQCProcessRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service)
) -> OnlineEQCProcessResponse:
    """處理線上 EQC 流程"""
    try:
        # EQCProcessingService.process_online_eqc 已經返回正確的 OnlineEQCProcessResponse 物件
        result = await eqc_service.process_online_eqc(request)
        # [TOOL] 統一化：直接回傳 Pydantic 物件，讓 FastAPI 處理序列化
        return result
    except Exception as e:
        logger.error(f"線上 EQC 處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate_eqc_step5_flow", response_model=EQCStep5TestFlowResponse)
async def generate_eqc_step5_flow(
    request: EQCStep5TestFlowRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> EQCStep5TestFlowResponse:
    """生成 EQC Step5 測試流程"""
    try:
        result = await eqc_service.generate_test_flow(request)
        return formatter.format_step5_flow_response(result)
    except Exception as e:
        logger.error(f"EQC Step5 流程生成失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/eqc/generate_test_flow", response_model=EQCStep5TestFlowResponse)
async def generate_test_flow_legacy(
    request: EQCStep5TestFlowRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> EQCStep5TestFlowResponse:
    """生成 EQC 測試流程 (向下相容)"""
    try:
        result = await eqc_service.generate_test_flow(request)
        return formatter.format_step5_flow_response(result)
    except Exception as e:
        logger.error(f"EQC 測試流程生成失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/analyze_eqc_real_data", response_model=EQCRealDataAnalysisResponse)
async def analyze_eqc_real_data(
    request: dict,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> EQCRealDataAnalysisResponse:
    """分析 EQC 真實資料"""
    try:
        # 從請求中提取資料夾路徑，如果沒有則使用預設邏輯
        folder_path = request.get('folder_path', '')
        result = await eqc_service.analyze_real_data(request)
        
        # [TOOL] 優化：使用統一的Pydantic回應模型
        analysis_response = EQCRealDataAnalysisResponse(
            status=result.get("status", "success"),
            online_eqc_fail=result.get("online_eqc_fail", 0),
            eqc_rt_pass=result.get("eqc_rt_pass", 0),
            match_rate=result.get("match_rate", "0%"),
            total_matches=result.get("total_matches", 0),
            total_rows=result.get("total_rows", 0),
            matched_count=result.get("matched_count", 0),
            search_method=result.get("search_method", "未知"),
            search_status=result.get("search_status", "未知"),
            folder_path=result.get("folder_path", folder_path),
            summary_data=result.get("summary_data"),
            api_version="2.0.0",
            timestamp=ResponseFormatter.get_current_timestamp()
        )
        
        return analysis_response
    except Exception as e:
        logger.error(f"EQC 真實資料分析失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 檔案管理相關端點
# ================================

@app.get("/api/upload_config", response_model=UploadConfigResponse)
async def get_upload_config(
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> UploadConfigResponse:
    """取得上傳配置"""
    try:
        config = await file_service.get_upload_config()
        return formatter.format_upload_config_response(config)
    except Exception as e:
        logger.error(f"取得上傳配置失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/upload_file", response_model=UploadResult)
async def upload_file(
    file: UploadFile = File(...),
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> UploadResult:
    """上傳檔案"""
    try:
        result = await file_service.upload_archive(file)
        return formatter.format_upload_result(result)
    except Exception as e:
        logger.error(f"檔案上傳失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/upload_and_process", response_model=UploadAndProcessResponse)
async def upload_and_process(
    request: UploadAndProcessRequest,
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> UploadAndProcessResponse:
    """上傳並處理檔案"""
    try:
        result = await file_service.upload_and_process(request)
        return formatter.format_upload_process_response(result)
    except Exception as e:
        logger.error(f"上傳並處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/download_file")
async def download_file(
    file_path: str,
    file_service: FileManagementService = Depends(get_file_management_service)
) -> FileResponse:
    """通用檔案下載端點"""
    try:
        # 使用檔案管理服務處理下載
        processed_path = await file_service.process_download_path(file_path)

        # 檢查檔案是否存在
        if not Path(processed_path).exists():
            raise HTTPException(status_code=404, detail=f"檔案不存在：{Path(file_path).name}")

        # 根據檔案副檔名設定 MIME 類型
        if file_path.endswith('.xlsx'):
            media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif file_path.endswith('.csv'):
            media_type = 'text/csv'
        elif file_path.endswith('.txt'):
            media_type = 'text/plain'
        else:
            media_type = 'application/octet-stream'

        return FileResponse(
            path=processed_path,
            filename=Path(processed_path).name,
            media_type=media_type
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"檔案下載失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下載失敗: {str(e)}")

@app.get("/api/today_processed_files")
async def get_today_processed_files(
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """取得今日處理的檔案"""
    try:
        result = await file_service.get_today_processed_files()
        # get_today_processed_files 已經回傳格式化的結果，直接回傳
        return result
    except Exception as e:
        logger.error(f"取得今日檔案失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/read_report", response_model=ReportReadResponse)
async def read_report(
    request: dict,
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> ReportReadResponse:
    """讀取 EQC 處理報告內容"""
    try:
        report_path = request.get('report_path', '')
        if not report_path:
            raise HTTPException(status_code=400, detail="報告路徑不能為空")

        content = await file_service.read_report_content(report_path)
        
        # [TOOL] 優化：使用統一的Pydantic回應模型
        report_response = ReportReadResponse(
            status="success",
            content=content,
            file_name=Path(report_path).name,
            api_version="2.0.0",
            timestamp=ResponseFormatter.get_current_timestamp()
        )
        
        return report_response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"讀取報告失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"讀取報告時發生錯誤: {str(e)}")

@app.get("/api/download_report")
async def download_report(
    report_path: str,
    file_service: FileManagementService = Depends(get_file_management_service)
):
    """下載 EQC 處理報告檔案"""
    try:
        processed_path = await file_service.process_download_path(report_path)

        if not Path(processed_path).exists():
            raise HTTPException(status_code=404, detail="報告檔案不存在")

        return FileResponse(
            path=processed_path,
            filename=Path(processed_path).name,
            media_type='application/octet-stream'
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下載報告失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下載失敗: {str(e)}")

@app.post("/api/upload_archive")
async def upload_archive(
    file: UploadFile = File(...),
    file_service: FileManagementService = Depends(get_file_management_service)
):
    """檔案上傳端點（單純上傳+解壓縮）"""
    try:
        result = await file_service.upload_archive(file)
        return result
    except Exception as e:
        logger.error(f"檔案上傳失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/upload_and_process_ft_summary")
async def upload_and_process_ft_summary(
    file: UploadFile = File(...),
    processing_mode: str = Form("summary_only"),
    file_service: FileManagementService = Depends(get_file_management_service)
):
    """檔案上傳並自動執行 FT Summary 處理"""
    try:
        logger.info(f"開始上傳並處理 FT Summary: {file.filename}, 模式: {processing_mode}")

        # 1. 上傳和解壓縮檔案
        upload_result = await file_service.upload_archive(file)

        # 檢查上傳結果狀態
        if upload_result.get("status") == "error":
            error_message = upload_result.get("message", "檔案上傳失敗")
            raise HTTPException(status_code=400, detail=error_message)

        # 檢查解壓縮路徑
        extracted_path = upload_result.get("extracted_folder_path") or upload_result.get("extraction_result", {}).get("extract_dir")
        if not extracted_path:
            raise HTTPException(status_code=400, detail="檔案解壓縮失敗")

        logger.info(f"檔案解壓縮完成: {extracted_path}")

        # 2. 複製到 d:\temp\壓縮檔名\ 結構
        import shutil
        import os
        from pathlib import Path

        # 獲取壓縮檔名（不含副檔名）並添加時間戳避免衝突
        import time
        archive_name = Path(file.filename).stem
        unique_suffix = str(int(time.time() * 1000))  # 毫秒級時間戳
        unique_archive_name = f"{archive_name}_{unique_suffix}"
        temp_target_dir = Path(r"d:\temp") / unique_archive_name

        # 確保目標目錄存在
        temp_target_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"創建唯一處理目錄: {temp_target_dir}")

        # 複製解壓縮的內容到目標目錄
        extracted_path_obj = Path(extracted_path)
        if extracted_path_obj.is_file():
            # 如果是單個檔案，複製到目標目錄
            shutil.copy2(extracted_path_obj, temp_target_dir / extracted_path_obj.name)
        else:
            # 如果是目錄，複製所有內容
            for item in extracted_path_obj.rglob('*'):
                if item.is_file():
                    relative_path = item.relative_to(extracted_path_obj)
                    target_file = temp_target_dir / relative_path
                    target_file.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, target_file)

        logger.info(f"檔案複製完成: {temp_target_dir}")

        # 3. 執行 csv_to_summary.py
        import sys

        start_time = time.time()

        # 構建命令 - 使用當前 Python 解釋器
        python_exe = sys.executable
        script_path = os.path.join(os.getcwd(), "csv_to_summary.py")
        target_path = str(temp_target_dir)

        # 檢查腳本是否存在
        if not os.path.exists(script_path):
            raise HTTPException(status_code=500, detail=f"找不到處理腳本: {script_path}")

        cmd = [python_exe, script_path, target_path]
        if processing_mode == "full":
            cmd.append("--excel")

        logger.info(f"執行命令: {' '.join(cmd)}")
        logger.info(f"工作目錄: {os.getcwd()}")

        # 執行處理
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=os.getcwd(),
            timeout=300  # 5分鐘超時
        )

        processing_time = time.time() - start_time

        if result.returncode != 0:
            logger.error(f"csv_to_summary.py 執行失敗: {result.stderr}")
            logger.error(f"stdout: {result.stdout}")
            raise HTTPException(status_code=500, detail=f"處理失敗: {result.stderr}")

        logger.info(f"csv_to_summary.py 執行成功，耗時: {processing_time:.2f}秒")
        logger.info(f"處理輸出: {result.stdout}")

        # 4. 壓縮處理結果
        if REMOTE_PATH_AVAILABLE:
            try:
                from backend.shared.utils.remote_path_processor import create_result_archive
                # 使用原始檔名 + processed + 時間戳，而不是帶時間戳的目錄名
                archive_name_with_timestamp = f"{archive_name}_processed_{int(time.time())}"
                archive_path, archive_success = create_result_archive(str(temp_target_dir), archive_name_with_timestamp)

                if archive_success:
                    # 生成下載 URL
                    archive_filename = os.path.basename(archive_path)
                    download_url = f"/ft-eqc/api/download_result/{archive_filename}"
                    logger.info(f"壓縮檔創建成功: {archive_path}")
                else:
                    download_url = None
                    logger.warning("壓縮檔創建失敗")
            except Exception as e:
                logger.error(f"創建壓縮檔時發生錯誤: {e}")
                download_url = None
        else:
            download_url = None

        # 5. 檢查處理結果
        ft_summary_xlsx = os.path.join(temp_target_dir, "FT_SUMMARY.xlsx")
        ft_summary_csv = os.path.join(temp_target_dir, "FT_SUMMARY.csv")

        # 檢查是否成功產生 FT_SUMMARY 檔案
        has_xlsx = os.path.exists(ft_summary_xlsx)
        has_csv = os.path.exists(ft_summary_csv)

        if has_xlsx or has_csv:
            output_file = "FT_SUMMARY.xlsx" if has_xlsx else "FT_SUMMARY.csv"
            success_message = f"成功產生 {output_file}"
        else:
            success_message = "處理完成"

        # 構建回應
        response_data = {
            "status": "success",
            "message": success_message,
            "upload_result": upload_result,
            "processing_result": {
                "processing_time_seconds": processing_time,
                "processing_mode": processing_mode,
                "target_directory": str(temp_target_dir),
                "output_file": output_file if (has_xlsx or has_csv) else None
            },
            "ft_summary_output_file": ft_summary_xlsx if has_xlsx else ft_summary_csv,
            "download_url": download_url
        }

        return response_data

    except subprocess.TimeoutExpired:
        logger.error("csv_to_summary.py 執行超時")
        raise HTTPException(status_code=500, detail="處理超時，請檢查檔案大小和內容")
    except HTTPException:
        # 重新拋出 HTTP 異常
        raise
    except Exception as e:
        logger.error(f"上傳並處理 FT Summary 失敗: {str(e)}")
        logger.exception("詳細錯誤信息:")
        raise HTTPException(status_code=500, detail=f"處理失敗: {str(e)}")

@app.post("/api/clear_duplicate_cache")
async def clear_duplicate_cache(
    file_service: FileManagementService = Depends(get_file_management_service)
):
    """清[EXCEPT_CHAR]重複上傳快取記錄"""
    try:
        result = await file_service.clear_duplicate_cache()
        return {
            "status": "success",
            "message": "重複上傳快取已清[EXCEPT_CHAR]",
            "cleared_count": result.get("cleared_count", 0)
        }
    except Exception as e:
        logger.error(f"清[EXCEPT_CHAR]快取失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/test_7zip_path")
async def test_7zip_path(request: dict):
    """測試 7zip 路徑是否有效"""
    try:
        path = request.get("path", "").strip()
        if not path:
            return {
                "status": "error",
                "message": "路徑不能為空"
            }

        # 測試 7zip 路徑
        from backend.shared.utils.seven_zip_config import SevenZipConfig
        config = SevenZipConfig()
        is_valid, message = config.test_7zip_path(path)

        if is_valid:
            return {
                "status": "success",
                "message": message,
                "path": path
            }
        else:
            return {
                "status": "error",
                "message": message
            }

    except Exception as e:
        logger.error(f"測試 7zip 路徑失敗: {str(e)}")
        return {
            "status": "error",
            "message": f"測試失敗: {str(e)}"
        }

@app.post("/api/save_7zip_path")
async def save_7zip_path(request: dict):
    """儲存 7zip 路徑配置"""
    try:
        path = request.get("path", "").strip()
        if not path:
            return {
                "status": "error",
                "message": "路徑不能為空"
            }

        # 儲存 7zip 路徑
        from backend.shared.utils.seven_zip_config import SevenZipConfig
        config = SevenZipConfig()
        success, message = config.save_7zip_path(path)

        if success:
            return {
                "status": "success",
                "message": message,
                "path": path
            }
        else:
            return {
                "status": "error",
                "message": message
            }

    except Exception as e:
        logger.error(f"儲存 7zip 路徑失敗: {str(e)}")
        return {
            "status": "error",
            "message": f"儲存失敗: {str(e)}"
        }

@app.get("/api/get_7zip_path")
async def get_7zip_path():
    """獲取當前 7zip 路徑配置"""
    try:
        from backend.shared.utils.seven_zip_config import SevenZipConfig
        config = SevenZipConfig()
        path = config.get_7zip_path()

        return {
            "status": "success",
            "path": path,
            "message": "獲取 7zip 路徑成功"
        }

    except Exception as e:
        logger.error(f"獲取 7zip 路徑失敗: {str(e)}")
        return {
            "status": "error",
            "message": f"獲取失敗: {str(e)}",
            "path": ""
        }

@app.get("/api/archive_info")
async def get_archive_info(
    archive_path: str,
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """取得壓縮檔資訊（不解壓縮）"""
    try:
        info = await file_service.get_archive_info(archive_path)
        return formatter.format_archive_info_response(info)
    except Exception as e:
        logger.error(f"取得壓縮檔資訊失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/check_file_exists", response_model=FileExistsCheckResponse)
async def check_file_exists(
    request: dict,
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> FileExistsCheckResponse:
    """檢查檔案是否存在"""
    try:
        file_path = request.get('file_path', '')
        result = await file_service.check_file_exists(file_path)
        
        # [TOOL] 統一化：使用 Pydantic 回應模型
        response = FileExistsCheckResponse(
            exists=result.get('file_exists', False),
            file_path=file_path,
            api_version="2.0.0",
            timestamp=ResponseFormatter.get_current_timestamp(),
            report_file=None  # 根據實際需求可能有處理報告檔案路徑
        )
        
        return response
    except Exception as e:
        logger.error(f"檔案檢查失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 清理服務相關端點
# ================================


@app.get("/api/temp_files_info")
async def get_temp_files_info(
    cleanup_service: CleanupService = Depends(get_cleanup_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """取得暫存檔案資訊"""
    try:
        info = await cleanup_service.get_temp_files_info()
        return formatter.format_temp_files_info(info)
    except Exception as e:
        logger.error(f"取得暫存檔案資訊失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/cleanup_temp_files")
async def cleanup_temp_files(
    cleanup_service: CleanupService = Depends(get_cleanup_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """清理臨時檔案"""
    try:
        result = await cleanup_service.cleanup_temp_files()
        return formatter.format_cleanup_response(result)
    except Exception as e:
        logger.error(f"清理臨時檔案失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/cleanup_files_manual")
async def manual_cleanup_files(
    cleanup_service: CleanupService = Depends(get_cleanup_service)
):
    """手動觸發檔案清理"""
    try:
        result = await cleanup_service.manual_cleanup_files()
        return {
            "status": "success",
            "message": "手動清理完成",
            "data": result.get("data", {}),
            "total_cleaned": result.get("data", {}).get("total_cleaned", 0)
        }
    except HTTPException:
        # 保持原始HTTP狀態碼，不轉換為500
        raise
    except Exception as e:
        logger.error(f"手動清理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/cleanup_status")
async def get_cleanup_status(
    cleanup_service: CleanupService = Depends(get_cleanup_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """取得清理狀態"""
    try:
        status = await cleanup_service.get_cleanup_status()
        return formatter.format_status_response(status)
    except Exception as e:
        logger.error(f"取得清理狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/start_cleanup_scheduler")
async def start_cleanup_scheduler(
    cleanup_service: CleanupService = Depends(get_cleanup_service)
):
    """啟動清理調度器"""
    try:
        result = await cleanup_service.start_scheduler()
        return {"message": "清理調度器已啟動", "result": result}
    except Exception as e:
        logger.error(f"啟動清理調度器失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stop_cleanup_scheduler")
async def stop_cleanup_scheduler(
    cleanup_service: CleanupService = Depends(get_cleanup_service)
):
    """停止清理調度器"""
    try:
        result = await cleanup_service.stop_scheduler()
        return {"message": "清理調度器已停止", "result": result}
    except Exception as e:
        logger.error(f"停止清理調度器失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 向下相容端點 (漸進式移轉)
# ================================

@app.post("/api/ft_eqc_grouping", response_model=FTEQCGroupingResponse)
async def ft_eqc_grouping(
    request: FTEQCGroupingRequest,
    processor: FTEQCGroupingProcessor = Depends(get_legacy_grouping_processor),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> FTEQCGroupingResponse:
    """FT-EQC 分組處理 (向下相容)"""
    try:
        # 修復方法名：process_grouping -> process_folder
        folder_path = request.dict().get('folder_path', '')
        result = processor.process_folder(folder_path)
        return formatter.format_grouping_response(result)
    except Exception as e:
        logger.error(f"FT-EQC 分組處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/online_eqc_fail_analysis")
async def online_eqc_fail_analysis(
    processor: OnlineEQCFailProcessor = Depends(get_legacy_online_fail_processor),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """Online EQC 失敗分析 (向下相容)"""
    try:
        result = processor.analyze_failures()
        return formatter.format_analysis_response(result)
    except Exception as e:
        logger.error(f"Online EQC 失敗分析失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# FT Summary 批量處理端點
# ================================

@app.post("/api/process_ft_summary", response_model=FTSummaryProcessResponse)
async def process_ft_summary(
    request: FTSummaryProcessRequest
) -> FTSummaryProcessResponse:
    """FT Summary 批量處理：輸入資料夾路徑，自動生成 FT_SUMMARY.csv，支援遠端路徑和壓縮下載"""
    try:
        logger.info(f"開始 FT Summary 批量處理：{request.folder_path}")

        # 檢查 FT Summary 處理器是否可用
        if not FT_SUMMARY_AVAILABLE or BatchCsvToExcelProcessor is None:
            raise HTTPException(
                status_code=503,
                detail="FT Summary 處理器無法使用，請安裝相關依賴：pip install pandas openpyxl numpy xlsxwriter"
            )

        # 初始化變數
        cleanup_paths_list = []
        is_remote_processing = False

        # 處理輸入路徑（支援遠端路徑）
        if REMOTE_PATH_AVAILABLE and RemotePathProcessor().is_remote_path(request.folder_path):
            logger.info(f"檢測到遠端路徑，開始複製: {request.folder_path}")
            processed_path, process_type, success = process_input_path(request.folder_path)

            if not success:
                return FTSummaryProcessResponse(
                    status="error",
                    message="遠端路徑複製失敗",
                    error_message=f"無法複製遠端路徑: {request.folder_path}"
                )

            folder_path = processed_path
            is_remote_processing = True
            cleanup_paths_list.append(processed_path)
            logger.info(f"遠端路徑複製完成: {request.folder_path} -> {folder_path}")
        else:
            # 使用現有的路徑轉換邏輯 (與 /ui 端點相同)
            original_path, folder_path = APIUtils.process_folder_path(request.folder_path)

            # 驗證轉換後的路徑
            is_valid, error_msg = APIUtils.validate_folder_path(folder_path)
            if not is_valid:
                logger.error(f"路徑驗證失敗: {error_msg} (原路徑: {original_path})")
                return FTSummaryProcessResponse(
                    status="error",
                    message=f"路徑驗證失敗: {error_msg}",
                    error_message=f"原始路徑: {original_path}, 轉換路徑: {folder_path}"
                )
        
        # 直接執行 csv_to_summary.py 命令
        import subprocess
        import time
        
        # 轉換為相對路徑（如同您直接使用的方式）
        # 從絕對路徑轉換為相對路徑
        import os
        relative_path = os.path.relpath(folder_path, os.getcwd())
        
        # 構建命令
        cmd = [sys.executable, "csv_to_summary.py", relative_path]
        if request.processing_mode == "full":
            cmd.append("--excel")
        cmd.append("--verbose")
        
        logger.info(f"執行命令: {' '.join(cmd)}")
        logger.info(f"工作目錄: {os.getcwd()}")
        logger.info(f"相對路徑: {relative_path}")
        
        # 記錄開始時間
        start_time = time.perf_counter()
        
        # 執行命令 - 修復編碼問題
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=os.getcwd(),
            encoding='utf-8',
            errors='replace',
            env={**os.environ, 'PYTHONIOENCODING': 'utf-8', 'PYTHONUTF8': '1'}
        )
        
        # 計算處理時間
        processing_time = time.perf_counter() - start_time
        
        if result.returncode == 0:
                # 成功：構建回應
                logger.info("csv_to_summary.py 執行成功")

                # 檢查生成的檔案
                ft_summary_csv = os.path.join(folder_path, "FT_SUMMARY.csv")
                ft_summary_xlsx = os.path.join(folder_path, "FT_SUMMARY.xlsx")
                eqc_summary_csv = os.path.join(folder_path, "EQC_SUMMARY.csv")
                eqc_summary_xlsx = os.path.join(folder_path, "EQC_SUMMARY.xlsx")

                # 統計檔案數量
                import glob
                csv_files = glob.glob(os.path.join(folder_path, "*.csv"))
                ft_files = [f for f in csv_files if any(keyword in f.lower() for keyword in ["ft1", "ft2", "ft3", "final_test"])]
                eqc_files = [f for f in csv_files if any(keyword in f.lower() for keyword in ["eqc", "onlieeqc"])]

                # 處理壓縮和下載（如果是遠端處理或文件上傳）
                download_url = None
                if is_remote_processing and REMOTE_PATH_AVAILABLE:
                    try:
                        # 創建壓縮檔
                        folder_name = os.path.basename(folder_path)
                        archive_name = f"{folder_name}_processed_{int(time.time())}"
                        archive_path, archive_success = create_result_archive(folder_path, archive_name)

                        if archive_success:
                            # 生成下載 URL
                            archive_filename = os.path.basename(archive_path)
                            download_url = f"/ft-eqc/api/download_result/{archive_filename}"
                            logger.info(f"壓縮檔創建成功: {archive_path}")

                            # 添加壓縮檔到清理列表（稍後清理）
                            cleanup_paths_list.append(archive_path)
                        else:
                            logger.warning("壓縮檔創建失敗")
                    except Exception as e:
                        logger.error(f"創建壓縮檔時發生錯誤: {e}")

                # 構建回應
                response = FTSummaryProcessResponse(
                    status="success",
                    message=f"成功處理 {len(csv_files)} 個檔案" + (" (已創建下載檔案)" if download_url else ""),
                    total_files=len(csv_files),
                    processed_files=len(ft_files) + len(eqc_files),
                    skipped_files=0,
                    failed_files=0,
                    ft_summary_files=len(ft_files),
                    ft_summary_output_file=ft_summary_xlsx if os.path.exists(ft_summary_xlsx) else ft_summary_csv,
                    processing_time_seconds=processing_time,
                    eqc_summary_files=len(eqc_files),
                    eqc_summary_output_file=eqc_summary_xlsx if os.path.exists(eqc_summary_xlsx) else eqc_summary_csv,
                    eqc_all_pass_file=None,
                    ft_file_list=[os.path.basename(f) for f in ft_files],
                    eqc_file_list=[os.path.basename(f) for f in eqc_files]
                )

                # 添加下載 URL 到回應中（如果有的話）
                if download_url:
                    response.download_url = download_url

                # 延遲清理（給用戶時間下載）
                if cleanup_paths_list:
                    # 這裡可以實現延遲清理邏輯，或者在下載完成後清理
                    logger.info(f"標記清理路徑: {cleanup_paths_list}")

                return response
        else:
            # 失敗：回傳錯誤訊息
            logger.error(f"csv_to_summary.py 執行失敗，返回代碼: {result.returncode}")
            logger.error(f"錯誤輸出: {result.stderr}")
            return FTSummaryProcessResponse(
                status="error",
                message=f"處理失敗，返回代碼: {result.returncode}",
                total_files=0,
                processed_files=0,
                failed_files=0,
                processing_time_seconds=processing_time,
                error_message=result.stderr or f"返回代碼: {result.returncode}",
                eqc_summary_files=0,
                eqc_summary_output_file=None,
                eqc_all_pass_file=None,
                ft_file_list=[],
                eqc_file_list=[]
            )

    except Exception as e:
        logger.error(f"FT Summary 批量處理異常：{str(e)}")
        # 清理臨時文件
        if 'cleanup_paths_list' in locals() and cleanup_paths_list:
            try:
                cleanup_paths(cleanup_paths_list)
            except Exception as cleanup_error:
                logger.error(f"清理臨時文件失敗: {cleanup_error}")
        raise HTTPException(status_code=500, detail=f"處理失敗：{str(e)}")


@app.get("/api/download_result/{filename}")
async def download_result(filename: str, background_tasks: BackgroundTasks):
    """下載處理結果壓縮檔"""
    try:
        # 檢查檔案名稱安全性
        if '..' in filename or '/' in filename or '\\' in filename:
            raise HTTPException(status_code=400, detail="無效的檔案名稱")

        # 構建檔案路徑
        temp_base_dir = Path(r"d:\temp")
        file_path = temp_base_dir / filename

        # 檢查檔案是否存在
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="檔案不存在")

        # 檢查是否為 ZIP 檔案
        if not filename.lower().endswith('.zip'):
            raise HTTPException(status_code=400, detail="只支援 ZIP 檔案下載")

        logger.info(f"開始下載檔案: {file_path}")

        # 解析檔案名稱來找到對應的來源資料夾
        # 檔案名格式: {原始名稱}_{時間戳1}_processed_{時間戳2}.zip
        if '_processed_' in filename:
            # 移除 _processed_{時間戳}.zip 部分
            base_part = filename.split('_processed_')[0]
            # base_part 現在是 {原始名稱}_{時間戳1}，這就是我們的資料夾名
            source_folder_path = temp_base_dir / base_part
        else:
            # 備用方案：嘗試根據檔案名找到對應的資料夾
            base_name = filename.replace('.zip', '')
            source_folder_path = temp_base_dir / base_name

        # 添加後台任務來清理檔案
        background_tasks.add_task(
            cleanup_after_download,
            str(file_path),
            str(source_folder_path)
        )

        logger.info(f"已安排清理任務: ZIP={file_path}, 資料夾={source_folder_path}")

        # 返回檔案
        return FileResponse(
            path=str(file_path),
            filename=filename,
            media_type='application/zip',
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下載檔案失敗: {e}")
        raise HTTPException(status_code=500, detail=f"下載失敗: {str(e)}")

@app.get("/api/ft-summary-status")
async def ft_summary_status():
    """檢查 FT Summary 功能狀態"""
    try:
        status = {
            "ft_summary_available": FT_SUMMARY_AVAILABLE,
            "required_files": {},
            "processor_status": "unknown",
            "description": "FT Summary 批量處理：CSV 到 Excel 轉換和 Summary 生成"
        }

        # 檢查關鍵檔案
        from pathlib import Path

        # 獲取專案根目錄
        current_file = Path(__file__)
        project_root = current_file.parent.parent.parent.parent

        key_files = {
            "batch_csv_to_excel_processor.py": Path("batch_csv_to_excel_processor.py"),
            "frontend_template": project_root / "frontend" / "analytics" / "templates" / "ft_summary_ui.html"
        }

        for file_name, file_path in key_files.items():
            status["required_files"][file_name] = file_path.exists()
        
        # 如果 FT Summary 可用，測試處理器初始化
        if FT_SUMMARY_AVAILABLE and BatchCsvToExcelProcessor is not None:
            try:
                processor = BatchCsvToExcelProcessor(enable_logging=False)
                status["processor_status"] = "ready"
            except Exception as e:
                status["processor_status"] = f"error: {str(e)}"
        else:
            status["processor_status"] = "unavailable"
        
        return status

    except Exception as e:
        logger.error(f"檢查 FT Summary 狀態失敗：{str(e)}")
        return {"error": str(e)}


@app.get("/api/debug-paths")
async def debug_paths():
    """調試路徑資訊"""
    from pathlib import Path

    current_file = Path(__file__)
    project_root = current_file.parent.parent.parent.parent
    frontend_template_path = project_root / "frontend" / "analytics" / "templates" / "ft_summary_ui.html"

    return {
        "current_file": str(current_file),
        "project_root": str(project_root),
        "frontend_template_path": str(frontend_template_path),
        "frontend_template_exists": frontend_template_path.exists(),
        "working_directory": str(Path.cwd())
    }


@app.get("/ft-summary-ui")
async def ft_summary_ui():
    """提供 FT Summary 處理 Web 介面"""
    try:
        # 先檢查 FT Summary 是否可用
        if not FT_SUMMARY_AVAILABLE or BatchCsvToExcelProcessor is None:
            error_html = """
            <!DOCTYPE html>
            <html lang="zh-TW">
            <head>
                <meta charset="UTF-8">
                <title>FT Summary - 服務不可用</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .error { color: #d32f2f; }
                </style>
            </head>
            <body>
                <h1 class="error">FT Summary 服務目前不可用</h1>
                <p>請安裝相關依賴：pip install pandas openpyxl numpy xlsxwriter</p>
                <a href="/api/ft-summary-status">檢查狀態</a>
            </body>
            </html>
            """
            return HTMLResponse(content=error_html, status_code=503)

        # 嘗試使用前端的模板檔案
        from pathlib import Path

        # 獲取專案根目錄
        current_file = Path(__file__)
        project_root = current_file.parent.parent.parent.parent  # 回到專案根目錄

        # 前端模板路徑
        frontend_template_path = project_root / "frontend" / "analytics" / "templates" / "ft_summary_ui.html"

        # 後端模板路徑 (備用)
        backend_template_path = current_file.parent.parent / "web" / "templates" / "ft_summary_ui.html"

        logger.info(f"[TEMPLATE] 檢查前端模板路徑: {frontend_template_path}")
        logger.info(f"[TEMPLATE] 前端模板是否存在: {frontend_template_path.exists()}")

        template_path = None
        if frontend_template_path.exists():
            template_path = frontend_template_path
            logger.info("[OK] 使用前端模板檔案")
        elif backend_template_path.exists():
            template_path = backend_template_path
            logger.info("[FALLBACK] 使用後端模板檔案")

        if template_path:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 如果使用前端模板，需要處理 Flask 的 url_for 函數
            if template_path == frontend_template_path:
                # 將 Flask 的 url_for 替換為靜態路徑
                content = content.replace(
                    "{{ url_for('shared.static', filename='css/variables.css') }}",
                    "/static/css/variables.css"
                )
                content = content.replace(
                    "{{ url_for('shared.static', filename='css/base.css') }}",
                    "/static/css/base.css"
                )
                content = content.replace(
                    "{{ url_for('shared.static', filename='css/global.css') }}",
                    "/static/css/global.css"
                )
                content = content.replace(
                    "{{ url_for('analytics.static', filename='css/analytics.css') }}",
                    "/static/analytics/css/analytics.css"
                )
                content = content.replace(
                    "{{ url_for('shared.static', filename='js/core/utils.js') }}",
                    "/static/js/core/utils.js"
                )
                content = content.replace(
                    "{{ url_for('shared.static', filename='js/core/api-client.js') }}",
                    "/static/js/core/api-client.js"
                )
                content = content.replace(
                    "{{ url_for('shared.static', filename='js/core/dom-manager.js') }}",
                    "/static/js/core/dom-manager.js"
                )
                content = content.replace(
                    "{{ url_for('shared.static', filename='js/core/status-manager.js') }}",
                    "/static/js/core/status-manager.js"
                )
                content = content.replace(
                    "{{ url_for('shared.static', filename='js/components/file-upload.js') }}",
                    "/static/js/components/file-upload.js"
                )
                content = content.replace(
                    "{{ url_for('analytics.static', filename='js/ft-summary-processor.js') }}",
                    "/static/analytics/js/ft-summary-processor.js"
                )

            return HTMLResponse(content=content)
        else:
            # 如果模板檔案不存在，返回簡單的 HTML 介面
            simple_html = """
            <!DOCTYPE html>
            <html lang="zh-TW">
            <head>
                <meta charset="UTF-8">
                <title>FT Summary 批量處理</title>
            </head>
            <body>
                <h1>FT Summary 批量處理</h1>
                <p>模板檔案載入中...</p>
            </body>
            </html>
            """
            return HTMLResponse(content=simple_html)
    except Exception as e:
        logger.error(f"載入 FT Summary 介面失敗：{str(e)}")
        raise HTTPException(status_code=500, detail="無法載入介面")


# ================================
# 應用程式生命週期事件
# ================================

@app.on_event("startup")
async def startup_event():
    """應用程式啟動事件"""
    global EQC_MONITORING_AVAILABLE
    logger.info("[START] FT-EQC API 服務啟動中...")

    # 延遲導入並註冊 EQC 監控路由 (已移除監控增強功能)
    # EQC 監控增強功能已在清理過程中移除
    EQC_MONITORING_AVAILABLE = False
    logger.info("EQC 監控增強功能已移除，跳過相關 API 註冊")

    # 註冊統一監控儀表板路由 (已移除監控增強功能)
    # 監控增強功能已在清理過程中移除
    logger.info("監控增強功能已移除，跳過相關 API 註冊")

    # PTS Renamer 已統一到 Flask 路由 (http://localhost:5000/pts-renamer/)
    # 不再需要 FastAPI 路由註冊
    logger.info("[INFO] PTS Renamer 使用統一 Flask 路由: http://localhost:5000/pts-renamer/")

    logger.info("[OK] 模組化架構已載入")
    logger.info("[OK] FastAPI 依賴注入機制已啟用")
    
    # 初始化並發任務管理器
    initialize_concurrent_task_manager()
    
    # 非同步初始化清理服務，避免阻塞主線程
    try:
        # 啟用自動初始化清理服務
        asyncio.create_task(initialize_cleanup_service_async())
        logger.info("[OK] 檔案清理服務初始化已啟動")
    except Exception as e:
        logger.error(f"[ERROR] 檔案清理服務初始化失敗: {str(e)}")
    
    logger.info("[OK] 所有服務模組已初始化")

async def initialize_cleanup_service_async():
    """非同步初始化清理服務，避免阻塞啟動流程"""
    try:
        await asyncio.sleep(0.1)  # 讓主線程繼續
        from frontend.tasks.services.file_cleanup_scheduler import FileCleanupScheduler
        cleanup_scheduler = FileCleanupScheduler()
        cleanup_service = get_cleanup_service()
        cleanup_service.set_cleanup_scheduler(cleanup_scheduler)
        logger.info("[OK] 清理服務非同步初始化完成")
    except Exception as e:
        logger.error(f"[ERROR] 清理服務非同步初始化失敗: {str(e)}")

@app.on_event("shutdown")
async def shutdown_event():
    """應用程式關閉事件"""
    logger.info("🛑 FT-EQC API 服務正在關閉...")
    
    # 優雅關閉並發任務管理器
    shutdown_concurrent_task_manager()
    
    # 優雅關閉背景清理服務
    try:
        cleanup_service = get_cleanup_service()
        if cleanup_service.cleanup_scheduler:
            result = cleanup_service.cleanup_scheduler.stop_scheduler()
            logger.info("[OK] 背景清理調度器已優雅關閉")
        else:
            logger.info("[BOARD] 清理調度器未運行，無需關閉")
    except Exception as e:
        logger.error(f"[ERROR] 關閉背景調度器時發生錯誤: {str(e)}")
    
    logger.info("[OK] 服務關閉完成")

# ================================
# 開發環境執行設定
# ================================

if __name__ == "__main__":
    import uvicorn
    
    # 檢查是否為開發環境
    is_dev = os.getenv("ENVIRONMENT", "development") == "development"
    
    uvicorn.run(
        "ft_eqc_api:app",
        host="127.0.0.1",  # 修復：使用本地回環地址避免網路綁定問題
        port=8010,
        reload=is_dev,
        log_level="info"
    )