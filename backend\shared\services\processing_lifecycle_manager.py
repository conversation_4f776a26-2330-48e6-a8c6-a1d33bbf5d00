"""
Epic-03 Story 3.2: 處理狀態生命週期管理
管理郵件處理狀態的完整生命週期，包括狀態轉換規則、觸發條件和異常處理
"""

from enum import Enum
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass
from loguru import logger

from backend.shared.services.process_status_enums import ProcessStatus
from backend.shared.infrastructure.logging.logger_manager import LoggerManager


@dataclass
class StatusTransition:
    """狀態轉換定義"""
    from_status: ProcessStatus
    to_status: ProcessStatus
    condition: Optional[Callable[[Dict[str, Any]], bool]] = None
    timeout_seconds: Optional[int] = None
    auto_trigger: bool = False
    metadata_requirements: Optional[List[str]] = None


@dataclass
class LifecycleEvent:
    """生命週期事件"""
    event_type: str
    status_id: int
    email_id: int
    old_status: str
    new_status: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None
    duration: Optional[float] = None


class ProcessingLifecycleManager:
    """
    處理狀態生命週期管理器
    
    負責管理處理狀態的完整生命週期：
    1. 狀態轉換規則驗證
    2. 自動狀態轉換觸發
    3. 超時檢測和處理
    4. 狀態變更事件追蹤
    5. 異常狀態恢復
    """
    
    def __init__(self):
        """初始化生命週期管理器"""
        self.logger = LoggerManager().get_logger("ProcessingLifecycleManager")
        
        # 事件追蹤
        self.lifecycle_events: List[LifecycleEvent] = []
        
        # 狀態轉換規則
        self.transition_rules = self._initialize_transition_rules()
        
        # 超時設定 (秒)
        self.default_timeouts = {
            ProcessStatus.PENDING: 300,     # 5分鐘
            ProcessStatus.PARSING: 600,     # 10分鐘
            ProcessStatus.PROCESSING: 1800, # 30分鐘
            ProcessStatus.VALIDATION: 300,  # 5分鐘
        }
        
        # 自動觸發條件
        self.auto_triggers: Dict[ProcessStatus, Callable] = {}
        
        self.logger.info("處理狀態生命週期管理器已初始化")
    
    def _initialize_transition_rules(self) -> Dict[ProcessStatus, List[StatusTransition]]:
        """
        初始化狀態轉換規則
        
        狀態流程圖:
        PENDING → PARSING → PROCESSING → VALIDATION → COMPLETED
            ↓        ↓           ↓            ↓
          FAILED   FAILED     FAILED      FAILED
            ↓        ↓           ↓            ↓
         TIMEOUT  TIMEOUT    TIMEOUT     TIMEOUT
        """
        rules = {
            ProcessStatus.PENDING: [
                StatusTransition(
                    from_status=ProcessStatus.PENDING,
                    to_status=ProcessStatus.PARSING,
                    timeout_seconds=300,
                    auto_trigger=True
                ),
                StatusTransition(
                    from_status=ProcessStatus.PENDING,
                    to_status=ProcessStatus.FAILED,
                    condition=lambda meta: meta.get('error') is not None
                ),
                StatusTransition(
                    from_status=ProcessStatus.PENDING,
                    to_status=ProcessStatus.TIMEOUT,
                    condition=lambda meta: meta.get('timeout') is True
                )
            ],
            
            ProcessStatus.PARSING: [
                StatusTransition(
                    from_status=ProcessStatus.PARSING,
                    to_status=ProcessStatus.PROCESSING,
                    condition=lambda meta: meta.get('parsing_completed') is True,
                    metadata_requirements=['parsed_data']
                ),
                StatusTransition(
                    from_status=ProcessStatus.PARSING,
                    to_status=ProcessStatus.FAILED,
                    condition=lambda meta: meta.get('parsing_error') is not None
                ),
                StatusTransition(
                    from_status=ProcessStatus.PARSING,
                    to_status=ProcessStatus.TIMEOUT,
                    timeout_seconds=600
                )
            ],
            
            ProcessStatus.PROCESSING: [
                StatusTransition(
                    from_status=ProcessStatus.PROCESSING,
                    to_status=ProcessStatus.VALIDATION,
                    condition=lambda meta: meta.get('processing_completed') is True,
                    metadata_requirements=['processed_data']
                ),
                StatusTransition(
                    from_status=ProcessStatus.PROCESSING,
                    to_status=ProcessStatus.FAILED,
                    condition=lambda meta: meta.get('processing_error') is not None
                ),
                StatusTransition(
                    from_status=ProcessStatus.PROCESSING,
                    to_status=ProcessStatus.TIMEOUT,
                    timeout_seconds=1800
                )
            ],
            
            ProcessStatus.VALIDATION: [
                StatusTransition(
                    from_status=ProcessStatus.VALIDATION,
                    to_status=ProcessStatus.COMPLETED,
                    condition=lambda meta: meta.get('validation_passed') is True,
                    metadata_requirements=['final_result']
                ),
                StatusTransition(
                    from_status=ProcessStatus.VALIDATION,
                    to_status=ProcessStatus.FAILED,
                    condition=lambda meta: meta.get('validation_failed') is True
                ),
                StatusTransition(
                    from_status=ProcessStatus.VALIDATION,
                    to_status=ProcessStatus.TIMEOUT,
                    timeout_seconds=300
                )
            ],
            
            # 終止狀態沒有轉換
            ProcessStatus.COMPLETED: [],
            ProcessStatus.FAILED: [],
            ProcessStatus.TIMEOUT: []
        }
        
        return rules
    
    def validate_status_transition(self, current_status: str, new_status: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        驗證狀態轉換是否合法
        
        Args:
            current_status: 當前狀態
            new_status: 目標狀態  
            metadata: 轉換元數據
            
        Returns:
            bool: 轉換是否合法
        """
        try:
            current = ProcessStatus(current_status)
            target = ProcessStatus(new_status)
            
            # 檢查是否有定義的轉換規則
            if current not in self.transition_rules:
                self.logger.warning(f"未定義的狀態轉換規則: {current}")
                return False
            
            available_transitions = self.transition_rules[current]
            
            # 尋找匹配的轉換規則
            for transition in available_transitions:
                if transition.to_status == target:
                    # 檢查條件
                    if transition.condition and metadata:
                        if not transition.condition(metadata):
                            continue
                    
                    # 檢查必需的元數據
                    if transition.metadata_requirements and metadata:
                        missing_meta = [
                            req for req in transition.metadata_requirements 
                            if req not in metadata
                        ]
                        if missing_meta:
                            self.logger.warning(f"缺少必需的元數據: {missing_meta}")
                            continue
                    
                    return True
            
            self.logger.warning(f"非法的狀態轉換: {current} -> {target}")
            return False
            
        except ValueError as e:
            self.logger.error(f"狀態轉換驗證失敗: {e}")
            return False
    
    def handle_status_change(self, status_id: int, email_id: int, old_status: str, new_status: str, metadata: Optional[Dict[str, Any]] = None):
        """
        處理狀態變更事件
        
        Args:
            status_id: 狀態記錄ID
            email_id: 郵件ID
            old_status: 舊狀態
            new_status: 新狀態
            metadata: 變更元數據
        """
        try:
            # 記錄生命週期事件
            event = LifecycleEvent(
                event_type="status_change",
                status_id=status_id,
                email_id=email_id,
                old_status=old_status,
                new_status=new_status,
                timestamp=datetime.now(timezone.utc),
                metadata=metadata
            )
            
            self.lifecycle_events.append(event)
            
            # 計算狀態持續時間
            if old_status != new_status:
                duration = self._calculate_status_duration(status_id, old_status)
                if duration:
                    event.duration = duration
            
            # 觸發後續處理
            self._trigger_post_transition_actions(status_id, email_id, new_status, metadata)
            
            self.logger.info(f"處理狀態變更: status_id={status_id}, {old_status} -> {new_status}")
            
        except Exception as e:
            self.logger.error(f"處理狀態變更失敗: {e}")
    
    def check_timeouts(self, active_statuses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        檢查超時狀態
        
        Args:
            active_statuses: 活躍狀態列表
            
        Returns:
            List[Dict[str, Any]]: 超時的狀態列表
        """
        timeout_statuses = []
        current_time = datetime.now(timezone.utc)
        
        try:
            for status_info in active_statuses:
                status = status_info.get('status')
                started_at = status_info.get('started_at')
                status_id = status_info.get('id')
                
                if not status or not started_at or not status_id:
                    continue
                
                try:
                    current_status = ProcessStatus(status)
                    
                    # 獲取超時設定
                    timeout_seconds = self.default_timeouts.get(current_status)
                    if not timeout_seconds:
                        continue
                    
                    # 檢查是否超時
                    elapsed = (current_time - started_at).total_seconds()
                    if elapsed > timeout_seconds:
                        timeout_info = {
                            'status_id': status_id,
                            'email_id': status_info.get('email_id'),
                            'current_status': status,
                            'elapsed_seconds': elapsed,
                            'timeout_seconds': timeout_seconds,
                            'started_at': started_at
                        }
                        timeout_statuses.append(timeout_info)
                        
                        self.logger.warning(f"檢測到超時狀態: status_id={status_id}, elapsed={elapsed}s")
                
                except ValueError:
                    # 無效的狀態值，跳過
                    continue
                    
        except Exception as e:
            self.logger.error(f"檢查超時狀態失敗: {e}")
        
        return timeout_statuses
    
    def handle_timeout(self, status_id: int, email_id: int, current_status: str, elapsed_seconds: float):
        """
        處理超時狀態
        
        Args:
            status_id: 狀態記錄ID
            email_id: 郵件ID
            current_status: 當前狀態
            elapsed_seconds: 已經過的秒數
        """
        try:
            # 記錄超時事件
            event = LifecycleEvent(
                event_type="timeout",
                status_id=status_id,
                email_id=email_id,
                old_status=current_status,
                new_status=ProcessStatus.TIMEOUT.value,
                timestamp=datetime.now(timezone.utc),
                metadata={
                    'elapsed_seconds': elapsed_seconds,
                    'timeout_reason': f'Status {current_status} exceeded timeout limit'
                },
                duration=elapsed_seconds
            )
            
            self.lifecycle_events.append(event)
            
            self.logger.warning(f"處理超時: status_id={status_id}, elapsed={elapsed_seconds}s")
            
        except Exception as e:
            self.logger.error(f"處理超時失敗: {e}")
    
    def get_status_suggestions(self, current_status: str, metadata: Optional[Dict[str, Any]] = None) -> List[str]:
        """
        獲取建議的下一個狀態
        
        Args:
            current_status: 當前狀態
            metadata: 當前元數據
            
        Returns:
            List[str]: 建議的下一個狀態列表
        """
        suggestions = []
        
        try:
            current = ProcessStatus(current_status)
            
            if current in self.transition_rules:
                available_transitions = self.transition_rules[current]
                
                for transition in available_transitions:
                    # 檢查條件是否滿足
                    if transition.condition and metadata:
                        if transition.condition(metadata):
                            suggestions.append(transition.to_status.value)
                    elif not transition.condition:
                        # 無條件轉換
                        suggestions.append(transition.to_status.value)
                        
        except ValueError:
            self.logger.warning(f"無效的狀態: {current_status}")
        
        return suggestions
    
    def get_lifecycle_events(self, status_id: Optional[int] = None, email_id: Optional[int] = None, limit: int = 100) -> List[LifecycleEvent]:
        """
        獲取生命週期事件
        
        Args:
            status_id: 可選的狀態ID過濾
            email_id: 可選的郵件ID過濾
            limit: 返回的最大事件數量
            
        Returns:
            List[LifecycleEvent]: 事件列表
        """
        events = self.lifecycle_events.copy()
        
        # 過濾條件
        if status_id:
            events = [e for e in events if e.status_id == status_id]
        
        if email_id:
            events = [e for e in events if e.email_id == email_id]
        
        # 按時間戳排序並限制數量
        events.sort(key=lambda e: e.timestamp, reverse=True)
        return events[:limit]
    
    def clear_old_events(self, days: int = 7):
        """
        清理舊的生命週期事件
        
        Args:
            days: 保留天數
        """
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=days)
        
        original_count = len(self.lifecycle_events)
        self.lifecycle_events = [
            event for event in self.lifecycle_events
            if event.timestamp > cutoff_time
        ]
        
        cleared_count = original_count - len(self.lifecycle_events)
        self.logger.info(f"清理舊的生命週期事件: {cleared_count} 個事件，保留 {days} 天")
    
    def get_status_statistics(self) -> Dict[str, Any]:
        """
        獲取狀態統計信息
        
        Returns:
            Dict[str, Any]: 統計信息
        """
        try:
            if not self.lifecycle_events:
                return {
                    'total_events': 0,
                    'status_changes': 0,
                    'timeouts': 0,
                    'average_durations': {}
                }
            
            # 統計事件類型
            event_types = {}
            status_durations = {}
            
            for event in self.lifecycle_events:
                # 事件類型統計
                event_type = event.event_type
                event_types[event_type] = event_types.get(event_type, 0) + 1
                
                # 狀態持續時間統計
                if event.duration and event.old_status:
                    old_status = event.old_status
                    if old_status not in status_durations:
                        status_durations[old_status] = []
                    status_durations[old_status].append(event.duration)
            
            # 計算平均持續時間
            average_durations = {}
            for status, durations in status_durations.items():
                if durations:
                    average_durations[status] = sum(durations) / len(durations)
            
            return {
                'total_events': len(self.lifecycle_events),
                'event_types': event_types,
                'status_changes': event_types.get('status_change', 0),
                'timeouts': event_types.get('timeout', 0),
                'average_durations': average_durations,
                'unique_emails': len(set(e.email_id for e in self.lifecycle_events)),
                'unique_statuses': len(set(e.status_id for e in self.lifecycle_events))
            }
            
        except Exception as e:
            self.logger.error(f"獲取狀態統計失敗: {e}")
            return {'error': str(e)}
    
    def _calculate_status_duration(self, status_id: int, old_status: str) -> Optional[float]:
        """計算狀態持續時間"""
        try:
            # 查找同一個 status_id 的上一個狀態變更事件
            for event in reversed(self.lifecycle_events):
                if (event.status_id == status_id and 
                    event.new_status == old_status and 
                    event.event_type == "status_change"):
                    
                    duration = (datetime.now(timezone.utc) - event.timestamp).total_seconds()
                    return duration
            
            return None
            
        except Exception:
            return None
    
    def _trigger_post_transition_actions(self, status_id: int, email_id: int, new_status: str, metadata: Optional[Dict[str, Any]]):
        """觸發狀態轉換後的動作"""
        try:
            # 根據新狀態觸發相應的動作
            if new_status == ProcessStatus.COMPLETED.value:
                self._handle_completion(status_id, email_id, metadata)
            elif new_status == ProcessStatus.FAILED.value:
                self._handle_failure(status_id, email_id, metadata)
            elif new_status == ProcessStatus.TIMEOUT.value:
                self._handle_timeout_completion(status_id, email_id, metadata)
                
        except Exception as e:
            self.logger.error(f"觸發後續動作失敗: {e}")
    
    def _handle_completion(self, status_id: int, email_id: int, metadata: Optional[Dict[str, Any]]):
        """處理完成狀態"""
        self.logger.info(f"處理完成: status_id={status_id}, email_id={email_id}")
    
    def _handle_failure(self, status_id: int, email_id: int, metadata: Optional[Dict[str, Any]]):
        """處理失敗狀態"""
        self.logger.warning(f"處理失敗: status_id={status_id}, email_id={email_id}")
    
    def _handle_timeout_completion(self, status_id: int, email_id: int, metadata: Optional[Dict[str, Any]]):
        """處理超時完成狀態"""
        self.logger.warning(f"處理超時: status_id={status_id}, email_id={email_id}")