"""
PTS Renamer SQLAlchemy Repository Implementation

This module provides the concrete implementation of the PTS Renamer repository
using SQLAlchemy and the existing outlook.db database infrastructure.
"""

import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, desc, func
from pathlib import Path
import json

from .pts_rename_repository import IPTSRenameRepository, RepositoryError
from .pts_rename_sqlalchemy_models import (
    PTSRenameJobModel,
    PTSRenameFileModel, 
    PTSRenameResultModel
)
from .pts_rename_database import (
    get_job_statistics,
    cleanup_expired_jobs
)
from ..models.pts_rename_entities import (
    PTSProcessingJob,
    PTSFile,
    PTSRenameResult,
    PTSQCFile,
    PTSDirectory,
    PTSOperationType,
    PTSJobPriority,
    JobId,
    UploadId,
    FileChecksum,
    RenamePattern,
    ProcessingTimeout
)
from .pts_rename_database_connection import PTSRenameDatabaseConnection


class PTSRenameSQLRepository(IPTSRenameRepository):
    """
    SQLAlchemy implementation of PTS Renamer repository

    Uses the PTS Renamer database with PTS-specific tables
    following the established database patterns.
    """
    
    def __init__(self, db_connection: PTSRenameDatabaseConnection):
        """
        Initialize repository with database connection
        
        Args:
            db_connection: Database connection instance
        """
        self.db_connection = db_connection
        
        # Initialize config to access storage paths
        from ..models.pts_rename_models import PTSRenameConfig
        self.config = PTSRenameConfig()
        
        # Initialize logger
        from loguru import logger
        self.logger = logger
    
    async def save_job(self, job: PTSProcessingJob) -> str:
        """Save a PTS processing job"""
        try:
            with self.db_connection.get_session() as session:
                # Convert domain entity to database model
                job_model = self._job_entity_to_model(job)
                
                session.add(job_model)
                session.commit()
                
                return job.job_id.value
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to save job: {str(e)}", 
                operation="save_job",
                details={"job_id": job.job_id.value}
            )
    
    async def get_job(self, job_id: str) -> Optional[PTSProcessingJob]:
        """Retrieve a PTS processing job by ID"""
        try:
            with self.db_connection.get_session() as session:
                job_model = session.query(PTSRenameJobModel).filter(
                    PTSRenameJobModel.id == job_id
                ).first()
                
                if not job_model:
                    return None
                
                return await self._job_model_to_entity(job_model, session)
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to get job: {str(e)}", 
                operation="get_job",
                details={"job_id": job_id}
            )
    
    async def update_job_status(self, job_id: str, status: str, 
                               error_message: Optional[str] = None) -> bool:
        """Update job status"""
        try:
            with self.db_connection.get_session() as session:
                job_model = session.query(PTSRenameJobModel).filter(
                    PTSRenameJobModel.id == job_id
                ).first()
                
                if not job_model:
                    return False
                
                job_model.status = status
                job_model.updated_at = datetime.now()
                
                if error_message:
                    job_model.error_message = error_message
                
                if status == 'processing' and not job_model.started_at:
                    job_model.started_at = datetime.now()
                elif status in ['completed', 'failed', 'cancelled']:
                    job_model.completed_at = datetime.now()
                
                session.commit()
                return True
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to update job status: {str(e)}", 
                operation="update_job_status",
                details={"job_id": job_id, "status": status}
            )
    
    async def update_job_progress(self, job_id: str, files_processed: int, 
                                 progress_percentage: int) -> bool:
        """Update job progress"""
        try:
            with self.db_connection.get_session() as session:
                job_model = session.query(PTSRenameJobModel).filter(
                    PTSRenameJobModel.id == job_id
                ).first()
                
                if not job_model:
                    return False
                
                job_model.files_processed = files_processed
                job_model.progress = progress_percentage
                job_model.updated_at = datetime.now()
                
                session.commit()
                return True
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to update job progress: {str(e)}", 
                operation="update_job_progress",
                details={"job_id": job_id}
            )
    
    async def add_job_result(self, job_id: str, result: PTSRenameResult) -> bool:
        """Add a processing result to a job"""
        try:
            with self.db_connection.get_session() as session:
                # Find the corresponding file record
                file_model = session.query(PTSRenameFileModel).filter(
                    PTSRenameFileModel.job_id == job_id,
                    PTSRenameFileModel.filename == result.original_name
                ).first()
                
                if not file_model:
                    # Create a temporary file record if not found
                    file_model = PTSRenameFileModel(
                        id=f"file_{uuid.uuid4().hex[:12]}",
                        job_id=job_id,
                        original_path=result.original_name,
                        filename=result.original_name,
                        size=result.file_size_bytes or 0,
                        checksum="unknown"
                    )
                    session.add(file_model)
                    session.flush()
                
                # Create result record
                result_model = self._result_entity_to_model(result, job_id, file_model.id)
                session.add(result_model)
                
                # Update file as processed if successful
                if result.success:
                    file_model.mark_as_processed()
                
                session.commit()
                return True
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to add job result: {str(e)}", 
                operation="add_job_result",
                details={"job_id": job_id}
            )
    
    async def get_pts_files(self, upload_id: str) -> List[PTSFile]:
        """Get PTS files for an upload"""
        try:
            with self.db_connection.get_session() as session:
                file_models = session.query(PTSRenameFileModel).join(
                    PTSRenameJobModel
                ).filter(
                    PTSRenameJobModel.upload_id == upload_id
                ).all()
                
                if file_models:
                    return [self._file_model_to_entity(model) for model in file_models]
                
                # If no files found in database, fall back to filesystem discovery
                from pathlib import Path
                import hashlib
                
                # Get upload directory from configuration system (uses .env variables)
                temp_storage_path = self.config.temp_storage_path
                upload_dir = Path(temp_storage_path) / upload_id
                
                self.logger.info(f"[PTS_FILES] Searching for PTS files in upload directory: {upload_dir}")
                self.logger.info(f"[PTS_FILES] Using temp_storage_path from config: {temp_storage_path}")
                self.logger.info(f"[PTS_FILES] Upload ID: {upload_id}")
                
                pts_files = []
                
                # Check if upload directory exists
                if upload_dir.exists():
                    self.logger.info(f"[PTS_FILES] Upload directory exists: {upload_dir}")
                    
                    # List directory contents for debugging
                    try:
                        dir_contents = list(upload_dir.iterdir())
                        self.logger.info(f"[PTS_FILES] Directory contents ({len(dir_contents)} items):")
                        for item in dir_contents:
                            item_type = "DIR" if item.is_dir() else "FILE"
                            self.logger.info(f"[PTS_FILES]   {item_type}: {item.name}")
                    except Exception as e:
                        self.logger.warning(f"[PTS_FILES] Failed to list directory contents: {e}")
                    
                    # Find all PTS and CPTS files recursively
                    pts_file_paths = list(upload_dir.rglob('*.pts')) + list(upload_dir.rglob('*.cpts'))
                    self.logger.info(f"[PTS_FILES] Found {len(pts_file_paths)} PTS/CPTS files")
                    
                    for file_path in pts_file_paths:
                        try:
                            self.logger.info(f"[PTS_FILES] Processing file: {file_path}")
                            
                            # Use factory method to create PTSFile with proper types and upload_id
                            pts_file = PTSFile.create_from_path(
                                file_path=file_path,
                                upload_id=upload_id,
                                extracted_from=None  # Could be set to original archive name if available
                            )
                            pts_files.append(pts_file)
                            self.logger.info(f"[PTS_FILES] Successfully processed: {file_path.name} ({pts_file.size} bytes)")
                            
                        except Exception as e:
                            self.logger.warning(f"[PTS_FILES] Failed to process file {file_path}: {e}")
                else:
                    self.logger.warning(f"[PTS_FILES] Upload directory does not exist: {upload_dir}")
                    
                    # Try to find possible alternative paths for debugging
                    temp_dir = Path(temp_storage_path)
                    if temp_dir.exists():
                        self.logger.info(f"[PTS_FILES] Base temp directory exists: {temp_dir}")
                        try:
                            base_contents = list(temp_dir.iterdir())
                            self.logger.info(f"[PTS_FILES] Base temp directory contents ({len(base_contents)} items):")
                            for item in base_contents[:10]:  # Show first 10 items
                                item_type = "DIR" if item.is_dir() else "FILE"
                                self.logger.info(f"[PTS_FILES]   {item_type}: {item.name}")
                            if len(base_contents) > 10:
                                self.logger.info(f"[PTS_FILES]   ... and {len(base_contents) - 10} more items")
                        except Exception as e:
                            self.logger.warning(f"[PTS_FILES] Failed to list base temp directory: {e}")
                    else:
                        self.logger.error(f"[PTS_FILES] Base temp directory does not exist: {temp_dir}")
                
                self.logger.info(f"[PTS_FILES] Final result: {len(pts_files)} PTS files found for upload {upload_id}")
                
                return pts_files
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to get PTS files: {str(e)}", 
                operation="get_pts_files",
                details={"upload_id": upload_id}
            )
    
    async def save_pts_file(self, pts_file: PTSFile) -> bool:
        """Save a PTS file record"""
        try:
            with self.db_connection.get_session() as session:
                file_model = self._file_entity_to_model(pts_file)
                session.add(file_model)
                session.commit()
                return True
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to save PTS file: {str(e)}", 
                operation="save_pts_file",
                details={"filename": pts_file.filename}
            )
    
    async def get_job_results(self, job_id: str) -> List[PTSRenameResult]:
        """Get all results for a job"""
        try:
            with self.db_connection.get_session() as session:
                result_models = session.query(PTSRenameResultModel).filter(
                    PTSRenameResultModel.job_id == job_id
                ).order_by(PTSRenameResultModel.created_at).all()
                
                return [self._result_model_to_entity(model) for model in result_models]
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to get job results: {str(e)}", 
                operation="get_job_results",
                details={"job_id": job_id}
            )
    
    async def get_jobs_by_status(self, status: str, limit: Optional[int] = None) -> List[PTSProcessingJob]:
        """Get jobs by status"""
        try:
            with self.db_connection.get_session() as session:
                query = session.query(PTSRenameJobModel).filter(
                    PTSRenameJobModel.status == status
                ).order_by(desc(PTSRenameJobModel.created_at))
                
                if limit:
                    query = query.limit(limit)
                
                job_models = query.all()
                
                jobs = []
                for model in job_models:
                    job = await self._job_model_to_entity(model, session)
                    jobs.append(job)
                
                return jobs
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to get jobs by status: {str(e)}", 
                operation="get_jobs_by_status",
                details={"status": status}
            )
    
    async def get_jobs_by_upload_id(self, upload_id: str) -> List[PTSProcessingJob]:
        """Get jobs for a specific upload"""
        try:
            with self.db_connection.get_session() as session:
                job_models = session.query(PTSRenameJobModel).filter(
                    PTSRenameJobModel.upload_id == upload_id
                ).order_by(desc(PTSRenameJobModel.created_at)).all()
                
                jobs = []
                for model in job_models:
                    job = await self._job_model_to_entity(model, session)
                    jobs.append(job)
                
                return jobs
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to get jobs by upload ID: {str(e)}", 
                operation="get_jobs_by_upload_id",
                details={"upload_id": upload_id}
            )
    
    async def delete_expired_jobs(self, expiry_hours: int = 24) -> int:
        """Delete expired jobs and their associated data"""
        try:
            with self.db_connection.get_session() as session:
                deleted_count = cleanup_expired_jobs(session, expiry_hours)
                return deleted_count
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to delete expired jobs: {str(e)}", 
                operation="delete_expired_jobs",
                details={"expiry_hours": expiry_hours}
            )
    
    async def get_job_statistics(self, days: int = 7) -> Dict[str, Any]:
        """Get job processing statistics"""
        try:
            with self.db_connection.get_session() as session:
                stats = get_job_statistics(session, days)
                return stats
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to get job statistics: {str(e)}", 
                operation="get_job_statistics",
                details={"days": days}
            )
    
    async def save_qc_file(self, qc_file: PTSQCFile) -> bool:
        """Save QC file record"""
        # QC files are tracked through job results for now
        # Could be extended with dedicated QC file table if needed
        return True
    
    async def save_directory(self, directory: PTSDirectory) -> bool:
        """Save directory record"""
        # Directories are tracked through job results for now
        # Could be extended with dedicated directory table if needed
        return True
    
    async def get_qc_files_for_job(self, job_id: str) -> List[PTSQCFile]:
        """Get QC files for a job"""
        # Return empty list for now - QC files tracked through results
        return []
    
    async def get_directories_for_job(self, job_id: str) -> List[PTSDirectory]:
        """Get directories for a job"""
        # Return empty list for now - directories tracked through results
        return []
    
    async def save_upload_record(self, upload_record: Dict[str, Any]) -> bool:
        """Save upload record using raw SQL to avoid datetime serialization issues"""
        try:
            from loguru import logger
            logger.info(f"[CRITICAL_FIX] Using raw SQL for upload record to bypass datetime issues")
            logger.info(f"[CRITICAL_FIX] Upload record data: {upload_record}")
            
            with self.db_connection.get_session() as session:
                # Use raw SQL to insert the record with proper datetime handling
                insert_sql = """
                    INSERT INTO pts_rename_jobs (
                        id, upload_id, status, operations, rename_config, 
                        qc_enabled, create_directories, progress, files_processed, 
                        total_files, error_message, result_download_url, 
                        compressed_file_size, compressed_file_name, download_expires_at,
                        created_at, updated_at, started_at, completed_at
                    ) VALUES (
                        :id, :upload_id, :status, :operations, :rename_config,
                        :qc_enabled, :create_directories, :progress, :files_processed,
                        :total_files, :error_message, :result_download_url,
                        :compressed_file_size, :compressed_file_name, :download_expires_at,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, NULL, NULL
                    )
                """
                
                # Prepare parameters with explicit values
                params = {
                    'id': upload_record['upload_id'],
                    'upload_id': upload_record['upload_id'], 
                    'status': 'upload_completed',
                    'operations': json.dumps(['upload']),
                    'rename_config': None,
                    'qc_enabled': False,
                    'create_directories': False,
                    'progress': 100,
                    'files_processed': upload_record.get('files_uploaded', 0),
                    'total_files': upload_record.get('files_uploaded', 0),
                    'error_message': None,
                    'result_download_url': None,
                    'compressed_file_size': None,
                    'compressed_file_name': None,
                    'download_expires_at': None
                }
                
                logger.info(f"[CRITICAL_FIX] Executing raw SQL with params: {params}")
                
                # Execute the raw SQL
                session.execute(insert_sql, params)
                session.commit()
                
                logger.info(f"[CRITICAL_FIX] Upload record saved successfully via raw SQL")
                return True
                
        except Exception as e:
            logger.error(f"[CRITICAL_FIX] Raw SQL upload record save failed: {e}")
            raise RepositoryError(
                f"Failed to save upload record: {str(e)}",
                operation="save_upload_record"
            )
    
    async def get_upload_record(self, upload_id: str) -> Optional[Dict[str, Any]]:
        """Get upload record by ID"""
        try:
            with self.db_connection.get_session() as session:
                upload_job = session.query(PTSRenameJobModel).filter(
                    PTSRenameJobModel.upload_id == upload_id,
                    PTSRenameJobModel.status == 'upload_completed'
                ).first()
                
                if upload_job:
                    return {
                        'upload_id': upload_job.upload_id,
                        'files_uploaded': upload_job.files_processed,
                        'created_at': upload_job.created_at,
                        'status': 'completed'
                    }
                return None
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to get upload record: {str(e)}",
                operation="get_upload_record"
            )
    
    async def update_job_compression_info(self, job_id: str, compression_info: Dict[str, Any]) -> bool:
        """Update job compression information"""
        try:
            with self.db_connection.get_session() as session:
                job = session.query(PTSRenameJobModel).filter(
                    PTSRenameJobModel.id == job_id
                ).first()
                
                if job:
                    # Store compression info in result_download_url field for now
                    # In a full implementation, you might want dedicated fields
                    job.result_download_url = compression_info.get('compressed_file_path')
                    job.updated_at = datetime.now()
                    
                    session.commit()
                    return True
                return False
                
        except SQLAlchemyError as e:
            raise RepositoryError(
                f"Failed to update job compression info: {str(e)}",
                operation="update_job_compression_info"
            )
    
    # Private helper methods for entity/model conversion
    
    def _job_entity_to_model(self, job: PTSProcessingJob) -> PTSRenameJobModel:
        """Convert job entity to database model"""
        # Use **kwargs approach to ensure PTSRenameJobModel.__init__ datetime conversion is invoked
        model_data = {
            'id': job.job_id.value,
            'upload_id': job.upload_id.value,
            'status': job.status,
            'operations': json.dumps([op.value for op in job.operations]),
            'qc_enabled': job.qc_enabled,
            'create_directories': job.create_directories,
            'progress': job.progress_percentage,
            'files_processed': job.files_processed,
            'total_files': job.total_files,
            'error_message': job.error_message,
            'created_at': job.created_at,
            'updated_at': job.updated_at,
            'started_at': job.started_at,
            'completed_at': job.completed_at
        }
        
        # Create model using **kwargs to trigger datetime string conversion in __init__
        model = PTSRenameJobModel(**model_data)
        
        # Set rename configuration if present
        if job.rename_pattern:
            model.rename_config_dict = {
                'old_pattern': job.rename_pattern.old_pattern,
                'new_pattern': job.rename_pattern.new_pattern
            }
        
        return model
    
    async def _job_model_to_entity(self, model: PTSRenameJobModel, session: Session) -> PTSProcessingJob:
        """Convert database model to job entity"""
        # Get associated files
        file_models = session.query(PTSRenameFileModel).filter(
            PTSRenameFileModel.job_id == model.id
        ).all()
        
        pts_files = [self._file_model_to_entity(file_model) for file_model in file_models]
        
        # Get results
        result_models = session.query(PTSRenameResultModel).filter(
            PTSRenameResultModel.job_id == model.id
        ).all()
        
        results = [self._result_model_to_entity(result_model) for result_model in result_models]
        
        # Convert operations
        operations = [PTSOperationType(op) for op in model.operations_list]
        
        # Create rename pattern if present
        rename_pattern = None
        if model.rename_config_dict:
            config = model.rename_config_dict
            if 'old_pattern' in config and 'new_pattern' in config:
                rename_pattern = RenamePattern(
                    old_pattern=config['old_pattern'],
                    new_pattern=config['new_pattern']
                )
        
        # Create job entity
        job = PTSProcessingJob(
            job_id=JobId(model.id),
            upload_id=UploadId(model.upload_id),
            pts_files=pts_files,
            operations=operations,
            results=results,
            status=model.status,
            created_at=model.created_at,
            updated_at=model.updated_at,
            started_at=model.started_at,
            completed_at=model.completed_at,
            error_message=model.error_message,
            rename_pattern=rename_pattern,
            qc_enabled=model.qc_enabled,
            create_directories=model.create_directories,
            files_processed=model.files_processed,
            total_files=model.total_files
        )
        
        return job
    
    def _file_entity_to_model(self, pts_file: PTSFile) -> PTSRenameFileModel:
        """Convert file entity to database model"""
        # Use **kwargs approach to ensure PTSRenameFileModel.__init__ datetime conversion is invoked
        model_data = {
            'id': f"file_{uuid.uuid4().hex[:12]}",
            'job_id': "",  # Will be set when associated with job
            'original_path': str(pts_file.original_path),
            'filename': pts_file.filename,
            'size': pts_file.size,
            'checksum': pts_file.checksum.value,
            'extracted_from': pts_file.extracted_from,
            'created_at': pts_file.created_at
        }
        
        return PTSRenameFileModel(**model_data)
    
    def _file_model_to_entity(self, model: PTSRenameFileModel) -> PTSFile:
        """Convert database model to file entity"""
        return PTSFile(
            original_path=Path(model.original_path),
            filename=model.filename,
            size=model.size,
            checksum=FileChecksum(model.checksum),
            upload_id=UploadId("unknown"),  # Will be resolved from job
            extracted_from=model.extracted_from,
            created_at=model.created_at
        )
    
    def _result_entity_to_model(self, result: PTSRenameResult, job_id: str, file_id: str) -> PTSRenameResultModel:
        """Convert result entity to database model"""
        # Use **kwargs approach to ensure PTSRenameResultModel.__init__ datetime conversion is invoked
        model_data = {
            'id': f"result_{uuid.uuid4().hex[:12]}",
            'job_id': job_id,
            'file_id': file_id,
            'operation': result.operation.value,
            'original_name': result.original_name,
            'new_name': result.new_name,
            'success': result.success,
            'error_message': result.error_message,
            'processing_time_seconds': int(result.processing_time_seconds) if result.processing_time_seconds else None,
            'file_size_bytes': result.file_size_bytes,
            'created_at': result.created_at
        }
        
        return PTSRenameResultModel(**model_data)
    
    def _result_model_to_entity(self, model: PTSRenameResultModel) -> PTSRenameResult:
        """Convert database model to result entity"""
        return PTSRenameResult(
            original_name=model.original_name,
            new_name=model.new_name,
            operation=PTSOperationType(model.operation),
            success=model.success,
            error_message=model.error_message,
            processing_time_seconds=float(model.processing_time_seconds) if model.processing_time_seconds else None,
            file_size_bytes=model.file_size_bytes,
            created_at=model.created_at
        )


# Factory function for creating repository instance
def create_pts_rename_repository(database_path: str = "outlook.db") -> PTSRenameSQLRepository:
    """
    Factory function to create PTS Rename repository instance

    Args:
        database_path: Path to the PTS Renamer database
        
    Returns:
        Configured repository instance
    """
    db_connection = PTSRenameDatabaseConnection(database_path)
    return PTSRenameSQLRepository(db_connection)