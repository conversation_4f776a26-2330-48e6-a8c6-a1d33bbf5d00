"""
郵件下載追蹤 Pydantic 模型
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class DownloadStatus(str, Enum):
    PENDING = "pending"
    DOWNLOADING = "downloading" 
    COMPLETED = "completed"
    FAILED = "failed"
    RETRY_SCHEDULED = "retry_scheduled"

class RetryStrategy(str, Enum):
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    FIXED = "fixed"

class DownloadStatusResponse(BaseModel):
    id: int
    email_id: int
    status: DownloadStatus
    download_attempt: int
    max_retry_count: int
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    last_retry_at: Optional[datetime]
    next_retry_at: Optional[datetime]
    error_type: Optional[str]
    error_message: Optional[str]
    download_size_bytes: Optional[int]
    download_duration_seconds: Optional[float]
    retry_strategy: RetryStrategy

    class Config:
        from_attributes = True

class DownloadStatisticsResponse(BaseModel):
    total_downloads: int
    successful_downloads: int
    failed_downloads: int
    pending_retries: int
    success_rate: float
    error_types: List[Dict[str, Any]]
    period_hours: int
    last_updated: str

class ManualRetryRequest(BaseModel):
    email_ids: List[int]
    reason: Optional[str] = "manual_retry"

class RetryResult(BaseModel):
    email_id: int
    tracking_id: int
    status: str

class ManualRetryResponse(BaseModel):
    success: bool
    message: str
    retry_results: List[RetryResult]

class DownloadRetryLogResponse(BaseModel):
    id: int
    download_status_id: int
    retry_attempt: int
    retry_reason: Optional[str]
    attempted_at: datetime
    result: Optional[str]
    error_message: Optional[str]
    duration_seconds: Optional[float]
    retry_strategy_used: Optional[str]
    retry_interval_used: Optional[int]

    class Config:
        from_attributes = True