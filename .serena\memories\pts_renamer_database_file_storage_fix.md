# PTS Renamer 資料庫檔案儲存問題修復

## 問題描述
PTS Renamer 系統出現 "No PTS files found for upload" 錯誤，導致預覽和處理功能無法正常運作。

## 根本原因分析
系統存在兩種不同的檔案儲存範式：
- **上傳服務**: 只將檔案儲存在檔案系統 (`tmp/pts_renamer/upload_id/`)
- **資料庫倉儲**: 期望從資料庫的 `PTSRenameFileModel` 表中找到檔案記錄
- **連接斷層**: 上傳服務未將發現的 PTS 檔案儲存到資料庫中

## 修復方案
在 `backend/pts_renamer/repositories/pts_rename_sql_repository.py` 的 `get_pts_files` 方法中實作智能回退機制：

```python
async def get_pts_files(self, upload_id: str) -> List[PTSFile]:
    # 1. 先嘗試從資料庫查找
    file_models = session.query(PTSRenameFileModel).join(PTSRenameJobModel)...
    
    if file_models:
        return [self._file_model_to_entity(model) for model in file_models]
    
    # 2. 資料庫中找不到時，從檔案系統直接搜尋
    upload_dir = Path("tmp/pts_renamer") / upload_id
    for file_path in upload_dir.rglob('*.pts'):
        # 建立 PTSFile 實體並計算 checksum
        pts_file = PTSFile(...)
        pts_files.append(pts_file)
    
    return pts_files
```

## 修復的技術問題
1. **循環依賴**: 避免在倉儲中實例化上傳服務
2. **檔案發現**: 使用 `rglob('*.pts')` 遞迴搜尋 PTS 檔案
3. **Checksum 計算**: 直接計算檔案的 SHA-256 checksum
4. **錯誤處理**: 單個檔案處理失敗不影響其他檔案

## 修復效果
- ✅ 預覽功能恢復正常
- ✅ 檔案發現機制可靠運作
- ✅ 支援資料庫和檔案系統雙重來源
- ✅ 向下兼容現有的資料庫儲存邏輯

## 相關檔案
- `backend/pts_renamer/repositories/pts_rename_sql_repository.py:201-255`
- `backend/pts_renamer/services/pts_rename_upload_service.py`
- `backend/pts_renamer/models/pts_rename_entities.py`

## 測試驗證
使用 `GMT_G2514XX_CTAF4_F1_XX.7z` 檔案測試：
- 上傳成功，解壓縮出 42 個檔案，包含 10 個 PTS 檔案
- 預覽功能正常顯示檔案列表
- 重新命名規則 `CTAF4_F1_02ENG01` → `CTAF4_F1_02` 正確設定