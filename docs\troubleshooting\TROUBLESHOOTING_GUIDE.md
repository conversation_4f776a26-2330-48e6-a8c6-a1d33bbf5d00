# PTS Renamer Troubleshooting Guide

## Overview

This comprehensive troubleshooting guide covers common issues, error resolution strategies, and maintenance procedures for the PTS Renamer system. All issues listed below have been resolved in the current version.

## Recently Resolved Issues (2025-08-22)

### ✅ RESOLVED: "No PTS files found for upload" Error

**Problem**: Preview functionality failing with empty file lists despite successful uploads.

**Symptoms:**
- File upload succeeds but preview shows no files
- Processing fails with "No PTS files found" error
- Archive extraction appears successful but files not discoverable

**Root Cause**: Disconnect between filesystem storage (upload service) and database lookup (repository).

**Solution Applied**: Implemented intelligent fallback mechanism in `pts_rename_sql_repository.py`.

**Technical Details:**
```python
# Fixed in: backend/pts_renamer/repositories/pts_rename_sql_repository.py:201-255
async def get_pts_files(self, upload_id: str) -> List[PTSFile]:
    # 1. Try database first (optimal path)
    file_models = session.query(PTSRenameFileModel)...
    if file_models:
        return [self._file_model_to_entity(model) for model in file_models]
    
    # 2. Fallback to filesystem scan (reliable path)
    upload_dir = Path("tmp/pts_renamer") / upload_id
    for file_path in upload_dir.rglob('*.pts'):
        pts_file = PTSFile(...)  # Create entity with checksum
        pts_files.append(pts_file)
    return pts_files
```

**Verification**: Tested with GMT_G2514XX_CTAF4_F1_XX.7z (42 files, 10 PTS files discovered).

---

### ✅ RESOLVED: Async/Await Integration Errors

**Problem**: TypeError and RuntimeError exceptions during file processing.

**Symptoms:**
- `TypeError: object NoneType can't be used in 'await' expression`
- `RuntimeError: Task scheduling conflicts in extraction pipeline`
- Upload process failing unpredictably

**Root Cause**: Mixing synchronous file operations with async/await syntax.

**Solution Applied**: Fixed synchronous operation handling in upload service.

**Technical Details:**
```python
# Fixed in: backend/pts_renamer/services/pts_rename_upload_service.py

# Line 247 - Removed incorrect await
# BEFORE: file_content = await file_data.read()
# AFTER:  file_content = file_data.read()

# Lines 333-340 - Direct extraction instead of async tasks
# BEFORE: extraction_result = await some_dramatiq_task(archive_path)
# AFTER:  extractor = ArchiveExtractor()
#         extraction_result = extractor.extract_archive(str(archive_path))
```

**Verification**: File upload and extraction working without async-related errors.

---

### ✅ RESOLVED: Binary Archive Security False Positives

**Problem**: Legitimate compressed files (7z, zip, rar) rejected as malicious.

**Symptoms:**
- "Suspicious content pattern detected" warnings for legitimate archives
- ZIP/7Z/RAR files failing upload validation
- Binary data triggering text-based malicious pattern detection

**Root Cause**: Content pattern scanning applied to binary compressed data.

**Solution Applied**: Context-aware security validation with archive format detection.

**Technical Details:**
```python
# Fixed in: backend/pts_renamer/services/pts_rename_upload_service.py:445-469

def validate_file_security(content: bytes, filename: str) -> List[str]:
    # NEW: Check if file is binary archive
    skip_content_scanning = is_archive_file(filename)
    
    if not skip_content_scanning:
        # Apply pattern scanning only to non-archive files
        for pattern in malicious_patterns:
            if pattern in content_lower:
                threats_detected.append(f"Suspicious content: {pattern}")
    else:
        logger.debug(f"[SECURITY] Skipping content scan for binary archive: {filename}")
```

**Verification**: 7Z archives upload successfully without false positive warnings.

---

### ✅ RESOLVED: Interface Over-Complexity

**Problem**: Flask interface too complex, not matching simplified design requirements.

**Symptoms:**
- Complex option cards and navigation elements
- Interface not matching HTML prototype design
- Too many configuration options confusing users

**Root Cause**: Template inheritance from complex base template with unnecessary features.

**Solution Applied**: Converted to standalone template with core functionality only.

**Technical Details:**
```html
<!-- Fixed in: frontend/pts_renamer/templates/pts_rename_main.html -->

<!-- BEFORE: Complex inheritance -->
{% extends "base.html" %}

<!-- AFTER: Standalone simplified template -->
<!DOCTYPE html>
<html lang="zh-TW">
<!-- Simplified structure with only essential features -->
```

**Features Removed:**
- Option card system
- FontAwesome icons
- CSS variable complexity
- QC suffix customization
- Directory grouping options
- Help text system
- System navigation elements

**Features Retained:**
- File upload area (drag-and-drop)
- Core processing options (rename, QC, directories)
- Rename pattern configuration
- Preview functionality
- Execute button

**Verification**: Interface matches simplified design requirements.

---

### ✅ RESOLVED: Excel Percentage Display Bug

**Problem**: Percentage values displayed as 6000% instead of 60% in Excel reports.

**Symptoms:**
- BIN 1 showing 6000% instead of 60%
- BIN 394 showing 4000% instead of 40%
- 100x multiplication error in percentage calculations

**Root Cause**: Double percentage conversion in Excel generation logic.

**Solution Applied**: Fixed percentage calculation in ft_summary_generator.py.

**Technical Details:**
```python
# Fixed in: backend/shared/infrastructure/adapters/excel/ft_summary_generator.py

# BEFORE: Double percentage conversion
percentage = (count / total) * 100 * 100  # ❌ Double multiplication

# AFTER: Correct percentage calculation  
percentage = (count / total) * 100  # ✅ Single multiplication
```

**Verification**: Excel reports now display correct percentages (60%, 40%).

---

## Common Error Patterns and Solutions

### 1. File Upload Issues

#### Error: "File too large"
**Cause**: File exceeds maximum upload size limit
**Solution**: 
```python
# Check file size limits in configuration
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
MAX_TOTAL_SIZE = 500 * 1024 * 1024  # 500MB total
```

#### Error: "Unsupported file format"
**Cause**: File extension not in allowed list
**Solution**:
```python
# Allowed archive formats
ALLOWED_EXTENSIONS = {'.zip', '.7z', '.rar'}
```

#### Error: "Archive extraction failed"
**Cause**: Corrupted or password-protected archive
**Solution**:
1. Verify archive integrity using archive software
2. Remove password protection
3. Re-compress using supported format

### 2. Processing Issues

#### Error: "No rename pattern specified"
**Cause**: Rename configuration missing or invalid
**Solution**:
```javascript
// Ensure rename configuration is properly set
const renameConfig = {
    pattern_before: "original_pattern",
    pattern_after: "new_pattern"
};
```

#### Error: "QC generation failed"
**Cause**: Invalid PTS file format or missing required sections
**Solution**:
1. Verify PTS file contains required sections:
   - Parameter section
   - QA section
   - Bin Definition section
2. Check file encoding (should be UTF-8 or ASCII)

#### Error: "Directory creation failed"
**Cause**: File system permissions or path length issues
**Solution**:
1. Check directory permissions
2. Verify path length is within OS limits
3. Ensure no illegal characters in filenames

### 3. Performance Issues

#### Issue: Slow upload processing
**Symptoms**: Long wait times during file upload
**Diagnosis Steps**:
1. Check file size (larger files take longer)
2. Monitor CPU usage during extraction
3. Check available disk space
4. Review log files for bottlenecks

**Solutions**:
```python
# Optimize extraction performance
extractor = ArchiveExtractor()
extractor.set_thread_count(4)  # Adjust based on CPU cores
extractor.set_memory_limit(1024)  # MB
```

#### Issue: High memory usage
**Symptoms**: System slowdown, memory errors
**Diagnosis Steps**:
1. Monitor memory usage during processing
2. Check for memory leaks in logs
3. Review file size distributions

**Solutions**:
```python
# Implement chunked processing for large files
def process_large_file_chunked(file_path: Path, chunk_size: int = 1024*1024):
    with open(file_path, 'rb') as f:
        while True:
            chunk = f.read(chunk_size)
            if not chunk:
                break
            process_chunk(chunk)
```

### 4. Database Issues

#### Error: "Database connection failed"
**Cause**: Database file locked, corrupted, or inaccessible
**Diagnosis Steps**:
1. Check database file exists: `outlook.db`
2. Verify file permissions
3. Check for lock files (`.lock`, `.wal`, `.shm`)

**Solutions**:
```bash
# Check database integrity
sqlite3 outlook.db "PRAGMA integrity_check;"

# Fix database if needed
sqlite3 outlook.db ".backup backup.db"
```

#### Error: "Table does not exist"
**Cause**: Database schema not initialized or outdated
**Solution**:
```python
# Run database migrations
from backend.shared.infrastructure.database import DatabaseManager
db_manager = DatabaseManager()
db_manager.run_migrations()
```

### 5. Security Issues

#### Warning: "Potential security threat detected"
**Cause**: File contains patterns that match malicious content signatures
**Diagnosis Steps**:
1. Verify file is legitimate business content
2. Check if file is binary archive (should skip content scanning)
3. Review security logs for specific threat details

**Solutions**:
```python
# For legitimate files that trigger false positives
# Check if file should be excluded from content scanning
def is_safe_file_type(filename: str) -> bool:
    safe_extensions = {'.zip', '.7z', '.rar', '.tar.gz'}
    return Path(filename).suffix.lower() in safe_extensions
```

## Diagnostic Tools and Commands

### 1. System Health Check
```bash
# Check system status
python scripts/health_check.py

# Verify database connectivity
python scripts/db_health.py

# Test file processing pipeline
python scripts/test_pipeline.py
```

### 2. Log Analysis
```bash
# View recent error logs
tail -n 100 logs/pts_renamer_error.log

# Search for specific errors
grep "ERROR" logs/pts_renamer.log | tail -20

# Monitor real-time logs
tail -f logs/pts_renamer.log
```

### 3. Database Inspection
```sql
-- Check upload records
SELECT * FROM pts_rename_jobs ORDER BY created_at DESC LIMIT 10;

-- Check file records
SELECT * FROM pts_rename_files WHERE upload_id = 'specific_upload_id';

-- Verify table structure
.schema pts_rename_jobs
.schema pts_rename_files
```

### 4. File System Inspection
```bash
# Check upload directories
ls -la tmp/pts_renamer/

# Verify file permissions
find tmp/pts_renamer/ -type f -not -perm 644

# Check disk space
df -h
```

## Monitoring and Maintenance

### 1. Regular Maintenance Tasks

#### Daily Checks
- Monitor log files for errors
- Check disk space usage
- Verify upload directory cleanup

#### Weekly Checks
- Database integrity check
- Performance metrics review
- Security audit log review

#### Monthly Checks
- System backup verification
- Performance optimization review
- Security policy update review

### 2. Performance Monitoring

#### Key Metrics to Track
```python
# Response time metrics
upload_time_avg = average_upload_processing_time()
extraction_time_avg = average_extraction_time()
processing_time_avg = average_processing_time()

# Error rate metrics
error_rate = failed_uploads / total_uploads
success_rate = successful_uploads / total_uploads

# Resource usage metrics
cpu_usage_avg = average_cpu_usage()
memory_usage_avg = average_memory_usage()
disk_usage = current_disk_usage()
```

#### Performance Thresholds
```yaml
Response Time Thresholds:
  upload_processing: < 30 seconds
  file_extraction: < 60 seconds
  preview_generation: < 5 seconds
  
Error Rate Thresholds:
  failed_uploads: < 5%
  security_false_positives: < 1%
  processing_failures: < 2%

Resource Usage Thresholds:
  cpu_usage: < 80%
  memory_usage: < 85%
  disk_usage: < 90%
```

### 3. Backup and Recovery

#### Backup Procedures
```bash
# Database backup
sqlite3 outlook.db ".backup backup_$(date +%Y%m%d).db"

# Configuration backup
tar -czf config_backup_$(date +%Y%m%d).tar.gz config/

# Logs backup
tar -czf logs_backup_$(date +%Y%m%d).tar.gz logs/
```

#### Recovery Procedures
```bash
# Restore database from backup
cp backup_20250822.db outlook.db

# Verify database integrity after restore
sqlite3 outlook.db "PRAGMA integrity_check;"

# Restart services
python start_integrated_services.py
```

## Emergency Procedures

### 1. System Unresponsive
```bash
# Check service status
ps aux | grep python

# Kill hung processes
pkill -f pts_renamer

# Restart services
python start_integrated_services.py
```

### 2. Database Corruption
```bash
# Create backup of corrupted database
cp outlook.db outlook_corrupted_$(date +%Y%m%d).db

# Attempt database repair
sqlite3 outlook.db ".recover" | sqlite3 outlook_recovered.db

# Verify recovered database
sqlite3 outlook_recovered.db "PRAGMA integrity_check;"

# Replace with recovered database if valid
mv outlook_recovered.db outlook.db
```

### 3. Security Incident
```bash
# Stop all services immediately
pkill -f pts_renamer

# Review security logs
grep "SECURITY" logs/*.log > security_incident_$(date +%Y%m%d).log

# Check for suspicious files
find tmp/pts_renamer/ -type f -newer /tmp/incident_time

# Clean upload directories
rm -rf tmp/pts_renamer/*

# Restart with enhanced monitoring
python start_integrated_services.py --security-enhanced
```

## Contact and Support

### Internal Support
- **System Administrator**: Check server status and logs
- **Database Administrator**: Database-related issues
- **Security Team**: Security incidents and threat analysis
- **Development Team**: Code bugs and feature issues

### External Resources
- **SQLite Documentation**: https://sqlite.org/docs.html
- **Python Documentation**: https://docs.python.org/
- **Flask Documentation**: https://flask.palletsprojects.com/
- **Security Best Practices**: OWASP guidelines

### Escalation Procedures
1. **Level 1**: Log analysis and basic troubleshooting
2. **Level 2**: Database and file system investigation
3. **Level 3**: Code analysis and security review
4. **Level 4**: System architecture review and redesign

---

**Last Updated**: 2025-08-22  
**Version**: 2.0  
**Next Review**: 2025-09-22