"""
Test data factories and fixtures for database field display improvements testing

Provides comprehensive test data for:
- Story 1.1: Chinese field titles testing
- Story 1.2: Boolean visual tags testing (especially false → red "失敗" tags)
- Story 1.3: Field category organization testing

Usage:
    from tests.fixtures.database_field_display_fixtures import *
    
    # Get test record with false download status (Story 1.2 key case)
    test_record = EmailDownloadStatusFactory.build_failure_case()
    
    # Get complete test dataset
    test_data = DatabaseFieldDisplayFixtures.get_comprehensive_test_data()

Author: Test Automation Specialist
Date: 2025-08-19
"""

import factory
import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json
import random


@dataclass
class TestColumn:
    """Represents a database table column for testing"""
    name: str
    type: str
    display_name: str = None
    category: str = None
    
    def __post_init__(self):
        if self.display_name is None:
            self.display_name = self.name


@dataclass 
class TestRecord:
    """Represents a database record for testing"""
    id: int
    data: Dict[str, Any]
    
    def __getitem__(self, key):
        """Allow dict-like access"""
        if key == 'id':
            return self.id
        return self.data.get(key)
    
    def get(self, key, default=None):
        """Dict-like get method"""
        if key == 'id':
            return self.id
        return self.data.get(key, default)


class EmailDownloadStatusFactory:
    """Factory for creating email_download_status test records"""
    
    @classmethod
    def build_failure_case(cls) -> TestRecord:
        """Build record with download failure - key test case for Story 1.2"""
        return TestRecord(
            id=1,
            data={
                'email_id': 123,
                'is_remote_download_success': False,  # Critical: Should show red "失敗" tag
                'is_processing_success': True,        # Success for contrast
                'download_error_message': 'Connection timeout after 30 seconds',
                'processing_error_message': None,
                'retry_count': 2,
                'last_download_attempt': '2025-08-19T10:15:00Z',
                'created_at': '2025-08-19T09:00:00Z',
                'updated_at': '2025-08-19T10:15:30Z'
            }
        )
    
    @classmethod
    def build_success_case(cls) -> TestRecord:
        """Build record with successful download"""
        return TestRecord(
            id=2,
            data={
                'email_id': 124,
                'is_remote_download_success': True,   # Should show green "成功" tag
                'is_processing_success': True,
                'download_error_message': None,
                'processing_error_message': None,
                'retry_count': 0,
                'last_download_attempt': '2025-08-19T08:30:00Z',
                'created_at': '2025-08-19T08:00:00Z',
                'updated_at': '2025-08-19T08:30:15Z'
            }
        )
    
    @classmethod
    def build_unknown_case(cls) -> TestRecord:
        """Build record with unknown/null status"""
        return TestRecord(
            id=3,
            data={
                'email_id': 125,
                'is_remote_download_success': None,   # Should show grey "未知" tag
                'is_processing_success': False,       # Processing failed
                'download_error_message': None,
                'processing_error_message': 'Invalid email format detected',
                'retry_count': 1,
                'last_download_attempt': '2025-08-19T11:00:00Z',
                'created_at': '2025-08-19T10:30:00Z',
                'updated_at': '2025-08-19T11:00:30Z'
            }
        )
    
    @classmethod
    def build_processing_failure_case(cls) -> TestRecord:
        """Build record with download success but processing failure"""
        return TestRecord(
            id=4,
            data={
                'email_id': 126,
                'is_remote_download_success': True,   # Download succeeded
                'is_processing_success': False,       # But processing failed - red "失敗" tag
                'download_error_message': None,
                'processing_error_message': 'Parse error: unable to extract vendor code',
                'retry_count': 3,
                'last_download_attempt': '2025-08-19T12:00:00Z',
                'created_at': '2025-08-19T11:30:00Z',
                'updated_at': '2025-08-19T12:00:30Z'
            }
        )
    
    @classmethod
    def build_mixed_error_case(cls) -> TestRecord:
        """Build record with multiple error types for category testing"""
        return TestRecord(
            id=5,
            data={
                'email_id': 127,
                'is_remote_download_success': False,
                'is_processing_success': False,
                'download_error_message': 'SSL certificate verification failed',
                'processing_error_message': 'Attachment parsing timeout',
                'retry_count': 5,
                'last_download_attempt': '2025-08-19T13:00:00Z',
                'created_at': '2025-08-19T12:00:00Z',
                'updated_at': '2025-08-19T13:00:30Z'
            }
        )
    
    @classmethod
    def build_batch(cls, count: int = 10) -> List[TestRecord]:
        """Build a batch of diverse test records"""
        records = []
        
        # Include key test cases
        records.extend([
            cls.build_failure_case(),
            cls.build_success_case(), 
            cls.build_unknown_case(),
            cls.build_processing_failure_case(),
            cls.build_mixed_error_case()
        ])
        
        # Fill remaining with random data
        for i in range(len(records), count):
            records.append(cls.build_random(i + 6))
        
        return records
    
    @classmethod
    def build_random(cls, record_id: int) -> TestRecord:
        """Build record with randomized data"""
        download_success = random.choice([True, False, None])
        processing_success = random.choice([True, False, None])
        
        # Generate appropriate error messages based on status
        download_error = None
        processing_error = None
        
        if download_success is False:
            download_error = random.choice([
                'Connection timeout',
                'SSL certificate error',
                'Network unreachable',
                'Server returned 500 error',
                'Authentication failed'
            ])
        
        if processing_success is False:
            processing_error = random.choice([
                'Parse error: invalid format',
                'Attachment extraction failed', 
                'Encoding error: unsupported charset',
                'Content validation failed',
                'Database constraint violation'
            ])
        
        return TestRecord(
            id=record_id,
            data={
                'email_id': 1000 + record_id,
                'is_remote_download_success': download_success,
                'is_processing_success': processing_success,
                'download_error_message': download_error,
                'processing_error_message': processing_error,
                'retry_count': random.randint(0, 5),
                'last_download_attempt': f'2025-08-19T{random.randint(8, 17)}:{random.randint(10, 50)}:00Z',
                'created_at': f'2025-08-19T{random.randint(6, 15)}:00:00Z',
                'updated_at': f'2025-08-19T{random.randint(8, 18)}:00:00Z'
            }
        )


class ColumnDefinitionFactory:
    """Factory for creating column definitions for testing"""
    
    @classmethod
    def get_email_download_status_columns(cls) -> List[TestColumn]:
        """Get column definitions for email_download_status table with Chinese titles"""
        return [
            TestColumn('id', 'INTEGER', 'ID', '基本資訊'),
            TestColumn('email_id', 'INTEGER', '郵件 ID', '基本資訊'),
            TestColumn('is_remote_download_success', 'BOOLEAN', '下載成功', '處理狀態'),  # Story 1.1 & 1.2
            TestColumn('is_processing_success', 'BOOLEAN', '處理成功', '處理狀態'),      # Story 1.1 & 1.2
            TestColumn('download_error_message', 'TEXT', '下載錯誤訊息', '錯誤訊息'),
            TestColumn('processing_error_message', 'TEXT', '處理錯誤訊息', '錯誤訊息'),
            TestColumn('retry_count', 'INTEGER', '重試次數', '處理狀態'),
            TestColumn('last_download_attempt', 'TIMESTAMP', '最後下載嘗試', '處理狀態'),
            TestColumn('created_at', 'TIMESTAMP', '創建時間', '基本資訊'),
            TestColumn('updated_at', 'TIMESTAMP', '更新時間', '技術欄位')
        ]
    
    @classmethod
    def get_emails_columns(cls) -> List[TestColumn]:
        """Get column definitions for emails table (for comparison testing)"""
        return [
            TestColumn('id', 'INTEGER', 'ID', '基本資訊'),
            TestColumn('sender', 'TEXT', '寄件者', '基本資訊'),
            TestColumn('subject', 'TEXT', '主旨', '基本資訊'),
            TestColumn('received_time', 'TIMESTAMP', '接收時間', '基本資訊'),
            TestColumn('vendor_code', 'TEXT', '廠商代碼', '解析資料'),
            TestColumn('parse_status', 'TEXT', '解析狀態', '解析資料'),
            TestColumn('extraction_method', 'TEXT', '解析方法', '解析資料')
        ]


class MockAPIResponseFactory:
    """Factory for creating mock API responses"""
    
    @classmethod
    def build_database_info_response(cls) -> Dict[str, Any]:
        """Build mock database info API response"""
        return {
            "success": True,
            "data": {
                "db_size": 2048576,  # 2MB
                "tables": {
                    "emails": 1250,
                    "senders": 89,
                    "attachments": 456,
                    "email_process_status": 1250,
                    "email_download_status": 25,    # Our target table
                    "email_download_retry_log": 123
                }
            }
        }
    
    @classmethod
    def build_table_data_response(cls, table_name: str, records: List[TestRecord] = None) -> Dict[str, Any]:
        """Build mock table data API response"""
        if records is None:
            records = EmailDownloadStatusFactory.build_batch(5)
        
        if table_name == "email_download_status":
            columns = ColumnDefinitionFactory.get_email_download_status_columns()
        elif table_name == "emails":
            columns = ColumnDefinitionFactory.get_emails_columns()
        else:
            columns = [TestColumn('id', 'INTEGER', 'ID')]
        
        # Convert columns to API format
        api_columns = [
            {"name": col.name, "type": col.type}
            for col in columns
        ]
        
        # Convert records to API format
        api_records = []
        for record in records:
            record_dict = {"id": record.id}
            record_dict.update(record.data)
            api_records.append(record_dict)
        
        return {
            "success": True,
            "data": {
                "columns": api_columns,
                "records": api_records
            }
        }
    
    @classmethod
    def build_record_detail_response(cls, record_id: int, record: TestRecord = None) -> Dict[str, Any]:
        """Build mock record detail API response"""
        if record is None:
            record = EmailDownloadStatusFactory.build_failure_case()
            record.id = record_id
        
        record_dict = {"id": record.id}
        record_dict.update(record.data)
        
        return {
            "success": True,
            "data": record_dict
        }
    
    @classmethod
    def build_search_response(cls, search_term: str, records: List[TestRecord] = None) -> Dict[str, Any]:
        """Build mock search API response"""
        if records is None:
            all_records = EmailDownloadStatusFactory.build_batch(10)
            
            # Filter records based on search term
            filtered_records = []
            for record in all_records:
                if cls._matches_search(record, search_term):
                    filtered_records.append(record)
            
            records = filtered_records
        
        api_records = []
        for record in records:
            record_dict = {"id": record.id}
            record_dict.update(record.data)
            api_records.append(record_dict)
        
        return {
            "success": True,
            "data": {
                "records": api_records,
                "total": len(api_records)
            }
        }
    
    @classmethod
    def _matches_search(cls, record: TestRecord, search_term: str) -> bool:
        """Check if record matches search term"""
        search_lower = search_term.lower()
        
        # Search in string fields
        string_fields = ['download_error_message', 'processing_error_message']
        for field in string_fields:
            value = record.get(field)
            if value and search_lower in str(value).lower():
                return True
        
        # Search boolean fields
        if 'false' in search_lower or '失敗' in search_lower:
            if record.get('is_remote_download_success') is False or record.get('is_processing_success') is False:
                return True
        
        if 'true' in search_lower or '成功' in search_lower:
            if record.get('is_remote_download_success') is True or record.get('is_processing_success') is True:
                return True
        
        return False


class DatabaseFieldDisplayFixtures:
    """Main fixture class providing comprehensive test data"""
    
    @classmethod
    def get_comprehensive_test_data(cls) -> Dict[str, Any]:
        """Get complete test data set for all three stories"""
        return {
            # Story 1.1: Chinese field titles
            'columns': ColumnDefinitionFactory.get_email_download_status_columns(),
            'column_mappings': cls.get_chinese_title_mappings(),
            
            # Story 1.2: Boolean visual tags (with key false cases)
            'test_records': EmailDownloadStatusFactory.build_batch(15),
            'boolean_test_cases': cls.get_boolean_test_cases(),
            
            # Story 1.3: Field categorization
            'field_categories': cls.get_field_categories(),
            'category_test_data': cls.get_category_test_data(),
            
            # API responses
            'api_responses': cls.get_mock_api_responses(),
            
            # Edge cases and error scenarios
            'edge_cases': cls.get_edge_cases()
        }
    
    @classmethod
    def get_chinese_title_mappings(cls) -> Dict[str, str]:
        """Get Chinese title mappings for Story 1.1 testing"""
        return {
            'is_remote_download_success': '下載成功',
            'is_processing_success': '處理成功', 
            'email_id': '郵件 ID',
            'last_download_attempt': '最後下載嘗試',
            'download_error_message': '下載錯誤訊息',
            'processing_error_message': '處理錯誤訊息',
            'retry_count': '重試次數',
            'created_at': '創建時間',
            'updated_at': '更新時間'
        }
    
    @classmethod
    def get_boolean_test_cases(cls) -> List[Dict[str, Any]]:
        """Get specific test cases for Story 1.2 boolean visual tags"""
        return [
            {
                'name': 'download_failure_case',
                'description': 'Download failed - should show red 失敗 tag',
                'record': EmailDownloadStatusFactory.build_failure_case(),
                'expected_tag': 'failed-status-tag',
                'expected_text': '失敗',
                'expected_color': 'rgb(248, 215, 218)'  # Red background
            },
            {
                'name': 'download_success_case', 
                'description': 'Download succeeded - should show green 成功 tag',
                'record': EmailDownloadStatusFactory.build_success_case(),
                'expected_tag': 'success-status-tag',
                'expected_text': '成功',
                'expected_color': 'rgb(212, 237, 218)'  # Green background
            },
            {
                'name': 'unknown_status_case',
                'description': 'Unknown status - should show grey 未知 tag',
                'record': EmailDownloadStatusFactory.build_unknown_case(),
                'expected_tag': 'unknown-status-tag',
                'expected_text': '未知',
                'expected_color': 'rgb(245, 245, 245)'  # Grey background
            },
            {
                'name': 'processing_failure_case',
                'description': 'Processing failed - should show red 失敗 tag',
                'record': EmailDownloadStatusFactory.build_processing_failure_case(),
                'expected_tag': 'failed-status-tag',
                'expected_text': '失敗',
                'expected_color': 'rgb(248, 215, 218)'
            }
        ]
    
    @classmethod
    def get_field_categories(cls) -> Dict[str, List[str]]:
        """Get field categories for Story 1.3 testing"""
        return {
            '基本資訊': ['id', 'email_id', 'created_at'],
            '處理狀態': [
                'is_remote_download_success',
                'is_processing_success', 
                'retry_count',
                'last_download_attempt'
            ],
            '錯誤訊息': [
                'download_error_message',
                'processing_error_message'
            ],
            '技術欄位': ['updated_at']
        }
    
    @classmethod
    def get_category_test_data(cls) -> Dict[str, Any]:
        """Get test data for field categorization"""
        return {
            'expected_categories': ['基本資訊', '處理狀態', '錯誤訊息', '技術欄位'],
            'category_field_counts': {
                '基本資訊': 3,
                '處理狀態': 4,
                '錯誤訊息': 2,
                '技術欄位': 1
            },
            'critical_fields': {
                'is_remote_download_success': '處理狀態',
                'is_processing_success': '處理狀態',
                'download_error_message': '錯誤訊息'
            }
        }
    
    @classmethod
    def get_mock_api_responses(cls) -> Dict[str, Any]:
        """Get mock API responses for testing"""
        test_records = EmailDownloadStatusFactory.build_batch(8)
        
        return {
            'database_info': MockAPIResponseFactory.build_database_info_response(),
            'table_data': MockAPIResponseFactory.build_table_data_response('email_download_status', test_records),
            'record_detail': MockAPIResponseFactory.build_record_detail_response(1),
            'search_results': MockAPIResponseFactory.build_search_response('失敗', [test_records[0]]),  # Failure search
            'empty_search': MockAPIResponseFactory.build_search_response('nonexistent', [])
        }
    
    @classmethod
    def get_edge_cases(cls) -> Dict[str, Any]:
        """Get edge case test data"""
        return {
            'null_boolean_record': TestRecord(
                id=999,
                data={
                    'email_id': 999,
                    'is_remote_download_success': None,
                    'is_processing_success': None,
                    'download_error_message': None,
                    'processing_error_message': None,
                    'retry_count': 0,
                    'last_download_attempt': None,
                    'created_at': '2025-08-19T00:00:00Z',
                    'updated_at': None
                }
            ),
            'long_error_messages': TestRecord(
                id=998,
                data={
                    'email_id': 998,
                    'is_remote_download_success': False,
                    'is_processing_success': False,
                    'download_error_message': 'Very long error message ' * 20,  # Test truncation
                    'processing_error_message': 'Another extremely long error message that should be truncated ' * 15,
                    'retry_count': 10,
                    'last_download_attempt': '2025-08-19T23:59:59Z',
                    'created_at': '2025-08-19T00:00:00Z',
                    'updated_at': '2025-08-19T23:59:59Z'
                }
            ),
            'special_characters': TestRecord(
                id=997,
                data={
                    'email_id': 997,
                    'is_remote_download_success': True,
                    'is_processing_success': False,
                    'download_error_message': None,
                    'processing_error_message': 'Error with special chars: <>&"\'{}[]',
                    'retry_count': 1,
                    'last_download_attempt': '2025-08-19T12:00:00Z',
                    'created_at': '2025-08-19T11:00:00Z', 
                    'updated_at': '2025-08-19T12:00:00Z'
                }
            )
        }
    
    @classmethod
    def get_performance_test_data(cls, record_count: int = 100) -> Dict[str, Any]:
        """Get large dataset for performance testing"""
        return {
            'large_record_set': EmailDownloadStatusFactory.build_batch(record_count),
            'columns': ColumnDefinitionFactory.get_email_download_status_columns(),
            'api_response': MockAPIResponseFactory.build_table_data_response(
                'email_download_status',
                EmailDownloadStatusFactory.build_batch(record_count)
            )
        }
    
    @classmethod
    def export_test_data_json(cls, filepath: str = 'test_data.json'):
        """Export test data to JSON file for external testing tools"""
        test_data = cls.get_comprehensive_test_data()
        
        # Convert TestRecord objects to dictionaries for JSON serialization
        serializable_data = {}
        for key, value in test_data.items():
            if key == 'test_records':
                serializable_data[key] = [
                    {'id': record.id, **record.data}
                    for record in value
                ]
            elif key == 'boolean_test_cases':
                serializable_data[key] = [
                    {
                        'name': case['name'],
                        'description': case['description'],
                        'record': {'id': case['record'].id, **case['record'].data},
                        'expected_tag': case['expected_tag'],
                        'expected_text': case['expected_text'],
                        'expected_color': case['expected_color']
                    }
                    for case in value
                ]
            elif key == 'columns':
                serializable_data[key] = [
                    {
                        'name': col.name,
                        'type': col.type,
                        'display_name': col.display_name,
                        'category': col.category
                    }
                    for col in value
                ]
            else:
                serializable_data[key] = value
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, ensure_ascii=False, indent=2)
        
        return filepath


# Helper functions for test setup
def setup_test_database_with_fixtures():
    """Set up test database with fixture data (mock implementation)"""
    fixtures = DatabaseFieldDisplayFixtures.get_comprehensive_test_data()
    
    # This would insert test data into actual test database
    # For now, return the fixtures for use in tests
    return fixtures


def cleanup_test_data():
    """Clean up test data after tests complete"""
    # This would clean up any test data created in database
    pass


def create_playwright_test_data():
    """Create specific test data for Playwright E2E tests"""
    return {
        'failure_record': EmailDownloadStatusFactory.build_failure_case(),
        'success_record': EmailDownloadStatusFactory.build_success_case(),
        'unknown_record': EmailDownloadStatusFactory.build_unknown_case(),
        'mixed_batch': EmailDownloadStatusFactory.build_batch(5)
    }


if __name__ == '__main__':
    # Demo the fixtures
    print("🧪 Database Field Display Test Fixtures Demo")
    
    # Show key test cases
    print("\n📋 Key Test Cases:")
    
    # Story 1.2: Boolean visual tags
    failure_case = EmailDownloadStatusFactory.build_failure_case()
    print(f"   Failure Case: ID {failure_case.id}, download_success={failure_case['is_remote_download_success']} → Should show red '失敗' tag")
    
    success_case = EmailDownloadStatusFactory.build_success_case()
    print(f"   Success Case: ID {success_case.id}, download_success={success_case['is_remote_download_success']} → Should show green '成功' tag")
    
    unknown_case = EmailDownloadStatusFactory.build_unknown_case()
    print(f"   Unknown Case: ID {unknown_case.id}, download_success={unknown_case['is_remote_download_success']} → Should show grey '未知' tag")
    
    # Story 1.1: Chinese titles
    print(f"\n🔤 Chinese Title Mappings:")
    mappings = DatabaseFieldDisplayFixtures.get_chinese_title_mappings()
    for en, zh in mappings.items():
        print(f"   {en} → {zh}")
    
    # Story 1.3: Categories
    print(f"\n📁 Field Categories:")
    categories = DatabaseFieldDisplayFixtures.get_field_categories()
    for cat, fields in categories.items():
        print(f"   {cat}: {', '.join(fields)}")
    
    # Export test data
    export_path = DatabaseFieldDisplayFixtures.export_test_data_json('tests/fixtures/test_data_export.json')
    print(f"\n💾 Test data exported to: {export_path}")
    
    print(f"\n✅ Fixtures ready for comprehensive testing across all three stories!")