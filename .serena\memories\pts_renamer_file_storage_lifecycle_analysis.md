# PTS Renamer 檔案存儲生命週期分析

## 完整檔案流程生命週期

基於對代碼的深入分析，PTS Renamer 系統的檔案存儲生命週期如下：

### 1. 上傳檔案存儲位置 ✅
**位置**: `/tmp/pts_renamer/{upload_id}/`
- **配置來源**: `PTSRenameConfig.temp_storage_path` (默認: `/tmp/pts_renamer`)
- **檔案結構**: 
  ```
  /tmp/pts_renamer/
  ├── {upload_id}/
  │   ├── {original_filename}     # 上傳的原始檔案
  │   └── staging/                # 暫存區域
  ```
- **服務**: `PTSRenameUploadService` 負責管理
- **持久性**: 根據配置保留期限清理

### 2. 解壓縮檔案存儲位置 ✅
**位置**: `/tmp/pts_renamer/{upload_id}/{filename}_extracted/`
- **解壓縮邏輯**: 
  - 使用 Dramatiq `extract_archive_task` 進行異步解壓縮
  - 每個壓縮檔案創建獨立的解壓縮目錄
  - 支援 .zip, .7z, .rar 格式
- **檔案結構**:
  ```
  /tmp/pts_renamer/{upload_id}/
  ├── compressed_file.7z
  └── compressed_file_extracted/
      ├── file1.pts
      ├── file2.pts
      └── ...
  ```
- **Dramatiq 配置**: `queue="processing_queue"`, `max_retries=3`, `time_limit=900000ms`

### 3. 處理後壓縮檔存儲位置 ✅
**位置**: `/tmp/pts_renamer/results/{job_id}/`
- **配置來源**: `PTSRenameConfig.result_storage_path` (默認: `/tmp/pts_renamer/results`)
- **壓縮流程**:
  1. **準備階段**: 複製處理後檔案到 `compression_source/`
  2. **Dramatiq 壓縮**: 使用 `create_download_archive_task`
  3. **本地備份**: 同時創建本地 ZIP 檔案
- **檔案結構**:
  ```
  /tmp/pts_renamer/results/{job_id}/
  ├── compression_source/         # 壓縮源檔案
  │   ├── renamed_file1.pts
  │   ├── renamed_file2.pts
  │   ├── QC_files/
  │   └── processing_report.txt
  ├── pts_results_{job_id}_{timestamp}.zip  # 最終壓縮檔
  └── download_mappings/          # 下載令牌映射
      └── {download_token}.json
  ```

### 4. 前端下載功能 ✅
**下載URL**: `http://localhost:5000/pts-renamer/api/download/{download_token}`

#### 下載流程:
1. **URL 生成**: `PTSRenameDownloadService.generate_download_url()`
   - 生成安全的下載令牌: `{job_id}_{uuid}`
   - 設置過期時間 (默認 24 小時)
   - 存儲令牌映射到 JSON 檔案

2. **下載令牌管理**:
   ```json
   {
     "download_token": "job123_abc456def789",
     "file_path": "/tmp/pts_renamer/results/job123/pts_results_job123_20250122_143000.zip",
     "job_id": "job123",
     "created_at": "2025-01-22T14:30:00",
     "expires_at": "2025-01-23T14:30:00",
     "downloaded": false,
     "download_count": 0
   }
   ```

3. **前端整合**: 
   - Flask Presenter 層提供 `handle_download_request()` 方法
   - 驗證 job 狀態、檢查過期時間
   - 返回下載 URL 給前端使用

#### 安全特性:
- **令牌過期**: 自動過期機制
- **存取追蹤**: 記錄下載次數和時間
- **檔案驗證**: 檢查檔案存在性
- **狀態檢查**: 確保 job 已完成

### 5. 清理機制
**自動清理**: `PTSRenameDownloadService.cleanup_processed_files()`
- **保留期**: 根據 `cleanup_retention_hours` 配置 (默認 24 小時)
- **清理範圍**: 
  - 處理結果目錄
  - 過期的下載令牌
  - 相關的資料庫記錄
- **清理策略**: 基於檔案創建時間的滾動清理

### 6. Dramatiq 整合
#### 使用的 Dramatiq 任務:
1. **extract_archive_task**: 解壓縮上傳的檔案
   - Queue: `processing_queue`
   - 重試: 3 次
   - 超時: 15 分鐘

2. **create_download_archive_task**: 壓縮處理結果
   - Queue: 默認隊列
   - 重試: 3 次
   - 支援自定義壓縮檔名

3. **pts_file_compression_task**: PTS 專用壓縮
   - 來自 `pts_rename_dramatiq_integration.py`
   - 整合現有壓縮基礎設施

### 7. 配置參數總結
```python
class PTSRenameConfig:
    temp_storage_path = "/tmp/pts_renamer"           # 暫存路徑
    result_storage_path = "/tmp/pts_renamer/results" # 結果路徑
    cleanup_retention_hours = 24                     # 清理保留時間
    enable_compression = True                        # 啟用壓縮
    compression_level = 6                            # 壓縮級別
    base_download_url = "http://localhost:5000/pts-renamer/api/download" # 下載基礎URL
```

## 結論

PTS Renamer 系統提供了完整的檔案生命週期管理：

✅ **上傳**: 安全存儲到 `/tmp/pts_renamer/{upload_id}/`
✅ **解壓縮**: Dramatiq 異步處理到 `{filename}_extracted/`
✅ **處理**: 在原地修改檔案
✅ **壓縮**: 結果存儲到 `/tmp/pts_renamer/results/{job_id}/`
✅ **下載**: 通過安全令牌提供前端存取
✅ **清理**: 自動清理過期檔案和令牌

整個流程完全使用 Dramatiq 進行異步處理，確保高性能和可靠性。前端可以通過 Flask API 安全地下載處理結果。