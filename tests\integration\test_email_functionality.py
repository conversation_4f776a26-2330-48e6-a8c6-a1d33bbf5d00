"""
Test Email Functionality
Test the actual email functionality that was failing with the mo column error
"""

import sys
from pathlib import Path

# Add src directory to Python path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_email_database_operations():
    """Test email database operations that were failing"""
    print("Testing Email Database Operations:")
    print("-" * 50)
    
    try:
        # Import the database models and engine
        from backend.shared.infrastructure.database.models import (
            DatabaseEngine, EmailDB, Base
        )
        
        # Initialize database
        print("1. Initializing database engine...")
        db_engine = DatabaseEngine("sqlite:///data/email_inbox.db")
        db_engine.initialize()
        print("   ✓ Database engine initialized successfully")
        
        # Test session creation
        print("2. Creating database session...")
        session = db_engine.get_session()
        print("   ✓ Database session created successfully")
        
        # Test the problematic query (that was causing the original error)
        print("3. Testing problematic email query...")
        try:
            emails = session.query(EmailDB).limit(5).all()
            print(f"   ✓ Successfully queried emails (found {len(emails)} emails)")
            
            # Test accessing the 'mo' field specifically
            for i, email in enumerate(emails):
                mo_value = email.mo
                pd_value = email.pd
                lot_value = email.lot
                print(f"   Email {i+1}: mo='{mo_value}', pd='{pd_value}', lot='{lot_value}'")
                
        except Exception as e:
            print(f"   ✗ Email query failed: {e}")
            return False
        
        # Test creating a new email record
        print("4. Testing email record creation...")
        try:
            from datetime import datetime
            
            new_email = EmailDB(
                message_id="test-functionality-001",
                sender="<EMAIL>",
                sender_display_name="Functionality Tester",
                subject="Database Repair Verification Test",
                body="Testing that mo column and other new fields work correctly",
                received_time=datetime.now(),
                created_at=datetime.now(),
                is_read=False,
                is_processed=False,
                has_attachments=False,
                attachment_count=0,
                pd="TEST-PD-FUNC",
                lot="TEST-LOT-FUNC", 
                mo="TEST-MO-FUNC",  # This was the problematic field
                yield_value="99.9",
                vendor_code="TEST-VENDOR",
                parse_status="pending",
                extraction_method="test",
                llm_service_used="test-service"
            )
            
            session.add(new_email)
            session.commit()
            
            test_id = new_email.id
            print(f"   ✓ Successfully created new email record (ID: {test_id})")
            
        except Exception as e:
            print(f"   ✗ Email creation failed: {e}")
            return False
        
        # Test updating the email record
        print("5. Testing email record update...")
        try:
            email_to_update = session.query(EmailDB).filter_by(id=test_id).first()
            if email_to_update:
                email_to_update.mo = "UPDATED-MO-FUNC"
                email_to_update.extraction_method = "updated-test"
                email_to_update.llm_analysis_result = '{"status": "tested", "success": true}'
                
                session.commit()
                print("   ✓ Successfully updated email record")
            else:
                print("   ✗ Could not find email to update")
                return False
                
        except Exception as e:
            print(f"   ✗ Email update failed: {e}")
            return False
        
        # Test complex query with joins (if there are related records)
        print("6. Testing complex queries...")
        try:
            # Query with all the columns that were causing issues
            query = session.query(
                EmailDB.id,
                EmailDB.message_id,
                EmailDB.sender,
                EmailDB.subject,
                EmailDB.mo,  # The problematic column
                EmailDB.pd,
                EmailDB.lot,
                EmailDB.extraction_method,
                EmailDB.llm_analysis_result,
                EmailDB.llm_service_used
            ).filter(EmailDB.mo.isnot(None)).all()
            
            print(f"   ✓ Complex query executed successfully (found {len(query)} results)")
            
        except Exception as e:
            print(f"   ✗ Complex query failed: {e}")
            return False
        
        # Clean up test data
        print("7. Cleaning up test data...")
        try:
            session.query(EmailDB).filter_by(id=test_id).delete()
            session.commit()
            print("   ✓ Test data cleaned up successfully")
            
        except Exception as e:
            print(f"   ✗ Cleanup failed: {e}")
        
        # Close session
        session.close()
        print("8. Database session closed")
        
        return True
        
    except ImportError as e:
        print(f"   ✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"   ✗ Unexpected error: {e}")
        return False

def test_email_processor_compatibility():
    """Test if email processor can work with new schema"""
    print("\nTesting Email Processor Compatibility:")
    print("-" * 50)
    
    try:
        # Try to import email processor components
        print("1. Testing email processor imports...")
        
        try:
            from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
            print("   ✓ EmailDatabase imported successfully")
        except ImportError as e:
            print(f"   ✗ EmailDatabase import failed: {e}")
            return False
            
        # Test EmailDatabase initialization
        print("2. Testing EmailDatabase initialization...")
        try:
            email_db = EmailDatabase()
            print("   ✓ EmailDatabase initialized successfully")
        except Exception as e:
            print(f"   ✗ EmailDatabase initialization failed: {e}")
            return False
        
        # Test basic operations
        print("3. Testing basic email database operations...")
        try:
            # This should not fail now that mo column exists
            emails = email_db.get_all_emails()
            print(f"   ✓ Successfully retrieved {len(emails)} emails")
            
            # Test email creation
            from datetime import datetime
            
            test_email_data = {
                'message_id': 'processor-test-001',
                'sender': '<EMAIL>',
                'sender_display_name': 'Processor Test',
                'subject': 'Email Processor Compatibility Test',
                'body': 'Testing processor compatibility with new schema',
                'received_time': datetime.now(),
                'has_attachments': False,
                'attachment_count': 0,
                'pd': 'PROC-PD-001',
                'lot': 'PROC-LOT-001',
                'mo': 'PROC-MO-001',
                'yield_value': '98.7',
                'vendor_code': 'PROC-VENDOR'
            }
            
            saved_email = email_db.save_email(test_email_data)
            if saved_email:
                print(f"   ✓ Email saved successfully via processor (ID: {saved_email.id})")
                
                # Clean up
                email_db.delete_email(saved_email.id)
                print("   ✓ Test email cleaned up")
            else:
                print("   ✗ Email saving failed")
                return False
                
        except Exception as e:
            print(f"   ✗ Email processor operations failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ✗ Email processor compatibility test failed: {e}")
        return False

def main():
    print("=" * 70)
    print("EMAIL FUNCTIONALITY VERIFICATION TEST")
    print("=" * 70)
    print("Testing if the database schema repair fixed the email functionality")
    print()
    
    # Run all tests
    database_test = test_email_database_operations()
    processor_test = test_email_processor_compatibility()
    
    print("\n" + "=" * 70)
    print("FINAL TEST RESULTS")
    print("=" * 70)
    
    if database_test and processor_test:
        print("🎉 SUCCESS - All email functionality tests passed!")
        print()
        print("✓ Database schema repair was successful")
        print("✓ Email queries now work correctly") 
        print("✓ The 'mo' column error has been resolved")
        print("✓ Email processor compatibility confirmed")
        print("✓ System should now handle email operations normally")
        print()
        print("The original error:")
        print("'(sqlite3.OperationalError) no such column: emails.mo'")
        print("has been RESOLVED!")
        
        return True
    else:
        print("❌ FAILURE - Some tests failed")
        print()
        if not database_test:
            print("✗ Database operations test failed")
        if not processor_test:
            print("✗ Email processor compatibility test failed")
        print()
        print("Additional investigation may be needed.")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)