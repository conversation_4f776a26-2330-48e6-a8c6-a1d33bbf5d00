# Story 2: 核心追蹤服務實現 - EmailDownloadTracker

## 📋 Story 概要

**Story ID**: EDTS-002  
**Story 名稱**: 核心追蹤服務實現  
**Epic**: 郵件下載狀態追蹤  
**優先級**: P0 (Critical)  
**估計工作量**: 20 Story Points (3-4 工作日)  
**依賴**: Story 1 (資料庫結構創建)

### User Story
**As a** 後端開發者  
**I want** 實現 EmailDownloadTracker 核心服務  
**So that** 系統能夠自動追蹤每個郵件下載的完整生命週期並提供詳細的狀態管理

---

## 🎯 驗收標準

### 必須完成的功能 (Must Have)
- [ ] ✅ 實現 EmailDownloadTracker 核心服務類
- [ ] ✅ 下載開始時自動創建追蹤記錄
- [ ] ✅ 下載成功時更新完成狀態
- [ ] ✅ 下載失敗時更新錯誤信息和安排重試
- [ ] ✅ 支援多種重試策略（指數退避、線性、固定）
- [ ] ✅ 提供狀態查詢和統計功能
- [ ] ✅ 與現有 EmailSyncService 完全整合

### 應該有的功能 (Should Have)
- [ ] ✅ 支援批量狀態查詢
- [ ] ✅ 詳細的錯誤分類和記錄
- [ ] ✅ 性能優化（快取、批處理）
- [ ] ✅ 完整的日誌記錄

### 期望的功能 (Could Have)
- [ ] ✅ 自動清理過期記錄
- [ ] ✅ 狀態變更事件通知
- [ ] ✅ 監控指標收集

---

## 🏗️ 技術實現詳情

### 服務架構設計

```
EmailDownloadTracker (核心服務)
├── DownloadStatusManager (狀態管理)
├── RetryStrategyCalculator (重試策略)
├── StatisticsCollector (統計收集)
├── ErrorClassifier (錯誤分類)
└── PerformanceOptimizer (性能優化)
```

### 實現文件結構

```
backend/
├── email/
│   └── services/
│       ├── email_download_tracker.py           # 主服務
│       ├── download_status_manager.py          # 狀態管理器
│       ├── retry_strategy_calculator.py        # 重試策略計算器
│       └── download_error_classifier.py        # 錯誤分類器
├── shared/
│   └── infrastructure/
│       └── adapters/
│           └── email_inbox/
│               └── email_sync_service.py       # 修改整合
└── tests/
    ├── unit/
    │   └── email/
    │       └── services/
    │           └── test_email_download_tracker.py
    └── integration/
        └── test_download_tracking_integration.py
```

---

## 🔧 詳細實現步驟

### Step 1: 核心服務實現 (2天)

**檔案位置**: `backend/email/services/email_download_tracker.py`

```python
"""
郵件下載追蹤核心服務
實現郵件下載狀態的完整生命週期管理
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from enum import Enum
import json
import asyncio
from contextlib import asynccontextmanager

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.shared.infrastructure.adapters.database.models import db_engine
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    EmailDownloadStatusDB, EmailDownloadRetryLogDB, DownloadStatus, RetryStrategy
)
from backend.email.models.download_tracking_models import DownloadStatusResponse
from backend.email.services.retry_strategy_calculator import RetryStrategyCalculator
from backend.email.services.download_error_classifier import DownloadErrorClassifier

class EmailDownloadTracker:
    """
    郵件下載追蹤核心服務
    
    職責:
    1. 管理郵件下載的完整生命週期
    2. 追蹤下載狀態變更
    3. 計算和安排重試策略
    4. 收集下載統計信息
    5. 提供查詢和管理接口
    """
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("EmailDownloadTracker")
        self.retry_calculator = RetryStrategyCalculator()
        self.error_classifier = DownloadErrorClassifier()
        self._cache = {}  # 簡單快取機制
        self._cache_ttl = 300  # 5分鐘快取
        
    # ==================== 核心生命週期管理 ====================
    
    async def start_download_tracking(
        self, 
        email_id: Optional[int] = None,
        email_message_id: Optional[str] = None,
        max_retry_count: int = 3,
        retry_strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
        retry_interval: int = 60
    ) -> int:
        """
        開始下載追蹤
        
        Args:
            email_id: 郵件 ID（如果已知）
            email_message_id: 郵件 Message ID（用於後續查找）
            max_retry_count: 最大重試次數
            retry_strategy: 重試策略
            retry_interval: 基礎重試間隔（秒）
            
        Returns:
            下載追蹤記錄 ID
            
        Raises:
            ValueError: 參數無效
            RuntimeError: 資料庫操作失敗
        """
        try:
            async with self._get_db_session() as session:
                # 如果沒有 email_id，根據 message_id 查找
                if email_id is None and email_message_id:
                    email_id = await self._find_email_id_by_message_id(session, email_message_id)
                
                if email_id is None:
                    raise ValueError("必須提供 email_id 或 email_message_id")
                
                # 檢查是否已有追蹤記錄
                existing = await self._get_existing_tracking(session, email_id)
                
                if existing and existing.status == DownloadStatus.DOWNLOADING:
                    # 如果已經在下載中，返回現有記錄
                    self.logger.warning(f"郵件已在下載中: email_id={email_id}, tracking_id={existing.id}")
                    return existing.id
                
                if existing:
                    # 重置現有記錄
                    tracking_id = await self._reset_existing_tracking(
                        session, existing, max_retry_count, retry_strategy, retry_interval
                    )
                else:
                    # 創建新記錄
                    tracking_id = await self._create_new_tracking(
                        session, email_id, max_retry_count, retry_strategy, retry_interval
                    )
                
                await session.commit()
                
                # 清除相關快取
                self._invalidate_cache(email_id)
                
                self.logger.info(f"開始郵件下載追蹤: email_id={email_id}, tracking_id={tracking_id}")
                return tracking_id
                
        except Exception as e:
            self.logger.error(f"開始下載追蹤失敗: email_id={email_id}, error={e}")
            raise RuntimeError(f"開始下載追蹤失敗: {e}")
    
    async def update_download_success(
        self, 
        tracking_id: int, 
        download_size: Optional[int] = None,
        duration: Optional[float] = None,
        server_response: Optional[str] = None,
        additional_metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        更新下載成功狀態
        
        Args:
            tracking_id: 追蹤記錄 ID
            download_size: 下載大小（位元組）
            duration: 下載耗時（秒）
            server_response: 服務器響應代碼
            additional_metadata: 額外元數據
            
        Returns:
            是否更新成功
        """
        try:
            async with self._get_db_session() as session:
                status = await self._get_tracking_by_id(session, tracking_id)
                
                if not status:
                    self.logger.warning(f"找不到下載追蹤記錄: tracking_id={tracking_id}")
                    return False
                
                # 更新成功狀態
                status.status = DownloadStatus.COMPLETED
                status.completed_at = datetime.utcnow()
                status.download_size_bytes = download_size
                status.download_duration_seconds = duration
                status.server_response_code = server_response
                
                # 如果有額外元數據，更新 error_details 欄位
                if additional_metadata:
                    status.error_details = json.dumps(additional_metadata)
                
                await session.commit()
                
                # 清除快取
                self._invalidate_cache(status.email_id)
                
                self.logger.info(
                    f"下載成功: tracking_id={tracking_id}, email_id={status.email_id}, "
                    f"size={download_size}, duration={duration}"
                )
                
                # 觸發成功事件（如果需要）
                await self._trigger_success_event(status)
                
                return True
                
        except Exception as e:
            self.logger.error(f"更新下載成功狀態失敗: tracking_id={tracking_id}, error={e}")
            return False
    
    async def update_download_failure(
        self, 
        tracking_id: int, 
        error_type: str,
        error_message: str, 
        error_details: Optional[Dict[str, Any]] = None,
        server_response: Optional[str] = None,
        should_retry: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        更新下載失敗狀態並安排重試
        
        Args:
            tracking_id: 追蹤記錄 ID
            error_type: 錯誤類型
            error_message: 錯誤訊息
            error_details: 詳細錯誤信息
            server_response: 服務器響應代碼
            should_retry: 是否應該重試（None=自動判斷）
            
        Returns:
            包含重試決策信息的字典
        """
        try:
            async with self._get_db_session() as session:
                status = await self._get_tracking_by_id(session, tracking_id)
                
                if not status:
                    self.logger.warning(f"找不到下載追蹤記錄: tracking_id={tracking_id}")
                    return {'success': False, 'reason': 'tracking_not_found'}
                
                # 分類錯誤
                classified_error = self.error_classifier.classify_error(
                    error_type, error_message, error_details
                )
                
                # 更新失敗信息
                status.error_type = classified_error['error_type']
                status.error_message = error_message
                status.error_details = json.dumps(error_details) if error_details else None
                status.server_response_code = server_response
                status.last_retry_at = datetime.utcnow()
                
                # 記錄重試日誌
                await self._log_retry_attempt(session, status, 'failed', error_message)
                
                # 決定是否重試
                if should_retry is None:
                    should_retry = self._should_retry(status, classified_error)
                
                retry_info = {'will_retry': False, 'next_retry_at': None, 'reason': None}
                
                if should_retry and status.download_attempt < status.max_retry_count:
                    # 安排重試
                    retry_info = await self._schedule_retry(session, status)
                else:
                    # 標記為最終失敗
                    status.status = DownloadStatus.FAILED
                    retry_info['reason'] = 'max_retries_exceeded' if status.download_attempt >= status.max_retry_count else 'retry_not_recommended'
                    
                    self.logger.error(
                        f"下載最終失敗: tracking_id={tracking_id}, email_id={status.email_id}, "
                        f"attempts={status.download_attempt}, reason={retry_info['reason']}"
                    )
                
                await session.commit()
                
                # 清除快取
                self._invalidate_cache(status.email_id)
                
                # 觸發失敗事件
                await self._trigger_failure_event(status, classified_error, retry_info)
                
                return {
                    'success': True,
                    'tracking_id': tracking_id,
                    'email_id': status.email_id,
                    'classified_error': classified_error,
                    'retry_info': retry_info
                }
                
        except Exception as e:
            self.logger.error(f"更新下載失敗狀態失敗: tracking_id={tracking_id}, error={e}")
            return {'success': False, 'reason': 'update_error', 'error': str(e)}
    
    # ==================== 查詢和統計功能 ====================
    
    async def get_email_download_status(self, email_id: int) -> Optional[Dict[str, Any]]:
        """獲取特定郵件的下載狀態"""
        try:
            # 檢查快取
            cache_key = f"status_{email_id}"
            if cache_key in self._cache:
                cache_entry = self._cache[cache_key]
                if datetime.utcnow() < cache_entry['expires']:
                    return cache_entry['data']
            
            async with self._get_db_session() as session:
                status = await self._get_existing_tracking(session, email_id)
                
                if not status:
                    return None
                
                result = await self._status_to_dict(status)
                
                # 更新快取
                self._cache[cache_key] = {
                    'data': result,
                    'expires': datetime.utcnow() + timedelta(seconds=self._cache_ttl)
                }
                
                return result
                
        except Exception as e:
            self.logger.error(f"獲取郵件下載狀態失敗: email_id={email_id}, error={e}")
            return None
    
    async def get_download_status_list(
        self, 
        status_filter: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
        order_by: str = 'created_at',
        order_direction: str = 'DESC'
    ) -> List[Dict[str, Any]]:
        """獲取下載狀態列表"""
        try:
            async with self._get_db_session() as session:
                query = session.query(EmailDownloadStatusDB)
                
                # 狀態過濾
                if status_filter:
                    query = query.filter(EmailDownloadStatusDB.status == status_filter)
                
                # 排序
                order_column = getattr(EmailDownloadStatusDB, order_by, EmailDownloadStatusDB.created_at)
                if order_direction.upper() == 'DESC':
                    query = query.order_by(order_column.desc())
                else:
                    query = query.order_by(order_column.asc())
                
                # 分頁
                query = query.offset(offset).limit(limit)
                
                statuses = query.all()
                
                results = []
                for status in statuses:
                    result = await self._status_to_dict(status)
                    results.append(result)
                
                return results
                
        except Exception as e:
            self.logger.error(f"獲取下載狀態列表失敗: error={e}")
            return []
    
    async def get_pending_retries(self, limit: int = 50) -> List[Dict[str, Any]]:
        """獲取待重試的下載任務"""
        try:
            async with self._get_db_session() as session:
                current_time = datetime.utcnow()
                
                pending_retries = session.query(EmailDownloadStatusDB).filter(
                    EmailDownloadStatusDB.status == DownloadStatus.RETRY_SCHEDULED,
                    EmailDownloadStatusDB.next_retry_at <= current_time
                ).limit(limit).all()
                
                results = []
                for status in pending_retries:
                    result = {
                        'tracking_id': status.id,
                        'email_id': status.email_id,
                        'attempt': status.download_attempt,
                        'next_retry_at': status.next_retry_at.isoformat(),
                        'error_type': status.error_type,
                        'error_message': status.error_message,
                        'retry_strategy': status.retry_strategy,
                        'max_retry_count': status.max_retry_count
                    }
                    results.append(result)
                
                self.logger.info(f"獲取待重試任務: {len(results)} 個")
                return results
                
        except Exception as e:
            self.logger.error(f"獲取待重試任務失敗: {e}")
            return []
    
    async def get_download_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """獲取下載統計信息"""
        try:
            # 檢查快取
            cache_key = f"stats_{hours}"
            if cache_key in self._cache:
                cache_entry = self._cache[cache_key]
                if datetime.utcnow() < cache_entry['expires']:
                    return cache_entry['data']
            
            async with self._get_db_session() as session:
                since_time = datetime.utcnow() - timedelta(hours=hours)
                
                # 基本統計
                stats = await self._calculate_basic_statistics(session, since_time)
                
                # 錯誤類型統計
                error_stats = await self._calculate_error_statistics(session, since_time)
                
                # 性能統計
                performance_stats = await self._calculate_performance_statistics(session, since_time)
                
                result = {
                    **stats,
                    'error_types': error_stats,
                    'performance': performance_stats,
                    'period_hours': hours,
                    'last_updated': datetime.utcnow().isoformat()
                }
                
                # 更新快取（統計數據快取時間較短）
                self._cache[cache_key] = {
                    'data': result,
                    'expires': datetime.utcnow() + timedelta(seconds=60)  # 1分鐘快取
                }
                
                return result
                
        except Exception as e:
            self.logger.error(f"獲取下載統計失敗: {e}")
            return {}
    
    # ==================== 管理功能 ====================
    
    async def reset_download_status(self, email_id: int) -> bool:
        """重置郵件下載狀態"""
        try:
            async with self._get_db_session() as session:
                status = await self._get_existing_tracking(session, email_id)
                
                if not status:
                    self.logger.warning(f"找不到下載追蹤記錄: email_id={email_id}")
                    return False
                
                # 刪除重試日誌
                session.query(EmailDownloadRetryLogDB).filter_by(
                    download_status_id=status.id
                ).delete()
                
                # 刪除追蹤記錄
                session.delete(status)
                
                await session.commit()
                
                # 清除快取
                self._invalidate_cache(email_id)
                
                self.logger.info(f"重置郵件下載狀態: email_id={email_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"重置下載狀態失敗: email_id={email_id}, error={e}")
            return False
    
    async def cleanup_old_records(self, days: int = 30) -> Dict[str, int]:
        """清理舊記錄"""
        try:
            async with self._get_db_session() as session:
                cutoff_time = datetime.utcnow() - timedelta(days=days)
                
                # 刪除舊的完成記錄
                completed_query = session.query(EmailDownloadStatusDB).filter(
                    EmailDownloadStatusDB.status == DownloadStatus.COMPLETED,
                    EmailDownloadStatusDB.completed_at < cutoff_time
                )
                completed_count = completed_query.count()
                completed_query.delete()
                
                # 刪除舊的失敗記錄
                failed_query = session.query(EmailDownloadStatusDB).filter(
                    EmailDownloadStatusDB.status == DownloadStatus.FAILED,
                    EmailDownloadStatusDB.created_at < cutoff_time
                )
                failed_count = failed_query.count()
                failed_query.delete()
                
                await session.commit()
                
                result = {
                    'completed_deleted': completed_count,
                    'failed_deleted': failed_count,
                    'total_deleted': completed_count + failed_count
                }
                
                self.logger.info(f"清理舊記錄完成: {result}")
                return result
                
        except Exception as e:
            self.logger.error(f"清理舊記錄失敗: {e}")
            return {'error': str(e)}
    
    # ==================== 私有輔助方法 ====================
    
    @asynccontextmanager
    async def _get_db_session(self):
        """獲取異步資料庫會話"""
        session = db_engine.get_session()
        try:
            yield session
        finally:
            session.close()
    
    async def _find_email_id_by_message_id(self, session, message_id: str) -> Optional[int]:
        """根據 message_id 查找 email_id"""
        from backend.shared.infrastructure.adapters.database.models import EmailDB
        
        email = session.query(EmailDB).filter_by(message_id=message_id).first()
        return email.id if email else None
    
    async def _get_existing_tracking(self, session, email_id: int) -> Optional[EmailDownloadStatusDB]:
        """獲取現有追蹤記錄"""
        return session.query(EmailDownloadStatusDB).filter_by(email_id=email_id).first()
    
    async def _get_tracking_by_id(self, session, tracking_id: int) -> Optional[EmailDownloadStatusDB]:
        """根據 ID 獲取追蹤記錄"""
        return session.query(EmailDownloadStatusDB).filter_by(id=tracking_id).first()
    
    async def _reset_existing_tracking(
        self, session, existing, max_retry_count, retry_strategy, retry_interval
    ) -> int:
        """重置現有追蹤記錄"""
        existing.status = DownloadStatus.DOWNLOADING
        existing.download_attempt += 1
        existing.started_at = datetime.utcnow()
        existing.completed_at = None
        existing.error_type = None
        existing.error_message = None
        existing.error_details = None
        existing.next_retry_at = None
        existing.max_retry_count = max_retry_count
        existing.retry_strategy = retry_strategy
        existing.retry_interval_seconds = retry_interval
        
        self.logger.info(
            f"重置郵件下載追蹤: email_id={existing.email_id}, "
            f"attempt={existing.download_attempt}"
        )
        return existing.id
    
    async def _create_new_tracking(
        self, session, email_id, max_retry_count, retry_strategy, retry_interval
    ) -> int:
        """創建新追蹤記錄"""
        download_status = EmailDownloadStatusDB(
            email_id=email_id,
            status=DownloadStatus.DOWNLOADING,
            max_retry_count=max_retry_count,
            retry_strategy=retry_strategy,
            retry_interval_seconds=retry_interval,
            started_at=datetime.utcnow()
        )
        session.add(download_status)
        session.flush()  # 獲取 ID
        
        self.logger.info(
            f"創建郵件下載追蹤: email_id={email_id}, tracking_id={download_status.id}"
        )
        return download_status.id
    
    async def _schedule_retry(self, session, status) -> Dict[str, Any]:
        """安排重試"""
        # 計算下次重試時間
        next_retry_interval = self.retry_calculator.calculate_next_interval(
            strategy=status.retry_strategy,
            attempt=status.download_attempt,
            base_interval=status.retry_interval_seconds,
            error_type=status.error_type
        )
        
        status.status = DownloadStatus.RETRY_SCHEDULED
        status.next_retry_at = datetime.utcnow() + timedelta(seconds=next_retry_interval)
        status.download_attempt += 1
        
        retry_info = {
            'will_retry': True,
            'next_retry_at': status.next_retry_at.isoformat(),
            'retry_interval': next_retry_interval,
            'attempt': status.download_attempt,
            'max_attempts': status.max_retry_count
        }
        
        self.logger.warning(
            f"安排重試: tracking_id={status.id}, attempt={status.download_attempt}, "
            f"next_retry={status.next_retry_at}"
        )
        
        return retry_info
    
    async def _log_retry_attempt(self, session, status, result, error_message):
        """記錄重試嘗試"""
        retry_log = EmailDownloadRetryLogDB(
            download_status_id=status.id,
            retry_attempt=status.download_attempt,
            retry_reason=status.error_type,
            result=result,
            error_message=error_message,
            retry_strategy_used=status.retry_strategy,
            retry_interval_used=status.retry_interval_seconds
        )
        session.add(retry_log)
    
    def _should_retry(self, status, classified_error) -> bool:
        """判斷是否應該重試"""
        # 根據錯誤類型決定是否重試
        non_retryable_errors = {
            'authentication_error',
            'permission_denied',
            'malformed_request',
            'resource_not_found'
        }
        
        if classified_error['error_type'] in non_retryable_errors:
            return False
        
        # 檢查重試次數
        if status.download_attempt >= status.max_retry_count:
            return False
        
        return True
    
    async def _status_to_dict(self, status: EmailDownloadStatusDB) -> Dict[str, Any]:
        """將狀態記錄轉換為字典"""
        return {
            'id': status.id,
            'email_id': status.email_id,
            'status': status.status,
            'download_attempt': status.download_attempt,
            'max_retry_count': status.max_retry_count,
            'created_at': status.created_at.isoformat(),
            'started_at': status.started_at.isoformat() if status.started_at else None,
            'completed_at': status.completed_at.isoformat() if status.completed_at else None,
            'last_retry_at': status.last_retry_at.isoformat() if status.last_retry_at else None,
            'next_retry_at': status.next_retry_at.isoformat() if status.next_retry_at else None,
            'error_type': status.error_type,
            'error_message': status.error_message,
            'download_size_bytes': status.download_size_bytes,
            'download_duration_seconds': status.download_duration_seconds,
            'retry_strategy': status.retry_strategy,
            'server_response_code': status.server_response_code
        }
    
    async def _calculate_basic_statistics(self, session, since_time) -> Dict[str, Any]:
        """計算基本統計信息"""
        total_downloads = session.query(EmailDownloadStatusDB).filter(
            EmailDownloadStatusDB.created_at >= since_time
        ).count()
        
        successful_downloads = session.query(EmailDownloadStatusDB).filter(
            EmailDownloadStatusDB.created_at >= since_time,
            EmailDownloadStatusDB.status == DownloadStatus.COMPLETED
        ).count()
        
        failed_downloads = session.query(EmailDownloadStatusDB).filter(
            EmailDownloadStatusDB.created_at >= since_time,
            EmailDownloadStatusDB.status == DownloadStatus.FAILED
        ).count()
        
        pending_retries = session.query(EmailDownloadStatusDB).filter(
            EmailDownloadStatusDB.status == DownloadStatus.RETRY_SCHEDULED
        ).count()
        
        success_rate = (successful_downloads / total_downloads * 100) if total_downloads > 0 else 0
        
        return {
            'total_downloads': total_downloads,
            'successful_downloads': successful_downloads,
            'failed_downloads': failed_downloads,
            'pending_retries': pending_retries,
            'success_rate': success_rate
        }
    
    async def _calculate_error_statistics(self, session, since_time) -> List[Dict[str, Any]]:
        """計算錯誤統計信息"""
        from sqlalchemy import text
        
        error_stats = session.execute(text("""
            SELECT error_type, COUNT(*) as count
            FROM email_download_status 
            WHERE created_at >= :since_time AND error_type IS NOT NULL
            GROUP BY error_type
            ORDER BY count DESC
        """), {'since_time': since_time}).fetchall()
        
        return [{'type': row[0], 'count': row[1]} for row in error_stats]
    
    async def _calculate_performance_statistics(self, session, since_time) -> Dict[str, Any]:
        """計算性能統計信息"""
        from sqlalchemy import func
        
        performance_query = session.query(
            func.avg(EmailDownloadStatusDB.download_duration_seconds).label('avg_duration'),
            func.max(EmailDownloadStatusDB.download_duration_seconds).label('max_duration'),
            func.min(EmailDownloadStatusDB.download_duration_seconds).label('min_duration'),
            func.avg(EmailDownloadStatusDB.download_size_bytes).label('avg_size')
        ).filter(
            EmailDownloadStatusDB.created_at >= since_time,
            EmailDownloadStatusDB.status == DownloadStatus.COMPLETED,
            EmailDownloadStatusDB.download_duration_seconds.isnot(None)
        ).first()
        
        return {
            'avg_download_time_seconds': float(performance_query.avg_duration or 0),
            'max_download_time_seconds': float(performance_query.max_duration or 0),
            'min_download_time_seconds': float(performance_query.min_duration or 0),
            'avg_download_size_bytes': int(performance_query.avg_size or 0)
        }
    
    def _invalidate_cache(self, email_id: int):
        """清除特定郵件的快取"""
        cache_key = f"status_{email_id}"
        if cache_key in self._cache:
            del self._cache[cache_key]
        
        # 清除統計快取
        stats_keys = [key for key in self._cache.keys() if key.startswith('stats_')]
        for key in stats_keys:
            del self._cache[key]
    
    async def _trigger_success_event(self, status):
        """觸發成功事件"""
        # 可以在這裡添加事件通知邏輯
        pass
    
    async def _trigger_failure_event(self, status, classified_error, retry_info):
        """觸發失敗事件"""
        # 可以在這裡添加事件通知邏輯
        pass
```

### Step 2: 重試策略計算器 (1天)

**檔案位置**: `backend/email/services/retry_strategy_calculator.py`

```python
"""
重試策略計算器
實現多種重試策略的時間間隔計算
"""

import math
import random
from typing import Union
from enum import Enum

from backend.shared.infrastructure.adapters.database.download_tracking_models import RetryStrategy
from backend.shared.infrastructure.logging.logger_manager import LoggerManager

class RetryStrategyCalculator:
    """
    重試策略計算器
    
    支援的策略:
    1. 指數退避 (Exponential Backoff)
    2. 線性增長 (Linear)
    3. 固定間隔 (Fixed)
    4. 抖動退避 (Jittered Backoff)
    """
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("RetryStrategyCalculator")
        
        # 策略參數配置
        self.config = {
            'exponential': {
                'max_interval': 3600,  # 最大1小時
                'multiplier': 2.0,
                'jitter': True
            },
            'linear': {
                'max_interval': 1800,  # 最大30分鐘
                'increment': 1.0,
                'jitter': False
            },
            'fixed': {
                'jitter': False
            }
        }
    
    def calculate_next_interval(
        self, 
        strategy: Union[RetryStrategy, str],
        attempt: int,
        base_interval: int,
        error_type: str = None
    ) -> int:
        """
        計算下次重試間隔
        
        Args:
            strategy: 重試策略
            attempt: 當前嘗試次數
            base_interval: 基礎間隔（秒）
            error_type: 錯誤類型（用於調整策略）
            
        Returns:
            下次重試間隔（秒）
        """
        if isinstance(strategy, str):
            strategy_str = strategy
        else:
            strategy_str = strategy.value
        
        try:
            # 根據錯誤類型調整基礎間隔
            adjusted_base = self._adjust_base_interval(base_interval, error_type)
            
            if strategy_str == RetryStrategy.EXPONENTIAL:
                interval = self._calculate_exponential(attempt, adjusted_base)
            elif strategy_str == RetryStrategy.LINEAR:
                interval = self._calculate_linear(attempt, adjusted_base)
            elif strategy_str == RetryStrategy.FIXED:
                interval = self._calculate_fixed(adjusted_base)
            else:
                self.logger.warning(f"未知重試策略: {strategy_str}, 使用指數退避")
                interval = self._calculate_exponential(attempt, adjusted_base)
            
            # 應用抖動
            if self.config.get(strategy_str, {}).get('jitter', False):
                interval = self._apply_jitter(interval)
            
            self.logger.debug(
                f"計算重試間隔: strategy={strategy_str}, attempt={attempt}, "
                f"base={adjusted_base}, result={interval}"
            )
            
            return int(interval)
            
        except Exception as e:
            self.logger.error(f"計算重試間隔失敗: {e}")
            return base_interval  # 返回基礎間隔作為後備
    
    def _calculate_exponential(self, attempt: int, base_interval: int) -> int:
        """計算指數退避間隔"""
        config = self.config['exponential']
        multiplier = config['multiplier']
        max_interval = config['max_interval']
        
        # 指數退避: base * (multiplier ^ (attempt - 1))
        interval = base_interval * (multiplier ** (attempt - 1))
        
        # 限制最大間隔
        return min(interval, max_interval)
    
    def _calculate_linear(self, attempt: int, base_interval: int) -> int:
        """計算線性增長間隔"""
        config = self.config['linear']
        increment = config['increment']
        max_interval = config['max_interval']
        
        # 線性增長: base + (increment * base * (attempt - 1))
        interval = base_interval + (increment * base_interval * (attempt - 1))
        
        # 限制最大間隔
        return min(interval, max_interval)
    
    def _calculate_fixed(self, base_interval: int) -> int:
        """計算固定間隔"""
        return base_interval
    
    def _adjust_base_interval(self, base_interval: int, error_type: str) -> int:
        """根據錯誤類型調整基礎間隔"""
        if not error_type:
            return base_interval
        
        # 錯誤類型調整係數
        adjustment_factors = {
            'timeout': 2.0,           # 超時錯誤，增加間隔
            'connection_error': 1.5,  # 連接錯誤，稍微增加
            'server_error': 3.0,      # 服務器錯誤，大幅增加
            'rate_limit': 5.0,        # 速率限制，大幅增加
            'temporary_error': 1.2,   # 臨時錯誤，略微增加
            'authentication_error': 0.5,  # 認證錯誤，減少（通常不會通過重試解決）
        }
        
        factor = adjustment_factors.get(error_type, 1.0)
        adjusted = int(base_interval * factor)
        
        if factor != 1.0:
            self.logger.debug(
                f"根據錯誤類型調整間隔: error_type={error_type}, "
                f"factor={factor}, original={base_interval}, adjusted={adjusted}"
            )
        
        return adjusted
    
    def _apply_jitter(self, interval: int) -> int:
        """應用抖動以避免雷群效應"""
        # 添加 ±25% 的隨機抖動
        jitter_range = interval * 0.25
        jitter = random.uniform(-jitter_range, jitter_range)
        
        jittered_interval = max(1, interval + jitter)  # 確保至少1秒
        
        return int(jittered_interval)
    
    def get_strategy_info(self, strategy: Union[RetryStrategy, str]) -> dict:
        """獲取策略信息"""
        if isinstance(strategy, str):
            strategy_str = strategy
        else:
            strategy_str = strategy.value
        
        strategy_descriptions = {
            RetryStrategy.EXPONENTIAL: {
                'name': '指數退避',
                'description': '每次重試間隔呈指數增長，適合大多數場景',
                'formula': 'base_interval * (2 ^ (attempt - 1))',
                'max_interval': self.config['exponential']['max_interval'],
                'recommended_for': ['網絡錯誤', '臨時服務器錯誤', '超時']
            },
            RetryStrategy.LINEAR: {
                'name': '線性增長',
                'description': '每次重試間隔線性增長，適合穩定的錯誤恢復場景',
                'formula': 'base_interval + (base_interval * (attempt - 1))',
                'max_interval': self.config['linear']['max_interval'],
                'recommended_for': ['資源競爭', '負載過高']
            },
            RetryStrategy.FIXED: {
                'name': '固定間隔',
                'description': '固定重試間隔，適合已知恢復時間的場景',
                'formula': 'base_interval (constant)',
                'max_interval': None,
                'recommended_for': ['定期維護', '批處理錯誤']
            }
        }
        
        return strategy_descriptions.get(strategy_str, {
            'name': '未知策略',
            'description': '未知的重試策略',
            'formula': 'unknown',
            'max_interval': None,
            'recommended_for': []
        })
```

### Step 3: 錯誤分類器 (0.5天)

**檔案位置**: `backend/email/services/download_error_classifier.py`

```python
"""
下載錯誤分類器
將各種錯誤分類並提供重試建議
"""

import re
from typing import Dict, Any, Optional
from backend.shared.infrastructure.logging.logger_manager import LoggerManager

class DownloadErrorClassifier:
    """
    下載錯誤分類器
    
    將錯誤分類為以下類型:
    1. 網絡錯誤 (Network Errors)
    2. 認證錯誤 (Authentication Errors)  
    3. 服務器錯誤 (Server Errors)
    4. 超時錯誤 (Timeout Errors)
    5. 速率限制 (Rate Limiting)
    6. 配置錯誤 (Configuration Errors)
    7. 未知錯誤 (Unknown Errors)
    """
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("DownloadErrorClassifier")
        
        # 錯誤模式定義
        self.error_patterns = {
            'connection_error': [
                r'connection.*refused',
                r'connection.*reset',
                r'connection.*timeout',
                r'network.*unreachable',
                r'host.*unreachable',
                r'no route to host',
                r'connection.*aborted'
            ],
            'authentication_error': [
                r'authentication.*failed',
                r'invalid.*credentials',
                r'unauthorized',
                r'access.*denied',
                r'permission.*denied',
                r'login.*failed',
                r'wrong.*password'
            ],
            'server_error': [
                r'internal.*server.*error',
                r'service.*unavailable',
                r'bad.*gateway',
                r'gateway.*timeout',
                r'server.*error',
                r'http.*5\d\d'
            ],
            'timeout': [
                r'timeout',
                r'timed.*out',
                r'response.*timeout',
                r'read.*timeout',
                r'connection.*timeout'
            ],
            'rate_limit': [
                r'rate.*limit',
                r'too.*many.*requests',
                r'quota.*exceeded',
                r'throttle',
                r'http.*429'
            ],
            'client_error': [
                r'bad.*request',
                r'not.*found',
                r'method.*not.*allowed',
                r'http.*4\d\d'
            ],
            'dns_error': [
                r'dns.*resolution.*failed',
                r'name.*not.*resolved',
                r'hostname.*not.*found',
                r'dns.*lookup.*failed'
            ],
            'ssl_error': [
                r'ssl.*error',
                r'certificate.*error',
                r'handshake.*failed',
                r'ssl.*verification.*failed'
            ]
        }
        
        # 錯誤嚴重程度和重試建議
        self.error_severity = {
            'connection_error': {'severity': 'medium', 'retryable': True, 'priority': 2},
            'authentication_error': {'severity': 'high', 'retryable': False, 'priority': 4},
            'server_error': {'severity': 'medium', 'retryable': True, 'priority': 3},
            'timeout': {'severity': 'low', 'retryable': True, 'priority': 1},
            'rate_limit': {'severity': 'medium', 'retryable': True, 'priority': 5},
            'client_error': {'severity': 'high', 'retryable': False, 'priority': 4},
            'dns_error': {'severity': 'medium', 'retryable': True, 'priority': 2},
            'ssl_error': {'severity': 'high', 'retryable': True, 'priority': 3},
            'unknown': {'severity': 'medium', 'retryable': True, 'priority': 2}
        }
    
    def classify_error(
        self, 
        error_type: str, 
        error_message: str, 
        error_details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分類錯誤
        
        Args:
            error_type: 原始錯誤類型
            error_message: 錯誤訊息
            error_details: 詳細錯誤信息
            
        Returns:
            包含分類結果的字典
        """
        try:
            # 標準化輸入
            normalized_type = error_type.lower() if error_type else ""
            normalized_message = error_message.lower() if error_message else ""
            
            # 首先嘗試直接匹配錯誤類型
            classified_type = self._match_error_type(normalized_type)
            
            # 如果直接匹配失敗，嘗試匹配錯誤訊息
            if classified_type == 'unknown':
                classified_type = self._match_error_message(normalized_message)
            
            # 如果還是未知，嘗試從詳細信息中提取
            if classified_type == 'unknown' and error_details:
                classified_type = self._match_error_details(error_details)
            
            # 獲取錯誤屬性
            error_info = self.error_severity.get(classified_type, self.error_severity['unknown'])
            
            # 生成分類結果
            result = {
                'original_type': error_type,
                'error_type': classified_type,
                'severity': error_info['severity'],
                'retryable': error_info['retryable'],
                'priority': error_info['priority'],
                'classification_confidence': self._calculate_confidence(
                    classified_type, normalized_type, normalized_message
                ),
                'retry_recommendations': self._get_retry_recommendations(classified_type),
                'troubleshooting_tips': self._get_troubleshooting_tips(classified_type)
            }
            
            self.logger.debug(
                f"錯誤分類完成: {error_type} -> {classified_type} "
                f"(confidence: {result['classification_confidence']})"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"錯誤分類失敗: {e}")
            return {
                'original_type': error_type,
                'error_type': 'unknown',
                'severity': 'medium',
                'retryable': True,
                'priority': 2,
                'classification_confidence': 0.0,
                'retry_recommendations': {},
                'troubleshooting_tips': []
            }
    
    def _match_error_type(self, error_type: str) -> str:
        """直接匹配錯誤類型"""
        for classified_type, patterns in self.error_patterns.items():
            for pattern in patterns:
                if re.search(pattern, error_type, re.IGNORECASE):
                    return classified_type
        
        return 'unknown'
    
    def _match_error_message(self, error_message: str) -> str:
        """匹配錯誤訊息"""
        for classified_type, patterns in self.error_patterns.items():
            for pattern in patterns:
                if re.search(pattern, error_message, re.IGNORECASE):
                    return classified_type
        
        return 'unknown'
    
    def _match_error_details(self, error_details: Dict[str, Any]) -> str:
        """從詳細信息中匹配錯誤"""
        # 檢查 HTTP 狀態碼
        if 'status_code' in error_details:
            status_code = error_details['status_code']
            if 400 <= status_code < 500:
                if status_code == 401 or status_code == 403:
                    return 'authentication_error'
                elif status_code == 429:
                    return 'rate_limit'
                else:
                    return 'client_error'
            elif 500 <= status_code < 600:
                return 'server_error'
        
        # 檢查其他詳細信息字段
        searchable_text = ' '.join(str(v) for v in error_details.values() if v)
        return self._match_error_message(searchable_text.lower())
    
    def _calculate_confidence(self, classified_type: str, error_type: str, error_message: str) -> float:
        """計算分類信心度"""
        if classified_type == 'unknown':
            return 0.0
        
        # 基礎信心度
        confidence = 0.5
        
        # 如果錯誤類型直接匹配，增加信心度
        if any(re.search(pattern, error_type, re.IGNORECASE) 
               for pattern in self.error_patterns.get(classified_type, [])):
            confidence += 0.3
        
        # 如果錯誤訊息匹配，增加信心度
        if any(re.search(pattern, error_message, re.IGNORECASE) 
               for pattern in self.error_patterns.get(classified_type, [])):
            confidence += 0.2
        
        return min(confidence, 1.0)
    
    def _get_retry_recommendations(self, error_type: str) -> Dict[str, Any]:
        """獲取重試建議"""
        recommendations = {
            'connection_error': {
                'strategy': 'exponential',
                'max_retries': 5,
                'base_interval': 60,
                'backoff_factor': 2.0
            },
            'authentication_error': {
                'strategy': 'none',
                'max_retries': 0,
                'base_interval': 0,
                'note': '認證錯誤通常無法通過重試解決'
            },
            'server_error': {
                'strategy': 'exponential',
                'max_retries': 3,
                'base_interval': 120,
                'backoff_factor': 2.0
            },
            'timeout': {
                'strategy': 'linear',
                'max_retries': 5,
                'base_interval': 30,
                'increment': 30
            },
            'rate_limit': {
                'strategy': 'exponential',
                'max_retries': 3,
                'base_interval': 300,  # 5分鐘
                'backoff_factor': 3.0
            },
            'client_error': {
                'strategy': 'none',
                'max_retries': 1,
                'base_interval': 0,
                'note': '客戶端錯誤通常需要修復請求'
            },
            'dns_error': {
                'strategy': 'exponential',
                'max_retries': 3,
                'base_interval': 60,
                'backoff_factor': 2.0
            },
            'ssl_error': {
                'strategy': 'exponential',
                'max_retries': 2,
                'base_interval': 60,
                'backoff_factor': 2.0
            }
        }
        
        return recommendations.get(error_type, recommendations['connection_error'])
    
    def _get_troubleshooting_tips(self, error_type: str) -> list:
        """獲取故障排除建議"""
        tips = {
            'connection_error': [
                '檢查網絡連接',
                '確認目標服務器是否可達',
                '檢查防火牆設置',
                '驗證代理配置'
            ],
            'authentication_error': [
                '檢查用戶名和密碼',
                '確認帳戶是否被鎖定',
                '驗證認證令牌是否過期',
                '檢查權限設置'
            ],
            'server_error': [
                '等待服務器恢復',
                '檢查服務器狀態頁面',
                '聯繫服務提供商',
                '嘗試備用服務器'
            ],
            'timeout': [
                '增加超時時間',
                '檢查網絡延遲',
                '優化請求大小',
                '使用更快的網絡連接'
            ],
            'rate_limit': [
                '減少請求頻率',
                '等待配額重置',
                '申請更高的速率限制',
                '實施請求批處理'
            ],
            'client_error': [
                '檢查請求格式',
                '驗證 API 版本',
                '確認請求參數',
                '查看 API 文檔'
            ],
            'dns_error': [
                '檢查 DNS 設置',
                '嘗試不同的 DNS 服務器',
                '清除 DNS 快取',
                '確認域名是否正確'
            ],
            'ssl_error': [
                '檢查 SSL 證書',
                '更新 SSL 庫',
                '驗證證書鏈',
                '檢查系統時間'
            ]
        }
        
        return tips.get(error_type, [
            '檢查系統日誌',
            '重啟相關服務',
            '聯繫技術支援'
        ])
```

### Step 4: EmailSyncService 整合 (0.5天)

**修改檔案**: `backend/shared/infrastructure/adapters/email_inbox/email_sync_service.py`

在現有的 EmailSyncService 中添加下載追蹤整合：

```python
# 在檔案頂部添加導入
from backend.email.services.email_download_tracker import EmailDownloadTracker

class EmailSyncService:
    def __init__(self, database: EmailDatabase = None):
        # ... 現有初始化代碼 ...
        
        # 添加下載追蹤器
        self.download_tracker = EmailDownloadTracker()
    
    async def sync_emails_once(self, max_emails: int = 100) -> Dict[str, Any]:
        """執行一次郵件同步（集成下載追蹤）"""
        sync_count = 0
        sync_errors = 0
        error_details = []
        
        try:
            # ... 現有連接代碼 ...
            
            try:
                # 讀取郵件
                emails = await self.email_reader.read_emails(count=max_emails)
                
                for i, email in enumerate(emails):
                    tracking_id = None
                    email_id = None
                    
                    try:
                        # 開始下載追蹤
                        tracking_id = await self.download_tracker.start_download_tracking(
                            email_message_id=email.message_id,
                            max_retry_count=3
                        )
                        
                        start_time = time.time()
                        
                        # 儲存郵件到資料庫
                        email_id = self.database.save_email(email)
                        
                        end_time = time.time()
                        duration = end_time - start_time
                        
                        if email_id:
                            # 下載成功
                            await self.download_tracker.update_download_success(
                                tracking_id,
                                download_size=len(email.body.encode('utf-8')) if email.body else 0,
                                duration=duration
                            )
                            
                            sync_count += 1
                        else:
                            # 下載失敗
                            if tracking_id:
                                await self.download_tracker.update_download_failure(
                                    tracking_id,
                                    'save_failed',
                                    'Database save returned None'
                                )
                            sync_errors += 1
                            
                    except Exception as e:
                        # 下載異常
                        if tracking_id:
                            await self.download_tracker.update_download_failure(
                                tracking_id,
                                'download_exception',
                                str(e),
                                {'email_index': i, 'total_emails': len(emails), 'email_id': email_id}
                            )
                        sync_errors += 1
                        error_details.append(f"Email {i}: {str(e)}")
                
                # ... 其餘現有代碼 ...
```

---

## 🧪 測試策略

### 單元測試

**檔案位置**: `tests/unit/email/services/test_email_download_tracker.py`

```python
"""
EmailDownloadTracker 單元測試
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from backend.email.services.email_download_tracker import EmailDownloadTracker
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    DownloadStatus, RetryStrategy
)

class TestEmailDownloadTracker:
    
    @pytest.fixture
    def tracker(self):
        return EmailDownloadTracker()
    
    @pytest.fixture
    def mock_session(self):
        session = Mock()
        session.query.return_value.filter_by.return_value.first.return_value = None
        session.commit = AsyncMock()
        return session
    
    @pytest.mark.asyncio
    async def test_start_download_tracking_new_email(self, tracker, mock_session):
        """測試開始追蹤新郵件"""
        with patch.object(tracker, '_get_db_session') as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_session
            
            result = await tracker.start_download_tracking(email_id=1)
            
            assert isinstance(result, int)
            mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_download_success(self, tracker, mock_session):
        """測試更新下載成功狀態"""
        # 創建模擬狀態記錄
        mock_status = Mock()
        mock_status.id = 1
        mock_status.email_id = 1
        mock_session.query.return_value.filter_by.return_value.first.return_value = mock_status
        
        with patch.object(tracker, '_get_db_session') as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_session
            
            result = await tracker.update_download_success(
                tracking_id=1,
                download_size=1024,
                duration=2.5
            )
            
            assert result == True
            assert mock_status.status == DownloadStatus.COMPLETED
            assert mock_status.download_size_bytes == 1024
            assert mock_status.download_duration_seconds == 2.5
    
    @pytest.mark.asyncio
    async def test_update_download_failure_with_retry(self, tracker, mock_session):
        """測試下載失敗並安排重試"""
        mock_status = Mock()
        mock_status.id = 1
        mock_status.email_id = 1
        mock_status.download_attempt = 1
        mock_status.max_retry_count = 3
        mock_status.retry_strategy = RetryStrategy.EXPONENTIAL
        mock_status.retry_interval_seconds = 60
        
        mock_session.query.return_value.filter_by.return_value.first.return_value = mock_status
        
        with patch.object(tracker, '_get_db_session') as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_session
            
            result = await tracker.update_download_failure(
                tracking_id=1,
                error_type='connection_error',
                error_message='Connection timeout'
            )
            
            assert result['success'] == True
            assert result['retry_info']['will_retry'] == True
            assert mock_status.status == DownloadStatus.RETRY_SCHEDULED
    
    @pytest.mark.asyncio
    async def test_get_download_statistics(self, tracker, mock_session):
        """測試獲取下載統計"""
        # 模擬查詢結果
        mock_session.query.return_value.filter.return_value.count.return_value = 100
        mock_session.execute.return_value.fetchall.return_value = [('timeout', 5)]
        
        with patch.object(tracker, '_get_db_session') as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_session
            
            stats = await tracker.get_download_statistics(hours=24)
            
            assert 'total_downloads' in stats
            assert 'success_rate' in stats
            assert 'error_types' in stats
```

### 整合測試

**檔案位置**: `tests/integration/test_download_tracking_integration.py`

```python
"""
下載追蹤整合測試
"""

import pytest
import asyncio
from datetime import datetime

from backend.email.services.email_download_tracker import EmailDownloadTracker
from backend.shared.infrastructure.adapters.database.migration_runner import MigrationRunner

class TestDownloadTrackingIntegration:
    
    @pytest.fixture(scope="class")
    def setup_database(self):
        """設置測試資料庫"""
        runner = MigrationRunner()
        migration_result = runner.run_download_tracking_migration()
        
        yield migration_result
        
        # 清理
        runner.rollback_download_tracking_migration()
    
    @pytest.mark.asyncio
    async def test_full_download_lifecycle(self, setup_database):
        """測試完整下載生命週期"""
        tracker = EmailDownloadTracker()
        
        # 1. 開始追蹤
        tracking_id = await tracker.start_download_tracking(email_id=1)
        assert tracking_id > 0
        
        # 2. 查詢狀態
        status = await tracker.get_email_download_status(email_id=1)
        assert status['status'] == 'downloading'
        
        # 3. 更新成功
        success = await tracker.update_download_success(
            tracking_id=tracking_id,
            download_size=2048,
            duration=1.5
        )
        assert success == True
        
        # 4. 驗證最終狀態
        final_status = await tracker.get_email_download_status(email_id=1)
        assert final_status['status'] == 'completed'
        assert final_status['download_size_bytes'] == 2048
    
    @pytest.mark.asyncio
    async def test_retry_mechanism(self, setup_database):
        """測試重試機制"""
        tracker = EmailDownloadTracker()
        
        # 開始追蹤
        tracking_id = await tracker.start_download_tracking(email_id=2, max_retry_count=2)
        
        # 第一次失敗
        result1 = await tracker.update_download_failure(
            tracking_id=tracking_id,
            error_type='connection_error',
            error_message='Connection timeout'
        )
        assert result1['retry_info']['will_retry'] == True
        
        # 查詢待重試任務
        pending = await tracker.get_pending_retries()
        assert len(pending) > 0
        
        # 第二次失敗（最終失敗）
        result2 = await tracker.update_download_failure(
            tracking_id=tracking_id,
            error_type='connection_error',
            error_message='Connection timeout'
        )
        assert result2['retry_info']['will_retry'] == False
        
        # 驗證最終狀態
        final_status = await tracker.get_email_download_status(email_id=2)
        assert final_status['status'] == 'failed'
```

---

## 📊 性能和監控

### 性能優化要點

1. **資料庫查詢優化**
   - 使用適當的索引
   - 批處理查詢
   - 連接池管理

2. **快取策略**
   - 狀態查詢快取
   - 統計數據快取
   - 錯誤分類快取

3. **異步處理**
   - 非阻塞 I/O
   - 並發限制
   - 資源管理

### 監控指標

1. **功能指標**
   - 追蹤創建速率
   - 成功/失敗比率
   - 重試成功率

2. **性能指標**
   - 響應時間
   - 記憶體使用
   - 資料庫連接數

3. **錯誤指標**
   - 錯誤分類準確性
   - 異常率
   - 系統可用性

---

## 🎯 成功標準

### 功能成功標準
- ✅ 所有核心功能正常工作
- ✅ 與現有系統完全整合
- ✅ 重試策略正確執行
- ✅ 錯誤分類準確率 > 90%

### 性能成功標準
- ✅ 狀態查詢響應時間 < 100ms
- ✅ 統計查詢響應時間 < 200ms
- ✅ 記憶體使用增長 < 50MB
- ✅ 並發處理能力 > 100 requests/sec

### 質量成功標準
- ✅ 單元測試覆蓋率 > 95%
- ✅ 整合測試全部通過
- ✅ 無記憶體洩漏
- ✅ 日誌記錄完整

---

## 📋 交付清單

### 代碼文件
- [ ] `email_download_tracker.py` - 核心服務
- [ ] `retry_strategy_calculator.py` - 重試策略計算器
- [ ] `download_error_classifier.py` - 錯誤分類器
- [ ] `email_sync_service.py` - 整合修改

### 測試文件
- [ ] 完整的單元測試套件
- [ ] 整合測試套件
- [ ] 性能測試腳本

### 文檔
- [ ] API 使用文檔
- [ ] 故障排除指南
- [ ] 性能調優指南

---

**Story 完成標準**: 所有驗收標準通過 ✅ 且交付清單項目完成 📋  

**下一個 Story**: [Story 3: 重試機制實現](./email-download-tracking-story-3-retry-service.md)