# TDD Story Template

## Story Information
**Epic ID**: {epic-id}  
**Story ID**: {story-id}  
**Story Title**: {story-title}  
**Priority**: {High/Medium/Low}  
**Estimated Effort**: {story-points}  
**Assignee**: {developer-name}  
**Created**: {timestamp}  

## Story Description
{detailed-story-description}

## Business Logic Requirements (強制完整)

### 資料模型要求
- **資料模型名稱**: {model-name}
- **主要屬性**: {key-attributes}
- **業務規則**: {business-rules}
- **驗證規則**: {validation-rules}

### 完整 CRUD 操作要求 (強制實現)
#### Create (新增) 要求
- [ ] **API 端點**: POST /{resource}
- [ ] **資料驗證**: {specific-validation-rules}
- [ ] **業務規則驗證**: {business-validation-rules}
- [ ] **權限檢查**: {permission-requirements}
- [ ] **事務處理**: {transaction-requirements}
- [ ] **錯誤處理**: {error-handling-requirements}
- [ ] **審計日誌**: {audit-requirements}

#### Read (查詢) 要求
- [ ] **單筆查詢**: GET /{resource}/{id}
- [ ] **列表查詢**: GET /{resource}
- [ ] **分頁支援**: {pagination-requirements}
- [ ] **篩選支援**: {filtering-requirements}
- [ ] **排序支援**: {sorting-requirements}
- [ ] **權限檢查**: {read-permission-requirements}
- [ ] **性能要求**: {performance-requirements}

#### Update (更新) 要求
- [ ] **API 端點**: PUT/PATCH /{resource}/{id}
- [ ] **部分更新支援**: {partial-update-requirements}
- [ ] **版本控制**: {version-control-requirements}
- [ ] **並發處理**: {concurrency-requirements}
- [ ] **業務規則驗證**: {update-business-rules}
- [ ] **權限檢查**: {update-permission-requirements}
- [ ] **審計日誌**: {update-audit-requirements}

#### Delete (刪除) 要求
- [ ] **API 端點**: DELETE /{resource}/{id}
- [ ] **軟刪除/硬刪除**: {deletion-strategy}
- [ ] **關聯資料處理**: {cascade-delete-rules}
- [ ] **權限檢查**: {delete-permission-requirements}
- [ ] **審計日誌**: {delete-audit-requirements}
- [ ] **恢復機制**: {recovery-requirements}

### 業務流程要求
#### 狀態管理
- **可能狀態**: {list-all-possible-states}
- **狀態轉換規則**: {state-transition-rules}
- **狀態轉換權限**: {state-transition-permissions}
- **狀態轉換事件**: {state-transition-events}

#### 工作流程
- **工作流程步驟**: {workflow-steps}
- **條件分支**: {conditional-branches}
- **回滾機制**: {rollback-mechanisms}
- **異常處理**: {exception-handling}

### 資料一致性要求
- **事務邊界**: {transaction-boundaries}
- **隔離層級**: {isolation-level}
- **鎖定策略**: {locking-strategy}
- **併發控制**: {concurrency-control}
- **資料約束**: {data-constraints}

## TDD Implementation Plan (強制執行)

### Phase 1: Red Phase - Test First
**目標**: 編寫失敗的測試，定義期望行為

#### Unit Tests (單元測試)
```
測試類別: {test-class-name}
測試方法: 
- test_create_{model}_with_valid_data_should_fail()
- test_create_{model}_with_invalid_data_should_fail()
- test_read_{model}_by_id_should_fail()
- test_update_{model}_with_valid_data_should_fail()
- test_delete_{model}_should_fail()
- test_{business_rule}_validation_should_fail()

預期結果: 所有測試應該失敗 (Red 狀態)
失敗原因: 功能尚未實現
```

#### Integration Tests (整合測試)
```
測試類別: {integration-test-class}
測試方法:
- test_{model}_crud_workflow_should_fail()
- test_{model}_business_process_should_fail()
- test_{model}_data_consistency_should_fail()

預期結果: 所有測試應該失敗 (Red 狀態)
失敗原因: 整合邏輯尚未實現
```

#### API Tests (API 測試)
```
測試類別: {api-test-class}
測試方法:
- test_post_{resource}_should_fail()
- test_get_{resource}_should_fail()
- test_put_{resource}_should_fail()
- test_delete_{resource}_should_fail()

預期結果: 所有測試應該失敗 (Red 狀態)
失敗原因: API 端點尚未實現
```

#### Database Tests (資料層測試)
```
測試類別: {database-test-class}
測試方法:
- test_{model}_create_in_database_should_fail()
- test_{model}_query_from_database_should_fail()
- test_{model}_update_in_database_should_fail()
- test_{model}_delete_from_database_should_fail()

預期結果: 所有測試應該失敗 (Red 狀態)
失敗原因: 資料層邏輯尚未實現
```

### Phase 2: Green Phase - Minimal Implementation
**目標**: 實現最小代碼讓所有測試通過

#### 實現策略
```
實現順序:
1. 資料模型定義
2. 基本 CRUD 操作
3. 業務規則驗證
4. 錯誤處理機制
5. 事務處理邏輯
6. API 端點實現
7. 整合邏輯實現

最小實現原則:
- 只實現讓測試通過的代碼
- 不實現測試未涵蓋的功能
- 保持代碼簡單和直接
- 避免過度設計
```

#### 核心業務邏輯實現
```
業務邏輯類別: {business-logic-class}
必須實現的方法:
- create_{model}(data) -> {model}
- get_{model}_by_id(id) -> {model}
- update_{model}(id, data) -> {model}
- delete_{model}(id) -> boolean
- validate_{business_rule}(data) -> boolean

資料訪問層: {data-access-class}
必須實現的方法:
- save_{model}(model) -> {model}
- find_{model}_by_id(id) -> {model}
- update_{model}_by_id(id, data) -> {model}
- delete_{model}_by_id(id) -> boolean
```

#### API 端點實現
```
控制器類別: {controller-class}
必須實現的端點:
- POST /{resource} - create_{resource}()
- GET /{resource}/{id} - get_{resource}(id)
- GET /{resource} - list_{resources}()
- PUT /{resource}/{id} - update_{resource}(id)
- DELETE /{resource}/{id} - delete_{resource}(id)

中介軟體:
- 身份驗證中介軟體
- 授權檢查中介軟體
- 輸入驗證中介軟體
- 錯誤處理中介軟體
```

### Phase 3: Refactor Phase - Quality Enhancement
**目標**: 改善代碼品質同時保持測試通過

#### 重構目標
```
代碼結構改善:
- 提取共用邏輯到服務層
- 改善錯誤處理機制
- 優化資料庫查詢
- 改善 API 回應格式
- 增強日誌記錄

設計模式應用:
- Repository 模式 (資料訪問)
- Service 模式 (業務邏輯)
- Factory 模式 (物件創建)
- Strategy 模式 (業務規則)

性能優化:
- 資料庫索引優化
- 查詢優化
- 快取策略實現
- 批次操作優化
```

#### 重構檢查清單
- [ ] 消除重複代碼
- [ ] 改善方法和類別命名
- [ ] 提取魔術數字為常數
- [ ] 改善錯誤訊息
- [ ] 增強日誌記錄
- [ ] 優化資料庫查詢
- [ ] 改善 API 文檔
- [ ] 增強安全性檢查

## Acceptance Criteria (驗收標準)

### 功能驗收標準
- [ ] **創建功能**: 用戶可以成功創建新的 {model}
- [ ] **查詢功能**: 用戶可以查詢和瀏覽 {model} 清單
- [ ] **更新功能**: 用戶可以編輯和更新現有的 {model}
- [ ] **刪除功能**: 用戶可以刪除不需要的 {model}
- [ ] **權限控制**: 只有授權用戶可以執行相應操作
- [ ] **錯誤處理**: 無效操作顯示適當的錯誤訊息

### 業務邏輯驗收標準 (強制)
- [ ] **完整 CRUD**: 所有四個 CRUD 操作都正確實現
- [ ] **業務規則**: 所有業務規則都正確執行
- [ ] **資料驗證**: 所有輸入資料都經過驗證
- [ ] **狀態管理**: 狀態轉換規則正確執行
- [ ] **工作流程**: 業務流程步驟正確執行
- [ ] **關聯處理**: 資料關聯正確維護

### 資料完整性驗收標準 (強制)
- [ ] **事務一致性**: 所有資料操作都在事務中執行
- [ ] **併發控制**: 併發操作不會導致資料不一致
- [ ] **約束檢查**: 所有資料約束都正確執行
- [ ] **外鍵完整性**: 外鍵關聯正確維護
- [ ] **唯一性約束**: 唯一性要求正確執行

### 錯誤處理驗收標準 (強制)
- [ ] **異常捕獲**: 所有可能的異常都被正確捕獲
- [ ] **錯誤回復**: 錯誤發生時系統可以正確回復
- [ ] **錯誤日誌**: 所有錯誤都被正確記錄
- [ ] **用戶友好**: 錯誤訊息對用戶友好且有意義
- [ ] **安全考量**: 錯誤訊息不洩露敏感信息

### 性能驗收標準
- [ ] **響應時間**: API 響應時間 < {response-time-requirement}
- [ ] **並發處理**: 支援 {concurrent-users} 併發用戶
- [ ] **資料量**: 支援 {data-volume} 筆資料
- [ ] **記憶體使用**: 記憶體使用 < {memory-limit}
- [ ] **CPU 使用**: CPU 使用 < {cpu-limit}

### 安全驗收標準
- [ ] **身份驗證**: 所有操作都需要有效身份驗證
- [ ] **授權檢查**: 權限檢查正確執行
- [ ] **輸入驗證**: 防止注入攻擊
- [ ] **輸出編碼**: 防止 XSS 攻擊
- [ ] **敏感資料**: 敏感資料正確加密

## Test Coverage Requirements (測試覆蓋要求)

### 單元測試覆蓋率
- [ ] **代碼覆蓋率**: ≥ 90%
- [ ] **分支覆蓋率**: ≥ 85%
- [ ] **函數覆蓋率**: = 100%
- [ ] **業務邏輯覆蓋率**: = 100%

### 整合測試覆蓋率
- [ ] **API 端點覆蓋率**: = 100%
- [ ] **業務流程覆蓋率**: = 100%
- [ ] **資料流轉覆蓋率**: = 100%
- [ ] **錯誤處理覆蓋率**: ≥ 80%

### 端到端測試覆蓋率
- [ ] **主要用戶場景**: = 100%
- [ ] **邊緣案例**: ≥ 80%
- [ ] **錯誤恢復場景**: ≥ 70%

## Definition of Done (完成定義)

### 開發完成標準
- [ ] 所有 TDD 循環已完成
- [ ] 所有測試都通過
- [ ] 代碼已通過審查
- [ ] 文檔已更新
- [ ] 業務完整性檢查通過

### 品質標準
- [ ] 無已知的 Bug
- [ ] 性能符合要求
- [ ] 安全檢查通過
- [ ] 可維護性良好
- [ ] 代碼品質符合標準

### 部署準備標準
- [ ] 配置管理完成
- [ ] 環境設定完成
- [ ] 監控設定完成
- [ ] 備份策略實施
- [ ] 部署腳本準備完成

## Dependencies and Constraints (依賴和約束)

### 技術依賴
- **框架**: {framework-dependencies}
- **資料庫**: {database-dependencies}
- **第三方服務**: {external-service-dependencies}
- **開發工具**: {development-tool-dependencies}

### 業務約束
- **時間約束**: {time-constraints}
- **資源約束**: {resource-constraints}
- **合規要求**: {compliance-requirements}
- **效能要求**: {performance-constraints}

### 技術約束
- **架構約束**: {architectural-constraints}
- **安全約束**: {security-constraints}
- **整合約束**: {integration-constraints}
- **維護約束**: {maintenance-constraints}

## Implementation Notes (實現注意事項)

### 開發注意事項
- {specific-implementation-notes}
- {architectural-considerations}
- {security-considerations}
- {performance-considerations}

### 測試注意事項
- {testing-strategy-notes}
- {test-data-requirements}
- {test-environment-setup}
- {test-automation-requirements}

### 部署注意事項
- {deployment-considerations}
- {environment-configurations}
- {monitoring-requirements}
- {backup-strategies}

## Progress Tracking (進度追蹤)

### TDD 循環追蹤
| 循環 | Red Phase | Green Phase | Refactor Phase | 完成時間 | 備註 |
|------|-----------|-------------|----------------|----------|------|
| 1    | [ ]       | [ ]         | [ ]            |          |      |
| 2    | [ ]       | [ ]         | [ ]            |          |      |
| 3    | [ ]       | [ ]         | [ ]            |          |      |

### 驗收標準追蹤
| 標準類別 | 總項目 | 已完成 | 進度 % | 最後更新 |
|----------|--------|--------|--------|----------|
| 功能驗收 |        |        |        |          |
| 業務邏輯 |        |        |        |          |
| 資料完整性 |      |        |        |          |
| 錯誤處理 |        |        |        |          |
| 性能要求 |        |        |        |          |
| 安全要求 |        |        |        |          |

### 完成檢查清單
- [ ] TDD 流程完整執行
- [ ] 所有測試通過
- [ ] 業務完整性驗證通過
- [ ] 代碼審查完成
- [ ] 文檔更新完成
- [ ] 部署準備完成
- [ ] QA 驗收通過

---
**Story Status**: {Draft/In Progress/Dev Complete/QA Review/Done}  
**Last Updated**: {timestamp}  
**Updated By**: {updater-name}
