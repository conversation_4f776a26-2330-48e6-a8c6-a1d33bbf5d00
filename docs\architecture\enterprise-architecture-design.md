# Enterprise Architecture Design - Outlook Summary Project

## 📋 Project Structure Overview

```
D:\project\python\outlook_summary\
├── 📋 README.md                          # 專案總覽
├── 📋 MIGRATION_GUIDE.md                 # 重組指南
├── 🔧 .env.example
├── 🔧 docker-compose.yml
├── 🔧 requirements.txt
├── 🔧 pyproject.toml
│
├── 📁 frontend/                          # 前端主目錄（Flask + HTML/JS）
│   ├── 📁 email/                         # 郵件功能領域
│   │   ├── 📁 templates/                 # HTML 模板
│   │   │   ├── 📄 inbox.html            # 收件匣頁面
│   │   │   ├── 📄 email_detail.html     # 郵件詳情
│   │   │   ├── 📄 email_compose.html    # 撰寫郵件
│   │   │   └── 📄 email_settings.html   # 郵件設定
│   │   ├── 📁 static/                    # 靜態資源
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 email.css         # 郵件專用樣式
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 email-list.js     # 郵件列表邏輯
│   │   │   │   ├── 📄 email-detail.js   # 郵件詳情邏輯
│   │   │   │   └── 📄 email-api.js      # 郵件 API 調用
│   │   │   └── 📁 images/               # 郵件相關圖片
│   │   ├── 📁 components/                # 可重用組件（HTML片段）
│   │   │   ├── 📄 email-card.html       # 郵件卡片組件
│   │   │   ├── 📄 attachment-viewer.html # 附件查看器
│   │   │   └── 📄 email-toolbar.html    # 郵件工具列
│   │   ├── 📁 routes/                    # 路由處理
│   │   │   └── 📄 email_routes.py       # 郵件相關路由
│   │   └── 📋 README.md                  # 郵件模組說明
│   │
│   ├── 📁 analytics/                     # 分析統計功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 dashboard.html        # 統計儀表板
│   │   │   ├── 📄 reports.html          # 報表頁面
│   │   │   ├── 📄 vendor_analysis.html  # 廠商分析
│   │   │   └── 📄 csv_processor.html    # CSV 處理頁面
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 analytics.css     # 統計專用樣式
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 charts.js         # 圖表邏輯
│   │   │   │   ├── 📄 reports.js        # 報表邏輯
│   │   │   │   └── 📄 analytics-api.js  # 統計 API
│   │   │   └── 📁 lib/                  # 第三方庫（Chart.js等）
│   │   ├── 📁 components/
│   │   │   ├── 📄 chart-widget.html     # 圖表組件
│   │   │   ├── 📄 data-table.html       # 數據表格
│   │   │   └── 📄 export-button.html    # 導出按鈕
│   │   ├── 📁 routes/
│   │   │   └── 📄 analytics_routes.py   # 統計路由
│   │   └── 📋 README.md                  # 統計模組說明
│   │
│   ├── 📁 file_management/               # 檔案管理功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 upload.html           # 檔案上傳頁面
│   │   │   ├── 📄 file_browser.html     # 檔案瀏覽器
│   │   │   ├── 📄 batch_processor.html  # 批量處理頁面
│   │   │   └── 📄 export_manager.html   # 導出管理
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 file-management.css
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 upload.js         # 檔案上傳邏輯
│   │   │   │   ├── 📄 file-browser.js   # 檔案瀏覽邏輯
│   │   │   │   └── 📄 batch-processor.js
│   │   │   └── 📁 icons/                # 檔案類型圖標
│   │   ├── 📁 components/
│   │   │   ├── 📄 file-uploader.html    # 檔案上傳組件
│   │   │   ├── 📄 progress-bar.html     # 進度條組件
│   │   │   └── 📄 file-preview.html     # 檔案預覽組件
│   │   ├── 📁 routes/
│   │   │   └── 📄 file_routes.py        # 檔案管理路由
│   │   └── 📋 README.md
│   │
│   ├── 📁 eqc/                          # EQC 品質管制功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 dashboard.html        # EQC 儀表板
│   │   │   ├── 📄 test_results.html     # 測試結果頁面
│   │   │   ├── 📄 quality_metrics.html  # 品質指標
│   │   │   └── 📄 vendor_scorecard.html # 廠商計分卡
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 eqc.css           # EQC 專用樣式
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 quality-charts.js # 品質圖表
│   │   │   │   ├── 📄 test-data.js      # 測試數據處理
│   │   │   │   └── 📄 eqc-api.js        # EQC API
│   │   │   └── 📁 assets/               # EQC 相關資源
│   │   ├── 📁 components/
│   │   │   ├── 📄 quality-gauge.html    # 品質儀表組件
│   │   │   ├── 📄 test-result-card.html # 測試結果卡片
│   │   │   └── 📄 vendor-rating.html    # 廠商評級組件
│   │   ├── 📁 routes/
│   │   │   └── 📄 eqc_routes.py         # EQC 路由
│   │   └── 📋 README.md
│   │
│   ├── 📁 tasks/                        # 任務管理功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 task_list.html        # 任務列表
│   │   │   ├── 📄 task_detail.html      # 任務詳情
│   │   │   ├── 📄 scheduler.html        # 任務調度器
│   │   │   └── 📄 task_history.html     # 任務歷史
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 tasks.css         # 任務專用樣式
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 task-scheduler.js # 任務調度邏輯
│   │   │   │   ├── 📄 task-monitor.js   # 任務監控
│   │   │   │   └── 📄 task-api.js       # 任務 API
│   │   │   └── 📁 icons/                # 任務狀態圖標
│   │   ├── 📁 components/
│   │   │   ├── 📄 task-card.html        # 任務卡片組件
│   │   │   ├── 📄 status-indicator.html # 狀態指示器
│   │   │   └── 📄 progress-tracker.html # 進度追蹤器
│   │   ├── 📁 routes/
│   │   │   └── 📄 task_routes.py        # 任務路由
│   │   └── 📋 README.md
│   │
│   ├── 📁 monitoring/                   # 監控功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 dashboard.html        # 監控儀表板
│   │   │   ├── 📄 system_status.html    # 系統狀態
│   │   │   ├── 📄 performance.html      # 性能監控
│   │   │   └── 📄 alerts.html           # 警報管理
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   └── 📄 monitoring.css    # 監控專用樣式
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 real-time-charts.js # 即時圖表
│   │   │   │   ├── 📄 alert-system.js   # 警報系統
│   │   │   │   └── 📄 monitoring-api.js # 監控 API
│   │   │   └── 📁 widgets/              # 監控小工具
│   │   ├── 📁 components/
│   │   │   ├── 📄 metric-widget.html    # 指標小工具
│   │   │   ├── 📄 alert-panel.html      # 警報面板
│   │   │   └── 📄 status-grid.html      # 狀態網格
│   │   ├── 📁 routes/
│   │   │   └── 📄 monitoring_routes.py  # 監控路由
│   │   └── 📋 README.md
│   │
│   ├── 📁 shared/                       # 共用前端資源
│   │   ├── 📁 templates/
│   │   │   ├── 📄 base.html             # 基礎模板
│   │   │   ├── 📄 layout.html           # 版面配置
│   │   │   ├── 📄 header.html           # 頁首組件
│   │   │   ├── 📄 footer.html           # 頁尾組件
│   │   │   ├── 📄 sidebar.html          # 側邊欄組件
│   │   │   └── 📄 error.html            # 錯誤頁面
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   ├── 📄 global.css        # 全域樣式
│   │   │   │   ├── 📄 theme.css         # 主題樣式
│   │   │   │   ├── 📄 utilities.css     # 工具類樣式
│   │   │   │   └── 📄 responsive.css    # 響應式樣式
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 app.js            # 主應用邏輯
│   │   │   │   ├── 📄 utils.js          # 工具函數
│   │   │   │   ├── 📄 api-client.js     # API 客戶端
│   │   │   │   ├── 📄 websocket.js      # WebSocket 客戶端
│   │   │   │   └── 📄 navigation.js     # 導航邏輯
│   │   │   ├── 📁 lib/                  # 第三方庫
│   │   │   │   ├── 📄 jquery.min.js
│   │   │   │   ├── 📄 bootstrap.min.js
│   │   │   │   ├── 📄 chart.js
│   │   │   │   └── 📄 datatables.min.js
│   │   │   ├── 📁 images/               # 共用圖片資源
│   │   │   │   ├── 📄 logo.png
│   │   │   │   ├── 📄 favicon.ico
│   │   │   │   └── 📁 icons/            # 圖標集
│   │   │   └── 📁 fonts/                # 字體檔案
│   │   ├── 📁 components/               # 全域可重用組件
│   │   │   ├── 📄 loading-spinner.html  # 載入動畫
│   │   │   ├── 📄 modal.html            # 模態框
│   │   │   ├── 📄 notification.html     # 通知組件
│   │   │   ├── 📄 pagination.html       # 分頁組件
│   │   │   ├── 📄 breadcrumb.html       # 麵包屑導航
│   │   │   └── 📄 search-box.html       # 搜尋框
│   │   └── 📋 README.md
│   │
│   ├── 📄 app.py                        # Flask 主應用程式
│   ├── 📄 config.py                     # 前端配置
│   ├── 📄 __init__.py
│   └── 📋 README.md                      # 前端說明文檔
│
├── 📁 backend/                          # 後端主目錄（FastAPI + Python）
│   ├── 📁 shared/                       # 共用後端組件
│   │   ├── 📁 core/                     # 核心基礎設施
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 database.py           # 資料庫連接與配置
│   │   │   ├── 📄 logging.py            # 日誌配置
│   │   │   ├── 📄 cache.py              # 快取配置（Redis）
│   │   │   ├── 📄 security.py           # 安全性配置
│   │   │   ├── 📄 middleware.py         # 中間件
│   │   │   └── 📄 exceptions.py         # 自定義例外處理
│   │   │
│   │   ├── 📁 infrastructure/           # 基礎設施層
│   │   │   ├── 📁 config/               # 配置管理
│   │   │   │   ├── 📄 __init__.py
│   │   │   │   ├── 📄 settings.py       # 應用設定
│   │   │   │   ├── 📄 database.py       # 資料庫設定
│   │   │   │   ├── 📄 logging.py        # 日誌設定
│   │   │   │   └── 📄 environment.py    # 環境變數管理
│   │   │   │
│   │   │   ├── 📁 adapters/             # 外部系統適配器
│   │   │   │   ├── 📄 __init__.py
│   │   │   │   ├── 📄 email_adapter.py  # 郵件系統適配器
│   │   │   │   ├── 📄 file_adapter.py   # 檔案系統適配器
│   │   │   │   ├── 📄 api_adapter.py    # 外部 API 適配器
│   │   │   │   ├── 📄 cache_adapter.py  # 快取適配器
│   │   │   │   └── 📄 notification_adapter.py # 通知適配器
│   │   │   │
│   │   │   ├── 📁 repositories/         # 資料存取層
│   │   │   │   ├── 📄 __init__.py
│   │   │   │   ├── 📄 base_repository.py # 基礎倉庫類
│   │   │   │   ├── 📄 email_repository.py # 郵件資料存取
│   │   │   │   ├── 📄 analytics_repository.py # 分析資料存取
│   │   │   │   ├── 📄 file_repository.py # 檔案資料存取
│   │   │   │   ├── 📄 eqc_repository.py  # EQC 資料存取
│   │   │   │   ├── 📄 task_repository.py # 任務資料存取
│   │   │   │   └── 📄 monitoring_repository.py # 監控資料存取
│   │   │   │
│   │   │   └── 📁 services/             # 基礎設施服務
│   │   │       ├── 📄 __init__.py
│   │   │       ├── 📄 email_service.py  # 郵件發送服務
│   │   │       ├── 📄 file_service.py   # 檔案處理服務
│   │   │       ├── 📄 cache_service.py  # 快取服務
│   │   │       ├── 📄 queue_service.py  # 佇列服務
│   │   │       ├── 📄 notification_service.py # 通知服務
│   │   │       └── 📄 websocket_service.py # WebSocket 服務
│   │   │
│   │   ├── 📁 domain/                   # 領域層（共用領域邏輯）
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 entities/             # 領域實體
│   │   │   │   ├── 📄 __init__.py
│   │   │   │   ├── 📄 base_entity.py    # 基礎實體類
│   │   │   │   ├── 📄 user.py           # 使用者實體
│   │   │   │   ├── 📄 email.py          # 郵件實體
│   │   │   │   ├── 📄 file.py           # 檔案實體
│   │   │   │   └── 📄 task.py           # 任務實體
│   │   │   │
│   │   │   ├── 📁 value_objects/        # 值物件
│   │   │   │   ├── 📄 __init__.py
│   │   │   │   ├── 📄 email_address.py  # 郵件地址值物件
│   │   │   │   ├── 📄 file_path.py      # 檔案路徑值物件
│   │   │   │   ├── 📄 datetime_range.py # 時間範圍值物件
│   │   │   │   └── 📄 pagination.py     # 分頁值物件
│   │   │   │
│   │   │   ├── 📁 events/               # 領域事件
│   │   │   │   ├── 📄 __init__.py
│   │   │   │   ├── 📄 base_event.py     # 基礎事件類
│   │   │   │   ├── 📄 email_events.py   # 郵件相關事件
│   │   │   │   ├── 📄 file_events.py    # 檔案相關事件
│   │   │   │   └── 📄 task_events.py    # 任務相關事件
│   │   │   │
│   │   │   └── 📁 services/             # 領域服務
│   │   │       ├── 📄 __init__.py
│   │   │       ├── 📄 email_parsing_service.py # 郵件解析服務
│   │   │       ├── 📄 file_processing_service.py # 檔案處理服務
│   │   │       ├── 📄 analytics_service.py # 分析服務
│   │   │       └── 📄 notification_service.py # 通知服務
│   │   │
│   │   └── 📋 README.md                  # 共用元件說明
│   │
│   ├── 📁 email/                        # 郵件功能領域後端
│   │   ├── 📁 api/                      # API 層
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 email_api.py          # 郵件 API 端點
│   │   │   ├── 📄 parser_api.py         # 解析 API 端點
│   │   │   ├── 📄 sync_api.py           # 同步 API 端點
│   │   │   └── 📄 schemas.py            # API 資料結構定義
│   │   │
│   │   ├── 📁 domain/                   # 領域層
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 entities/
│   │   │   │   ├── 📄 email.py          # 郵件實體
│   │   │   │   ├── 📄 attachment.py     # 附件實體
│   │   │   │   └── 📄 parsing_result.py # 解析結果實體
│   │   │   ├── 📁 value_objects/
│   │   │   │   ├── 📄 email_content.py  # 郵件內容值物件
│   │   │   │   └── 📄 parsing_config.py # 解析配置值物件
│   │   │   └── 📁 services/
│   │   │       ├── 📄 email_parser.py   # 郵件解析服務
│   │   │       ├── 📄 content_extractor.py # 內容提取服務
│   │   │       └── 📄 llm_analyzer.py   # LLM 分析服務
│   │   │
│   │   ├── 📁 application/              # 應用層
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 services/
│   │   │   │   ├── 📄 email_service.py  # 郵件應用服務
│   │   │   │   ├── 📄 parsing_service.py # 解析應用服務
│   │   │   │   └── 📄 sync_service.py   # 同步應用服務
│   │   │   ├── 📁 handlers/
│   │   │   │   ├── 📄 email_handlers.py # 郵件命令處理器
│   │   │   │   └── 📄 parsing_handlers.py # 解析命令處理器
│   │   │   └── 📁 queries/
│   │   │       ├── 📄 email_queries.py  # 郵件查詢
│   │   │       └── 📄 parsing_queries.py # 解析查詢
│   │   │
│   │   ├── 📁 infrastructure/           # 基礎設施層
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 models/
│   │   │   │   ├── 📄 email_model.py    # 郵件資料模型
│   │   │   │   └── 📄 parsing_model.py  # 解析資料模型
│   │   │   ├── 📁 repositories/
│   │   │   │   ├── 📄 email_repository.py # 郵件倉庫實作
│   │   │   │   └── 📄 parsing_repository.py # 解析倉庫實作
│   │   │   └── 📁 external/
│   │   │       ├── 📄 outlook_client.py # Outlook 客戶端
│   │   │       └── 📄 llm_client.py     # LLM 客戶端
│   │   │
│   │   └── 📋 README.md
│   │
│   ├── 📁 analytics/                    # 分析統計功能領域後端
│   │   ├── 📁 api/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 analytics_api.py      # 分析 API 端點
│   │   │   ├── 📄 reports_api.py        # 報表 API 端點
│   │   │   └── 📄 schemas.py
│   │   │
│   │   ├── 📁 domain/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 entities/
│   │   │   │   ├── 📄 report.py         # 報表實體
│   │   │   │   ├── 📄 metric.py         # 指標實體
│   │   │   │   └── 📄 chart.py          # 圖表實體
│   │   │   ├── 📁 value_objects/
│   │   │   │   ├── 📄 time_period.py    # 時間週期值物件
│   │   │   │   └── 📄 aggregation.py    # 聚合值物件
│   │   │   └── 📁 services/
│   │   │       ├── 📄 data_aggregator.py # 資料聚合服務
│   │   │       ├── 📄 chart_generator.py # 圖表生成服務
│   │   │       └── 📄 export_service.py # 導出服務
│   │   │
│   │   ├── 📁 application/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 services/
│   │   │   │   ├── 📄 analytics_service.py # 分析應用服務
│   │   │   │   └── 📄 reporting_service.py # 報表應用服務
│   │   │   ├── 📁 handlers/
│   │   │   │   └── 📄 analytics_handlers.py # 分析命令處理器
│   │   │   └── 📁 queries/
│   │   │       └── 📄 analytics_queries.py # 分析查詢
│   │   │
│   │   ├── 📁 infrastructure/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 models/
│   │   │   │   └── 📄 analytics_model.py # 分析資料模型
│   │   │   ├── 📁 repositories/
│   │   │   │   └── 📄 analytics_repository.py # 分析倉庫實作
│   │   │   └── 📁 external/
│   │   │       └── 📄 chart_service.py  # 圖表服務
│   │   │
│   │   └── 📋 README.md
│   │
│   ├── 📁 file_management/              # 檔案管理功能領域後端
│   │   ├── 📁 api/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 file_api.py           # 檔案 API 端點
│   │   │   ├── 📄 upload_api.py         # 上傳 API 端點
│   │   │   ├── 📄 batch_api.py          # 批量處理 API 端點
│   │   │   └── 📄 schemas.py
│   │   │
│   │   ├── 📁 domain/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 entities/
│   │   │   │   ├── 📄 file.py           # 檔案實體
│   │   │   │   ├── 📄 upload_session.py # 上傳會話實體
│   │   │   │   └── 📄 batch_job.py      # 批量作業實體
│   │   │   ├── 📁 value_objects/
│   │   │   │   ├── 📄 file_metadata.py  # 檔案元數據值物件
│   │   │   │   └── 📄 file_path.py      # 檔案路徑值物件
│   │   │   └── 📁 services/
│   │   │       ├── 📄 file_processor.py # 檔案處理服務
│   │   │       ├── 📄 virus_scanner.py  # 病毒掃描服務
│   │   │       └── 📄 compression_service.py # 壓縮服務
│   │   │
│   │   ├── 📁 application/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 services/
│   │   │   │   ├── 📄 file_service.py   # 檔案應用服務
│   │   │   │   └── 📄 batch_service.py  # 批量應用服務
│   │   │   ├── 📁 handlers/
│   │   │   │   └── 📄 file_handlers.py  # 檔案命令處理器
│   │   │   └── 📁 queries/
│   │   │       └── 📄 file_queries.py   # 檔案查詢
│   │   │
│   │   ├── 📁 infrastructure/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 models/
│   │   │   │   └── 📄 file_model.py     # 檔案資料模型
│   │   │   ├── 📁 repositories/
│   │   │   │   └── 📄 file_repository.py # 檔案倉庫實作
│   │   │   └── 📁 storage/
│   │   │       ├── 📄 local_storage.py  # 本地儲存
│   │   │       └── 📄 cloud_storage.py  # 雲端儲存
│   │   │
│   │   └── 📋 README.md
│   │
│   ├── 📁 eqc/                         # EQC 品質管制功能領域後端
│   │   ├── 📁 api/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 eqc_api.py           # EQC API 端點
│   │   │   ├── 📄 quality_api.py       # 品質 API 端點
│   │   │   ├── 📄 vendor_api.py        # 廠商 API 端點
│   │   │   └── 📄 schemas.py
│   │   │
│   │   ├── 📁 domain/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 entities/
│   │   │   │   ├── 📄 test_result.py    # 測試結果實體
│   │   │   │   ├── 📄 quality_metric.py # 品質指標實體
│   │   │   │   └── 📄 vendor.py         # 廠商實體
│   │   │   ├── 📁 value_objects/
│   │   │   │   ├── 📄 test_criteria.py  # 測試標準值物件
│   │   │   │   └── 📄 quality_score.py  # 品質分數值物件
│   │   │   └── 📁 services/
│   │   │       ├── 📄 quality_analyzer.py # 品質分析服務
│   │   │       ├── 📄 test_executor.py  # 測試執行服務
│   │   │       └── 📄 vendor_evaluator.py # 廠商評估服務
│   │   │
│   │   ├── 📁 application/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 services/
│   │   │   │   ├── 📄 eqc_service.py    # EQC 應用服務
│   │   │   │   └── 📄 quality_service.py # 品質應用服務
│   │   │   ├── 📁 handlers/
│   │   │   │   └── 📄 eqc_handlers.py   # EQC 命令處理器
│   │   │   └── 📁 queries/
│   │   │       └── 📄 eqc_queries.py    # EQC 查詢
│   │   │
│   │   ├── 📁 infrastructure/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 models/
│   │   │   │   └── 📄 eqc_model.py      # EQC 資料模型
│   │   │   ├── 📁 repositories/
│   │   │   │   └── 📄 eqc_repository.py # EQC 倉庫實作
│   │   │   └── 📁 external/
│   │   │       └── 📄 testing_equipment.py # 測試設備介面
│   │   │
│   │   └── 📋 README.md
│   │
│   ├── 📁 tasks/                       # 任務管理功能領域後端
│   │   ├── 📁 api/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 task_api.py          # 任務 API 端點
│   │   │   ├── 📄 scheduler_api.py     # 調度器 API 端點
│   │   │   └── 📄 schemas.py
│   │   │
│   │   ├── 📁 domain/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 entities/
│   │   │   │   ├── 📄 task.py           # 任務實體
│   │   │   │   ├── 📄 schedule.py       # 排程實體
│   │   │   │   └── 📄 execution.py      # 執行實體
│   │   │   ├── 📁 value_objects/
│   │   │   │   ├── 📄 task_config.py    # 任務配置值物件
│   │   │   │   └── 📄 cron_expression.py # Cron 表達式值物件
│   │   │   └── 📁 services/
│   │   │       ├── 📄 task_executor.py  # 任務執行服務
│   │   │       ├── 📄 scheduler.py      # 調度器服務
│   │   │       └── 📄 monitor.py        # 監控服務
│   │   │
│   │   ├── 📁 application/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 services/
│   │   │   │   ├── 📄 task_service.py   # 任務應用服務
│   │   │   │   └── 📄 scheduling_service.py # 調度應用服務
│   │   │   ├── 📁 handlers/
│   │   │   │   └── 📄 task_handlers.py  # 任務命令處理器
│   │   │   └── 📁 queries/
│   │   │       └── 📄 task_queries.py   # 任務查詢
│   │   │
│   │   ├── 📁 infrastructure/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 models/
│   │   │   │   └── 📄 task_model.py     # 任務資料模型
│   │   │   ├── 📁 repositories/
│   │   │   │   └── 📄 task_repository.py # 任務倉庫實作
│   │   │   └── 📁 workers/
│   │   │       ├── 📄 celery_worker.py  # Celery 工作者
│   │   │       └── 📄 dramatiq_worker.py # Dramatiq 工作者
│   │   │
│   │   └── 📋 README.md
│   │
│   ├── 📁 monitoring/                  # 監控功能領域後端
│   │   ├── 📁 api/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 monitoring_api.py    # 監控 API 端點
│   │   │   ├── 📄 metrics_api.py       # 指標 API 端點
│   │   │   ├── 📄 alerts_api.py        # 警報 API 端點
│   │   │   └── 📄 schemas.py
│   │   │
│   │   ├── 📁 domain/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 entities/
│   │   │   │   ├── 📄 metric.py         # 指標實體
│   │   │   │   ├── 📄 alert.py          # 警報實體
│   │   │   │   └── 📄 dashboard.py      # 儀表板實體
│   │   │   ├── 📁 value_objects/
│   │   │   │   ├── 📄 threshold.py      # 閾值值物件
│   │   │   │   └── 📄 alert_rule.py     # 警報規則值物件
│   │   │   └── 📁 services/
│   │   │       ├── 📄 metric_collector.py # 指標收集服務
│   │   │       ├── 📄 alert_manager.py  # 警報管理服務
│   │   │       └── 📄 dashboard_builder.py # 儀表板建構服務
│   │   │
│   │   ├── 📁 application/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 services/
│   │   │   │   ├── 📄 monitoring_service.py # 監控應用服務
│   │   │   │   └── 📄 alerting_service.py # 警報應用服務
│   │   │   ├── 📁 handlers/
│   │   │   │   └── 📄 monitoring_handlers.py # 監控命令處理器
│   │   │   └── 📁 queries/
│   │   │       └── 📄 monitoring_queries.py # 監控查詢
│   │   │
│   │   ├── 📁 infrastructure/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📁 models/
│   │   │   │   └── 📄 monitoring_model.py # 監控資料模型
│   │   │   ├── 📁 repositories/
│   │   │   │   └── 📄 monitoring_repository.py # 監控倉庫實作
│   │   │   ├── 📁 collectors/
│   │   │   │   ├── 📄 system_collector.py # 系統指標收集器
│   │   │   │   ├── 📄 application_collector.py # 應用指標收集器
│   │   │   │   └── 📄 custom_collector.py # 自定義指標收集器
│   │   │   └── 📁 exporters/
│   │   │       ├── 📄 prometheus_exporter.py # Prometheus 導出器
│   │   │       └── 📄 grafana_exporter.py # Grafana 導出器
│   │   │
│   │   └── 📋 README.md
│   │
│   ├── 📄 main.py                      # FastAPI 主應用程式
│   ├── 📄 config.py                    # 後端配置
│   ├── 📄 __init__.py
│   └── 📋 README.md                     # 後端說明文檔
│
├── 📁 docs/                            # 文檔目錄
│   ├── 📁 api/                         # API 文檔
│   │   ├── 📄 README.md
│   │   ├── 📄 openapi.yaml             # OpenAPI 規格
│   │   ├── 📄 email_api.md             # 郵件 API 文檔
│   │   ├── 📄 analytics_api.md         # 分析 API 文檔
│   │   ├── 📄 file_api.md              # 檔案 API 文檔
│   │   ├── 📄 eqc_api.md               # EQC API 文檔
│   │   ├── 📄 task_api.md              # 任務 API 文檔
│   │   └── 📄 monitoring_api.md        # 監控 API 文檔
│   │
│   ├── 📁 architecture/                # 架構文檔
│   │   ├── 📄 README.md
│   │   ├── 📄 system_overview.md       # 系統概覽
│   │   ├── 📄 frontend_architecture.md # 前端架構
│   │   ├── 📄 backend_architecture.md  # 後端架構
│   │   ├── 📄 database_design.md       # 資料庫設計
│   │   ├── 📄 security_design.md       # 安全性設計
│   │   └── 📄 deployment_architecture.md # 部署架構
│   │
│   ├── 📁 development/                 # 開發文檔
│   │   ├── 📄 README.md
│   │   ├── 📄 setup_guide.md           # 設置指南
│   │   ├── 📄 coding_standards.md      # 編碼標準
│   │   ├── 📄 testing_guide.md         # 測試指南
│   │   ├── 📄 debugging_guide.md       # 除錯指南
│   │   └── 📄 contribution_guide.md    # 貢獻指南
│   │
│   ├── 📁 deployment/                  # 部署文檔
│   │   ├── 📄 README.md
│   │   ├── 📄 docker_guide.md          # Docker 部署指南
│   │   ├── 📄 kubernetes_guide.md      # Kubernetes 部署指南
│   │   ├── 📄 production_guide.md      # 生產環境指南
│   │   └── 📄 monitoring_setup.md      # 監控設置
│   │
│   ├── 📁 user/                        # 使用者文檔
│   │   ├── 📄 README.md
│   │   ├── 📄 user_manual.md           # 使用者手冊
│   │   ├── 📄 admin_guide.md           # 管理員指南
│   │   ├── 📄 troubleshooting.md       # 故障排除
│   │   └── 📄 faq.md                   # 常見問題
│   │
│   └── 📁 migration/                   # 遷移文檔
│       ├── 📄 README.md
│       ├── 📄 migration_plan.md        # 遷移計劃
│       ├── 📄 data_migration.md        # 資料遷移
│       ├── 📄 version_upgrade.md       # 版本升級
│       └── 📄 rollback_plan.md         # 回滾計劃
│
├── 📁 database/                        # 資料庫相關
│   ├── 📁 migrations/                  # 資料庫遷移
│   │   ├── 📄 __init__.py
│   │   ├── 📄 001_initial_schema.py    # 初始架構
│   │   ├── 📄 002_add_email_tables.py  # 郵件表格
│   │   ├── 📄 003_add_analytics_tables.py # 分析表格
│   │   └── 📄 004_add_monitoring_tables.py # 監控表格
│   │
│   ├── 📁 seeds/                       # 種子資料
│   │   ├── 📄 __init__.py
│   │   ├── 📄 users.py                 # 使用者種子資料
│   │   ├── 📄 test_emails.py           # 測試郵件資料
│   │   └── 📄 sample_data.py           # 範例資料
│   │
│   ├── 📁 models/                      # 資料庫模型
│   │   ├── 📄 __init__.py
│   │   ├── 📄 base.py                  # 基礎模型
│   │   ├── 📄 user.py                  # 使用者模型
│   │   ├── 📄 email.py                 # 郵件模型
│   │   ├── 📄 analytics.py             # 分析模型
│   │   ├── 📄 file.py                  # 檔案模型
│   │   ├── 📄 task.py                  # 任務模型
│   │   └── 📄 monitoring.py            # 監控模型
│   │
│   ├── 📄 database.py                  # 資料庫配置
│   ├── 📄 connection.py                # 資料庫連接
│   └── 📋 README.md
│
├── 📁 tests/                           # 測試目錄
│   ├── 📁 unit/                        # 單元測試
│   │   ├── 📁 frontend/
│   │   │   ├── 📁 email/
│   │   │   ├── 📁 analytics/
│   │   │   ├── 📁 file_management/
│   │   │   ├── 📁 eqc/
│   │   │   ├── 📁 tasks/
│   │   │   └── 📁 monitoring/
│   │   └── 📁 backend/
│   │       ├── 📁 email/
│   │       ├── 📁 analytics/
│   │       ├── 📁 file_management/
│   │       ├── 📁 eqc/
│   │       ├── 📁 tasks/
│   │       └── 📁 monitoring/
│   │
│   ├── 📁 integration/                 # 整合測試
│   │   ├── 📁 api/
│   │   ├── 📁 database/
│   │   └── 📁 services/
│   │
│   ├── 📁 e2e/                         # 端對端測試
│   │   ├── 📁 email/
│   │   ├── 📁 analytics/
│   │   ├── 📁 file_management/
│   │   ├── 📁 eqc/
│   │   ├── 📁 tasks/
│   │   └── 📁 monitoring/
│   │
│   ├── 📁 fixtures/                    # 測試夾具
│   │   ├── 📄 sample_emails.json
│   │   ├── 📄 test_data.json
│   │   └── 📄 mock_responses.json
│   │
│   ├── 📄 conftest.py                  # pytest 配置
│   ├── 📄 test_config.py               # 測試配置
│   └── 📋 README.md
│
├── 📁 scripts/                         # 工具腳本
│   ├── 📁 deployment/                  # 部署腳本
│   │   ├── 📄 deploy.sh                # 部署腳本
│   │   ├── 📄 docker-build.sh          # Docker 建構
│   │   └── 📄 migrate.sh               # 遷移腳本
│   │
│   ├── 📁 development/                 # 開發工具
│   │   ├── 📄 setup.sh                 # 環境設置
│   │   ├── 📄 run-tests.sh             # 測試執行
│   │   └── 📄 format-code.sh           # 程式碼格式化
│   │
│   ├── 📁 maintenance/                 # 維護腳本
│   │   ├── 📄 backup.sh                # 備份腳本
│   │   ├── 📄 cleanup.sh               # 清理腳本
│   │   └── 📄 health-check.sh          # 健康檢查
│   │
│   └── 📋 README.md
│
├── 📁 config/                          # 配置檔案
│   ├── 📁 environments/                # 環境配置
│   │   ├── 📄 development.yaml         # 開發環境
│   │   ├── 📄 staging.yaml             # 測試環境
│   │   ├── 📄 production.yaml          # 生產環境
│   │   └── 📄 local.yaml               # 本地環境
│   │
│   ├── 📁 nginx/                       # Nginx 配置
│   │   ├── 📄 nginx.conf               # 主配置
│   │   ├── 📄 sites-available/         # 可用站點
│   │   └── 📄 ssl/                     # SSL 憑證
│   │
│   ├── 📁 docker/                      # Docker 配置
│   │   ├── 📄 Dockerfile.frontend      # 前端 Dockerfile
│   │   ├── 📄 Dockerfile.backend       # 後端 Dockerfile
│   │   └── 📄 docker-compose.yml       # 容器編排
│   │
│   └── 📋 README.md
│
├── 📁 logs/                            # 日誌目錄
│   ├── 📄 application.log              # 應用日誌
│   ├── 📄 error.log                    # 錯誤日誌
│   ├── 📄 access.log                   # 存取日誌
│   └── 📁 archived/                    # 歸檔日誌
│
├── 📁 uploads/                         # 上傳檔案目錄
│   ├── 📁 emails/                      # 郵件附件
│   ├── 📁 csv/                         # CSV 檔案
│   ├── 📁 reports/                     # 報表檔案
│   └── 📁 temp/                        # 暫存檔案
│
├── 📁 static/                          # 靜態檔案（生產環境）
│   ├── 📁 css/
│   ├── 📁 js/
│   ├── 📁 images/
│   └── 📁 fonts/
│
└── 📁 .github/                         # GitHub 配置
    ├── 📁 workflows/                   # GitHub Actions
    │   ├── 📄 ci.yml                   # 持續整合
    │   ├── 📄 cd.yml                   # 持續部署
    │   └── 📄 tests.yml                # 測試工作流
    │
    ├── 📄 ISSUE_TEMPLATE.md            # 問題模板
    ├── 📄 PULL_REQUEST_TEMPLATE.md     # PR 模板
    └── 📄 CODEOWNERS                   # 代碼擁有者
```

## 🏗️ Architecture Design Principles

### **Domain-Driven Design (DDD)**
- **功能領域劃分**: 按業務功能（Email, Analytics, File Management, EQC, Tasks, Monitoring）劃分
- **清晰邊界**: 每個領域都有明確的職責和邊界
- **共用核心**: 將通用功能抽取到 shared 模組

### **Clean Architecture**
- **分層架構**: API → Application → Domain → Infrastructure
- **依賴反轉**: 高層模組不依賴低層模組
- **關注點分離**: 每層有明確的職責

### **微服務準備**
- **模組化設計**: 每個功能領域都可以獨立部署
- **API 優先**: 明確的 API 介面定義
- **資料獨立**: 每個領域管理自己的資料

### **前後端分離**
- **獨立開發**: 前端和後端可以獨立開發和部署
- **API 溝通**: 通過 RESTful API 進行資料交換
- **技術選擇自由**: 前後端可以選擇不同的技術棧

## 🔧 Technology Stack

### **Frontend Technology**
- **Framework**: Flask (current) → Vue.js 3 (migration target)
- **UI Library**: Bootstrap 5 + Custom CSS
- **Build Tools**: Webpack / Vite (for Vue.js)
- **State Management**: Vuex / Pinia (for Vue.js)
- **HTTP Client**: Axios / Fetch API

### **Backend Technology**
- **Framework**: FastAPI (primary) + Flask (legacy)
- **Database**: SQLite (development) → PostgreSQL (production)
- **ORM**: SQLAlchemy
- **Task Queue**: Dramatiq + Redis
- **Caching**: Redis
- **API Documentation**: OpenAPI / Swagger

### **Infrastructure**
- **Containerization**: Docker + Docker Compose
- **Reverse Proxy**: Nginx
- **Monitoring**: Prometheus + Grafana
- **Logging**: Python logging + ELK Stack (optional)
- **CI/CD**: GitHub Actions

## 📊 Database Design

### **Core Tables**
```sql
-- Users table
users (id, username, email, role, created_at, updated_at)

-- Emails table  
emails (id, subject, sender, recipient, content, attachments, status, created_at)

-- Parsing results table
parsing_results (id, email_id, mo_number, lot_number, vendor_code, product_code, yield_value, created_at)

-- Files table
files (id, filename, filepath, size, mimetype, upload_time, status)

-- Tasks table
tasks (id, name, type, status, config, result, scheduled_at, started_at, completed_at)

-- Monitoring metrics table
monitoring_metrics (id, metric_name, value, timestamp, tags)
```

### **Relationships**
- **Users** ↔ **Emails**: One-to-Many
- **Emails** ↔ **Parsing Results**: One-to-One
- **Emails** ↔ **Files**: Many-to-Many (attachments)
- **Tasks** ↔ **Files**: One-to-Many
- **Users** ↔ **Tasks**: Many-to-Many (task assignments)

## 🔒 Security Design

### **Authentication & Authorization**
- **API Key Authentication**: For service-to-service communication
- **JWT Tokens**: For user authentication (future enhancement)
- **Role-Based Access Control**: Admin, User, Viewer roles
- **Environment-Based Security**: Development bypass options

### **Data Protection**
- **Input Validation**: Comprehensive validation for all inputs
- **XSS Prevention**: Input sanitization and output encoding
- **SQL Injection Prevention**: Parameterized queries via ORM
- **File Upload Security**: File type validation and virus scanning

### **Security Headers**
```python
# Security headers implementation
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000
```

## 🚀 Deployment Architecture

### **Development Environment**
```yaml
Services:
  - Flask Frontend: localhost:8000
  - FastAPI Backend: localhost:8010
  - SQLite Database: local file
  - Redis: localhost:6379
  - File Storage: local directory
```

### **Production Environment**
```yaml
Load Balancer (Nginx)
  ├── Frontend Containers (Flask/Vue.js)
  ├── Backend Containers (FastAPI)
  ├── Database (PostgreSQL)
  ├── Cache (Redis Cluster)
  ├── Task Queue (Dramatiq Workers)
  ├── File Storage (S3/MinIO)
  └── Monitoring (Prometheus/Grafana)
```

### **Scaling Strategy**
- **Horizontal Scaling**: Multiple backend instances behind load balancer
- **Database Scaling**: Read replicas and connection pooling
- **Task Queue Scaling**: Multiple worker instances
- **File Storage Scaling**: Object storage with CDN

## 📈 Performance Considerations

### **Backend Optimization**
- **Async Processing**: FastAPI with async/await
- **Connection Pooling**: Database connection management
- **Caching Strategy**: Redis for frequently accessed data
- **Background Tasks**: Dramatiq for heavy processing

### **Frontend Optimization**
- **Code Splitting**: Lazy loading of modules
- **Asset Optimization**: Minification and compression
- **CDN Integration**: Static asset delivery
- **Progressive Loading**: Skeleton screens and lazy loading

### **Database Optimization**
- **Indexing Strategy**: Optimized database indexes
- **Query Optimization**: Efficient query patterns
- **Data Archiving**: Archive old data to separate tables
- **Connection Management**: Connection pooling and timeout settings

## 🔄 Migration Strategy

### **Phase 1: Backend Restructure** ✅ **COMPLETED**
- Domain-driven backend structure
- Clean architecture implementation
- API standardization
- Security enhancements

### **Phase 2: Frontend Modernization** 🔄 **IN PROGRESS**
- Vue.js 3 integration
- Component-based architecture
- Modern build tools setup
- Progressive migration approach

### **Phase 3: Production Readiness** 📋 **PLANNED**
- Docker containerization
- CI/CD pipeline setup
- Monitoring and logging
- Performance optimization

### **Phase 4: Advanced Features** 🔮 **FUTURE**
- Microservices architecture
- Advanced monitoring
- AI/ML integration
- Real-time features

## 🎯 Benefits of This Architecture

### **Maintainability**
- **Clear Structure**: Easy to navigate and understand
- **Separation of Concerns**: Each component has a single responsibility
- **Modular Design**: Changes in one module don't affect others
- **Comprehensive Documentation**: Every layer and component documented

### **Scalability**
- **Horizontal Scaling**: Can add more instances as needed
- **Database Scaling**: Multiple scaling strategies available
- **Performance Optimization**: Multiple optimization layers
- **Resource Management**: Efficient resource utilization

### **Developer Experience**
- **Fast Development**: Clear patterns and structure
- **Easy Testing**: Testable architecture with clear dependencies
- **Team Collaboration**: Multiple developers can work on different domains
- **Code Quality**: Enforced patterns and standards

### **Business Value**
- **Faster Time-to-Market**: Efficient development process
- **Lower Maintenance Cost**: Clean, maintainable code
- **Better Reliability**: Robust error handling and monitoring
- **Future-Proof**: Ready for growth and technology changes

---

**Architecture Status**: ✅ **IMPLEMENTED AND VALIDATED**  
**Ready for**: Production deployment and Vue.js migration  
**Next Steps**: Frontend modernization and advanced monitoring setup