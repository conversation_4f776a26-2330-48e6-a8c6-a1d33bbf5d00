# 各廠商遠端資料夾下載方式詳解

## 系統概述
每個半導體測試廠商都有不同的檔案組織結構和下載策略，系統會根據郵件內容自動識別廠商並選擇對應的處理器。

## 🏭 各廠商下載方式詳細分析

### 1. **GTK 廠商** (`gtk_file_handler.py`)
**識別關鍵字**: `ft hold`, `ft lot`

**遠端路徑結構**:
```
\\************\test_log\GTK\temp\          # 主要搜尋路徑
\\************\test_log\GTK\FT\{PD}\{LOT}\ # 資料夾複製路徑
```

**下載策略** (按優先順序):
1. **MO搜尋壓縮檔** - 在 `temp` 目錄搜尋包含MO編號的壓縮檔 (*.zip, *.rar, *.7z)
2. **LOT搜尋檔案** - 在 `temp` 目錄搜尋包含LOT編號的所有檔案
3. **資料夾複製** - 從 `FT\{PD}\{LOT}` 複製整個資料夾

**特殊功能**:
- ✅ 支援資料夾複製 (對應VBA的 `CopyFilesGTK2`)
- ✅ 支援多個MO編號 (逗號分隔)
- ✅ 智能檔案選擇 (選擇最新的壓縮檔)

---

### 2. **ETD 廠商** (`etd_file_handler.py`)
**識別關鍵字**: `anf`

**遠端路徑結構**:
```
\\************\test_log\ETD\FT\{PD}\{LOT}\    # 主要路徑
\\************\test_log\Etrend\FT\{PD}\{LOT}\ # 備用路徑
```

**下載策略**:
1. **資料夾複製優先** - 直接複製整個 `{PD}\{LOT}` 資料夾
2. **雙路徑支援** - 先嘗試 ETD 路徑，失敗則嘗試 Etrend 路徑

**特殊功能**:
- ✅ 支援資料夾複製 (對應VBA的 `CopyFilesETD2`)
- ✅ 雙路徑備援機制
- ⚠️ 必須提供 PD 和 LOT 參數

---

### 3. **JCET 廠商** (`jcet_file_handler.py`)
**識別關鍵字**: `jcet`

**遠端路徑結構**:
```
\\************\test_log\JCET\JCET\              # 預設路徑
\\************\test_log\JCET\SUQIAN\{PD}\{LOT}\ # 宿遷特殊路徑
```

**下載策略**:
1. **智能路徑選擇** - 檢查郵件內容是否包含「宿遷」關鍵字
2. **MO搜尋壓縮檔** - 搜尋包含MO編號的壓縮檔

**特殊功能**:
- 🎯 **智能路徑判斷** - 根據郵件內容自動選擇宿遷或預設路徑
- 🔍 **關鍵字檢測** - 支援 `宿迁`, `宿遷`, `suqian`, `sq` 等變體
- ❌ 不支援資料夾複製

---

### 4. **XAHT 廠商** (`xaht_file_handler.py`)
**識別關鍵字**: `tianshui`, `西安`

**遠端路徑結構**:
```
\\************\test_log\XAHT\temp\
```

**下載策略**:
1. **單一路徑搜尋** - 只從 `temp` 目錄搜尋
2. **MO搜尋壓縮檔** - 搜尋包含MO編號的壓縮檔

**特殊功能**:
- 🎯 **簡化策略** - 最簡單的搜尋邏輯
- ❌ 不支援LOT搜尋
- ❌ 不支援資料夾複製

---

### 5. **LINGSEN 廠商** (`lingsen_file_handler.py`)
**識別關鍵字**: `lingsen`

**遠端路徑結構**:
```
\\************\test_log\Lingsen\temp\
```

**下載策略**:
1. **MO搜尋壓縮檔** - 搜尋包含MO編號的壓縮檔

**特殊功能**:
- 🎯 **固定路徑** - 使用固定的 temp 目錄
- ❌ 不支援資料夾複製

---

### 6. **MSEC 廠商** (`msec_file_handler.py`)
**識別關鍵字**: `msec`

**遠端路徑結構**:
```
\\************\test_log\MSECZD\           # 主要目錄
\\************\test_log\MSECZD\FT\       # FT子目錄
\\************\test_log\MSECZD\{PD}\     # 產品特定目錄
\\************\test_log\MSECZD\{PD}\FT\  # 產品FT目錄
\\************\test_log\MSECZD\{LOT}\    # LOT特定目錄
```

**下載策略**:
1. **多路徑搜尋** - 嘗試多個可能的路徑組合
2. **優先級搜尋** - MO > LOT > PD > 通用壓縮檔
3. **智能檔案選擇** - 優先選擇壓縮檔，其次選擇最新檔案

**特殊功能**:
- 🎯 **多路徑策略** - 最複雜的路徑搜尋邏輯
- ✅ 支援多個MO編號
- 🔄 **備用搜尋** - 找不到壓縮檔時搜尋一般檔案

---

### 7. **NANOTECH 廠商** (`nanotech_file_handler.py`)
**識別關鍵字**: `nanotech`

**遠端路徑結構**:
```
\\************\test_log\Nano\temp\{PD}\
```

**下載策略**:
1. **MO資料夾複製** - 複製 `{PD}\{MO}\` 資料夾下的所有檔案
2. **整個PD資料夾複製** - 如果MO資料夾不存在，複製整個PD資料夾

**特殊功能**:
- 🎯 **資料夾內檔案複製** - 特殊的MO資料夾邏輯
- ✅ 支援資料夾複製
- ⚠️ 需要有效的PD參數

---

### 8. **CHUZHOU (滁州) 廠商** (`chuzhou_file_handler.py`)
**識別關鍵字**: `chuzhou`, `滁州`

**遠端路徑結構**:
```
\\************\test_log\JCET\CHUZHOU\TO252\
```

**下載策略**:
1. **LOT搜尋優先** - 搜尋以LOT開頭的檔案 (不是MO)
2. **最新壓縮檔選擇** - 選擇修改時間最新的壓縮檔

**特殊功能**:
- 🎯 **LOT優先策略** - 唯一使用LOT而非MO搜尋的廠商
- 📅 **時間排序** - 根據檔案修改時間選擇最新檔案
- ❌ 不支援資料夾複製

---

### 9. **TSHT 廠商** (`tsht_file_handler.py`)
**識別關鍵字**: `tsht`

**遠端路徑結構**:
```
\\************\test_log\TSHT\FT\
```

**下載策略**:
1. **MO搜尋檔案** - 搜尋包含MO編號的所有檔案

**特殊功能**:
- 🎯 **簡單搜尋** - 基本的MO搜尋邏輯
- ❌ 不支援資料夾複製

---

## 📊 廠商功能對比表

| 廠商 | 路徑數量 | MO搜尋 | LOT搜尋 | 資料夾複製 | 特殊功能 |
|------|----------|--------|---------|------------|----------|
| **GTK** | 2 | ✅ | ✅ | ✅ | 多MO支援 |
| **ETD** | 2 | ✅ | ❌ | ✅ | 雙路徑備援 |
| **JCET** | 2 | ✅ | ❌ | ❌ | 智能路徑選擇 |
| **XAHT** | 1 | ✅ | ❌ | ❌ | 最簡化 |
| **LINGSEN** | 1 | ✅ | ❌ | ❌ | 固定路徑 |
| **MSEC** | 5 | ✅ | ✅ | ❌ | 多路徑搜尋 |
| **NANOTECH** | 1 | ✅ | ❌ | ✅ | MO資料夾複製 |
| **CHUZHOU** | 1 | ❌ | ✅ | ❌ | LOT優先 |
| **TSHT** | 1 | ✅ | ❌ | ❌ | 基本搜尋 |

## 🔄 通用下載流程

所有廠商都遵循以下基本流程：

```
1. 郵件解析 → 識別廠商代碼
   ↓
2. 選擇對應廠商處理器
   ↓
3. 建立目標目錄 (D:\temp\{PD}\{MO})
   ↓
4. 取得廠商特定的遠端路徑列表
   ↓
5. 按優先順序嘗試下載策略：
   ├── 策略1: MO搜尋壓縮檔
   ├── 策略2: LOT搜尋檔案
   └── 策略3: 資料夾複製
   ↓
6. 檔案完整性驗證 (大小比較)
   ↓
7. 完成下載
```

## 🛠️ 技術特性

### 網路連線處理
- **SMB協議支援** - 使用Windows網路共享
- **連通性檢查** - Ping測試和路徑驗證
- **重試機制** - 網路失敗時自動重試
- **超時控制** - 避免長時間等待

### 檔案處理
- **智能檔案選擇** - 優先選擇最新的壓縮檔
- **多格式支援** - .zip, .rar, .7z 壓縮檔
- **檔案驗證** - 複製後進行大小比較驗證
- **重複檔案處理** - 自動跳過已存在且大小相同的檔案

### 錯誤處理
- **詳細日誌記錄** - 每個步驟都有詳細日誌
- **失敗原因分析** - 提供可能的失敗原因
- **優雅降級** - 主要策略失敗時嘗試備用策略

這個系統完整實現了原VBA系統的所有廠商處理邏輯，並增加了更多企業級特性如錯誤處理、日誌記錄和網路容錯能力。