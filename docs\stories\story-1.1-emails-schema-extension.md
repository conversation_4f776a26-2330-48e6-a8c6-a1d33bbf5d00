# Story 1.1: emails表Schema擴展
# Story 1.1: Emails Table Schema Extension

---
epic_id: epic-01
story_id: 1.1
title: "emails表Schema擴展"
priority: P0
status: ready
estimated_days: 2
---

## Story 描述

為 emails 表新增 4 個狀態追蹤欄位，支援郵件下載和處理成功狀態的完整記錄，包括時間戳追蹤功能。

## 業務價值

- **狀態可視化**: 在 emails 表中直接查看下載和處理狀態
- **時間追蹤**: 記錄關鍵時間點，支援性能分析
- **查詢優化**: 減少跨表查詢，提升監控界面性能
- **數據完整性**: 在主表中保留關鍵狀態資訊

## 業務邏輯要求 (強制)

### 資料驗證規則
```python
# 新增欄位驗證邏輯
download_success: Boolean = Field(default=False, description="下載成功標誌")
processing_success: Boolean = Field(default=False, description="處理成功標誌")  
download_completed_at: Optional[DateTime] = Field(default=None, description="下載完成時間")
processing_completed_at: Optional[DateTime] = Field(default=None, description="處理完成時間")

# 業務規則
- download_success=True 時，download_completed_at 必須有值
- processing_success=True 時，processing_completed_at 必須有值
- 時間戳必須晚於 created_at
- Boolean 欄位預設值為 False
```

### 業務流程步驟
1. **郵件創建**: 新欄位使用預設值 (False, False, None, None)
2. **下載完成**: 更新 download_success=True, download_completed_at=當前時間
3. **處理完成**: 更新 processing_success=True, processing_completed_at=當前時間
4. **狀態查詢**: 支援基於狀態欄位的快速查詢

### 狀態轉換規則
```python
# 狀態轉換邏輯
def update_download_status(email_id: int, success: bool):
    if success:
        email.download_success = True
        email.download_completed_at = datetime.utcnow()
    # 失敗情況保持預設值，由詳細表記錄

def update_processing_status(email_id: int, success: bool):
    if success:
        email.processing_success = True  
        email.processing_completed_at = datetime.utcnow()
```

### 錯誤處理機制
- **遷移失敗**: 自動回滾到原始 schema
- **資料不一致**: 驗證和修復程序
- **外鍵約束**: 確保關聯表資料完整性
- **並發更新**: 樂觀鎖防止衝突

### 事務處理要求
- **ACID 特性**: 所有 schema 變更在單一事務中完成
- **原子性**: 要麼全部成功，要麼全部回滾
- **一致性**: 新舊 schema 之間的資料一致性
- **隔離性**: 遷移過程中不影響現有查詢

## TDD Implementation Plan (強制)

### Red Phase: Test First

**Unit Tests**:
```python
def test_emails_table_has_new_columns():
    """測試新欄位存在"""
    # 檢查 download_success 欄位
    assert hasattr(EmailDB, 'download_success')
    assert hasattr(EmailDB, 'processing_success')
    assert hasattr(EmailDB, 'download_completed_at')  
    assert hasattr(EmailDB, 'processing_completed_at')

def test_new_columns_default_values():
    """測試預設值正確"""
    email = EmailDB(message_id="test", sender="<EMAIL>", subject="test")
    assert email.download_success == False
    assert email.processing_success == False
    assert email.download_completed_at is None
    assert email.processing_completed_at is None

def test_update_download_status():
    """測試下載狀態更新"""
    email = create_test_email()
    update_download_success(email.id, True)
    
    updated_email = session.query(EmailDB).get(email.id)
    assert updated_email.download_success == True
    assert updated_email.download_completed_at is not None
```

**Integration Tests**:
```python
def test_database_migration_complete():
    """測試資料庫遷移完整性"""
    # 執行遷移
    migration.upgrade()
    
    # 驗證新欄位存在
    columns = get_table_columns('emails')
    assert 'download_success' in columns
    assert 'processing_success' in columns
    assert 'download_completed_at' in columns
    assert 'processing_completed_at' in columns

def test_existing_data_preserved():
    """測試現有資料完整性"""
    # 遷移前後資料比較
    before_count = session.query(EmailDB).count()
    migration.upgrade()
    after_count = session.query(EmailDB).count()
    
    assert before_count == after_count
```

**Database Tests**:
```python
def test_column_constraints():
    """測試欄位約束"""
    # Boolean 欄位約束測試
    # DateTime 欄位約束測試
    # 非空約束測試

def test_index_performance():
    """測試索引性能"""
    # 創建測試資料
    # 測試基於新欄位的查詢性能
    # 確保查詢時間 < 200ms
```

### Green Phase: Minimal Implementation

**最小實現策略**:
1. **模型更新**: 在 EmailDB 中新增 4 個欄位定義
2. **遷移腳本**: 創建 Alembic 遷移腳本
3. **基本驗證**: 實現欄位約束和預設值
4. **測試通過**: 確保所有測試通過

**核心業務邏輯**:
```python
# backend/shared/infrastructure/adapters/database/models.py
class EmailDB(Base):
    # ... 現有欄位 ...
    
    # 新增狀態追蹤欄位
    download_success = Column(Boolean, default=False, nullable=False, 
                            comment="郵件下載成功標誌")
    processing_success = Column(Boolean, default=False, nullable=False,
                              comment="郵件處理成功標誌") 
    download_completed_at = Column(DateTime, nullable=True,
                                 comment="下載完成時間")
    processing_completed_at = Column(DateTime, nullable=True,
                                   comment="處理完成時間")
    
    # 新增索引
    __table_args__ = (
        # ... 現有索引 ...
        Index('idx_email_download_success', 'download_success'),
        Index('idx_email_processing_success', 'processing_success'),
        Index('idx_email_download_completed', 'download_completed_at'),
        Index('idx_email_processing_completed', 'processing_completed_at'),
    )
```

### Refactor Phase: Quality Enhancement

**代碼優化目標**:
- 優化 SQL 查詢性能
- 改善索引策略
- 加強資料驗證邏輯
- 提升錯誤處理機制

**性能優化要求**:
- 基於新欄位的查詢 < 200ms
- 遷移時間 < 5分鐘 (開發環境)
- 記憶體使用增加 < 10%

## Acceptance Criteria (完整性強化)

### 功能驗收 (使用者角度)
- [ ] 新的 emails 記錄包含 4 個新狀態欄位
- [ ] 下載成功時自動更新 download_success 和時間戳  
- [ ] 處理成功時自動更新 processing_success 和時間戳
- [ ] 監控界面能正確顯示新欄位資訊

### 業務邏輯驗收 (完整CRUD操作)
- [ ] **Create**: 新 email 記錄包含正確的預設值
- [ ] **Read**: 基於新欄位的查詢功能正常工作
- [ ] **Update**: 狀態更新邏輯完整且正確
- [ ] **Delete**: 刪除記錄時清理相關索引

### 資料完整性驗收 (資料操作完整性)
- [ ] 現有 emails 資料在遷移後完整保留
- [ ] 新欄位預設值正確應用到所有現有記錄
- [ ] 資料庫約束和索引正確創建
- [ ] 外鍵關聯保持完整

### 錯誤處理驗收 (異常情況正確處理)
- [ ] 遷移失敗時能完整回滾
- [ ] 並發更新衝突得到正確處理
- [ ] 無效資料輸入被正確拒絕
- [ ] 系統異常不影響資料完整性

### 性能驗收 (響應時間和資源使用)
- [ ] 基於新欄位的查詢性能 < 200ms
- [ ] 遷移過程對系統性能影響最小
- [ ] 索引創建後查詢性能有明顯提升
- [ ] 記憶體和存儲空間使用合理

## 技術實現細節

### 遷移腳本設計
```python
# alembic/versions/xxx_add_email_status_fields.py
def upgrade():
    # 新增欄位
    op.add_column('emails', sa.Column('download_success', sa.Boolean(), 
                                    nullable=False, default=False))
    op.add_column('emails', sa.Column('processing_success', sa.Boolean(),
                                    nullable=False, default=False))
    op.add_column('emails', sa.Column('download_completed_at', sa.DateTime(),
                                    nullable=True))
    op.add_column('emails', sa.Column('processing_completed_at', sa.DateTime(),
                                    nullable=True))
    
    # 創建索引
    op.create_index('idx_email_download_success', 'emails', ['download_success'])
    op.create_index('idx_email_processing_success', 'emails', ['processing_success'])
    
def downgrade():
    # 回滾操作
    op.drop_index('idx_email_processing_success', 'emails')
    op.drop_index('idx_email_download_success', 'emails')
    op.drop_column('emails', 'processing_completed_at')
    op.drop_column('emails', 'download_completed_at')
    op.drop_column('emails', 'processing_success')
    op.drop_column('emails', 'download_success')
```

### 狀態更新服務
```python
# backend/shared/services/email_status_service.py
class EmailStatusService:
    def update_download_success(self, email_id: int, success: bool) -> bool:
        with self.db.get_session() as session:
            email = session.query(EmailDB).get(email_id)
            if email:
                email.download_success = success
                if success:
                    email.download_completed_at = datetime.utcnow()
                session.commit()
                return True
            return False
    
    def update_processing_success(self, email_id: int, success: bool) -> bool:
        # 類似實現
        pass
```

## 驗證程序

### 自動驗證
```python
def verify_migration_success():
    """驗證遷移成功"""
    # 檢查表結構
    # 驗證資料完整性
    # 測試查詢性能
    # 驗證索引存在
```

### 手動驗證
1. **資料庫檢查**: 使用 SQL 工具驗證新欄位
2. **功能測試**: 手動測試狀態更新功能
3. **性能測試**: 手動執行查詢性能測試
4. **回滾測試**: 驗證回滾功能正常

## 清理策略 (強制)

### 臨時檔案清理
- 清理遷移過程產生的暫存檔案
- 移除測試過程中的臨時資料
- 清理日誌檔案和調試資訊

### 測試資料清理
- 清理單元測試產生的測試資料
- 移除整合測試的模擬資料  
- 重置測試資料庫到乾淨狀態

### 開發環境清理
- 移除開發過程中的除錯代碼
- 清理不必要的註釋和暫時變數
- 確保代碼符合生產標準

---

**Story 負責人**: Database Developer  
**技術審查人**: Senior Backend Developer  
**測試負責人**: QA Engineer  

**創建時間**: 2025-08-19  
**預計完成**: 2025-08-21  
**狀態**: 準備開始