"""
Story 1.3: EmailDownloadRetryLogDB 表建立 - TDD 測試
測試重試記錄模型的完整功能，包括新增的枚舉、欄位和業務邏輯
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

from backend.shared.infrastructure.adapters.database.models import Base, EmailDB
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    EmailDownloadStatusDB, EmailDownloadRetryLogDB, DownloadStatus
)

# Red Phase: 測試預期會失敗的新功能
class TestRetryLogModelEnhancements:
    """測試重試記錄模型的增強功能 - 這些測試應該在 Red Phase 失敗"""

    def setup_method(self):
        """設置測試環境"""
        self.engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(self.engine)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.session = self.SessionLocal()

    def teardown_method(self):
        """清理測試環境"""
        self.session.close()
        Base.metadata.drop_all(self.engine)

    def test_retry_status_enum_missing(self):
        """
        Red Phase: 測試 RetryStatus 枚舉不存在
        預期失敗: RetryStatus 枚舉應該不存在
        """
        try:
            from backend.shared.infrastructure.adapters.database.download_tracking_models import RetryStatus
            # 如果導入成功，檢查是否有所需的狀態
            expected_statuses = ['SCHEDULED', 'RUNNING', 'SUCCESS', 'FAILED', 'CANCELLED', 'TIMEOUT']
            actual_statuses = [status.name for status in RetryStatus]
            
            for status in expected_statuses:
                assert status in actual_statuses, f"Missing RetryStatus.{status}"
            
            pytest.fail("RetryStatus enum should be incomplete or missing in Red Phase")
        except ImportError:
            # 預期的失敗 - RetryStatus 枚舉不存在
            pass

    def test_enhanced_retry_strategy_enum_missing(self):
        """
        Red Phase: 測試增強的 RetryStrategy 枚舉
        預期失敗: 應該缺少 CUSTOM 和 ADAPTIVE 策略
        """
        from backend.shared.infrastructure.adapters.database.download_tracking_models import RetryStrategy
        
        # 檢查是否有新的策略類型
        expected_new_strategies = ['CUSTOM', 'ADAPTIVE', 'FIXED_DELAY']
        actual_strategies = [strategy.name for strategy in RetryStrategy]
        
        missing_strategies = [s for s in expected_new_strategies if s not in actual_strategies]
        assert len(missing_strategies) > 0, "Enhanced RetryStrategy enum should be incomplete in Red Phase"

    def test_retry_log_enhanced_fields_missing(self):
        """
        Red Phase: 測試重試記錄模型缺少增強欄位
        預期失敗: 應該缺少新的欄位如 scheduled_at, status, success 等
        """
        # 創建測試數據
        email = EmailDB(
            message_id="<EMAIL>",
            sender="<EMAIL>", 
            subject="Test Email",
            body="Test Body",
            received_time=datetime.utcnow()
        )
        self.session.add(email)
        self.session.commit()

        download_status = EmailDownloadStatusDB(email_id=email.id)
        self.session.add(download_status)
        self.session.commit()

        # 嘗試創建包含新欄位的重試記錄
        retry_log_data = {
            'download_status_id': download_status.id,
            'email_id': email.id,  # 新欄位
            'retry_attempt': 1,
            'scheduled_at': datetime.utcnow(),  # 新欄位
            'started_at': None,  # 新欄位
            'completed_at': None,  # 新欄位
            'status': 'SCHEDULED',  # 新枚舉欄位
            'error_type': None,  # 新欄位
            'retry_delay_seconds': 60,  # 新欄位
            'success': False,  # 新欄位
            'duration_ms': None  # 新欄位
        }

        # 檢查哪些欄位不存在
        retry_log = EmailDownloadRetryLogDB()
        missing_fields = []
        
        for field_name, field_value in retry_log_data.items():
            if not hasattr(retry_log, field_name):
                missing_fields.append(field_name)

        assert len(missing_fields) > 0, f"Should be missing enhanced fields in Red Phase: {missing_fields}"

    def test_retry_log_constraints_missing(self):
        """
        Red Phase: 測試重試記錄模型缺少業務約束
        預期失敗: 應該缺少資料驗證和約束
        """
        # 創建測試數據
        email = EmailDB(
            message_id="<EMAIL>",
            sender="<EMAIL>",
            subject="Test Email 2", 
            body="Test Body",
            received_time=datetime.utcnow()
        )
        self.session.add(email)
        self.session.commit()

        download_status = EmailDownloadStatusDB(email_id=email.id)
        self.session.add(download_status)
        self.session.commit()

        # 測試無效的重試次數（應該被拒絕但目前可能不會）
        invalid_retry_log = EmailDownloadRetryLogDB(
            download_status_id=download_status.id,
            retry_attempt=15,  # 超過最大重試次數 (應該 <= 10)
            attempted_at=datetime.utcnow()
        )

        try:
            self.session.add(invalid_retry_log)
            self.session.commit()
            # 如果成功提交，說明缺少約束驗證
            pytest.fail("Should have validation constraint for retry_attempt <= 10")
        except (IntegrityError, ValueError):
            # 預期的失敗 - 約束不存在，所以不會拋出錯誤
            self.session.rollback()

    def test_retry_log_indexes_missing(self):
        """
        Red Phase: 測試重試記錄模型缺少性能索引
        預期失敗: 應該缺少複合索引和性能優化索引
        """
        retry_log_table = EmailDownloadRetryLogDB.__table__
        
        # 檢查預期的索引
        expected_indexes = [
            'idx_retry_log_email_id',
            'idx_retry_log_status', 
            'idx_retry_log_scheduled_at',
            'idx_retry_log_retry_strategy',
            'idx_retry_log_composite',
            'idx_retry_log_error_type'
        ]

        actual_indexes = [idx.name for idx in retry_log_table.indexes if idx.name]
        missing_indexes = [idx for idx in expected_indexes if idx not in actual_indexes]
        
        assert len(missing_indexes) > 0, f"Should be missing performance indexes in Red Phase: {missing_indexes}"

    def test_retry_service_missing(self):
        """
        Red Phase: 測試重試服務不存在
        預期失敗: RetryService 類應該不存在
        """
        try:
            from backend.shared.services.retry_service import RetryService
            pytest.fail("RetryService should not exist in Red Phase")
        except ImportError:
            # 預期的失敗 - RetryService 不存在
            pass

    def test_retry_repository_missing(self):
        """
        Red Phase: 測試重試倉庫不存在
        預期失敗: RetryLogRepository 類應該不存在
        """
        try:
            from backend.shared.infrastructure.adapters.database.retry_repository import RetryLogRepository
            pytest.fail("RetryLogRepository should not exist in Red Phase")
        except ImportError:
            # 預期的失敗 - RetryLogRepository 不存在
            pass

    def test_time_validation_missing(self):
        """
        Red Phase: 測試時間邏輯驗證缺失
        預期失敗: 應該缺少 completed_at >= started_at >= scheduled_at 的驗證
        """
        # 創建測試數據
        email = EmailDB(
            message_id="<EMAIL>",
            sender="<EMAIL>",
            subject="Test Email 3",
            body="Test Body", 
            received_time=datetime.utcnow()
        )
        self.session.add(email)
        self.session.commit()

        download_status = EmailDownloadStatusDB(email_id=email.id)
        self.session.add(download_status)
        self.session.commit()

        # 創建時間邏輯錯誤的記錄（completed_at < started_at）
        now = datetime.utcnow()
        invalid_retry_log = EmailDownloadRetryLogDB(
            download_status_id=download_status.id,
            retry_attempt=1,
            attempted_at=now + timedelta(hours=1),  # completed_at
        )

        try:
            # 嘗試設置錯誤的時間順序（如果這些欄位存在的話）
            if hasattr(invalid_retry_log, 'started_at'):
                invalid_retry_log.started_at = now + timedelta(hours=2)
            if hasattr(invalid_retry_log, 'completed_at'):
                invalid_retry_log.completed_at = now  # completed_at < started_at
            
            self.session.add(invalid_retry_log)
            self.session.commit()
            
            # 如果成功提交，說明缺少時間驗證
            pytest.fail("Should have time validation constraints")
        except (ValueError, IntegrityError):
            # 預期的失敗 - 時間驗證不存在
            self.session.rollback()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])