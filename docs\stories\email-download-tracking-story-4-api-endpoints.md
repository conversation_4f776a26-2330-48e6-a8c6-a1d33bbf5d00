# Story 4: API 端點實現 - REST API 接口

## 📋 Story 概要

**Story ID**: EDTS-004  
**Story 名稱**: API 端點實現  
**Epic**: 郵件下載狀態追蹤  
**優先級**: P1 (Important)  
**估計工作量**: 16 Story Points (2-3 工作日)  
**依賴**: Story 1, Story 2, Story 3

### User Story
**As a** 前端開發者和系統管理員  
**I want** 完整的 REST API 接口來管理郵件下載追蹤  
**So that** 可以通過標準化的 API 查詢狀態、觸發重試、獲取統計信息並集成到前端界面

---

## 🎯 驗收標準

### 必須完成的功能 (Must Have)
- [ ] ✅ 實現下載狀態查詢 API 端點
- [ ] ✅ 實現重試控制 API 端點
- [ ] ✅ 實現統計信息查詢 API 端點
- [ ] ✅ 實現管理功能 API 端點
- [ ] ✅ 完整的 API 文檔和 OpenAPI 規格
- [ ] ✅ 統一的錯誤處理和響應格式
- [ ] ✅ 請求驗證和安全措施

### 應該有的功能 (Should Have)
- [ ] ✅ API 版本控制
- [ ] ✅ 請求限流和配額管理
- [ ] ✅ 詳細的 API 日誌記錄
- [ ] ✅ 響應快取機制

### 期望的功能 (Could Have)
- [ ] ✅ API 監控和指標收集
- [ ] ✅ 批量操作支援
- [ ] ✅ WebSocket 實時更新

---

## 🏗️ 技術實現詳情

### API 架構設計

```
REST API Layer
├── /api/v1/email/download/
│   ├── status/              # 狀態查詢
│   ├── retry/               # 重試控制
│   ├── statistics/          # 統計信息
│   ├── monitoring/          # 監控數據
│   └── management/          # 管理功能
└── WebSocket /ws/download/  # 實時更新
```

### 實現文件結構

```
backend/
├── email/
│   └── routes/
│       ├── download_status_api.py         # 狀態查詢 API
│       ├── download_retry_api.py          # 重試控制 API
│       ├── download_statistics_api.py     # 統計信息 API
│       ├── download_monitoring_api.py     # 監控數據 API
│       └── download_management_api.py     # 管理功能 API
├── shared/
│   └── infrastructure/
│       └── adapters/
│           └── web_api/
│               ├── api_middleware.py      # API 中間件
│               ├── api_validators.py      # 請求驗證
│               ├── api_formatters.py      # 響應格式化
│               └── api_auth.py            # API 認證
└── tests/
    └── api/
        └── test_download_tracking_api.py
```

---

## 🔧 詳細實現步驟

### Step 1: 狀態查詢 API (1天)

**檔案位置**: `backend/email/routes/download_status_api.py`

```python
"""
郵件下載狀態查詢 API
提供下載狀態的查詢、過濾和分頁功能
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Path
from typing import List, Optional, Dict, Any
from datetime import datetime
import asyncio

from backend.email.services.email_download_tracker import EmailDownloadTracker
from backend.email.models.download_tracking_models import DownloadStatusResponse
from backend.shared.infrastructure.adapters.web_api.api_middleware import (
    get_api_rate_limiter, get_api_cache, validate_api_request
)
from backend.shared.infrastructure.adapters.web_api.api_validators import (
    PaginationParams, StatusFilterParams
)

router = APIRouter(prefix="/api/v1/email/download", tags=["email-download-status"])

# 依賴注入
async def get_download_tracker() -> EmailDownloadTracker:
    """獲取下載追蹤器實例"""
    tracker = EmailDownloadTracker()
    return tracker

@router.get("/status/{email_id}", 
           response_model=DownloadStatusResponse,
           summary="獲取特定郵件的下載狀態",
           description="根據郵件 ID 獲取詳細的下載狀態信息")
async def get_email_download_status(
    email_id: int = Path(..., description="郵件 ID", gt=0),
    tracker: EmailDownloadTracker = Depends(get_download_tracker),
    _: Dict = Depends(get_api_rate_limiter(calls=100, period=60))  # 1分鐘100次
):
    """
    獲取特定郵件的下載狀態
    
    - **email_id**: 郵件的唯一標識符
    - 返回完整的下載狀態信息，包括重試歷史和錯誤詳情
    """
    try:
        status = await tracker.get_email_download_status(email_id)
        
        if not status:
            raise HTTPException(
                status_code=404, 
                detail={
                    "error": "EMAIL_NOT_FOUND",
                    "message": f"郵件下載狀態未找到: email_id={email_id}",
                    "email_id": email_id
                }
            )
        
        return DownloadStatusResponse(**status)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail={
                "error": "INTERNAL_SERVER_ERROR",
                "message": f"獲取下載狀態失敗: {str(e)}",
                "email_id": email_id
            }
        )

@router.get("/status", 
           response_model=List[DownloadStatusResponse],
           summary="獲取下載狀態列表",
           description="獲取下載狀態列表，支援過濾、排序和分頁")
async def get_download_status_list(
    pagination: PaginationParams = Depends(),
    filters: StatusFilterParams = Depends(),
    tracker: EmailDownloadTracker = Depends(get_download_tracker),
    _: Dict = Depends(get_api_rate_limiter(calls=50, period=60))
):
    """
    獲取下載狀態列表
    
    支援的查詢參數:
    - **status**: 狀態過濾 (pending, downloading, completed, failed, retry_scheduled)
    - **limit**: 返回數量限制 (1-100, 默認50)
    - **offset**: 偏移量 (默認0)
    - **order_by**: 排序欄位 (created_at, started_at, completed_at)
    - **order_direction**: 排序方向 (ASC, DESC)
    """
    try:
        statuses = await tracker.get_download_status_list(
            status_filter=filters.status,
            limit=pagination.limit,
            offset=pagination.offset,
            order_by=filters.order_by,
            order_direction=filters.order_direction
        )
        
        return [DownloadStatusResponse(**status) for status in statuses]
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail={
                "error": "INTERNAL_SERVER_ERROR",
                "message": f"獲取下載狀態列表失敗: {str(e)}"
            }
        )

@router.get("/status/{email_id}/retry-history",
           summary="獲取郵件重試歷史",
           description="獲取特定郵件的詳細重試歷史記錄")
async def get_email_retry_history(
    email_id: int = Path(..., description="郵件 ID", gt=0),
    tracker: EmailDownloadTracker = Depends(get_download_tracker),
    _: Dict = Depends(get_api_rate_limiter(calls=50, period=60))
):
    """
    獲取郵件重試歷史
    
    返回包含所有重試嘗試的詳細記錄
    """
    try:
        # 這個功能需要在 tracker 中實現
        history = await tracker.get_retry_history(email_id)
        
        if not history:
            raise HTTPException(
                status_code=404,
                detail={
                    "error": "RETRY_HISTORY_NOT_FOUND",
                    "message": f"重試歷史未找到: email_id={email_id}",
                    "email_id": email_id
                }
            )
        
        return {
            "email_id": email_id,
            "retry_attempts": history,
            "total_attempts": len(history),
            "last_updated": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "INTERNAL_SERVER_ERROR",
                "message": f"獲取重試歷史失敗: {str(e)}",
                "email_id": email_id
            }
        )

@router.get("/pending-retries",
           summary="獲取待重試任務列表",
           description="獲取當前等待重試的下載任務")
async def get_pending_retries(
    limit: int = Query(50, ge=1, le=200, description="返回數量限制"),
    include_metadata: bool = Query(False, description="是否包含詳細元數據"),
    tracker: EmailDownloadTracker = Depends(get_download_tracker),
    _: Dict = Depends(get_api_rate_limiter(calls=30, period=60))
):
    """
    獲取待重試任務列表
    
    - **limit**: 返回的任務數量
    - **include_metadata**: 是否包含詳細的元數據信息
    """
    try:
        pending_retries = await tracker.get_pending_retries(limit=limit)
        
        # 如果不包含元數據，簡化響應
        if not include_metadata:
            simplified = []
            for retry in pending_retries:
                simplified.append({
                    "tracking_id": retry["tracking_id"],
                    "email_id": retry["email_id"],
                    "attempt": retry["attempt"],
                    "next_retry_at": retry["next_retry_at"],
                    "error_type": retry["error_type"]
                })
            pending_retries = simplified
        
        return {
            "pending_retries": pending_retries,
            "total_count": len(pending_retries),
            "retrieved_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "INTERNAL_SERVER_ERROR",
                "message": f"獲取待重試任務失敗: {str(e)}"
            }
        )
```

### Step 2: 重試控制 API (1天)

**檔案位置**: `backend/email/routes/download_retry_api.py`

```python
"""
郵件下載重試控制 API
提供手動重試觸發和重試管理功能
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

from backend.email.services.email_download_retry_service import EmailDownloadRetryService, RetryPriority
from backend.email.models.download_tracking_models import ManualRetryRequest, ManualRetryResponse
from backend.shared.infrastructure.adapters.web_api.api_middleware import (
    get_api_rate_limiter, validate_api_request, require_admin_auth
)

router = APIRouter(prefix="/api/v1/email/download/retry", tags=["email-download-retry"])

# 請求模型
class BatchRetryRequest(BaseModel):
    """批量重試請求模型"""
    email_ids: List[int] = Field(..., min_items=1, max_items=100, description="郵件 ID 列表")
    priority: str = Field("HIGH", description="重試優先級 (LOW, NORMAL, HIGH, URGENT)")
    reason: str = Field("manual_batch_retry", description="重試原因")
    force: bool = Field(False, description="是否強制重試（忽略最大重試次數限制）")

class ProcessRetryRequest(BaseModel):
    """處理重試請求模型"""
    batch_size: int = Field(10, ge=1, le=100, description="批處理大小")
    priority_filter: Optional[str] = Field(None, description="優先級過濾")
    timeout_seconds: int = Field(300, ge=60, le=1800, description="處理超時時間（秒）")

# 依賴注入
async def get_retry_service() -> EmailDownloadRetryService:
    """獲取重試服務實例"""
    service = EmailDownloadRetryService()
    await service.initialize()
    return service

@router.post("/manual", 
            response_model=ManualRetryResponse,
            summary="觸發手動重試",
            description="手動觸發指定郵件的重新下載")
async def trigger_manual_retry(
    request: ManualRetryRequest,
    retry_service: EmailDownloadRetryService = Depends(get_retry_service),
    _: Dict = Depends(get_api_rate_limiter(calls=20, period=60)),
    __: Dict = Depends(require_admin_auth)  # 需要管理員權限
):
    """
    觸發手動重試
    
    - **email_ids**: 要重試的郵件 ID 列表
    - **reason**: 重試原因（用於審計）
    - 需要管理員權限
    """
    try:
        # 驗證優先級
        priority = RetryPriority.HIGH  # 手動重試默認高優先級
        
        result = await retry_service.trigger_manual_retry(
            email_ids=request.email_ids,
            priority=priority,
            reason=request.reason
        )
        
        return ManualRetryResponse(
            success=result['success'],
            message=result['message'],
            retry_results=result.get('results', [])
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "MANUAL_RETRY_FAILED",
                "message": f"觸發手動重試失敗: {str(e)}",
                "email_ids": request.email_ids
            }
        )

@router.post("/batch",
            summary="批量重試處理",
            description="批量處理多個郵件的重試")
async def batch_retry(
    request: BatchRetryRequest,
    background_tasks: BackgroundTasks,
    retry_service: EmailDownloadRetryService = Depends(get_retry_service),
    _: Dict = Depends(get_api_rate_limiter(calls=10, period=60)),
    __: Dict = Depends(require_admin_auth)
):
    """
    批量重試處理
    
    - **email_ids**: 郵件 ID 列表（最多100個）
    - **priority**: 重試優先級
    - **force**: 是否強制重試，忽略最大重試次數
    - 在後台異步處理，立即返回任務ID
    """
    try:
        # 驗證優先級
        try:
            priority = RetryPriority[request.priority.upper()]
        except KeyError:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "INVALID_PRIORITY",
                    "message": f"無效的優先級: {request.priority}",
                    "valid_priorities": [p.name for p in RetryPriority]
                }
            )
        
        # 如果是小批量，直接處理
        if len(request.email_ids) <= 10:
            result = await retry_service.trigger_manual_retry(
                email_ids=request.email_ids,
                priority=priority,
                reason=request.reason
            )
            
            return {
                "success": result['success'],
                "message": result['message'],
                "processing_mode": "immediate",
                "results": result.get('results', [])
            }
        
        # 大批量使用後台任務
        task_id = f"batch_retry_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        background_tasks.add_task(
            _process_batch_retry_background,
            task_id,
            request.email_ids,
            priority,
            request.reason,
            request.force
        )
        
        return {
            "success": True,
            "message": f"批量重試任務已提交: {len(request.email_ids)} 個郵件",
            "processing_mode": "background",
            "task_id": task_id,
            "estimated_completion": datetime.utcnow() + timedelta(minutes=5)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "BATCH_RETRY_FAILED", 
                "message": f"批量重試失敗: {str(e)}"
            }
        )

@router.post("/process",
            summary="處理待重試任務",
            description="立即處理當前隊列中的待重試任務")
async def process_pending_retries(
    request: ProcessRetryRequest,
    retry_service: EmailDownloadRetryService = Depends(get_retry_service),
    _: Dict = Depends(get_api_rate_limiter(calls=5, period=60)),
    __: Dict = Depends(require_admin_auth)
):
    """
    處理待重試任務
    
    - **batch_size**: 一次處理的任務數量
    - **priority_filter**: 只處理指定優先級的任務
    - **timeout_seconds**: 處理超時時間
    """
    try:
        # 驗證優先級過濾器
        priority_filter = None
        if request.priority_filter:
            try:
                priority_filter = RetryPriority[request.priority_filter.upper()]
            except KeyError:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "error": "INVALID_PRIORITY_FILTER",
                        "message": f"無效的優先級過濾器: {request.priority_filter}"
                    }
                )
        
        # 設置超時處理
        result = await asyncio.wait_for(
            retry_service.process_pending_retries(
                batch_size=request.batch_size,
                priority_filter=priority_filter
            ),
            timeout=request.timeout_seconds
        )
        
        return {
            "success": result['success'],
            "message": result['message'],
            "processed_count": result['processed_count'],
            "success_count": result.get('success_count', 0),
            "failure_count": result.get('failure_count', 0),
            "processing_time_seconds": request.timeout_seconds,
            "results": result.get('results', [])
        }
        
    except asyncio.TimeoutError:
        raise HTTPException(
            status_code=408,
            detail={
                "error": "PROCESSING_TIMEOUT",
                "message": f"處理超時: {request.timeout_seconds}秒",
                "suggestion": "請減少批處理大小或增加超時時間"
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "PROCESSING_FAILED",
                "message": f"處理重試任務失敗: {str(e)}"
            }
        )

@router.get("/status",
           summary="獲取重試服務狀態",
           description="獲取重試服務的當前狀態和健康信息")
async def get_retry_service_status(
    retry_service: EmailDownloadRetryService = Depends(get_retry_service),
    _: Dict = Depends(get_api_rate_limiter(calls=30, period=60))
):
    """
    獲取重試服務狀態
    
    包含服務健康狀態、配置信息和統計數據
    """
    try:
        health = await retry_service.get_service_health()
        status = await retry_service.get_retry_status()
        
        return {
            "service_health": health,
            "retry_status": status,
            "last_checked": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "STATUS_CHECK_FAILED",
                "message": f"獲取重試服務狀態失敗: {str(e)}"
            }
        )

@router.post("/circuit-breaker/reset",
            summary="重置斷路器",
            description="強制重置重試服務的斷路器")
async def reset_circuit_breaker(
    retry_service: EmailDownloadRetryService = Depends(get_retry_service),
    _: Dict = Depends(get_api_rate_limiter(calls=5, period=60)),
    __: Dict = Depends(require_admin_auth)  # 需要管理員權限
):
    """
    重置斷路器
    
    強制重置重試服務的斷路器，恢復重試處理
    需要管理員權限
    """
    try:
        success = await retry_service.force_circuit_breaker_reset()
        
        if success:
            return {
                "success": True,
                "message": "斷路器已重置",
                "reset_at": datetime.utcnow().isoformat()
            }
        else:
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "RESET_FAILED",
                    "message": "斷路器重置失敗"
                }
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "RESET_ERROR",
                "message": f"重置斷路器異常: {str(e)}"
            }
        )

# 後台任務函數
async def _process_batch_retry_background(
    task_id: str,
    email_ids: List[int], 
    priority: RetryPriority,
    reason: str,
    force: bool
):
    """後台處理批量重試"""
    try:
        retry_service = EmailDownloadRetryService()
        await retry_service.initialize()
        
        # 分批處理
        batch_size = 20
        total_processed = 0
        total_success = 0
        
        for i in range(0, len(email_ids), batch_size):
            batch = email_ids[i:i + batch_size]
            
            result = await retry_service.trigger_manual_retry(
                email_ids=batch,
                priority=priority,
                reason=f"{reason}_batch_{i//batch_size + 1}"
            )
            
            if result['success']:
                batch_success = len([r for r in result['results'] if r['status'] == 'scheduled'])
                total_success += batch_success
            
            total_processed += len(batch)
            
            # 短暫休息避免過載
            await asyncio.sleep(1)
        
        # 記錄完成狀態（這裡可以保存到數據庫或快取）
        logger.info(f"批量重試任務完成: {task_id}, 成功={total_success}/{total_processed}")
        
    except Exception as e:
        logger.error(f"批量重試後台任務失敗: {task_id}, error={e}")
```

### Step 3: 統計信息 API (0.5天)

**檔案位置**: `backend/email/routes/download_statistics_api.py`

```python
"""
郵件下載統計信息 API
提供各種統計數據和分析報告
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from backend.email.services.email_download_tracker import EmailDownloadTracker
from backend.email.models.download_tracking_models import DownloadStatisticsResponse
from backend.shared.infrastructure.adapters.web_api.api_middleware import (
    get_api_rate_limiter, get_api_cache
)

router = APIRouter(prefix="/api/v1/email/download/statistics", tags=["email-download-statistics"])

async def get_download_tracker() -> EmailDownloadTracker:
    """獲取下載追蹤器實例"""
    return EmailDownloadTracker()

@router.get("/summary", 
           response_model=DownloadStatisticsResponse,
           summary="獲取下載統計摘要",
           description="獲取指定時間範圍內的下載統計摘要")
async def get_download_statistics_summary(
    hours: int = Query(24, ge=1, le=168, description="統計時間範圍（小時），最多一週"),
    tracker: EmailDownloadTracker = Depends(get_download_tracker),
    _: Dict = Depends(get_api_rate_limiter(calls=60, period=60)),
    cache: Dict = Depends(get_api_cache(ttl=300))  # 5分鐘快取
):
    """
    獲取下載統計摘要
    
    - **hours**: 統計時間範圍（1-168小時）
    - 返回成功率、錯誤分佈、性能指標等
    """
    try:
        # 檢查快取
        cache_key = f"stats_summary_{hours}"
        if cache_key in cache:
            return cache[cache_key]
        
        stats = await tracker.get_download_statistics(hours=hours)
        
        if not stats:
            # 返回空統計
            stats = {
                'total_downloads': 0,
                'successful_downloads': 0,
                'failed_downloads': 0,
                'pending_retries': 0,
                'success_rate': 0,
                'error_types': [],
                'period_hours': hours,
                'last_updated': datetime.utcnow().isoformat()
            }
        
        result = DownloadStatisticsResponse(**stats)
        
        # 更新快取
        cache[cache_key] = result
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "STATISTICS_ERROR",
                "message": f"獲取下載統計失敗: {str(e)}"
            }
        )

@router.get("/trends",
           summary="獲取趨勢分析",
           description="獲取下載成功率和性能的趨勢分析")
async def get_download_trends(
    period: str = Query("24h", description="分析週期 (1h, 6h, 24h, 7d)"),
    granularity: str = Query("1h", description="數據粒度 (1h, 6h, 1d)"),
    tracker: EmailDownloadTracker = Depends(get_download_tracker),
    _: Dict = Depends(get_api_rate_limiter(calls=30, period=60))
):
    """
    獲取趨勢分析
    
    - **period**: 分析時間週期
    - **granularity**: 數據點的時間間隔
    """
    try:
        # 解析週期
        period_mapping = {
            '1h': 1, '6h': 6, '24h': 24, '7d': 168
        }
        
        if period not in period_mapping:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "INVALID_PERIOD",
                    "message": f"無效的週期: {period}",
                    "valid_periods": list(period_mapping.keys())
                }
            )
        
        hours = period_mapping[period]
        
        # 獲取趨勢數據（這裡需要在 tracker 中實現趨勢分析）
        trends = await tracker.get_download_trends(
            hours=hours, 
            granularity=granularity
        )
        
        return {
            "period": period,
            "granularity": granularity,
            "data_points": trends.get('data_points', []),
            "summary": {
                "avg_success_rate": trends.get('avg_success_rate', 0),
                "trend_direction": trends.get('trend_direction', 'stable'),
                "peak_volume_time": trends.get('peak_volume_time'),
                "lowest_success_time": trends.get('lowest_success_time')
            },
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "TRENDS_ANALYSIS_ERROR",
                "message": f"趨勢分析失敗: {str(e)}"
            }
        )

@router.get("/errors",
           summary="獲取錯誤分析",
           description="獲取詳細的錯誤類型分析和建議")
async def get_error_analysis(
    hours: int = Query(24, ge=1, le=168),
    top_n: int = Query(10, ge=1, le=50, description="返回前N個錯誤類型"),
    tracker: EmailDownloadTracker = Depends(get_download_tracker),
    _: Dict = Depends(get_api_rate_limiter(calls=30, period=60))
):
    """
    獲取錯誤分析
    
    - **hours**: 分析時間範圍
    - **top_n**: 返回最常見的錯誤類型數量
    """
    try:
        stats = await tracker.get_download_statistics(hours=hours)
        error_types = stats.get('error_types', [])
        
        # 計算錯誤分析
        total_errors = sum(error['count'] for error in error_types)
        
        # 排序並限制數量
        top_errors = sorted(error_types, key=lambda x: x['count'], reverse=True)[:top_n]
        
        # 為每個錯誤類型添加分析
        error_analysis = []
        for error in top_errors:
            error_type = error['type']
            count = error['count']
            percentage = (count / total_errors * 100) if total_errors > 0 else 0
            
            # 獲取錯誤建議（這需要在 error_classifier 中實現）
            suggestions = _get_error_suggestions(error_type)
            
            error_analysis.append({
                "error_type": error_type,
                "count": count,
                "percentage": round(percentage, 2),
                "severity": suggestions.get('severity', 'medium'),
                "is_retryable": suggestions.get('is_retryable', True),
                "suggestions": suggestions.get('suggestions', []),
                "common_causes": suggestions.get('common_causes', [])
            })
        
        return {
            "period_hours": hours,
            "total_errors": total_errors,
            "unique_error_types": len(error_types),
            "top_errors": error_analysis,
            "overall_recommendations": _get_overall_recommendations(error_analysis),
            "analysis_time": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "ERROR_ANALYSIS_FAILED",
                "message": f"錯誤分析失敗: {str(e)}"
            }
        )

@router.get("/performance",
           summary="獲取性能指標",
           description="獲取下載性能相關的詳細指標")
async def get_performance_metrics(
    hours: int = Query(24, ge=1, le=168),
    tracker: EmailDownloadTracker = Depends(get_download_tracker),
    _: Dict = Depends(get_api_rate_limiter(calls=30, period=60))
):
    """
    獲取性能指標
    
    包含下載速度、響應時間、吞吐量等性能數據
    """
    try:
        stats = await tracker.get_download_statistics(hours=hours)
        performance = stats.get('performance', {})
        
        # 計算額外的性能指標
        total_downloads = stats.get('total_downloads', 0)
        successful_downloads = stats.get('successful_downloads', 0)
        
        # 估算吞吐量
        throughput_per_hour = total_downloads / hours if hours > 0 else 0
        
        # 性能評級
        performance_grade = _calculate_performance_grade(
            success_rate=stats.get('success_rate', 0),
            avg_duration=performance.get('avg_download_time_seconds', 0),
            throughput=throughput_per_hour
        )
        
        return {
            "period_hours": hours,
            "throughput": {
                "downloads_per_hour": round(throughput_per_hour, 2),
                "successful_downloads_per_hour": round(successful_downloads / hours, 2) if hours > 0 else 0,
                "peak_hour_estimate": round(throughput_per_hour * 1.5, 2)  # 估算峰值
            },
            "response_times": {
                "avg_download_time": performance.get('avg_download_time_seconds', 0),
                "max_download_time": performance.get('max_download_time_seconds', 0),
                "min_download_time": performance.get('min_download_time_seconds', 0)
            },
            "data_transfer": {
                "avg_download_size_bytes": performance.get('avg_download_size_bytes', 0),
                "avg_download_size_mb": round(performance.get('avg_download_size_bytes', 0) / 1024 / 1024, 2),
                "estimated_bandwidth_usage": round(
                    (performance.get('avg_download_size_bytes', 0) * total_downloads) / 1024 / 1024, 2
                )  # MB
            },
            "performance_grade": performance_grade,
            "recommendations": _get_performance_recommendations(performance_grade, performance),
            "measured_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                "error": "PERFORMANCE_METRICS_ERROR",
                "message": f"獲取性能指標失敗: {str(e)}"
            }
        )

# 輔助函數
def _get_error_suggestions(error_type: str) -> Dict[str, Any]:
    """獲取錯誤建議"""
    suggestions_map = {
        'connection_error': {
            'severity': 'medium',
            'is_retryable': True,
            'suggestions': ['檢查網絡連接', '驗證服務器可達性', '檢查防火牆設置'],
            'common_causes': ['網絡不穩定', '服務器臨時不可用', '防火牆阻擋']
        },
        'timeout': {
            'severity': 'low',
            'is_retryable': True,
            'suggestions': ['增加超時設置', '檢查網絡延遲', '優化請求大小'],
            'common_causes': ['網絡延遲高', '服務器響應慢', '請求過大']
        },
        'authentication_error': {
            'severity': 'high',
            'is_retryable': False,
            'suggestions': ['檢查認證憑據', '確認帳戶狀態', '更新密碼'],
            'common_causes': ['密碼錯誤', '帳戶被鎖定', '權限不足']
        }
        # 可以添加更多錯誤類型
    }
    
    return suggestions_map.get(error_type, {
        'severity': 'medium',
        'is_retryable': True,
        'suggestions': ['檢查系統日誌', '聯繫技術支援'],
        'common_causes': ['未知原因']
    })

def _get_overall_recommendations(error_analysis: List[Dict]) -> List[str]:
    """獲取整體建議"""
    recommendations = []
    
    high_severity_count = len([e for e in error_analysis if e.get('severity') == 'high'])
    total_errors = sum(e.get('count', 0) for e in error_analysis)
    
    if high_severity_count > 0:
        recommendations.append(f'發現 {high_severity_count} 種高嚴重性錯誤，建議優先處理')
    
    if total_errors > 100:
        recommendations.append('錯誤數量較多，建議檢查系統配置和網絡狀況')
    
    if not recommendations:
        recommendations.append('系統運行正常，繼續監控')
    
    return recommendations

def _calculate_performance_grade(success_rate: float, avg_duration: float, throughput: float) -> str:
    """計算性能等級"""
    score = 0
    
    # 成功率評分 (40%)
    if success_rate >= 99:
        score += 40
    elif success_rate >= 95:
        score += 35
    elif success_rate >= 90:
        score += 30
    elif success_rate >= 80:
        score += 20
    else:
        score += 10
    
    # 響應時間評分 (30%)
    if avg_duration <= 5:
        score += 30
    elif avg_duration <= 15:
        score += 25
    elif avg_duration <= 30:
        score += 20
    elif avg_duration <= 60:
        score += 15
    else:
        score += 5
    
    # 吞吐量評分 (30%)
    if throughput >= 100:
        score += 30
    elif throughput >= 50:
        score += 25
    elif throughput >= 20:
        score += 20
    elif throughput >= 10:
        score += 15
    else:
        score += 10
    
    # 根據分數返回等級
    if score >= 90:
        return 'A'
    elif score >= 80:
        return 'B'
    elif score >= 70:
        return 'C'
    elif score >= 60:
        return 'D'
    else:
        return 'F'

def _get_performance_recommendations(grade: str, performance: Dict) -> List[str]:
    """獲取性能改進建議"""
    recommendations = []
    
    if grade in ['D', 'F']:
        recommendations.append('系統性能較差，建議進行全面檢查和優化')
    
    avg_duration = performance.get('avg_download_time_seconds', 0)
    if avg_duration > 30:
        recommendations.append('下載時間較長，考慮優化網絡配置或增加並發處理')
    
    if not recommendations:
        recommendations.append('性能表現良好，繼續保持')
    
    return recommendations
```

### Step 4: API 中間件和工具 (0.5天)

**檔案位置**: `backend/shared/infrastructure/adapters/web_api/api_middleware.py`

```python
"""
API 中間件
提供速率限制、快取、認證等通用功能
"""

from fastapi import HTTPException, Request
from typing import Dict, Any, Optional
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json

# 全局快取和速率限制存儲
_rate_limit_store = defaultdict(lambda: deque())
_cache_store = {}

def get_api_rate_limiter(calls: int, period: int):
    """
    API 速率限制裝飾器
    
    Args:
        calls: 允許的調用次數
        period: 時間週期（秒）
    """
    def rate_limiter(request: Request) -> Dict[str, Any]:
        client_ip = request.client.host
        current_time = time.time()
        
        # 清理過期記錄
        window_start = current_time - period
        while _rate_limit_store[client_ip] and _rate_limit_store[client_ip][0] < window_start:
            _rate_limit_store[client_ip].popleft()
        
        # 檢查是否超過限制
        if len(_rate_limit_store[client_ip]) >= calls:
            raise HTTPException(
                status_code=429,
                detail={
                    "error": "RATE_LIMIT_EXCEEDED",
                    "message": f"速率限制: {calls} 次/{period} 秒",
                    "retry_after": int(period - (current_time - _rate_limit_store[client_ip][0]))
                }
            )
        
        # 記錄當前請求
        _rate_limit_store[client_ip].append(current_time)
        
        return {"rate_limit_remaining": calls - len(_rate_limit_store[client_ip])}
    
    return rate_limiter

def get_api_cache(ttl: int = 300):
    """
    API 響應快取
    
    Args:
        ttl: 快取存活時間（秒）
    """
    def cache_provider() -> Dict[str, Any]:
        current_time = time.time()
        
        # 清理過期快取
        expired_keys = [
            key for key, (data, expire_time) in _cache_store.items()
            if current_time > expire_time
        ]
        for key in expired_keys:
            del _cache_store[key]
        
        # 返回快取接口
        class CacheInterface:
            def __contains__(self, key: str) -> bool:
                return key in _cache_store and current_time <= _cache_store[key][1]
            
            def __getitem__(self, key: str) -> Any:
                if key in self:
                    return _cache_store[key][0]
                raise KeyError(key)
            
            def __setitem__(self, key: str, value: Any):
                _cache_store[key] = (value, current_time + ttl)
        
        return CacheInterface()
    
    return cache_provider

def require_admin_auth(request: Request) -> Dict[str, Any]:
    """
    管理員認證中間件
    """
    # 這裡實現實際的認證邏輯
    # 可能需要檢查 JWT token、API key 等
    
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        raise HTTPException(
            status_code=401,
            detail={
                "error": "AUTHORIZATION_REQUIRED",
                "message": "需要管理員認證"
            }
        )
    
    # 簡化的認證檢查（實際實現應該更加安全）
    if not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=401,
            detail={
                "error": "INVALID_AUTH_FORMAT",
                "message": "認證格式錯誤"
            }
        )
    
    # 這裡應該驗證 token 的有效性
    # token = auth_header[7:]  # 移除 "Bearer "
    # 驗證邏輯...
    
    return {"user_role": "admin"}

def validate_api_request(request: Request) -> Dict[str, Any]:
    """
    API 請求驗證中間件
    """
    # 檢查 Content-Type
    if request.method in ["POST", "PUT", "PATCH"]:
        content_type = request.headers.get("content-type", "")
        if not content_type.startswith("application/json"):
            raise HTTPException(
                status_code=415,
                detail={
                    "error": "UNSUPPORTED_MEDIA_TYPE",
                    "message": "僅支援 application/json"
                }
            )
    
    return {"validated": True}
```

**檔案位置**: `backend/shared/infrastructure/adapters/web_api/api_validators.py`

```python
"""
API 請求驗證器
定義通用的請求參數驗證模型
"""

from pydantic import BaseModel, Field, validator
from typing import Optional
from enum import Enum

class SortOrder(str, Enum):
    ASC = "ASC"
    DESC = "DESC"

class DownloadStatusFilter(str, Enum):
    PENDING = "pending"
    DOWNLOADING = "downloading"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRY_SCHEDULED = "retry_scheduled"

class PaginationParams(BaseModel):
    """分頁參數"""
    limit: int = Field(50, ge=1, le=100, description="返回數量限制")
    offset: int = Field(0, ge=0, description="偏移量")
    
    @validator('limit')
    def validate_limit(cls, v):
        if v > 100:
            raise ValueError('限制數量不能超過 100')
        return v

class StatusFilterParams(BaseModel):
    """狀態過濾參數"""
    status: Optional[DownloadStatusFilter] = Field(None, description="狀態過濾")
    order_by: str = Field("created_at", description="排序欄位")
    order_direction: SortOrder = Field(SortOrder.DESC, description="排序方向")
    
    @validator('order_by')
    def validate_order_by(cls, v):
        valid_fields = ['created_at', 'started_at', 'completed_at', 'download_attempt']
        if v not in valid_fields:
            raise ValueError(f'排序欄位必須是: {", ".join(valid_fields)}')
        return v
```

---

## 🧪 測試策略

### API 測試

**檔案位置**: `tests/api/test_download_tracking_api.py`

```python
"""
下載追蹤 API 測試
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from backend.main import app

client = TestClient(app)

class TestDownloadStatusAPI:
    
    def test_get_email_download_status_success(self):
        """測試獲取郵件下載狀態成功"""
        with patch('backend.email.services.email_download_tracker.EmailDownloadTracker') as mock_tracker:
            mock_tracker.return_value.get_email_download_status.return_value = {
                'id': 1,
                'email_id': 123,
                'status': 'completed',
                'download_attempt': 1,
                'created_at': '2025-08-19T10:00:00'
            }
            
            response = client.get("/api/v1/email/download/status/123")
            
            assert response.status_code == 200
            data = response.json()
            assert data['email_id'] == 123
            assert data['status'] == 'completed'
    
    def test_get_email_download_status_not_found(self):
        """測試郵件狀態未找到"""
        with patch('backend.email.services.email_download_tracker.EmailDownloadTracker') as mock_tracker:
            mock_tracker.return_value.get_email_download_status.return_value = None
            
            response = client.get("/api/v1/email/download/status/999")
            
            assert response.status_code == 404
            data = response.json()
            assert data['detail']['error'] == 'EMAIL_NOT_FOUND'
    
    def test_rate_limiting(self):
        """測試速率限制"""
        # 快速發送多個請求來觸發速率限制
        # 這個測試需要根據實際的速率限制配置調整
        pass

class TestDownloadRetryAPI:
    
    def test_trigger_manual_retry_success(self):
        """測試手動重試成功"""
        with patch('backend.email.services.email_download_retry_service.EmailDownloadRetryService') as mock_service:
            mock_service.return_value.trigger_manual_retry.return_value = {
                'success': True,
                'message': '重試已安排',
                'results': [{'email_id': 123, 'status': 'scheduled'}]
            }
            
            response = client.post(
                "/api/v1/email/download/retry/manual",
                json={"email_ids": [123], "reason": "test_retry"},
                headers={"Authorization": "Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data['success'] == True

class TestDownloadStatisticsAPI:
    
    def test_get_statistics_summary(self):
        """測試獲取統計摘要"""
        with patch('backend.email.services.email_download_tracker.EmailDownloadTracker') as mock_tracker:
            mock_tracker.return_value.get_download_statistics.return_value = {
                'total_downloads': 100,
                'successful_downloads': 95,
                'failed_downloads': 5,
                'success_rate': 95.0,
                'error_types': []
            }
            
            response = client.get("/api/v1/email/download/statistics/summary?hours=24")
            
            assert response.status_code == 200
            data = response.json()
            assert data['total_downloads'] == 100
            assert data['success_rate'] == 95.0
```

---

## 📊 成功標準

### 功能成功標準
- ✅ 所有 API 端點正常工作
- ✅ 請求驗證和錯誤處理完善
- ✅ 速率限制和快取機制有效
- ✅ API 文檔完整準確

### 性能成功標準
- ✅ API 響應時間 < 200ms (95th percentile)
- ✅ 支援 100 requests/sec 並發
- ✅ 快取命中率 > 70%
- ✅ 錯誤率 < 0.1%

### 安全成功標準
- ✅ 認證機制正確實現
- ✅ 輸入驗證無漏洞
- ✅ 速率限制防止濫用
- ✅ 敏感信息正確保護

---

## 📋 交付清單

### 代碼文件
- [ ] 所有 API 路由文件
- [ ] API 中間件和驗證器
- [ ] 請求/響應模型定義

### 文檔
- [ ] OpenAPI/Swagger 規格
- [ ] API 使用指南
- [ ] 錯誤代碼文檔

### 測試
- [ ] 完整的 API 測試套件
- [ ] 負載測試腳本
- [ ] 安全測試用例

---

**Story 完成標準**: 所有驗收標準通過 ✅ 且交付清單項目完成 📋

**下一個 Story**: [Story 5: 前端界面整合](./email-download-tracking-story-5-frontend-integration.md)