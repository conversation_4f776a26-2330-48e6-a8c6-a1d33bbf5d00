/*
 * PTS Renamer 專用樣式模組
 * 提供 PTS 檔案重命名工具的額外樣式支援
 */

/* 基礎變數擴展 */
:root {
    --pts-primary: #667eea;
    --pts-secondary: #764ba2;
    --pts-success: #28a745;
    --pts-warning: #ffc107;
    --pts-error: #e74c3c;
    --pts-info: #17a2b8;
    --pts-dark: #2c3e50;
    --pts-light: #f8f9fa;
    --pts-border: #e9ecef;
    --pts-text-muted: #6c757d;
}

/* 工具類別 */
.pts-fade-in {
    animation: ptsFileIn 0.3s ease-out;
}

.pts-fade-out {
    animation: ptsFadeOut 0.3s ease-out;
}

.pts-bounce {
    animation: ptsBounce 0.6s ease-out;
}

.pts-shake {
    animation: ptsShake 0.5s ease-out;
}

.pts-pulse {
    animation: ptsPulse 2s infinite;
}

/* 動畫定義 */
@keyframes ptsFileIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes ptsFadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

@keyframes ptsBounce {
    0%, 20%, 53%, 80%, 100% {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -30px, 0);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}

@keyframes ptsShake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
        transform: translate3d(4px, 0, 0);
    }
}

@keyframes ptsPulse {
    0% {
        transform: scale3d(1, 1, 1);
    }
    50% {
        transform: scale3d(1.05, 1.05, 1.05);
    }
    100% {
        transform: scale3d(1, 1, 1);
    }
}

/* 檔案類型圖示色彩 */
.file-icon.zip { color: #ff6b35; }
.file-icon.sevenZ { color: #4a90e2; }
.file-icon.rar { color: #9b59b6; }
.file-icon.pts { color: #2ecc71; }

/* 拖放狀態進階樣式 */
.upload-area.drag-enter {
    border-color: var(--pts-primary);
    background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
    transform: scale(1.02);
}

.upload-area.drag-over {
    border-color: var(--pts-success);
    background: linear-gradient(135deg, #f0fff4 0%, #e8f5e8 100%);
    box-shadow: 0 8px 32px rgba(40, 167, 69, 0.2);
}

.upload-area.drag-error {
    border-color: var(--pts-error);
    background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
    animation: ptsShake 0.5s ease-out;
}

/* 處理狀態樣式 */
.processing-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: inherit;
}

.processing-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--pts-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

.processing-text {
    color: var(--pts-dark);
    font-weight: 500;
    font-size: 14px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 檔案預覽樣式增強 */
.preview-item.file-added {
    background: linear-gradient(90deg, #e8f5e8 0%, transparent 100%);
    border-left: 3px solid var(--pts-success);
}

.preview-item.file-renamed {
    background: linear-gradient(90deg, #e8f2ff 0%, transparent 100%);
    border-left: 3px solid var(--pts-primary);
}

.preview-item.file-qc {
    background: linear-gradient(90deg, #fff3cd 0%, transparent 100%);
    border-left: 3px solid var(--pts-warning);
}

.preview-item.file-error {
    background: linear-gradient(90deg, #f8d7da 0%, transparent 100%);
    border-left: 3px solid var(--pts-error);
}

/* 進度條增強樣式 */
.progress-bar.processing {
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.progress-fill.animated {
    background: linear-gradient(45deg, 
        var(--pts-primary) 25%, 
        transparent 25%, 
        transparent 50%, 
        var(--pts-primary) 50%, 
        var(--pts-primary) 75%, 
        transparent 75%, 
        transparent);
    background-size: 30px 30px;
    animation: progressStripes 0.5s linear infinite;
}

@keyframes progressStripes {
    0% { background-position: 0 0; }
    100% { background-position: 30px 0; }
}

/* 成功狀態樣式 */
.success-glow {
    box-shadow: 0 0 20px rgba(40, 167, 69, 0.4);
    animation: successGlow 1s ease-out;
}

@keyframes successGlow {
    0% { box-shadow: 0 0 0 rgba(40, 167, 69, 0); }
    50% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.6); }
    100% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.4); }
}

/* 響應式增強 */
@media (max-width: 480px) {
    .pts-container {
        padding: 0 8px;
    }
    
    .processing-spinner {
        width: 30px;
        height: 30px;
        border-width: 3px;
    }
    
    .processing-text {
        font-size: 12px;
    }
}

/* 深色模式支援（預留） */
@media (prefers-color-scheme: dark) {
    :root {
        --pts-dark: #ffffff;
        --pts-light: #1a1a1a;
        --pts-border: #444444;
        --pts-text-muted: #cccccc;
    }
    
    .pts-section {
        background: #2a2a2a;
        border-color: #444444;
    }
    
    .upload-area {
        background: #333333;
        color: #ffffff;
    }
    
    .processing-overlay {
        background: rgba(42, 42, 42, 0.9);
    }
}

/* 列印樣式 */
@media print {
    .pts-container {
        max-width: none;
        padding: 0;
    }
    
    .error-card,
    .pts-section {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .execute-section,
    .error-actions {
        display: none;
    }
    
    .upload-area {
        border-style: solid;
    }
}