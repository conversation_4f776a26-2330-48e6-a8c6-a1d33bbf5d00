"""
PTS Renamer Presenter

This module contains the presenter layer for the PTS Renamer MVP architecture.
The presenter acts as the intermediary between the view (web interface) and 
the model (business logic), handling user interactions and coordinating 
business operations.
"""

from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
# import logging  # Using loguru instead
from datetime import datetime

from ..models.pts_rename_models import (
    PTSRenameJobRequest,
    PTSRenameJobStatus,
    PTSRenamePreviewRequest,
    PTSRenamePreviewResponse,
    PTSRenameUploadResponse,
    PTSRenameErrorResponse,
    PTSErrorCodes
)
from ..services.pts_rename_service import PTSRenameService, ServiceError
from ..services.pts_rename_upload_service import PTSRenameUploadService
# Using loguru for logging


class PTSRenamePresenter:
    """
    Presenter layer for PTS Renamer MVP architecture
    
    This class handles all user interactions and coordinates between
    the view layer (Flask routes/FastAPI endpoints) and the business
    logic layer (services). It ensures proper error handling, logging,
    and response formatting.
    """
    
    def __init__(self,
                 pts_service: PTSRenameService,
                 upload_service: PTSRenameUploadService,
                 logger = None):
        """
        Initialize PTS Rename Presenter
        
        Args:
            pts_service: Core PTS processing service
            upload_service: File upload handling service
            logger: Optional logger instance
        """
        self.pts_service = pts_service
        self.upload_service = upload_service
        from loguru import logger as loguru_logger
        self.logger = logger or loguru_logger
    
    async def handle_upload_request(self, files: List[Any]) -> Dict[str, Any]:
        """
        Handle file upload request from web interface
        
        This method processes uploaded files, validates them, and prepares
        them for PTS processing. It handles both individual PTS files and
        compressed archives containing PTS files.
        
        Args:
            files: List of uploaded file objects from Flask/FastAPI
            
        Returns:
            Dictionary containing upload response data
        """
        try:
            self.logger.info(f"Processing upload request with {len(files)} files")
            
            # Validate upload request
            validation_result = await self._validate_upload_request(files)
            if not validation_result["valid"]:
                return self._create_error_response(
                    PTSErrorCodes.UPLOAD_ERROR,
                    validation_result["message"],
                    {"validation_errors": validation_result["errors"]}
                )
            
            # Process upload using upload service
            upload_response = await self.upload_service.handle_compressed_upload(files)
            
            self.logger.info(f"Upload completed successfully: {upload_response.upload_id}")
            
            return {
                "success": True,
                "upload_id": upload_response.upload_id,
                "files_uploaded": upload_response.files_uploaded,
                "compressed_files_extracted": upload_response.compressed_files_extracted,
                "pts_files_found": upload_response.pts_files_found,
                "upload_size": upload_response.upload_size,
                "status": upload_response.status,
                "message": upload_response.message
            }
            
        except ServiceError as e:
            self.logger.error(f"Upload service error: {e}")
            return self._create_error_response(
                PTSErrorCodes.UPLOAD_ERROR,
                str(e),
                e.details
            )
        except Exception as e:
            self.logger.error(f"Unexpected upload error: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Upload processing failed",
                {"exception": str(e)}
            )
    
    async def handle_processing_request(self, request: PTSRenameJobRequest) -> Dict[str, Any]:
        """
        Handle PTS processing request from web interface
        
        This method validates the processing request, queues the processing
        job using Dramatiq, and returns the job status for tracking.
        
        Args:
            request: Processing request with configuration
            
        Returns:
            Dictionary containing job status and tracking information
        """
        try:
            self.logger.info(f"Processing request for upload {request.upload_id}")
            
            # Validate processing request
            validation_result = await self._validate_processing_request(request)
            if not validation_result["valid"]:
                return self._create_error_response(
                    PTSErrorCodes.INVALID_RENAME_PATTERN,
                    validation_result["message"],
                    {"validation_errors": validation_result["errors"]}
                )
            
            # Start processing using core service
            job_id = await self.pts_service.process_pts_files(request)
            
            # Get initial job status
            job_status = await self.pts_service.get_job_status(job_id)
            
            self.logger.info(f"Processing job {job_id} queued successfully")
            
            return {
                "success": True,
                "job_id": job_id,
                "status": job_status.status if job_status else "pending",
                "message": "Processing job queued successfully",
                "estimated_completion": self._estimate_completion_time(request)
            }
            
        except ServiceError as e:
            self.logger.error(f"Processing service error: {e}")
            return self._create_error_response(
                PTSErrorCodes.PROCESSING_TIMEOUT,
                str(e),
                e.details
            )
        except Exception as e:
            self.logger.error(f"Unexpected processing error: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Processing request failed",
                {"exception": str(e)}
            )
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get processing job status for web interface
        
        Args:
            job_id: Job identifier
            
        Returns:
            Dictionary containing current job status and progress
        """
        try:
            job_status = await self.pts_service.get_job_status(job_id)
            
            if not job_status:
                return self._create_error_response(
                    PTSErrorCodes.INTERNAL_ERROR,
                    f"Job {job_id} not found"
                )
            
            return {
                "success": True,
                "job_id": job_status.job_id,
                "status": job_status.status,
                "progress": job_status.progress,
                "files_processed": job_status.files_processed,
                "total_files": job_status.total_files,
                "error_message": job_status.error_message,
                "download_url": job_status.result_download_url,
                "download_expires_at": job_status.download_expires_at.isoformat() if job_status.download_expires_at else None,
                "created_at": job_status.created_at.isoformat(),
                "updated_at": job_status.updated_at.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get job status: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Failed to retrieve job status",
                {"exception": str(e)}
            )
    
    async def get_preview(self, request: PTSRenamePreviewRequest) -> Dict[str, Any]:
        """
        Generate processing preview for web interface
        
        Args:
            request: Preview request with configuration
            
        Returns:
            Dictionary containing preview information
        """
        try:
            self.logger.info(f"Generating preview for upload {request.upload_id}")
            
            # Generate preview using core service
            preview = await self.pts_service.generate_preview(
                request.upload_id,
                [op.value for op in request.operations],
                request.rename_config
            )
            
            return {
                "success": True,
                "upload_id": preview.upload_id,
                "total_files": preview.total_files,
                "files_preview": [
                    {
                        "original_name": fp.original_name,
                        "new_name": fp.new_name,
                        "qc_file_name": fp.qc_file_name,
                        "directory_name": fp.directory_name,
                        "file_size": fp.file_size,
                        "operations_applied": fp.operations_applied,
                        "warnings": fp.warnings
                    }
                    for fp in preview.files_preview
                ],
                "operations_summary": preview.operations_summary,
                "estimated_processing_time": preview.estimated_processing_time,
                "warnings": preview.warnings,
                "errors": preview.errors
            }
            
        except ServiceError as e:
            self.logger.error(f"Preview service error: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                str(e),
                e.details
            )
        except Exception as e:
            self.logger.error(f"Unexpected preview error: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Preview generation failed",
                {"exception": str(e)}
            )
    
    async def handle_download_request(self, job_id: str) -> Dict[str, Any]:
        """
        Handle download request for processed files
        
        Args:
            job_id: Job identifier
            
        Returns:
            Dictionary containing download information
        """
        try:
            job_status = await self.pts_service.get_job_status(job_id)
            
            if not job_status:
                return self._create_error_response(
                    PTSErrorCodes.INTERNAL_ERROR,
                    f"Job {job_id} not found"
                )
            
            if job_status.status != "completed":
                return self._create_error_response(
                    PTSErrorCodes.INTERNAL_ERROR,
                    f"Job {job_id} is not completed (status: {job_status.status})"
                )
            
            if not job_status.result_download_url:
                return self._create_error_response(
                    PTSErrorCodes.INTERNAL_ERROR,
                    f"Download URL not available for job {job_id}"
                )
            
            # Check if download has expired
            if job_status.download_expires_at and datetime.now() > job_status.download_expires_at:
                return self._create_error_response(
                    PTSErrorCodes.INTERNAL_ERROR,
                    "Download link has expired"
                )
            
            return {
                "success": True,
                "job_id": job_id,
                "download_url": job_status.result_download_url,
                "file_size": job_status.compressed_file_size,
                "file_name": job_status.compressed_file_name,
                "expires_at": job_status.download_expires_at.isoformat() if job_status.download_expires_at else None
            }
            
        except Exception as e:
            self.logger.error(f"Download request failed: {e}")
            return self._create_error_response(
                PTSErrorCodes.INTERNAL_ERROR,
                "Download request failed",
                {"exception": str(e)}
            )
    
    # Private helper methods
    
    async def _validate_upload_request(self, files: List[Any]) -> Dict[str, Any]:
        """Validate upload request"""
        errors = []
        
        if not files:
            errors.append("No files provided")
        
        if len(files) > 10:  # Configurable limit
            errors.append("Too many files (maximum 10 allowed)")
        
        total_size = 0
        for file in files:
            if hasattr(file, 'content_length') and file.content_length:
                total_size += file.content_length
        
        if total_size > 500 * 1024 * 1024:  # 500MB limit
            errors.append("Total upload size exceeds 500MB limit")
        
        return {
            "valid": len(errors) == 0,
            "message": "Validation failed" if errors else "Validation passed",
            "errors": errors
        }
    
    async def _validate_processing_request(self, request: PTSRenameJobRequest) -> Dict[str, Any]:
        """Validate processing request"""
        errors = []
        
        if not request.operations:
            errors.append("No operations specified")
        
        if "rename" in [op.value for op in request.operations]:
            if not request.rename_config:
                errors.append("Rename configuration required for rename operation")
            elif not request.rename_config.get("old_pattern") or not request.rename_config.get("new_pattern"):
                errors.append("Both old_pattern and new_pattern required for rename operation")
        
        return {
            "valid": len(errors) == 0,
            "message": "Validation failed" if errors else "Validation passed",
            "errors": errors
        }
    
    def _create_error_response(self, error_code: str, message: str, details: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            "success": False,
            "error": {
                "code": error_code,
                "message": message,
                "details": details or {},
                "timestamp": datetime.now().isoformat()
            }
        }
    
    def _estimate_completion_time(self, request: PTSRenameJobRequest) -> str:
        """Estimate job completion time"""
        # Simple estimation - could be made more sophisticated
        base_minutes = 2
        if request.qc_enabled:
            base_minutes += 1
        if request.create_directories:
            base_minutes += 2
        
        estimated_time = datetime.now().replace(minute=datetime.now().minute + base_minutes)
        return estimated_time.isoformat()


class PresenterError(Exception):
    """Exception raised by presenter operations"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.error_code = error_code or PTSErrorCodes.INTERNAL_ERROR
        self.details = details or {}
        self.timestamp = datetime.now()