"""
PTS Renamer Domain Entities

This module contains domain entities and value objects for the PTS File Renamer service.
These entities represent the core business objects and follow domain-driven design principles.
"""

from dataclasses import dataclass, field
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import hashlib
import re
from abc import ABC, abstractmethod


class PTSFileStatus(Enum):
    """Status enumeration for PTS files"""
    UPLOADED = "uploaded"
    EXTRACTED = "extracted"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"


class PTSOperationType(Enum):
    """Types of operations that can be performed on PTS files"""
    RENAME = "rename"
    QC_GENERATION = "qc_generation"
    DIRECTORY_CREATION = "directory_creation"


class PTSJobPriority(Enum):
    """Job priority levels"""
    LOW = 1
    NORMAL = 5
    HIGH = 8
    URGENT = 10


# Value Objects
@dataclass(frozen=True)
class UploadId:
    """Value object for upload identifier"""
    value: str
    
    def __post_init__(self):
        if not self.value or not re.match(r'^[a-zA-Z0-9_-]+$', self.value):
            raise ValueError("Invalid upload ID format")
        if len(self.value) > 255:
            raise ValueError("Upload ID too long")


@dataclass(frozen=True)
class JobId:
    """Value object for job identifier"""
    value: str
    
    def __post_init__(self):
        if not self.value or not re.match(r'^[a-zA-Z0-9_-]+$', self.value):
            raise ValueError("Invalid job ID format")
        if len(self.value) > 255:
            raise ValueError("Job ID too long")


@dataclass(frozen=True)
class FileChecksum:
    """Value object for file checksum"""
    value: str
    algorithm: str = "sha256"
    
    def __post_init__(self):
        if not self.value:
            raise ValueError("Checksum cannot be empty")
        if self.algorithm == "sha256" and len(self.value) != 64:
            raise ValueError("Invalid SHA-256 checksum length")
        if not re.match(r'^[a-fA-F0-9]+$', self.value):
            raise ValueError("Invalid checksum format")


@dataclass(frozen=True)
class RenamePattern:
    """Value object for rename patterns"""
    old_pattern: str
    new_pattern: str
    
    def __post_init__(self):
        # Validate old pattern as regex
        try:
            re.compile(self.old_pattern)
        except re.error as e:
            raise ValueError(f"Invalid regex pattern: {e}")
        
        # Validate new pattern
        if not self.new_pattern:
            raise ValueError("New pattern cannot be empty")
        
        # Check for invalid filename characters
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
        clean_pattern = self.new_pattern
        for placeholder in ['{old}', '{ext}', '{num}', '{name}', '{basename}']:
            clean_pattern = clean_pattern.replace(placeholder, '')
        
        for char in invalid_chars:
            if char in clean_pattern:
                raise ValueError(f"Invalid character '{char}' in new pattern")
    
    def apply_to_filename(self, filename: str, file_number: int = 1) -> str:
        """Apply rename pattern to filename"""
        path = Path(filename)
        old_name = path.stem
        extension = path.suffix
        
        # Apply regex substitution
        new_name = re.sub(self.old_pattern, self.new_pattern, filename)
        
        # Replace placeholders
        replacements = {
            '{old}': old_name,
            '{ext}': extension,
            '{num}': str(file_number),
            '{name}': filename,
            '{basename}': old_name
        }
        
        for placeholder, value in replacements.items():
            new_name = new_name.replace(placeholder, value)
        
        return new_name


@dataclass(frozen=True)
class ProcessingTimeout:
    """Value object for processing timeout"""
    seconds: int
    
    def __post_init__(self):
        if self.seconds < 30:
            raise ValueError("Timeout must be at least 30 seconds")
        if self.seconds > 3600:
            raise ValueError("Timeout cannot exceed 1 hour")
    
    @property
    def timedelta(self) -> timedelta:
        return timedelta(seconds=self.seconds)
    
    def is_expired(self, start_time: datetime) -> bool:
        """Check if timeout has expired since start time"""
        return datetime.now() - start_time > self.timedelta


@dataclass(frozen=True)
class PTSFile:
    """
    Value object representing a PTS file
    Immutable representation of a PTS file with its metadata
    """
    original_path: Path
    filename: str
    size: int
    checksum: FileChecksum
    upload_id: UploadId
    extracted_from: Optional[str] = None  # Original archive filename if extracted
    content_type: str = "application/octet-stream"
    created_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """Validate PTS file properties"""
        valid_extensions = ('.pts', '.cpts')
        if not self.filename.lower().endswith(valid_extensions):
            raise ValueError(f"Invalid PTS file extension: {self.filename}. Expected {valid_extensions}")
        if self.size <= 0:
            raise ValueError(f"Invalid file size: {self.size}")
        if len(self.filename) > 255:
            raise ValueError("Filename too long")
        
        # Validate filename characters
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in invalid_chars:
            if char in self.filename:
                raise ValueError(f"Invalid character '{char}' in filename")
    
    @classmethod
    def create_from_path(cls, file_path: Path, upload_id: Union[str, UploadId], 
                        extracted_from: Optional[str] = None) -> 'PTSFile':
        """Create PTSFile from file path"""
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        size = file_path.stat().st_size
        checksum_value = cls._calculate_checksum(file_path)
        
        # Convert string upload_id to UploadId if needed
        if isinstance(upload_id, str):
            upload_id = UploadId(upload_id)
        
        return cls(
            original_path=file_path,
            filename=file_path.name,
            size=size,
            checksum=FileChecksum(checksum_value),
            upload_id=upload_id,
            extracted_from=extracted_from
        )
    
    @staticmethod
    def _calculate_checksum(file_path: Path) -> str:
        """Calculate SHA-256 checksum of file"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    @property
    def file_stem(self) -> str:
        """Get filename without extension"""
        return Path(self.filename).stem
    
    @property
    def is_valid_pts_file(self) -> bool:
        """Check if this is a valid PTS file"""
        valid_extensions = ('.pts', '.cpts')
        return self.filename.lower().endswith(valid_extensions) and self.size > 0
    
    @property
    def size_mb(self) -> float:
        """Get file size in megabytes"""
        return self.size / (1024 * 1024)
    
    def verify_checksum(self) -> bool:
        """Verify file checksum against stored value"""
        if not self.original_path.exists():
            return False
        
        current_checksum = self._calculate_checksum(self.original_path)
        return current_checksum == self.checksum.value
    
    def is_pts_content_valid(self) -> bool:
        """Basic validation of PTS file content"""
        try:
            with open(self.original_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(1024)  # Read first 1KB
                # Basic PTS file validation - should contain common PTS keywords
                pts_keywords = ['Parameter', 'QA', 'Bin Definition', 'TestFlow']
                return any(keyword in content for keyword in pts_keywords)
        except Exception:
            return False


@dataclass
class PTSQCFile:
    """
    Entity representing a QC file generated from a PTS file
    Mutable entity that tracks QC file generation process
    """
    source_pts_file: PTSFile
    qc_filename: str
    qc_file_path: Optional[Path] = None
    generated_at: Optional[datetime] = None
    generation_status: PTSFileStatus = PTSFileStatus.UPLOADED
    error_message: Optional[str] = None
    
    def __post_init__(self):
        """Validate QC file properties"""
        if not self.qc_filename.endswith('_QC.pts'):
            raise ValueError(f"Invalid QC filename format: {self.qc_filename}")
    
    @classmethod
    def create_from_pts_file(cls, pts_file: PTSFile) -> 'PTSQCFile':
        """Create QC file entity from PTS file"""
        qc_filename = f"{pts_file.file_stem}_QC.pts"
        return cls(
            source_pts_file=pts_file,
            qc_filename=qc_filename
        )
    
    def mark_as_generated(self, file_path: Path) -> None:
        """Mark QC file as successfully generated"""
        self.qc_file_path = file_path
        self.generated_at = datetime.now()
        self.generation_status = PTSFileStatus.PROCESSED
    
    def mark_as_failed(self, error_message: str) -> None:
        """Mark QC file generation as failed"""
        self.error_message = error_message
        self.generation_status = PTSFileStatus.FAILED


@dataclass
class PTSDirectory:
    """
    Entity representing a directory created for a PTS file
    Mutable entity that tracks directory creation process
    """
    source_pts_file: PTSFile
    directory_name: str
    directory_path: Optional[Path] = None
    created_at: Optional[datetime] = None
    creation_status: PTSFileStatus = PTSFileStatus.UPLOADED
    files_copied: List[str] = field(default_factory=list)
    error_message: Optional[str] = None
    
    @classmethod
    def create_from_pts_file(cls, pts_file: PTSFile) -> 'PTSDirectory':
        """Create directory entity from PTS file"""
        directory_name = pts_file.file_stem
        return cls(
            source_pts_file=pts_file,
            directory_name=directory_name
        )
    
    def mark_as_created(self, directory_path: Path, copied_files: List[str]) -> None:
        """Mark directory as successfully created"""
        self.directory_path = directory_path
        self.created_at = datetime.now()
        self.files_copied = copied_files
        self.creation_status = PTSFileStatus.PROCESSED
    
    def mark_as_failed(self, error_message: str) -> None:
        """Mark directory creation as failed"""
        self.error_message = error_message
        self.creation_status = PTSFileStatus.FAILED


@dataclass
class PTSRenameResult:
    """
    Value object representing the result of a PTS file operation
    Immutable record of what happened during processing
    """
    original_name: str
    new_name: Optional[str]
    operation: PTSOperationType
    success: bool
    error_message: Optional[str] = None
    processing_time_seconds: Optional[float] = None
    file_size_bytes: Optional[int] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    @classmethod
    def success_result(cls, original_name: str, new_name: str, operation: PTSOperationType, 
                      processing_time: float = None, file_size: int = None) -> 'PTSRenameResult':
        """Create a successful result"""
        return cls(
            original_name=original_name,
            new_name=new_name,
            operation=operation,
            success=True,
            processing_time_seconds=processing_time,
            file_size_bytes=file_size
        )
    
    @classmethod
    def failure_result(cls, original_name: str, operation: PTSOperationType, 
                      error_message: str, processing_time: float = None) -> 'PTSRenameResult':
        """Create a failed result"""
        return cls(
            original_name=original_name,
            new_name=None,
            operation=operation,
            success=False,
            error_message=error_message,
            processing_time_seconds=processing_time
        )


# Domain Services (Abstract Base Classes)
class PTSFileProcessor(ABC):
    """Abstract base class for PTS file processors"""
    
    @abstractmethod
    def can_process(self, pts_file: PTSFile, operation: PTSOperationType) -> bool:
        """Check if this processor can handle the given file and operation"""
        pass
    
    @abstractmethod
    def process(self, pts_file: PTSFile, operation: PTSOperationType, 
               config: Optional[Dict[str, Any]] = None) -> PTSRenameResult:
        """Process the PTS file with the given operation"""
        pass


@dataclass
class PTSProcessingJob:
    """
    Aggregate root for PTS processing job
    Manages the entire lifecycle of a PTS processing request
    """
    job_id: JobId
    upload_id: UploadId
    pts_files: List[PTSFile]
    operations: List[PTSOperationType]
    results: List[PTSRenameResult] = field(default_factory=list)
    status: str = "pending"
    priority: PTSJobPriority = PTSJobPriority.NORMAL
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    # Processing configuration
    rename_pattern: Optional[RenamePattern] = None
    processing_timeout: ProcessingTimeout = field(default_factory=lambda: ProcessingTimeout(300))
    qc_enabled: bool = False
    create_directories: bool = False
    
    # Progress tracking
    total_files: int = 0
    files_processed: int = 0
    current_operation: Optional[PTSOperationType] = None
    
    # Result tracking
    qc_files: List[PTSQCFile] = field(default_factory=list)
    directories: List[PTSDirectory] = field(default_factory=list)
    compressed_result_path: Optional[Path] = None
    download_url: Optional[str] = None
    download_expires_at: Optional[datetime] = None
    
    # Metadata
    user_id: Optional[str] = None
    client_ip: Optional[str] = None
    user_agent: Optional[str] = None
    
    def __post_init__(self):
        """Initialize job properties"""
        self.total_files = len(self.pts_files)
        
        # Validate job configuration
        if not self.pts_files:
            raise ValueError("Job must have at least one PTS file")
        if not self.operations:
            raise ValueError("Job must have at least one operation")
    
    @classmethod
    def create_job(cls, job_id: str, upload_id: str, pts_files: List[PTSFile], 
                   operations: List[PTSOperationType], **kwargs) -> 'PTSProcessingJob':
        """Factory method to create a new processing job"""
        return cls(
            job_id=JobId(job_id),
            upload_id=UploadId(upload_id),
            pts_files=pts_files,
            operations=operations,
            **kwargs
        )
    
    def start_processing(self) -> None:
        """Mark job as started"""
        if self.status != "pending":
            raise ValueError(f"Cannot start job in status: {self.status}")
        
        self.status = "processing"
        self.started_at = datetime.now()
        self.updated_at = datetime.now()
    
    def set_current_operation(self, operation: PTSOperationType) -> None:
        """Set the current operation being processed"""
        self.current_operation = operation
        self.updated_at = datetime.now()
    
    def add_result(self, result: PTSRenameResult) -> None:
        """Add a processing result"""
        self.results.append(result)
        if result.success:
            self.files_processed += 1
        self.updated_at = datetime.now()
    
    def mark_as_completed(self, compressed_result_path: Optional[Path] = None, 
                         download_url: Optional[str] = None) -> None:
        """Mark job as completed"""
        if self.status not in ["processing", "compressing"]:
            raise ValueError(f"Cannot complete job in status: {self.status}")
        
        self.status = "completed"
        self.completed_at = datetime.now()
        self.updated_at = datetime.now()
        self.compressed_result_path = compressed_result_path
        self.download_url = download_url
        self.current_operation = None
        
        # Set download expiration (24 hours from completion)
        if download_url:
            self.download_expires_at = datetime.now() + timedelta(hours=24)
    
    def mark_as_failed(self, error_message: str) -> None:
        """Mark job as failed"""
        self.status = "failed"
        self.error_message = error_message
        self.completed_at = datetime.now()
        self.updated_at = datetime.now()
        self.current_operation = None
    
    def cancel(self, reason: str = "User cancelled") -> None:
        """Cancel the job"""
        if self.is_completed:
            raise ValueError("Cannot cancel completed job")
        
        self.status = "cancelled"
        self.error_message = reason
        self.completed_at = datetime.now()
        self.updated_at = datetime.now()
        self.current_operation = None
    
    def is_timeout_exceeded(self) -> bool:
        """Check if processing timeout has been exceeded"""
        if not self.started_at:
            return False
        return self.processing_timeout.is_expired(self.started_at)
    
    @property
    def progress_percentage(self) -> int:
        """Calculate progress percentage"""
        if self.total_files == 0:
            return 0
        return min(100, int((self.files_processed / self.total_files) * 100))
    
    @property
    def is_completed(self) -> bool:
        """Check if job is completed (success or failure)"""
        return self.status in ["completed", "failed", "cancelled"]
    
    @property
    def is_active(self) -> bool:
        """Check if job is currently active"""
        return self.status in ["pending", "processing", "compressing"]
    
    @property
    def processing_duration(self) -> Optional[float]:
        """Get processing duration in seconds"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        elif self.started_at:
            return (datetime.now() - self.started_at).total_seconds()
        return None
    
    @property
    def estimated_completion_time(self) -> Optional[datetime]:
        """Estimate completion time based on current progress"""
        if not self.started_at or self.files_processed == 0:
            return None
        
        elapsed = datetime.now() - self.started_at
        avg_time_per_file = elapsed.total_seconds() / self.files_processed
        remaining_files = self.total_files - self.files_processed
        estimated_remaining = timedelta(seconds=avg_time_per_file * remaining_files)
        
        return datetime.now() + estimated_remaining
    
    def get_operation_summary(self) -> Dict[str, int]:
        """Get summary of operations performed"""
        summary = {}
        for operation in PTSOperationType:
            count = sum(1 for result in self.results 
                       if result.operation == operation and result.success)
            summary[operation.value] = count
        return summary
    
    def get_success_rate(self) -> float:
        """Get success rate as percentage"""
        if not self.results:
            return 0.0
        successful = sum(1 for result in self.results if result.success)
        return (successful / len(self.results)) * 100
    
    def get_failed_operations(self) -> List[PTSRenameResult]:
        """Get list of failed operations"""
        return [result for result in self.results if not result.success]
    
    def validate_for_processing(self) -> List[str]:
        """Validate job configuration before processing"""
        errors = []
        
        # Check if files still exist
        for pts_file in self.pts_files:
            if not pts_file.original_path.exists():
                errors.append(f"File not found: {pts_file.filename}")
        
        # Validate rename configuration
        if PTSOperationType.RENAME in self.operations and not self.rename_pattern:
            errors.append("Rename pattern required for rename operation")
        
        # Check timeout
        if self.is_timeout_exceeded():
            errors.append("Job processing timeout exceeded")
        
        return errors