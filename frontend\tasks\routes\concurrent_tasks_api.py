"""
並發任務管理器 API 模組
專門處理企業級並發任務管理相關的所有 API 端點
"""

from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

from fastapi import APIRouter, HTTPException, Form
from fastapi.responses import HTMLResponse
from loguru import logger

from backend.tasks.services.concurrent_task_manager import get_task_manager, TaskPriority

# 建立 APIRouter 實例
router = APIRouter(
    tags=["並發任務管理"],
    responses={404: {"description": "Not found"}}
)

# ================================
# 並發任務管理 API 端點
# ================================

@router.post("/api/tasks/code_comparison")
async def submit_code_comparison_task(
    input_path: str = Form(...),
    code_region: Optional[str] = Form(None),
    with_excel: bool = Form(False),
    verbose: bool = Form(False),
    priority: str = Form("normal")
) -> Dict[str, Any]:
    """
    提交程式碼對比任務（非阻塞）
    
    Args:
        input_path: 輸入路徑（資料夾或壓縮檔）
        code_region: 程式碼區間設定，格式：main_start,main_end,backup_start,backup_end
        with_excel: 是否產生Excel檔案
        verbose: 詳細模式
        priority: 任務優先級 (low, normal, high, critical)
    
    Returns:
        Dict: 包含任務ID的響應
    """
    try:
        # 解析優先級
        priority_map = {
            "low": TaskPriority.LOW,
            "normal": TaskPriority.NORMAL,
            "high": TaskPriority.HIGH,
            "critical": TaskPriority.CRITICAL
        }
        task_priority = priority_map.get(priority.lower(), TaskPriority.NORMAL)
        
        # 獲取任務管理器並提交任務
        task_manager = get_task_manager()
        task_id = task_manager.submit_code_comparison_task(
            input_path=input_path,
            code_region=code_region,
            with_excel=with_excel,
            verbose=verbose,
            priority=task_priority
        )
        
        logger.info(f"[TASK] 程式碼對比任務已提交: {task_id}")
        
        return {
            "status": "accepted",
            "message": "任務已提交，正在後台執行",
            "task_id": task_id,
            "estimated_duration": "30分鐘",
            "api_version": "2.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except ValueError as e:
        logger.error(f"[ERROR] 任務提交參數錯誤: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"[ERROR] 任務提交失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/tasks/system/status")
async def get_system_status() -> Dict[str, Any]:
    """
    獲取系統狀態
    
    Returns:
        Dict: 系統狀態資訊
    """
    try:
        task_manager = get_task_manager()
        system_status = task_manager.get_system_status()
        
        logger.info(f"[QUERY] 系統狀態查詢: {system_status['active_tasks']} 個活躍任務")
        
        return {
            "status": "success",
            "data": system_status,
            "api_version": "2.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[ERROR] 獲取系統狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/tasks/active")
async def list_active_tasks() -> Dict[str, Any]:
    """
    列出活躍任務
    
    Returns:
        Dict: 活躍任務列表
    """
    try:
        task_manager = get_task_manager()
        active_tasks = task_manager.list_active_tasks()
        
        logger.info(f"[QUERY] 活躍任務查詢: {len(active_tasks)} 個任務")
        
        return {
            "status": "success",
            "data": active_tasks,
            "count": len(active_tasks),
            "api_version": "2.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[ERROR] 獲取活躍任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/tasks/completed")
async def list_completed_tasks(limit: int = 50) -> Dict[str, Any]:
    """
    列出已完成任務
    
    Args:
        limit: 返回數量限制
    
    Returns:
        Dict: 已完成任務列表
    """
    try:
        task_manager = get_task_manager()
        completed_tasks = task_manager.list_completed_tasks(limit=limit)
        
        logger.info(f"[QUERY] 已完成任務查詢: {len(completed_tasks)} 個任務")
        
        return {
            "status": "success",
            "data": completed_tasks,
            "count": len(completed_tasks),
            "limit": limit,
            "api_version": "2.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[ERROR] 獲取已完成任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/tasks/{task_id}/status")
async def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    獲取任務狀態
    
    Args:
        task_id: 任務ID
        
    Returns:
        Dict: 任務狀態資訊
    """
    try:
        task_manager = get_task_manager()
        task_status = task_manager.get_task_status(task_id)
        
        if not task_status:
            raise HTTPException(status_code=404, detail="任務不存在")
        
        logger.info(f"[QUERY] 任務狀態查詢: {task_id} - {task_status['status']}")
        
        return {
            "status": "success",
            "data": task_status,
            "api_version": "2.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ERROR] 獲取任務狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/api/tasks/{task_id}")
async def cancel_task(task_id: str) -> Dict[str, Any]:
    """
    取消任務
    
    Args:
        task_id: 任務ID
        
    Returns:
        Dict: 取消結果
    """
    try:
        task_manager = get_task_manager()
        cancelled = task_manager.cancel_task(task_id)
        
        if not cancelled:
            raise HTTPException(status_code=400, detail="任務無法取消（可能已完成或不存在）")
        
        logger.info(f"[CANCEL] 任務已取消: {task_id}")
        
        return {
            "status": "success",
            "message": "任務已取消",
            "task_id": task_id,
            "api_version": "2.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ERROR] 取消任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task_manager", response_class=HTMLResponse)
async def task_manager_ui():
    """
    並發任務管理器使用者介面
    
    Returns:
        HTMLResponse: 任務管理器前端介面
    """
    try:
        # 讀取 HTML 模板
        template_path = Path(__file__).parent.parent / "web" / "templates" / "concurrent_task_manager.html"
        
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            return HTMLResponse(content=html_content)
        else:
            # 如果模板檔案不存在，返回簡單的 HTML
            simple_html = """
            <!DOCTYPE html>
            <html lang="zh-TW">
            <head>
                <meta charset="UTF-8">
                <title>並發任務管理器</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 800px; margin: 0 auto; }
                    .btn { padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>並發任務管理器</h1>
                    <p>模板檔案未找到，請檢查檔案路徑：</p>
                    <p><code>{template_path}</code></p>
                    <p>您可以通過以下 API 端點進行操作：</p>
                    <ul>
                        <li><a href="/api/tasks/active" class="btn">查看活躍任務</a></li>
                        <li><a href="/api/tasks/completed" class="btn">查看已完成任務</a></li>
                        <li><a href="/api/tasks/system/status" class="btn">系統狀態</a></li>
                        <li><a href="/docs" class="btn">API 文檔</a></li>
                    </ul>
                </div>
            </body>
            </html>
            """.format(template_path=template_path)
            return HTMLResponse(content=simple_html)
    except Exception as e:
        logger.error(f"載入任務管理器介面失敗：{str(e)}")
        raise HTTPException(status_code=500, detail="無法載入介面")

# ================================
# 並發任務管理器生命週期管理
# ================================

def initialize_concurrent_task_manager() -> None:
    """
    初始化並發任務管理器
    
    這個函數被主應用程式在啟動時調用
    """
    try:
        task_manager = get_task_manager(
            max_workers=4,
            enable_notifications=True
        )
        logger.info("[OK] 並發任務管理器已初始化")
    except Exception as e:
        logger.error(f"[ERROR] 並發任務管理器初始化失敗: {str(e)}")

def shutdown_concurrent_task_manager() -> None:
    """
    優雅關閉並發任務管理器
    
    這個函數被主應用程式在關閉時調用
    """
    try:
        from backend.tasks.services.concurrent_task_manager import _task_manager_instance
        if _task_manager_instance:
            _task_manager_instance.shutdown(wait=True)
            logger.info("[OK] 並發任務管理器已優雅關閉")
    except Exception as e:
        logger.error(f"[ERROR] 關閉並發任務管理器時發生錯誤: {str(e)}")