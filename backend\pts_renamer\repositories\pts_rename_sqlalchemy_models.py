"""
PTS Renamer SQLAlchemy ORM Models

This module contains SQLAlchemy ORM models for PTS Renamer data persistence.
These models are designed to work with SQLAlchemy sessions and queries.
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, 
    ForeignKey, Index, UniqueConstraint, JSON, func
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, validates
import json
from loguru import logger

Base = declarative_base()


class PTSRenameJobModel(Base):
    """
    SQLAlchemy ORM model for PTS processing jobs
    
    This is the proper ORM model that can be used with SQLAlchemy sessions
    """
    __tablename__ = 'pts_rename_jobs'
    
    # Primary key
    id = Column(String(50), primary_key=True)
    
    # Core job fields
    upload_id = Column(String(255), nullable=True, index=True)
    status = Column(String(50), nullable=False, default='pending', index=True)
    
    # Configuration fields (stored as JSON text)
    operations = Column(Text, nullable=True)  # JSON string
    rename_config = Column(Text, nullable=True)  # JSON string
    
    # Processing options
    qc_enabled = Column(Boolean, default=False, nullable=False)
    create_directories = Column(Boolean, default=False, nullable=False)
    
    # Progress tracking
    progress = Column(Integer, default=0, nullable=False)
    files_processed = Column(Integer, default=0, nullable=False)
    total_files = Column(Integer, default=0, nullable=False)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    
    # Results and download
    result_download_url = Column(String(500), nullable=True)
    compressed_file_size = Column(Integer, nullable=True)
    compressed_file_name = Column(String(255), nullable=True)
    download_expires_at = Column(DateTime, nullable=True)
    
    # Timestamps - use func.now() to prevent datetime serialization issues
    created_at = Column(DateTime, default=func.now(), nullable=False, index=True)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    files = relationship("PTSRenameFileModel", back_populates="job", cascade="all, delete-orphan")
    results = relationship("PTSRenameResultModel", back_populates="job", cascade="all, delete-orphan")
    
    def __init__(self, **kwargs):
        # Debug logging
        logger.info(f"[DEBUG_DATETIME] PTSRenameJobModel.__init__ called with kwargs: {list(kwargs.keys())}")
        for key, value in kwargs.items():
            if 'created_at' in key or 'updated_at' in key:
                logger.info(f"[DEBUG_DATETIME]   {key}: {value} (type: {type(value)})")
        
        # Handle JSON serialization for operations and rename_config
        if 'operations' in kwargs and not isinstance(kwargs['operations'], str):
            kwargs['operations'] = json.dumps(kwargs['operations'] or [])
        if 'rename_config' in kwargs and not isinstance(kwargs['rename_config'], str):
            kwargs['rename_config'] = json.dumps(kwargs['rename_config'] or {})
        
        # CRITICAL FIX: Completely remove datetime fields to prevent SQLite error
        # The SQLAlchemy Column defaults will handle datetime creation
        datetime_fields = ['created_at', 'updated_at', 'started_at', 'completed_at', 'download_expires_at']
        for field in datetime_fields:
            if field in kwargs:
                logger.warning(f"[DEBUG_DATETIME] REMOVED datetime field {field}: {kwargs[field]} (type: {type(kwargs[field])}) to prevent SQLite error")
                del kwargs[field]
        
        super().__init__(**kwargs)
    
    def __setattr__(self, key, value):
        # Additional protection against string datetime assignment
        datetime_fields = ['created_at', 'updated_at', 'started_at', 'completed_at', 'download_expires_at']
        if key in datetime_fields and isinstance(value, str):
            try:
                from datetime import datetime as dt
                # Parse ISO format datetime string back to datetime object
                date_str = value
                if date_str.endswith('Z'):
                    date_str = date_str.replace('Z', '+00:00')
                elif '+' not in date_str and 'T' in date_str and len(date_str) > 19:
                    date_str = date_str[:26]
                
                value = dt.fromisoformat(date_str)
                logger.info(f"[DEBUG_DATETIME] __setattr__ converted string datetime {key}: {value}")
            except (ValueError, AttributeError) as e:
                # If parsing fails, let SQLAlchemy use its default
                logger.warning(f"[DEBUG_DATETIME] __setattr__ failed to parse {key}: {value}, error: {e}")
                return  # Don't set the attribute at all
        
        super().__setattr__(key, value)
    
    def to_dict(self):
        """Convert to dictionary representation"""
        return {
            'id': self.id,
            'upload_id': self.upload_id,
            'status': self.status,
            'operations': json.loads(self.operations) if self.operations else [],
            'rename_config': json.loads(self.rename_config) if self.rename_config else {},
            'qc_enabled': self.qc_enabled,
            'create_directories': self.create_directories,
            'progress': self.progress,
            'files_processed': self.files_processed,
            'total_files': self.total_files,
            'error_message': self.error_message,
            'result_download_url': self.result_download_url,
            'compressed_file_size': self.compressed_file_size,
            'compressed_file_name': self.compressed_file_name,
            'download_expires_at': self.download_expires_at.isoformat() if self.download_expires_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
    
    @validates('status')
    def validate_status(self, key, status):
        """Validate job status values"""
        valid_statuses = [
            'pending', 'upload_completed', 'extracting', 'processing', 
            'compressing', 'completed', 'failed', 'cancelled'
        ]
        if status not in valid_statuses:
            raise ValueError(f"Invalid status: {status}. Must be one of: {valid_statuses}")
        return status
    
    @validates('progress')
    def validate_progress(self, key, progress):
        """Validate progress percentage"""
        if progress < 0 or progress > 100:
            raise ValueError(f"Progress must be between 0 and 100, got: {progress}")
        return progress
    
    # Helper properties for compatibility with repository conversion methods
    
    @property
    def operations_list(self):
        """Get operations as a list"""
        return json.loads(self.operations) if self.operations else []
    
    @operations_list.setter
    def operations_list(self, value):
        """Set operations from a list"""
        self.operations = json.dumps(value)
    
    @property
    def rename_config_dict(self):
        """Get rename config as a dictionary"""
        return json.loads(self.rename_config) if self.rename_config else {}
    
    @rename_config_dict.setter
    def rename_config_dict(self, value):
        """Set rename config from a dictionary"""
        self.rename_config = json.dumps(value)


class PTSRenameFileModel(Base):
    """
    SQLAlchemy ORM model for PTS files
    
    Tracks individual PTS files within processing jobs
    """
    __tablename__ = 'pts_rename_files'
    
    # Primary key
    id = Column(String(50), primary_key=True)
    
    # Foreign key to job
    job_id = Column(String(50), ForeignKey('pts_rename_jobs.id'), nullable=False, index=True)
    
    # File information
    original_path = Column(String(500), nullable=False)
    filename = Column(String(255), nullable=False)
    size = Column(Integer, nullable=False, default=0)
    checksum = Column(String(64), nullable=True)  # SHA-256 checksum
    extracted_from = Column(String(255), nullable=True)  # Original archive filename if extracted
    
    # Processing status
    processed = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # Relationships
    job = relationship("PTSRenameJobModel", back_populates="files")
    results = relationship("PTSRenameResultModel", back_populates="file", cascade="all, delete-orphan")
    
    def __init__(self, **kwargs):
        # Handle datetime string serialization - convert back to datetime objects
        datetime_fields = ['created_at']
        for field in datetime_fields:
            if field in kwargs and isinstance(kwargs[field], str):
                try:
                    # Parse ISO format datetime string back to datetime object
                    from datetime import datetime as dt
                    # Handle various ISO format variations
                    date_str = kwargs[field]
                    if date_str.endswith('Z'):
                        date_str = date_str.replace('Z', '+00:00')
                    elif '+' not in date_str and 'T' in date_str and len(date_str) > 19:
                        # Handle microseconds without timezone
                        date_str = date_str[:26]
                    
                    kwargs[field] = dt.fromisoformat(date_str)
                except (ValueError, AttributeError) as e:
                    # If parsing fails, remove the field to let Column default take over
                    logger.warning(f"[DEBUG_DATETIME] Failed to parse datetime field {field}: {kwargs.get(field)}, error: {e}, using Column default")
                    del kwargs[field]
        
        super().__init__(**kwargs)
    
    def to_dict(self):
        """Convert to dictionary representation"""
        return {
            'id': self.id,
            'job_id': self.job_id,
            'original_path': self.original_path,
            'filename': self.filename,
            'size': self.size,
            'checksum': self.checksum,
            'extracted_from': self.extracted_from,
            'processed': self.processed,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class PTSRenameResultModel(Base):
    """
    SQLAlchemy ORM model for processing results
    
    Tracks the results of operations performed on PTS files
    """
    __tablename__ = 'pts_rename_results'
    
    # Primary key
    id = Column(String(50), primary_key=True)
    
    # Foreign keys
    job_id = Column(String(50), ForeignKey('pts_rename_jobs.id'), nullable=False, index=True)
    file_id = Column(String(50), ForeignKey('pts_rename_files.id'), nullable=True, index=True)
    
    # Operation details
    operation = Column(String(50), nullable=False)
    original_name = Column(String(255), nullable=False)
    new_name = Column(String(255), nullable=True)
    
    # Result status
    success = Column(Boolean, nullable=False, default=False)
    error_message = Column(Text, nullable=True)
    processing_time_seconds = Column(Integer, nullable=True)  # Processing time in seconds
    file_size_bytes = Column(Integer, nullable=True)  # File size in bytes
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # Relationships
    job = relationship("PTSRenameJobModel", back_populates="results")
    file = relationship("PTSRenameFileModel", back_populates="results")
    
    def __init__(self, **kwargs):
        # Handle datetime string serialization - convert back to datetime objects
        datetime_fields = ['created_at']
        for field in datetime_fields:
            if field in kwargs and isinstance(kwargs[field], str):
                try:
                    # Parse ISO format datetime string back to datetime object
                    from datetime import datetime as dt
                    # Handle various ISO format variations
                    date_str = kwargs[field]
                    if date_str.endswith('Z'):
                        date_str = date_str.replace('Z', '+00:00')
                    elif '+' not in date_str and 'T' in date_str and len(date_str) > 19:
                        # Handle microseconds without timezone
                        date_str = date_str[:26]
                    
                    kwargs[field] = dt.fromisoformat(date_str)
                except (ValueError, AttributeError) as e:
                    # If parsing fails, remove the field to let Column default take over
                    logger.warning(f"[DEBUG_DATETIME] Failed to parse datetime field {field}: {kwargs.get(field)}, error: {e}, using Column default")
                    del kwargs[field]
        
        super().__init__(**kwargs)
    
    def to_dict(self):
        """Convert to dictionary representation"""
        return {
            'id': self.id,
            'job_id': self.job_id,
            'file_id': self.file_id,
            'operation': self.operation,
            'original_name': self.original_name,
            'new_name': self.new_name,
            'success': self.success,
            'error_message': self.error_message,
            'processing_time_seconds': self.processing_time_seconds,
            'file_size_bytes': self.file_size_bytes,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


# Add indexes for performance
Index('idx_pts_jobs_status', PTSRenameJobModel.status)
Index('idx_pts_jobs_upload', PTSRenameJobModel.upload_id)
Index('idx_pts_jobs_created', PTSRenameJobModel.created_at)
Index('idx_pts_files_job', PTSRenameFileModel.job_id)
Index('idx_pts_results_job', PTSRenameResultModel.job_id)
Index('idx_pts_results_file', PTSRenameResultModel.file_id)