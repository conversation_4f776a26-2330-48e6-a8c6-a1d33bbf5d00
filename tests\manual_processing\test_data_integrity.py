#!/usr/bin/env python3
"""
手動處理功能 - 資料完整性測試
測試郵件資料的正確傳遞、驗證和儲存
"""

import pytest
import requests
import json
import os
import sqlite3
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path


class TestDataIntegrity:
    """資料完整性測試類"""
    
    def __init__(self):
        self.base_url = os.getenv('API_BASE_URL', 'http://localhost:5000')
        self.api_key = os.getenv('PARSER_API_KEY', 'dev-parser-key-12345')
        self.headers = {
            'Content-Type': 'application/json',
            'X-API-Key': self.api_key
        }
        self.test_results = []
        self.db_path = self._find_database_path()
        self.test_email_ids = []
    
    def _find_database_path(self) -> str:
        """尋找資料庫檔案路徑"""
        possible_paths = [
            'D:/project/python/outlook_summary/data/email_inbox.db',
            './data/email_inbox.db',
            './email_inbox.db'
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                return path
        
        # 如果找不到，使用預設路徑
        return 'D:/project/python/outlook_summary/data/email_inbox.db'
    
    def log_test_result(self, test_name: str, passed: bool, details: str = "", error: str = ""):
        """記錄測試結果"""
        result = {
            'test_name': test_name,
            'passed': passed,
            'timestamp': datetime.now().isoformat(),
            'details': details,
            'error': error
        }
        self.test_results.append(result)
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"[{status}] {test_name}")
        if details:
            print(f"    詳細: {details}")
        if error:
            print(f"    錯誤: {error}")
    
    def setup_test_data(self):
        """準備測試資料"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 創建測試郵件
            test_emails = [
                {
                    'subject': 'Test Manual Processing Email 1',
                    'sender': '<EMAIL>',
                    'body': 'Test body for manual processing',
                    'parse_status': 'pending',
                    'received_time': datetime.now().isoformat()
                },
                {
                    'subject': 'Test Manual Processing Email 2',
                    'sender': '<EMAIL>',
                    'body': 'Another test body',
                    'parse_status': 'failed',
                    'received_time': datetime.now().isoformat()
                }
            ]
            
            for email in test_emails:
                cursor.execute("""
                    INSERT INTO emails (subject, sender, body, parse_status, received_time, message_id)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    email['subject'],
                    email['sender'],
                    email['body'],
                    email['parse_status'],
                    email['received_time'],
                    f"test-{datetime.now().timestamp()}-{len(self.test_email_ids)}"
                ))
                
                email_id = cursor.lastrowid
                self.test_email_ids.append(email_id)
            
            conn.commit()
            conn.close()
            
            self.log_test_result(
                "測試資料準備",
                True,
                f"成功建立 {len(self.test_email_ids)} 筆測試郵件"
            )
            
        except Exception as e:
            self.log_test_result(
                "測試資料準備",
                False,
                "",
                str(e)
            )
    
    def test_required_fields_validation(self):
        """測試必填欄位驗證"""
        if not self.test_email_ids:
            self.log_test_result(
                "必填欄位驗證",
                False,
                "",
                "沒有可用的測試郵件 ID"
            )
            return
        
        email_id = self.test_email_ids[0]
        endpoint = f"/api/parser/emails/{email_id}/manual-input"
        url = f"{self.base_url}{endpoint}"
        
        # 測試缺少必填欄位的情況
        test_cases = [
            {
                'name': '缺少 vendor_code',
                'data': {'pd': 'TEST_PD', 'lot': 'TEST_LOT'},
                'should_fail': True
            },
            {
                'name': '缺少 pd',
                'data': {'vendor_code': 'TEST', 'lot': 'TEST_LOT'},
                'should_fail': True
            },
            {
                'name': '缺少 lot',
                'data': {'vendor_code': 'TEST', 'pd': 'TEST_PD'},
                'should_fail': True
            },
            {
                'name': '所有必填欄位完整',
                'data': {'vendor_code': 'TEST', 'pd': 'TEST_PD', 'lot': 'TEST_LOT'},
                'should_fail': False
            }
        ]
        
        for test_case in test_cases:
            try:
                response = requests.post(url, headers=self.headers, json=test_case['data'], timeout=10)
                
                if test_case['should_fail']:
                    # 應該失敗的情況
                    if response.status_code == 400:
                        self.log_test_result(
                            f"必填欄位驗證 - {test_case['name']}",
                            True,
                            "正確拒絕不完整資料"
                        )
                    else:
                        self.log_test_result(
                            f"必填欄位驗證 - {test_case['name']}",
                            False,
                            f"應該返回 400，實際: {response.status_code}",
                            response.text[:200]
                        )
                else:
                    # 應該成功的情況
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('success'):
                            self.log_test_result(
                                f"必填欄位驗證 - {test_case['name']}",
                                True,
                                "正確接受完整資料"
                            )
                        else:
                            self.log_test_result(
                                f"必填欄位驗證 - {test_case['name']}",
                                False,
                                "API 返回成功但 success 為 false",
                                json.dumps(data, ensure_ascii=False)
                            )
                    else:
                        self.log_test_result(
                            f"必填欄位驗證 - {test_case['name']}",
                            False,
                            f"應該返回 200，實際: {response.status_code}",
                            response.text[:200]
                        )
                        
            except Exception as e:
                self.log_test_result(
                    f"必填欄位驗證 - {test_case['name']}",
                    False,
                    "",
                    str(e)
                )
    
    def test_optional_fields_handling(self):
        """測試選填欄位處理"""
        if not self.test_email_ids:
            return
        
        email_id = self.test_email_ids[0]
        endpoint = f"/api/parser/emails/{email_id}/manual-input"
        url = f"{self.base_url}{endpoint}"
        
        # 測試包含選填欄位的資料
        test_data = {
            'vendor_code': 'JCET',
            'pd': 'G2892K21D(CA)',
            'lot': 'YHW0049.Y',
            'mo': '**********.1',
            'yield_value': 97.38
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=test_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    # 檢查資料庫是否正確儲存
                    if self._verify_database_update(email_id, test_data):
                        self.log_test_result(
                            "選填欄位處理",
                            True,
                            "選填欄位正確儲存到資料庫"
                        )
                    else:
                        self.log_test_result(
                            "選填欄位處理",
                            False,
                            "API 成功但資料庫未正確更新",
                            ""
                        )
                else:
                    self.log_test_result(
                        "選填欄位處理",
                        False,
                        "API 返回失敗",
                        json.dumps(data, ensure_ascii=False)
                    )
            else:
                self.log_test_result(
                    "選填欄位處理",
                    False,
                    f"API 調用失敗，狀態碼: {response.status_code}",
                    response.text[:200]
                )
                
        except Exception as e:
            self.log_test_result(
                "選填欄位處理",
                False,
                "",
                str(e)
            )
    
    def test_data_format_validation(self):
        """測試資料格式驗證"""
        if not self.test_email_ids:
            return
        
        email_id = self.test_email_ids[0]
        endpoint = f"/api/parser/emails/{email_id}/manual-input"
        url = f"{self.base_url}{endpoint}"
        
        # 測試各種格式驗證
        test_cases = [
            {
                'name': '良率超出範圍 (大於100)',
                'data': {
                    'vendor_code': 'TEST',
                    'pd': 'TEST_PD',
                    'lot': 'TEST_LOT',
                    'yield_value': 150.0
                },
                'should_fail': True
            },
            {
                'name': '良率超出範圍 (小於0)',
                'data': {
                    'vendor_code': 'TEST',
                    'pd': 'TEST_PD',
                    'lot': 'TEST_LOT',
                    'yield_value': -10.0
                },
                'should_fail': True
            },
            {
                'name': '良率正常範圍',
                'data': {
                    'vendor_code': 'TEST',
                    'pd': 'TEST_PD',
                    'lot': 'TEST_LOT',
                    'yield_value': 95.5
                },
                'should_fail': False
            },
            {
                'name': '包含特殊字符的產品編號',
                'data': {
                    'vendor_code': 'TEST',
                    'pd': 'G2892K21D(CA)-V1.0',
                    'lot': 'TEST_LOT'
                },
                'should_fail': False
            }
        ]
        
        for test_case in test_cases:
            try:
                response = requests.post(url, headers=self.headers, json=test_case['data'], timeout=10)
                
                if test_case['should_fail']:
                    if response.status_code == 400:
                        self.log_test_result(
                            f"資料格式驗證 - {test_case['name']}",
                            True,
                            "正確拒絕無效格式"
                        )
                    else:
                        self.log_test_result(
                            f"資料格式驗證 - {test_case['name']}",
                            False,
                            f"應該返回 400，實際: {response.status_code}",
                            response.text[:200]
                        )
                else:
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('success'):
                            self.log_test_result(
                                f"資料格式驗證 - {test_case['name']}",
                                True,
                                "正確接受有效格式"
                            )
                        else:
                            self.log_test_result(
                                f"資料格式驗證 - {test_case['name']}",
                                False,
                                "API 返回成功但 success 為 false",
                                json.dumps(data, ensure_ascii=False)
                            )
                    else:
                        self.log_test_result(
                            f"資料格式驗證 - {test_case['name']}",
                            False,
                            f"應該返回 200，實際: {response.status_code}",
                            response.text[:200]
                        )
                        
            except Exception as e:
                self.log_test_result(
                    f"資料格式驗證 - {test_case['name']}",
                    False,
                    "",
                    str(e)
                )
    
    def test_database_update_accuracy(self):
        """測試資料庫更新準確性"""
        if len(self.test_email_ids) < 2:
            return
        
        email_id = self.test_email_ids[1]
        
        # 準備測試資料
        test_data = {
            'vendor_code': 'GTK',
            'pd': 'TEST_PRODUCT_123',
            'lot': 'LOT_ABC_001',
            'mo': 'MO_XYZ_789',
            'yield_value': 88.5
        }
        
        endpoint = f"/api/parser/emails/{email_id}/manual-input"
        url = f"{self.base_url}{endpoint}"
        
        try:
            # 執行 API 調用
            response = requests.post(url, headers=self.headers, json=test_data, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    # 驗證資料庫更新
                    if self._verify_complete_database_update(email_id, test_data):
                        self.log_test_result(
                            "資料庫更新準確性",
                            True,
                            "所有欄位正確更新"
                        )
                    else:
                        self.log_test_result(
                            "資料庫更新準確性",
                            False,
                            "資料庫更新不完整或不正確",
                            ""
                        )
                else:
                    self.log_test_result(
                        "資料庫更新準確性",
                        False,
                        "API 返回失敗",
                        json.dumps(data, ensure_ascii=False)
                    )
            else:
                self.log_test_result(
                    "資料庫更新準確性",
                    False,
                    f"API 調用失敗，狀態碼: {response.status_code}",
                    response.text[:200]
                )
                
        except Exception as e:
            self.log_test_result(
                "資料庫更新準確性",
                False,
                "",
                str(e)
            )
    
    def _verify_database_update(self, email_id: int, expected_data: Dict[str, Any]) -> bool:
        """驗證資料庫是否正確更新"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT vendor_code, pd, lot, mo, yield_value, extraction_method, parse_status
                FROM emails WHERE id = ?
            """, (email_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if not row:
                return False
            
            vendor_code, pd, lot, mo, yield_value, extraction_method, parse_status = row
            
            # 檢查基本欄位
            checks = [
                vendor_code == expected_data.get('vendor_code'),
                pd == expected_data.get('pd'),
                lot == expected_data.get('lot'),
                extraction_method == 'manual',
                parse_status == 'parsed'
            ]
            
            return all(checks)
            
        except Exception as e:
            print(f"資料庫驗證錯誤: {e}")
            return False
    
    def _verify_complete_database_update(self, email_id: int, expected_data: Dict[str, Any]) -> bool:
        """驗證資料庫完整更新（包含選填欄位）"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT vendor_code, pd, lot, mo, yield_value, extraction_method, 
                       parse_status, parsed_at
                FROM emails WHERE id = ?
            """, (email_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if not row:
                return False
            
            (vendor_code, pd, lot, mo, yield_value, 
             extraction_method, parse_status, parsed_at) = row
            
            # 檢查所有欄位
            checks = [
                vendor_code == expected_data.get('vendor_code'),
                pd == expected_data.get('pd'),
                lot == expected_data.get('lot'),
                mo == expected_data.get('mo'),
                abs(float(yield_value or 0) - float(expected_data.get('yield_value', 0))) < 0.01,
                extraction_method == 'manual',
                parse_status == 'parsed',
                parsed_at is not None
            ]
            
            return all(checks)
            
        except Exception as e:
            print(f"完整資料庫驗證錯誤: {e}")
            return False
    
    def cleanup_test_data(self):
        """清理測試資料"""
        try:
            if self.test_email_ids:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                placeholders = ','.join(['?' for _ in self.test_email_ids])
                cursor.execute(f"DELETE FROM emails WHERE id IN ({placeholders})", self.test_email_ids)
                
                conn.commit()
                conn.close()
                
                self.log_test_result(
                    "測試資料清理",
                    True,
                    f"成功清理 {len(self.test_email_ids)} 筆測試資料"
                )
        except Exception as e:
            self.log_test_result(
                "測試資料清理",
                False,
                "",
                str(e)
            )
    
    def run_all_tests(self):
        """執行所有測試"""
        print("=" * 60)
        print("手動處理功能 - 資料完整性測試")
        print("=" * 60)
        print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"基礎 URL: {self.base_url}")
        print(f"資料庫路徑: {self.db_path}")
        print("-" * 60)
        
        # 準備測試資料
        self.setup_test_data()
        
        try:
            # 執行測試
            self.test_required_fields_validation()
            self.test_optional_fields_handling()
            self.test_data_format_validation()
            self.test_database_update_accuracy()
        finally:
            # 清理測試資料
            self.cleanup_test_data()
        
        # 輸出測試總結
        print("-" * 60)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        total_tests = len(self.test_results)
        
        print(f"測試總結: {passed_tests}/{total_tests} 通過")
        
        if passed_tests == total_tests:
            print("🎉 所有資料完整性測試通過！")
            return True
        else:
            print("⚠️  部分測試失敗，請檢查詳細結果")
            return False
    
    def generate_report(self, output_file: str = None):
        """生成測試報告"""
        if output_file is None:
            output_file = f"data_integrity_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            'test_type': 'Data Integrity Test',
            'timestamp': datetime.now().isoformat(),
            'base_url': self.base_url,
            'database_path': self.db_path,
            'total_tests': len(self.test_results),
            'passed_tests': sum(1 for result in self.test_results if result['passed']),
            'failed_tests': sum(1 for result in self.test_results if not result['passed']),
            'test_results': self.test_results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"測試報告已儲存至: {output_file}")
        return output_file


if __name__ == "__main__":
    # 執行測試
    test_runner = TestDataIntegrity()
    success = test_runner.run_all_tests()
    test_runner.generate_report()
    
    # 返回適當的退出碼
    exit(0 if success else 1)