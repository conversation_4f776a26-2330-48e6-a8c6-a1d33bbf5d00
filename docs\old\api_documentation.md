# 統一監控儀表板 API 文檔

## 📋 概述

統一監控儀表板提供了完整的 REST API 和 WebSocket 接口，用於監控半導體郵件處理系統的各個方面。本文檔詳細說明了所有可用的 API 端點、請求格式和回應結構。

## 🎯 任務 18 更新摘要

**更新日期**: 2025-01-04  
**版本**: v1.0.0  
**狀態**: ✅ 已完成

### 主要變更
1. **完成 Dramatiq 遷移**: 完全遷移到 Dramatiq 任務系統，移除所有 Celery 相關的 API 端點
2. **統一 Dramatiq 架構**: 所有任務監控統一使用 Dramatiq
3. **新增監控類型**: 檔案處理監控、業務指標監控
4. **增強系統監控**: 更詳細的系統健康狀態和資源監控

## 🏗️ API 架構

### ✅ **新後端架構整合 (2025-08-17)**

基於最新完成的後端重構，所有 API 服務已遷移到新的模組化架構：

### 基礎 URL
```
http://localhost:8000/api/monitoring
```

### 新架構服務位置
- **監控服務**: `backend/monitoring/`
- **API 路由**: `backend/monitoring/api/`
- **核心逻輯**: `backend/shared/infrastructure/`

### 認證
基於新的 `backend.*` 架構，API 使用領域驅動設計的依賴注入模式。所有服務經過 `backend/shared/infrastructure/` 統一管理，未來版本將支援 JWT 認證。

### 回應格式
所有 API 端點都使用統一的 JSON 回應格式：

```json
{
  "status": "success" | "error",
  "data": {
    // 具體資料內容
  },
  "timestamp": "2025-01-04T10:30:00Z",
  "message": "可選的訊息" // 僅在錯誤時出現
}
```

## 📊 API 端點詳細說明

### 1. 儀表板主要端點

#### GET /dashboard
獲取完整的儀表板資料，包含所有監控指標的摘要。

**回應範例**:
```json
{
  "status": "success",
  "data": {
    "metrics": {
      "email_metrics": { ... },
      "dramatiq_metrics": { ... },
      "system_metrics": { ... },
      "file_metrics": { ... },
      "business_metrics": { ... }
    },
    "timestamp": "2025-01-04T10:30:00Z",
    "update_interval": 30,
    "system_status": "healthy"
  }
}
```

#### GET /health
系統健康檢查，不依賴具體服務的獨立檢查。

**回應範例**:
```json
{
  "status": "healthy",
  "services": {
    "overall": true,
    "email_service": true,
    "dramatiq_service": true,
    "database": true,
    "redis": true
  },
  "timestamp": "2025-01-04T10:30:00Z"
}
```

### 2. 郵件監控端點

#### GET /email/queue
獲取郵件佇列狀態，包含各廠商的處理統計。

**回應範例**:
```json
{
  "status": "success",
  "data": {
    "pending_count": 15,
    "processing_count": 3,
    "completed_count": 142,
    "failed_count": 2,
    "vendor_counts": {
      "GTK": 8,
      "JCET": 4,
      "ETD": 3
    },
    "code_comparison_active": 2
  },
  "timestamp": "2025-01-04T10:30:00Z"
}
```

#### GET /email/performance
獲取郵件處理效能指標。

**查詢參數**:
- `time_range`: 時間範圍 (1h, 6h, 24h, 7d)

### 3. Dramatiq 任務監控端點

#### GET /dramatiq/tasks
獲取 Dramatiq 任務狀態，支援 8 種任務類型的完整監控。

**回應範例**:
```json
{
  "status": "success",
  "data": {
    "total_active": 12,
    "total_pending": 8,
    "total_completed": 1543,
    "total_failed": 23,
    "total_retrying": 2,
    "task_type_counts": {
      "code_comparison": {
        "active": 3,
        "pending": 2,
        "completed": 245,
        "failed": 5,
        "retrying": 1
      },
      "csv_to_summary": {
        "active": 2,
        "pending": 1,
        "completed": 189,
        "failed": 3,
        "retrying": 0
      }
      // ... 其他任務類型
    },
    "worker_status": {
      "worker-1": "online",
      "worker-2": "online",
      "worker-3": "offline"
    },
    "worker_load": {
      "worker-1": 4,
      "worker-2": 3,
      "worker-3": 0
    },
    "active_workers": 2,
    "total_workers": 3,
    "avg_task_duration": {
      "code_comparison": 45.2,
      "csv_to_summary": 12.8
    },
    "task_success_rate": {
      "code_comparison": 95.2,
      "csv_to_summary": 98.1
    },
    "overall_success_rate": 96.8,
    "broker_status": "healthy",
    "redis_connection_status": "healthy"
  },
  "timestamp": "2025-01-04T10:30:00Z"
}
```

#### GET /dramatiq/workers
獲取 Dramatiq 工作者狀態。

#### GET /dramatiq/queues
獲取 Dramatiq 佇列狀態。

#### GET /dramatiq/tasks/{task_type}
獲取特定任務類型的詳細狀態。

**支援的任務類型**:
- `code_comparison` - 程式碼比較任務
- `csv_to_summary` - CSV 摘要生成任務
- `compression` - 壓縮任務
- `decompression` - 解壓縮任務
- `email_processing` - 郵件處理任務
- `data_analysis` - 資料分析任務
- `file_processing` - 檔案處理任務
- `batch_processing` - 批次處理任務

### 4. 系統監控端點

#### GET /system/resources
獲取系統資源狀態，包含 CPU、記憶體、磁碟使用率等。

**回應範例**:
```json
{
  "status": "success",
  "data": {
    "cpu_percent": 45.2,
    "memory_percent": 68.5,
    "disk_percent": 23.1,
    "memory_available_mb": 2048.5,
    "disk_free_gb": 125.8,
    "load_average_1m": 1.2,
    "active_connections": 15,
    "websocket_connections": 8,
    "service_health": {
      "email_service": "healthy",
      "dramatiq_service": "healthy",
      "database": "healthy",
      "scheduler": "healthy",
      "redis": "healthy"
    },
    "database_connections": 5,
    "uptime_seconds": 86400,
    "resource_warnings": [],
    "is_critical": false
  },
  "timestamp": "2025-01-04T10:30:00Z"
}
```

#### GET /system/health
獲取系統健康詳細資訊。

### 5. 檔案處理監控端點

#### GET /files/processing
獲取檔案處理狀態，包含附件下載、壓縮解壓縮等。

**回應範例**:
```json
{
  "status": "success",
  "data": {
    "attachments": {
      "downloaded": 156,
      "pending": 8,
      "failed": 3,
      "total_size_mb": 245.8,
      "success_rate": 95.2
    },
    "compression": {
      "active": 2,
      "pending": 1,
      "completed_today": 45,
      "avg_time_seconds": 12.5,
      "success_rate": 98.1
    },
    "decompression": {
      "active": 1,
      "pending": 0,
      "completed_today": 38,
      "avg_time_seconds": 8.2
    },
    "file_types": {
      "counts": {
        "csv": 89,
        "excel": 45,
        "zip": 22
      },
      "sizes": {
        "csv": 125.4,
        "excel": 89.2,
        "zip": 31.2
      }
    },
    "parsing": {
      "avg_time_seconds": 5.8,
      "success_rate": 96.5
    }
  },
  "timestamp": "2025-01-04T10:30:00Z"
}
```

#### GET /files/storage
獲取儲存空間狀態。

### 6. 業務指標監控端點

#### GET /business/metrics
獲取業務指標，包含 MO/LOT 處理統計、資料品質等。

**回應範例**:
```json
{
  "status": "success",
  "data": {
    "mo_lot_processing": {
      "mo_processed_today": 156,
      "lot_processed_today": 489,
      "mo_processed_total": 12456,
      "lot_processed_total": 38921
    },
    "data_quality": {
      "quality_score": 94.2,
      "validation_errors": 8,
      "duplicate_mo": 2,
      "duplicate_lot": 1,
      "missing_data": 3,
      "quality_issues": {
        "validation_errors": 8,
        "duplicate_mo": 2,
        "duplicate_lot": 1,
        "missing_data": 3,
        "anomaly_mo": 1,
        "anomaly_lot": 0,
        "data_conflicts": 0
      }
    },
    "vendor_stats": {
      "GTK": {
        "mo_count": 45,
        "lot_count": 156,
        "success_rate": 95.2,
        "avg_yield": 88.5
      },
      "JCET": {
        "mo_count": 32,
        "lot_count": 98,
        "success_rate": 92.1,
        "avg_yield": 91.2
      }
    },
    "reports": {
      "generated_today": 23,
      "pending": 2,
      "failed": 0,
      "avg_generation_time": 45.2
    },
    "yield_rates": {
      "overall": 89.8,
      "by_vendor": {
        "GTK": 88.5,
        "JCET": 91.2,
        "ETD": 87.9
      }
    },
    "anomalies": {
      "anomaly_mo_count": 1,
      "anomaly_lot_count": 0,
      "data_conflicts": 0
    }
  },
  "timestamp": "2025-01-04T10:30:00Z"
}
```

#### GET /business/vendors
獲取廠商處理統計。

### 7. 告警管理端點

#### GET /alerts
獲取活躍告警。

**查詢參數**:
- `level`: 告警級別篩選 (info, warning, error, critical)

#### POST /alerts/{alert_id}/acknowledge
確認告警。

### 8. 趨勢分析端點

#### GET /trends/{metric_type}
獲取趨勢資料。

**查詢參數**:
- `time_range`: 時間範圍 (1h, 6h, 24h, 7d)

#### GET /predictions/load
獲取負載預測。

**查詢參數**:
- `hours_ahead`: 預測小時數 (預設 24)

### 9. 歷史資料端點

#### GET /history/metrics
獲取歷史指標資料。

**查詢參數**:
- `metric_type`: 指標類型
- `start_time`: 開始時間
- `end_time`: 結束時間

### 10. 統計資料端點

#### GET /statistics/summary
獲取統計摘要，包含所有監控類型的摘要資訊。

## 🔧 錯誤處理

### HTTP 狀態碼
- `200 OK`: 請求成功
- `400 Bad Request`: 請求參數錯誤
- `404 Not Found`: 資源不存在
- `500 Internal Server Error`: 伺服器內部錯誤
- `503 Service Unavailable`: 服務不可用

### 錯誤回應格式
```json
{
  "status": "error",
  "message": "錯誤描述",
  "timestamp": "2025-01-04T10:30:00Z"
}
```

## 🚀 使用範例

### 基本監控查詢
```bash
# 獲取儀表板概覽
curl -X GET "http://localhost:8000/api/monitoring/dashboard"

# 檢查系統健康狀態
curl -X GET "http://localhost:8000/api/monitoring/health"

# 獲取 Dramatiq 任務狀態
curl -X GET "http://localhost:8000/api/monitoring/dramatiq/tasks"
```

### 特定監控查詢
```bash
# 獲取郵件佇列狀態
curl -X GET "http://localhost:8000/api/monitoring/email/queue"

# 獲取系統資源使用情況
curl -X GET "http://localhost:8000/api/monitoring/system/resources"

# 獲取特定任務類型狀態
curl -X GET "http://localhost:8000/api/monitoring/dramatiq/tasks/code_comparison"
```

### 告警管理
```bash
# 獲取活躍告警
curl -X GET "http://localhost:8000/api/monitoring/alerts"

# 確認特定告警
curl -X POST "http://localhost:8000/api/monitoring/alerts/alert-123/acknowledge"
```

## 📈 效能考量

- **快取策略**: 高頻查詢的資料會被快取 30 秒
- **並行處理**: 資料收集使用並行機制，提升回應速度
- **錯誤隔離**: 單一收集器失敗不影響其他資料的獲取
- **降級處理**: 服務不可用時提供基本功能

## 🔮 未來版本

### v1.1.0 (計劃中)
- JWT 認證支援
- API 速率限制
- 更多業務指標
- 自定義告警規則

### v1.2.0 (計劃中)
- GraphQL 支援
- 批量查詢 API
- 資料匯出功能
- 進階分析功能

## 📞 支援

如有問題或建議，請聯繫開發團隊或查看相關文檔：
- 部署指南: `docs/deployment/`
- 故障排除: `docs/troubleshooting/`
- 開發指南: `docs/development/`