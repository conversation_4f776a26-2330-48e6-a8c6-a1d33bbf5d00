"""
測試檔案處理器
測試 CopyFiles 功能的 Python 實作
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

from backend.shared.infrastructure.adapters.file_handlers import (
    FileHandlerFactory,
    GTKFileHandler,
    XAHTFileHandler,
    JCETFileHandler,
    NFMEFileHandler,
    ETDFileHandler
)


class TestFileHandlerFactory:
    """測試檔案處理器工廠"""
    
    def test_create_handler(self):
        """測試建立處理器"""
        factory = FileHandlerFactory("/test/path")
        
        # 測試支援的廠商
        handler = factory.create_handler("GTK")
        assert handler is not None
        assert isinstance(handler, GTKFileHandler)
        
        # 測試不支援的廠商
        handler = factory.create_handler("UNKNOWN")
        assert handler is None
        
    def test_get_supported_vendors(self):
        """測試取得支援的廠商列表"""
        vendors = FileHandlerFactory.get_supported_vendors()
        assert "GTK" in vendors
        assert "XAHT" in vendors
        assert "JCET" in vendors
        assert "NFME" in vendors
        assert "ETD" in vendors


class TestGTKFileHandler:
    """測試 GTK 檔案處理器"""
    
    def setup_method(self):
        """設定測試環境"""
        self.temp_dir = tempfile.mkdtemp()
        self.source_base = Path(self.temp_dir) / "source"
        self.dest_base = Path(self.temp_dir) / "dest"
        
        # 建立 GTK 目錄結構
        self.gtk_temp = self.source_base / "GTK" / "temp"
        self.gtk_temp.mkdir(parents=True)
        
        self.gtk_ft = self.source_base / "GTK" / "FT" / "TEST_PD" / "LOT123"
        self.gtk_ft.mkdir(parents=True)
        
    def teardown_method(self):
        """清理測試環境"""
        shutil.rmtree(self.temp_dir)
        
    def test_copy_files_with_mo_archive(self):
        """測試使用 MO 複製壓縮檔"""
        # 建立測試壓縮檔
        test_file = self.gtk_temp / "MO123456.zip"
        test_file.write_bytes(b"test zip content")
        
        # 建立處理器
        handler = GTKFileHandler(str(self.source_base))
        
        # 執行複製
        success = handler.copy_files(
            file_name="MO123456",
            file_temp=str(self.dest_base),
            pd="TEST_PD",
            lot="LOT123"
        )
        
        # 驗證
        assert success is True
        expected_file = self.dest_base / "TEST_PD" / "MO123456" / "MO123456.zip"
        assert expected_file.exists()
        assert expected_file.read_bytes() == b"test zip content"
        
    def test_copy_files_with_lot(self):
        """測試使用 LOT 複製檔案"""
        # 建立測試檔案
        test_file = self.gtk_temp / "LOT123_test.csv"
        test_file.write_text("test csv content")
        
        # 建立處理器
        handler = GTKFileHandler(str(self.source_base))
        
        # 執行複製（沒有找到 MO，會用 LOT）
        success = handler.copy_files(
            file_name="MO999999",  # 不存在的 MO
            file_temp=str(self.dest_base),
            pd="TEST_PD",
            lot="LOT123"
        )
        
        # 驗證
        assert success is True
        expected_file = self.dest_base / "TEST_PD" / "MO999999" / "LOT123_test.csv"
        assert expected_file.exists()
        
    def test_copy_entire_folder(self):
        """測試複製整個資料夾"""
        # 建立測試資料夾和檔案
        test_file1 = self.gtk_ft / "file1.txt"
        test_file1.write_text("content 1")
        test_file2 = self.gtk_ft / "file2.csv"
        test_file2.write_text("content 2")
        
        # 建立處理器
        handler = GTKFileHandler(str(self.source_base))
        
        # 執行複製
        success = handler.copy_files(
            file_name="MO999999",  # 不存在的 MO
            file_temp=str(self.dest_base),
            pd="TEST_PD",
            lot="LOT123"
        )
        
        # 驗證
        assert success is True
        expected_dir = self.dest_base / "TEST_PD" / "LOT123"
        assert expected_dir.exists()
        assert (expected_dir / "file1.txt").read_text() == "content 1"
        assert (expected_dir / "file2.csv").read_text() == "content 2"
        
    def test_skip_existing_file_same_size(self):
        """測試跳過已存在且大小相同的檔案"""
        # 建立來源檔案
        source_file = self.gtk_temp / "MO123456.zip"
        source_file.write_bytes(b"test content")
        
        # 建立目標檔案（相同大小）
        dest_dir = self.dest_base / "TEST_PD" / "MO123456"
        dest_dir.mkdir(parents=True)
        dest_file = dest_dir / "MO123456.zip"
        dest_file.write_bytes(b"test content")
        
        # 記錄修改時間
        original_mtime = dest_file.stat().st_mtime
        
        # 建立處理器
        handler = GTKFileHandler(str(self.source_base))
        
        # 執行複製
        success = handler.copy_files(
            file_name="MO123456",
            file_temp=str(self.dest_base),
            pd="TEST_PD",
            lot="LOT123"
        )
        
        # 驗證
        assert success is True
        # 檔案應該沒有被覆寫（修改時間相同）
        assert dest_file.stat().st_mtime == original_mtime


class TestXAHTFileHandler:
    """測試 XAHT 檔案處理器"""
    
    def setup_method(self):
        """設定測試環境"""
        self.temp_dir = tempfile.mkdtemp()
        self.source_base = Path(self.temp_dir) / "source"
        self.dest_base = Path(self.temp_dir) / "dest"
        
        # 建立 XAHT 目錄結構
        self.xaht_temp = self.source_base / "XAHT" / "temp"
        self.xaht_temp.mkdir(parents=True)
        
    def teardown_method(self):
        """清理測試環境"""
        shutil.rmtree(self.temp_dir)
        
    def test_xaht_only_mo_search(self):
        """測試 XAHT 只支援 MO 搜尋"""
        # 建立測試檔案
        test_file = self.xaht_temp / "TEST_MO123456_data.rar"
        test_file.write_bytes(b"test rar content")
        
        # 建立處理器
        handler = XAHTFileHandler(str(self.source_base))
        
        # 執行複製
        success = handler.copy_files(
            file_name="MO123456",
            file_temp=str(self.dest_base),
            pd="TEST_PD",
            lot="LOT123"
        )
        
        # 驗證
        assert success is True
        expected_file = self.dest_base / "TEST_PD" / "MO123456" / "TEST_MO123456_data.rar"
        assert expected_file.exists()


class TestNFMEFileHandler:
    """測試 NFME 檔案處理器"""
    
    def setup_method(self):
        """設定測試環境"""
        self.temp_dir = tempfile.mkdtemp()
        self.source_base = Path(self.temp_dir) / "source"
        self.dest_base = Path(self.temp_dir) / "dest"
        
        # 建立 NFME 目錄結構
        self.nfme_ft = self.source_base / "NFME" / "FT" / "TEST_PD"
        self.nfme_ft.mkdir(parents=True)
        
    def teardown_method(self):
        """清理測試環境"""
        shutil.rmtree(self.temp_dir)
        
    def test_nfme_special_file_types(self):
        """測試 NFME 只複製特定類型檔案"""
        # 建立各種檔案
        lsr_file = self.nfme_ft / "test_lsr_file.txt"
        lsr_file.write_text("lsr content")
        
        data_csv = self.nfme_ft / "test_data.csv"
        data_csv.write_text("csv content")
        
        other_file = self.nfme_ft / "other.txt"
        other_file.write_text("should not be copied")
        
        # 建立處理器
        handler = NFMEFileHandler(str(self.source_base))
        
        # 執行複製
        success = handler.copy_files(
            file_name="MO123456",
            file_temp=str(self.dest_base),
            pd="TEST_PD",
            lot="LOT123"
        )
        
        # 驗證
        assert success is True
        dest_dir = self.dest_base / "TEST_PD" / "MO123456"
        
        # 應該複製的檔案
        assert (dest_dir / "test_lsr_file.txt").exists()
        assert (dest_dir / "test_data.csv").exists()
        
        # 不應該複製的檔案
        assert not (dest_dir / "other.txt").exists()


class TestFileHandlerIntegration:
    """整合測試"""
    
    def test_factory_process_vendor_files(self):
        """測試工廠的便利方法"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 建立測試環境
            source_base = Path(temp_dir) / "source"
            gtk_temp = source_base / "GTK" / "temp"
            gtk_temp.mkdir(parents=True)
            
            # 建立測試檔案
            test_file = gtk_temp / "MO123456.zip"
            test_file.write_bytes(b"test content")
            
            # 建立工廠
            factory = FileHandlerFactory(str(source_base))
            
            # 執行處理
            dest_dir = Path(temp_dir) / "dest"
            result = factory.process_vendor_files(
                vendor_code="GTK",
                mo="MO123456",
                temp_path=str(dest_dir),
                pd="TEST_PD",
                lot="LOT123"
            )
            
            # 驗證
            assert result['success'] is True
            assert result['vendor'] == "GTK"
            assert result['mo'] == "MO123456"
            
            # 檢查檔案
            expected_file = dest_dir / "TEST_PD" / "MO123456" / "MO123456.zip"
            assert expected_file.exists()