# 核心領域模型設計

## [BOARD] 目錄

1. [領域實體設計](#領域實體設計)
2. [值物件設計](#值物件設計)
3. [領域服務設計](#領域服務設計)
4. [測試驅動開發](#測試驅動開發)
5. [實作範例](#實作範例)

## 領域實體設計

### [BUILDING_CONSTRUCTION] 核心領域實體架構

```python
# src/domain/entities/
├── email.py           # 郵件實體
├── vendor.py          # 廠商實體
├── processing_result.py # 處理結果實體
├── attachment.py      # 附件實體
├── parsing_result.py  # 解析結果實體
├── statistics.py      # 統計實體
├── report.py          # 報表實體
├── alert.py           # 警示實體
└── user.py            # 使用者實體
```

### [E_MAIL] Email 實體

```python
# tests/unit/domain/test_email_entity.py (TDD - 先寫測試)
import pytest
from datetime import datetime
from src.domain.entities.email import Email, EmailStatus
from src.domain.value_objects.email_address import EmailAddress
from src.domain.exceptions import InvalidEmailException

class TestEmailEntity:
    """郵件實體測試"""
    
    def test_create_email_with_valid_data(self):
        """測試建立有效郵件"""
        # Arrange
        received_date = datetime.now()
        sender = EmailAddress("<EMAIL>")
        recipient = EmailAddress("<EMAIL>")
        subject = "GTK FT HOLD MO:F123 LOT:ABC"
        body = "測試郵件內容"
        
        # Act
        email = Email(
            received_date=received_date,
            sender=sender,
            recipient=recipient,
            subject=subject,
            body=body
        )
        
        # Assert
        assert email.received_date == received_date
        assert email.sender == sender
        assert email.recipient == recipient
        assert email.subject == subject
        assert email.body == body
        assert email.status == EmailStatus.PENDING
        assert email.id is None  # 新建立的實體沒有 ID
        
    def test_email_status_transition(self):
        """測試郵件狀態轉換"""
        email = self._create_test_email()
        
        # 測試狀態轉換
        email.mark_as_processing()
        assert email.status == EmailStatus.PROCESSING
        assert email.processed_at is not None
        
        email.mark_as_completed()
        assert email.status == EmailStatus.COMPLETED
        
    def test_email_status_invalid_transition(self):
        """測試無效的狀態轉換"""
        email = self._create_test_email()
        email.mark_as_completed()
        
        # 已完成的郵件不能重新處理
        with pytest.raises(InvalidEmailException):
            email.mark_as_processing()
    
    def test_add_attachment(self):
        """測試新增附件"""
        email = self._create_test_email()
        
        from src.domain.entities.attachment import Attachment
        attachment = Attachment(
            filename="test.csv",
            content_type="text/csv",
            size=1024
        )
        
        email.add_attachment(attachment)
        assert len(email.attachments) == 1
        assert email.attachments[0] == attachment
        
    def test_email_vendor_detection(self):
        """測試廠商檢測"""
        # GTK 郵件
        gtk_email = Email(
            received_date=datetime.now(),
            sender=EmailAddress("<EMAIL>"),
            recipient=EmailAddress("<EMAIL>"),
            subject="GTK FT HOLD MO:F123",
            body=""
        )
        
        assert gtk_email.detect_vendor() == "GTK"
        
        # ETD 郵件
        etd_email = Email(
            received_date=datetime.now(),
            sender=EmailAddress("<EMAIL>"),
            recipient=EmailAddress("<EMAIL>"),
            subject="ANF/Product/Version/Test",
            body=""
        )
        
        assert etd_email.detect_vendor() == "ETD"
        
    def test_email_equality(self):
        """測試郵件相等性"""
        email1 = self._create_test_email()
        email2 = self._create_test_email()
        
        # 相同內容但不同實體
        assert email1 != email2
        
        # 設定相同 ID
        email1.id = 1
        email2.id = 1
        assert email1 == email2
        
    def _create_test_email(self) -> Email:
        """建立測試用郵件"""
        return Email(
            received_date=datetime.now(),
            sender=EmailAddress("<EMAIL>"),
            recipient=EmailAddress("<EMAIL>"),
            subject="測試主題",
            body="測試內容"
        )

# src/domain/entities/email.py (實作)
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional
from enum import Enum

from ..value_objects.email_address import EmailAddress
from ..exceptions import InvalidEmailException

class EmailStatus(Enum):
    """郵件狀態"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class Email:
    """郵件實體"""
    
    received_date: datetime
    sender: EmailAddress
    recipient: EmailAddress
    subject: str
    body: str
    status: EmailStatus = EmailStatus.PENDING
    id: Optional[int] = None
    processed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    attachments: List['Attachment'] = field(default_factory=list)
    
    def mark_as_processing(self) -> None:
        """標記為處理中"""
        if self.status == EmailStatus.COMPLETED:
            raise InvalidEmailException("已完成的郵件無法重新處理")
        
        self.status = EmailStatus.PROCESSING
        self.processed_at = datetime.now()
        
    def mark_as_completed(self) -> None:
        """標記為已完成"""
        self.status = EmailStatus.COMPLETED
        self.error_message = None
        
    def mark_as_failed(self, error_message: str) -> None:
        """標記為失敗"""
        self.status = EmailStatus.FAILED
        self.error_message = error_message
        self.retry_count += 1
        
    def can_retry(self, max_retries: int = 3) -> bool:
        """檢查是否可以重試"""
        return self.status == EmailStatus.FAILED and self.retry_count < max_retries
        
    def add_attachment(self, attachment: 'Attachment') -> None:
        """新增附件"""
        self.attachments.append(attachment)
        
    def detect_vendor(self) -> Optional[str]:
        """檢測廠商"""
        subject_lower = self.subject.lower()
        sender_domain = self.sender.domain.lower()
        
        # GTK 檢測
        if ("ft hold" in subject_lower or 
            "ft lot" in subject_lower or 
            "gtk" in sender_domain):
            return "GTK"
            
        # ETD 檢測  
        if ("anf" in subject_lower or 
            "etd" in sender_domain):
            return "ETD"
            
        # XAHT 檢測
        if ("tianshui" in self.body.lower() or 
            "西安" in self.body or 
            "xaht" in sender_domain):
            return "XAHT"
            
        # JCET 檢測
        if ("jcet" in self.body.lower() or 
            "jcetglobal.com" in sender_domain):
            return "JCET"
            
        # LINGSEN 檢測
        if ("lingsen" in self.body.lower() or 
            "lingsen" in sender_domain):
            return "LINGSEN"
            
        return None
        
    def __eq__(self, other) -> bool:
        """相等性比較"""
        if not isinstance(other, Email):
            return False
        return self.id is not None and self.id == other.id
        
    def __hash__(self) -> int:
        """雜湊值"""
        return hash(self.id) if self.id else hash(id(self))
```

### [FACTORY] Vendor 實體

```python
# tests/unit/domain/test_vendor_entity.py (TDD - 先寫測試)
import pytest
from src.domain.entities.vendor import Vendor, VendorStatus
from src.domain.value_objects.vendor_config import VendorConfig

class TestVendorEntity:
    """廠商實體測試"""
    
    def test_create_vendor_with_valid_data(self):
        """測試建立有效廠商"""
        # Arrange
        config = VendorConfig(
            email_patterns=["@gtk.com", "@greatek.com"],
            subject_keywords=["ft hold", "ft lot"],
            parser_type="GTK"
        )
        
        # Act
        vendor = Vendor(
            code="GTK",
            name="Greatek Technology",
            config=config
        )
        
        # Assert
        assert vendor.code == "GTK"
        assert vendor.name == "Greatek Technology"
        assert vendor.config == config
        assert vendor.status == VendorStatus.ACTIVE
        assert vendor.is_active()
        
    def test_vendor_email_pattern_matching(self):
        """測試廠商郵件模式匹配"""
        config = VendorConfig(
            email_patterns=["@gtk.com", "@greatek.com"],
            subject_keywords=["ft hold"],
            parser_type="GTK"
        )
        vendor = Vendor("GTK", "Greatek", config)
        
        assert vendor.matches_email("<EMAIL>")
        assert vendor.matches_email("<EMAIL>")
        assert not vendor.matches_email("<EMAIL>")
        
    def test_vendor_subject_matching(self):
        """測試廠商主題匹配"""
        config = VendorConfig(
            email_patterns=["@gtk.com"],
            subject_keywords=["ft hold", "ft lot"],
            parser_type="GTK"
        )
        vendor = Vendor("GTK", "Greatek", config)
        
        assert vendor.matches_subject("GTK FT HOLD MO:F123")
        assert vendor.matches_subject("FT LOT processing")
        assert not vendor.matches_subject("Normal email")
        
    def test_vendor_deactivation(self):
        """測試廠商停用"""
        vendor = self._create_test_vendor()
        
        vendor.deactivate()
        assert vendor.status == VendorStatus.INACTIVE
        assert not vendor.is_active()
        
    def test_vendor_statistics_update(self):
        """測試廠商統計更新"""
        vendor = self._create_test_vendor()
        
        # 更新統計
        vendor.update_statistics(
            total_emails=100,
            processed_emails=95,
            avg_yield=98.5
        )
        
        assert vendor.total_emails == 100
        assert vendor.processed_emails == 95
        assert vendor.success_rate == 95.0
        assert vendor.avg_yield == 98.5
        
    def _create_test_vendor(self) -> Vendor:
        """建立測試用廠商"""
        config = VendorConfig(
            email_patterns=["@test.com"],
            subject_keywords=["test"],
            parser_type="TEST"
        )
        return Vendor("TEST", "Test Vendor", config)

# src/domain/entities/vendor.py (實作)
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional
from enum import Enum

from ..value_objects.vendor_config import VendorConfig

class VendorStatus(Enum):
    """廠商狀態"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    MAINTENANCE = "maintenance"

@dataclass
class Vendor:
    """廠商實體"""
    
    code: str
    name: str
    config: VendorConfig
    status: VendorStatus = VendorStatus.ACTIVE
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # 統計資料
    total_emails: int = 0
    processed_emails: int = 0
    failed_emails: int = 0
    avg_yield: Optional[float] = None
    last_email_at: Optional[datetime] = None
    
    def is_active(self) -> bool:
        """檢查廠商是否啟用"""
        return self.status == VendorStatus.ACTIVE
        
    def deactivate(self) -> None:
        """停用廠商"""
        self.status = VendorStatus.INACTIVE
        self.updated_at = datetime.now()
        
    def activate(self) -> None:
        """啟用廠商"""
        self.status = VendorStatus.ACTIVE
        self.updated_at = datetime.now()
        
    def matches_email(self, email_address: str) -> bool:
        """檢查郵件地址是否匹配"""
        email_lower = email_address.lower()
        return any(pattern.lower() in email_lower 
                  for pattern in self.config.email_patterns)
                  
    def matches_subject(self, subject: str) -> bool:
        """檢查主題是否匹配"""
        subject_lower = subject.lower()
        return any(keyword.lower() in subject_lower 
                  for keyword in self.config.subject_keywords)
                  
    def update_statistics(self, 
                         total_emails: int,
                         processed_emails: int,
                         avg_yield: Optional[float] = None) -> None:
        """更新統計資料"""
        self.total_emails = total_emails
        self.processed_emails = processed_emails
        self.failed_emails = total_emails - processed_emails
        self.avg_yield = avg_yield
        self.updated_at = datetime.now()
        
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_emails == 0:
            return 0.0
        return (self.processed_emails / self.total_emails) * 100
        
    def __eq__(self, other) -> bool:
        """相等性比較"""
        if not isinstance(other, Vendor):
            return False
        return self.code == other.code
        
    def __hash__(self) -> int:
        """雜湊值"""
        return hash(self.code)
```

### [CHART] ProcessingResult 實體

```python
# tests/unit/domain/test_processing_result_entity.py (TDD - 先寫測試)
import pytest
from datetime import datetime, timedelta
from src.domain.entities.processing_result import ProcessingResult, ProcessingStatus
from src.domain.entities.email import Email
from src.domain.value_objects.email_address import EmailAddress

class TestProcessingResultEntity:
    """處理結果實體測試"""
    
    def test_create_processing_result_success(self):
        """測試建立成功的處理結果"""
        email = self._create_test_email()
        
        result = ProcessingResult(
            email=email,
            vendor_code="GTK",
            status=ProcessingStatus.SUCCESS
        )
        
        assert result.email == email
        assert result.vendor_code == "GTK"
        assert result.status == ProcessingStatus.SUCCESS
        assert result.processing_duration is None
        assert result.is_successful()
        
    def test_processing_result_with_data(self):
        """測試包含解析資料的處理結果"""
        email = self._create_test_email()
        
        result = ProcessingResult(
            email=email,
            vendor_code="GTK",
            status=ProcessingStatus.SUCCESS,
            mo_number="F123456",
            lot_number="ABC.1",
            yield_value=98.5,
            in_qty=1000
        )
        
        assert result.mo_number == "F123456"
        assert result.lot_number == "ABC.1"
        assert result.yield_value == 98.5
        assert result.in_qty == 1000
        
    def test_processing_result_failure(self):
        """測試失敗的處理結果"""
        email = self._create_test_email()
        
        result = ProcessingResult(
            email=email,
            vendor_code="GTK",
            status=ProcessingStatus.FAILED,
            error_message="解析失敗：找不到 MO 編號"
        )
        
        assert result.status == ProcessingStatus.FAILED
        assert result.error_message == "解析失敗：找不到 MO 編號"
        assert not result.is_successful()
        
    def test_calculate_processing_duration(self):
        """測試計算處理時間"""
        email = self._create_test_email()
        start_time = datetime.now()
        
        result = ProcessingResult(
            email=email,
            vendor_code="GTK",
            status=ProcessingStatus.SUCCESS,
            started_at=start_time
        )
        
        # 模擬處理完成
        end_time = start_time + timedelta(seconds=5)
        result.mark_completed(end_time)
        
        assert result.processing_duration.total_seconds() == 5
        
    def _create_test_email(self) -> Email:
        """建立測試用郵件"""
        return Email(
            received_date=datetime.now(),
            sender=EmailAddress("<EMAIL>"),
            recipient=EmailAddress("<EMAIL>"),
            subject="GTK FT HOLD MO:F123",
            body="測試內容"
        )

# src/domain/entities/processing_result.py (實作)
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from enum import Enum

from .email import Email

class ProcessingStatus(Enum):
    """處理狀態"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    SKIPPED = "skipped"

@dataclass
class ProcessingResult:
    """處理結果實體"""
    
    email: Email
    vendor_code: str
    status: ProcessingStatus
    id: Optional[int] = None
    
    # 解析出的資料
    mo_number: Optional[str] = None
    lot_number: Optional[str] = None
    product: Optional[str] = None
    yield_value: Optional[float] = None
    in_qty: Optional[int] = None
    
    # 處理資訊
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    processing_duration: Optional[timedelta] = None
    error_message: Optional[str] = None
    confidence_score: Optional[float] = None
    
    # 檔案處理結果
    files_processed: List[str] = None
    summary_generated: bool = False
    network_path: Optional[str] = None
    
    # 額外資料
    extracted_data: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化後處理"""
        if self.files_processed is None:
            self.files_processed = []
        if self.extracted_data is None:
            self.extracted_data = {}
            
    def is_successful(self) -> bool:
        """檢查是否成功"""
        return self.status == ProcessingStatus.SUCCESS
        
    def mark_completed(self, completed_at: Optional[datetime] = None) -> None:
        """標記為完成"""
        if completed_at is None:
            completed_at = datetime.now()
            
        self.completed_at = completed_at
        
        if self.started_at:
            self.processing_duration = completed_at - self.started_at
            
    def add_processed_file(self, filename: str) -> None:
        """新增已處理檔案"""
        if filename not in self.files_processed:
            self.files_processed.append(filename)
            
    def set_summary_path(self, network_path: str) -> None:
        """設定摘要檔案路徑"""
        self.network_path = network_path
        self.summary_generated = True
        
    def add_extracted_data(self, key: str, value: Any) -> None:
        """新增解析資料"""
        self.extracted_data[key] = value
        
    def get_summary(self) -> Dict[str, Any]:
        """取得處理摘要"""
        return {
            "vendor_code": self.vendor_code,
            "status": self.status.value,
            "mo_number": self.mo_number,
            "lot_number": self.lot_number,
            "yield_value": self.yield_value,
            "files_count": len(self.files_processed),
            "processing_duration_seconds": (
                self.processing_duration.total_seconds() 
                if self.processing_duration else None
            ),
            "summary_generated": self.summary_generated,
            "error_message": self.error_message
        }
```

## 值物件設計

### [E_MAIL] EmailAddress 值物件

```python
# tests/unit/domain/test_email_address_value_object.py
import pytest
from src.domain.value_objects.email_address import EmailAddress
from src.domain.exceptions import InvalidEmailAddressException

class TestEmailAddressValueObject:
    """郵件地址值物件測試"""
    
    def test_create_valid_email_address(self):
        """測試建立有效郵件地址"""
        email = EmailAddress("<EMAIL>")
        
        assert email.address == "<EMAIL>"
        assert email.local_part == "test"
        assert email.domain == "example.com"
        
    def test_invalid_email_address_format(self):
        """測試無效的郵件地址格式"""
        with pytest.raises(InvalidEmailAddressException):
            EmailAddress("invalid-email")
            
        with pytest.raises(InvalidEmailAddressException):
            EmailAddress("@example.com")
            
        with pytest.raises(InvalidEmailAddressException):
            EmailAddress("test@")
            
    def test_email_address_equality(self):
        """測試郵件地址相等性"""
        email1 = EmailAddress("<EMAIL>")
        email2 = EmailAddress("<EMAIL>")
        email3 = EmailAddress("<EMAIL>")
        
        assert email1 == email2
        assert email1 != email3
        
    def test_email_address_case_insensitive(self):
        """測試郵件地址大小寫不敏感"""
        email1 = EmailAddress("<EMAIL>")
        email2 = EmailAddress("<EMAIL>")
        
        assert email1 == email2
        assert email1.domain == "example.com"

# src/domain/value_objects/email_address.py
import re
from dataclasses import dataclass

from ..exceptions import InvalidEmailAddressException

@dataclass(frozen=True)
class EmailAddress:
    """郵件地址值物件"""
    
    address: str
    
    def __post_init__(self):
        """驗證郵件地址格式"""
        if not self._is_valid_email(self.address):
            raise InvalidEmailAddressException(f"無效的郵件地址: {self.address}")
        
        # 正規化郵件地址（轉為小寫）
        object.__setattr__(self, 'address', self.address.lower())
    
    @property
    def local_part(self) -> str:
        """取得本地部分 (@ 前面)"""
        return self.address.split('@')[0]
    
    @property
    def domain(self) -> str:
        """取得網域部分 (@ 後面)"""
        return self.address.split('@')[1]
    
    def _is_valid_email(self, email: str) -> bool:
        """驗證郵件地址格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def __str__(self) -> str:
        return self.address
```

## 領域服務設計

### [SEARCH] EmailParsingService

```python
# tests/unit/domain/test_email_parsing_service.py
import pytest
from src.domain.services.email_parsing_service import EmailParsingService
from src.domain.entities.email import Email
from src.domain.entities.vendor import Vendor
from src.domain.value_objects.email_address import EmailAddress
from src.domain.value_objects.vendor_config import VendorConfig

class TestEmailParsingService:
    """郵件解析服務測試"""
    
    def test_parse_gtk_email(self):
        """測試解析 GTK 郵件"""
        # Arrange
        service = EmailParsingService()
        email = Email(
            received_date=datetime.now(),
            sender=EmailAddress("<EMAIL>"),
            recipient=EmailAddress("<EMAIL>"),
            subject="GTK FT HOLD MO:F123456 LOT:ABC.1 YIELD:98.5%",
            body="IN QTY: 1000"
        )
        
        vendor = self._create_gtk_vendor()
        
        # Act
        result = service.parse_email(email, vendor)
        
        # Assert
        assert result.mo_number == "F123456"
        assert result.lot_number == "ABC.1"
        assert result.yield_value == 98.5
        
    def test_parse_email_with_confidence_score(self):
        """測試解析郵件並計算信心分數"""
        service = EmailParsingService()
        email = self._create_gtk_email()
        vendor = self._create_gtk_vendor()
        
        result = service.parse_email(email, vendor)
        
        assert result.confidence_score >= 0.0
        assert result.confidence_score <= 1.0

# src/domain/services/email_parsing_service.py
from typing import Optional
import re

from ..entities.email import Email
from ..entities.vendor import Vendor
from ..entities.processing_result import ProcessingResult, ProcessingStatus

class EmailParsingService:
    """郵件解析領域服務"""
    
    def parse_email(self, email: Email, vendor: Vendor) -> ProcessingResult:
        """解析郵件內容"""
        
        if vendor.code == "GTK":
            return self._parse_gtk_email(email)
        elif vendor.code == "ETD":
            return self._parse_etd_email(email)
        else:
            return self._create_failed_result(
                email, vendor.code, f"不支援的廠商: {vendor.code}"
            )
    
    def _parse_gtk_email(self, email: Email) -> ProcessingResult:
        """解析 GTK 郵件"""
        try:
            mo_number = self._extract_keyword_value("mo", email.subject)
            lot_number = self._extract_keyword_value("lot", email.subject)
            yield_value = self._extract_yield_value(email.subject)
            in_qty = self._extract_in_qty(email.body)
            
            confidence = self._calculate_confidence([mo_number, lot_number])
            
            return ProcessingResult(
                email=email,
                vendor_code="GTK",
                status=ProcessingStatus.SUCCESS,
                mo_number=mo_number,
                lot_number=lot_number,
                yield_value=yield_value,
                in_qty=in_qty,
                confidence_score=confidence
            )
            
        except Exception as e:
            return self._create_failed_result(email, "GTK", str(e))
    
    def _extract_keyword_value(self, keyword: str, text: str) -> Optional[str]:
        """從文字中提取關鍵字的值"""
        pattern = rf"{keyword}[:：]\s*([^\s,]+)"
        match = re.search(pattern, text, re.IGNORECASE)
        return match.group(1) if match else None
    
    def _extract_yield_value(self, text: str) -> Optional[float]:
        """提取良率值"""
        pattern = r"yield[:：]\s*([0-9.]+)%?"
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return float(match.group(1))
        return None
    
    def _extract_in_qty(self, text: str) -> Optional[int]:
        """提取入料數量"""
        pattern = r"in\s+qty[:：]\s*([0-9,]+)"
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return int(match.group(1).replace(',', ''))
        return None
    
    def _calculate_confidence(self, extracted_values: list) -> float:
        """計算解析信心分數"""
        valid_count = sum(1 for value in extracted_values if value is not None)
        total_count = len(extracted_values)
        return valid_count / total_count if total_count > 0 else 0.0
    
    def _create_failed_result(self, email: Email, vendor_code: str, error: str) -> ProcessingResult:
        """建立失敗結果"""
        return ProcessingResult(
            email=email,
            vendor_code=vendor_code,
            status=ProcessingStatus.FAILED,
            error_message=error,
            confidence_score=0.0
        )
```

## 測試驅動開發流程

### [RED_CIRCLE] Red - 寫失敗的測試

```bash
# 1. 先寫測試（會失敗）
pytest tests/unit/domain/test_email_entity.py::TestEmailEntity::test_create_email_with_valid_data -v
# [ERROR] FAILED - 因為還沒實作

# 2. 寫最少的程式碼讓測試通過
# 實作 Email 實體的基本功能

# 3. 執行測試確認通過
pytest tests/unit/domain/test_email_entity.py::TestEmailEntity::test_create_email_with_valid_data -v
# [OK] PASSED
```

### [GREEN_CIRCLE] Green - 讓測試通過

```python
# 最小實作讓測試通過
@dataclass
class Email:
    received_date: datetime
    sender: EmailAddress
    recipient: EmailAddress
    subject: str
    body: str
    status: EmailStatus = EmailStatus.PENDING
    id: Optional[int] = None
```

### [BLUE_CIRCLE] Refactor - 重構

```python
# 重構：加入更多功能和驗證
@dataclass
class Email:
    received_date: datetime
    sender: EmailAddress
    recipient: EmailAddress
    subject: str
    body: str
    status: EmailStatus = EmailStatus.PENDING
    id: Optional[int] = None
    processed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    attachments: List['Attachment'] = field(default_factory=list)
    
    def mark_as_processing(self) -> None:
        # 實作狀態轉換邏輯
        pass
```

### [TEST_TUBE] 程式測試驗證

```python
# scripts/test_domain_models.py
"""實際執行領域模型驗證"""

from datetime import datetime
from src.domain.entities.email import Email, EmailStatus
from src.domain.value_objects.email_address import EmailAddress

def test_real_email_processing():
    """實際郵件處理測試"""
    
    # 建立真實的郵件資料
    email = Email(
        received_date=datetime.now(),
        sender=EmailAddress("<EMAIL>"),
        recipient=EmailAddress("<EMAIL>"),
        subject="GTK FT HOLD MO:F2330641D LOT:DLF2A.1D YIELD:95.5%",
        body="IN QTY: 2000"
    )
    
    print(f"[OK] 郵件建立成功")
    print(f"[E_MAIL] 主題: {email.subject}")
    print(f"[FACTORY] 檢測到廠商: {email.detect_vendor()}")
    print(f"[CHART] 狀態: {email.status.value}")
    
    # 測試狀態轉換
    email.mark_as_processing()
    print(f"[REFRESH] 處理中狀態: {email.status.value}")
    
    email.mark_as_completed()
    print(f"[OK] 完成狀態: {email.status.value}")
    
    return email

if __name__ == "__main__":
    test_real_email_processing()
```

這個領域模型設計遵循了 TDD 原則，確保所有核心業務邏輯都有完整的測試覆蓋，並且能正確處理真實的郵件資料。