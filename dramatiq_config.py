"""
Dramatiq 配置文件 - 生產級異步任務隊列配置

📋 配置模式：
  🧪 開發模式 (USE_MEMORY_BROKER=true) - 使用內存代理
  🚀 生產模式 (USE_MEMORY_BROKER=false) - 使用 Redis 代理

🔧 切換方式：
  - 開發模式：保持預設設定
  - 生產模式：設定環境變數 USE_MEMORY_BROKER=false

📊 任務隊列：
  - eqc_queue: EQC 工作流程任務
  - search_queue: 產品搜尋任務
  - processing_queue: CSV 摘要和程式碼比較
  - health_queue: 健康檢查
"""

import os
import dramatiq
from dramatiq.brokers.redis import RedisBroker
from dramatiq.brokers.stub import StubBroker
from dramatiq.results import Results
from dramatiq.results.backends import RedisBackend, StubBackend
from dramatiq.middleware import AgeLimit, TimeLimit, Retries, Callbacks
from dramatiq.middleware.asyncio import AsyncIO
from loguru import logger

# 檢查是否使用開發模式
USE_MEMORY_BROKER = os.getenv('USE_MEMORY_BROKER', 'false').lower() == 'true'

# 配置 Dramatiq Broker 和 Result Backend
if USE_MEMORY_BROKER:
    # 開發模式：使用內存代理
    broker = StubBroker()
    result_backend = StubBackend()
    logger.info("[MEMORY] Dramatiq 使用內存代理進行開發測試")
else:
    # 生產模式：使用 Redis
    redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    result_redis_url = os.getenv('REDIS_RESULT_URL', 'redis://localhost:6379/1')
    
    broker = RedisBroker(url=redis_url)
    result_backend = RedisBackend(url=result_redis_url)
    logger.info(f"[REDIS] Dramatiq 使用 Redis 代理: {redis_url}")
    logger.info(f"[REDIS] Dramatiq 結果後端: {result_redis_url}")

# 配置中間件 - 避免重複添加
if not hasattr(broker, '_middleware_configured'):
    broker.add_middleware(AsyncIO())  # 添加 AsyncIO 支援
    broker.add_middleware(AgeLimit(max_age=3600000))  # 1小時任務過期
    broker.add_middleware(TimeLimit(time_limit=1800000))  # 30分鐘執行時間限制
    # 🔧 修復：配置詳細的重試中間件
    retries_middleware = Retries(
        max_retries=3,
        # 喯用詳細日誌記錄
        # 注意：Dramatiq Retries 中間件會自動管理重試遏輯和計數
        min_backoff=1000,  # 1秒最小延遲
        max_backoff=30000  # 30秒最大延遲
    )
    broker.add_middleware(retries_middleware)  # 最多重試3次
    broker.add_middleware(Callbacks())
    broker.add_middleware(Results(backend=result_backend))  # 🔑 關鍵：支援 store_results 選項
    broker._middleware_configured = True
    logger.info("✅ Dramatiq 中間件配置完成")
    logger.info(f"   - 重試中間件: 最多{retries_middleware.max_retries}次")
    logger.info(f"   - 結果存儲: {'Redis' if not USE_MEMORY_BROKER else '內存'} Backend")
    logger.info(f"   - AsyncIO 支援: 已啟用")
else:
    logger.debug("⚠️ Dramatiq 中間件已配置，跳過重複添加")
    logger.debug("   使用現有中間件配置（包含 Retries 中間件）")

# 設置全局 broker
dramatiq.set_broker(broker)

# 隊列配置
QUEUE_CONFIG = {
    'eqc_queue': {
        'max_retries': 3,
        'time_limit': 1800000,  # 30分鐘
        'priority': 10
    },
    'search_queue': {
        'max_retries': 3,
        'time_limit': 300000,   # 5分鐘
        'priority': 5
    },
    'processing_queue': {
        'max_retries': 2,
        'time_limit': 600000,   # 10分鐘
        'priority': 5
    },
    'health_queue': {
        'max_retries': 1,
        'time_limit': 30000,    # 30秒
        'priority': 1
    },
    'pipeline_queue': {
        'max_retries': 3,        # 🔧 修復：確保與 actor 配置一致
        'time_limit': 600000,    # 10分鐘
        'priority': 8,
        'retry_backoff': True    # 啟用指數式回退
    }
}

# Worker 配置
WORKER_CONFIG = {
    'processes': os.getenv('DRAMATIQ_PROCESSES', 4),
    'threads': os.getenv('DRAMATIQ_THREADS', 8),
    'worker_timeout': 600000,  # 10分鐘
}

# 監控配置
MONITORING_CONFIG = {
    'enable_prometheus': True,
    'prometheus_port': 9191,
    'enable_logging': True,
    'log_level': 'INFO'
}

def get_broker():
    """獲取配置好的 broker"""
    return broker

def get_result_backend():
    """獲取配置好的 result backend"""
    return result_backend

def get_queue_config(queue_name: str):
    """獲取隊列配置"""
    return QUEUE_CONFIG.get(queue_name, QUEUE_CONFIG['processing_queue'])

# 導出配置
__all__ = [
    'broker',
    'result_backend', 
    'get_broker',
    'get_result_backend',
    'get_queue_config',
    'QUEUE_CONFIG',
    'WORKER_CONFIG',
    'MONITORING_CONFIG'
]