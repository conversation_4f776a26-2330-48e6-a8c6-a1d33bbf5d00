# 郵件收件夾 + FT-EQC 整合系統使用說明

## 系統概述

這是一個整合了 Flask 郵件收件夾和 FastAPI FT-EQC 處理的 Web 應用程式系統。可以從 POP3 伺服器同步郵件，並提供現代化的 Web 介面來管理郵件，同時整合了完整的 FT-EQC 業務處理流程。

## 主要功能

### 核心功能
- [E_MAIL] **POP3 郵件同步** - 從 POP3 伺服器自動同步郵件
- [DISK] **本地存儲** - 使用 SQLite 資料庫本地存儲郵件
- [GLOBE_WITH_MERIDIANS] **Web 介面** - 現代化的 Web 收件夾介面
- [BUSTS_IN_SILHOUETTE] **寄件者分組** - 以寄件者為分頁組織郵件
- [SEARCH] **搜尋功能** - 支援主旨、內容、寄件者搜尋
- [CLIP] **附件支援** - 顯示和管理郵件附件

### 業務流程整合
- [ROCKET] **EQC 處理** - 啟動 EQC 處理流程
- [CHART] **報告生成** - 自動生成 EQCTOTALDATA 報告
- [SEARCH] **程式碼分析** - 程式碼區間檢測
- [DESKTOP] **GTK 整合** - 連接 GTK 介面
- [BOARD] **FT Summary** - 整合 FT 總結功能
- [FAST] **一鍵完成** - 完整業務流程自動化

## 系統架構

```
frontend/app.py             # 主應用程式啟動檔案 (Flask 工廠模式)
├── src/
│   ├── infrastructure/
│   │   ├── adapters/
│   │   │   ├── database/           # 資料庫層
│   │   │   ├── web_api/           # Web API 服務
│   │   │   ├── email_inbox/       # 郵件同步服務
│   │   │   ├── pop3/              # POP3 實作
│   │   │   └── smtp/              # SMTP 實作
│   │   └── logging/               # 日誌管理
│   ├── presentation/
│   │   └── web/
│   │       ├── templates/         # HTML 模板
│   │       └── static/           # CSS、JavaScript
│   └── data_models/              # 資料模型
```

## 系統架構

```
郵件收件夫 + FT-EQC 整合系統
├── Flask 服務 (端口 5000)
│   ├── 郵件收件夾管理
│   ├── POP3 同步服務
│   └── 郵件資料庫操作
├── FastAPI 服務 (端口 8010)
│   ├── FT-EQC 分組處理
│   ├── EQC 檢測和分析
│   └── 報告生成功能
└── 整合服務管理
    ├── 雙服務同時啟動
    ├── 服務間通訊
    └── 統一的 Web 介面
```

## 快速開始

### 1. 環境設置

確保已安裝 Python 3.8+ 和相關套件：

```bash
pip install -r requirements.txt
```

### 2. 配置郵件設定

創建 `.env` 檔案並設置郵件伺服器參數：

```env
# POP3 設定
POP3_SERVER=your.pop3.server.com
POP3_PORT=995
POP3_USERNAME=your_username
POP3_PASSWORD=your_password
POP3_USE_SSL=True

# 資料庫設定
DB_PATH=./emails.db
```

### 3. 啟動應用程式

#### 方式一：整合啟動（推薦）

```bash
# 使用整合啟動器
python start_integrated_services.py

# 自定義端口
python start_integrated_services.py --flask-port 5000 --fastapi-port 8010

# 分離啟動模式
python start_integrated_services.py --mode separate
```

#### 方式二：使用前端應用程式

```bash
# 啟動前端 Flask 服務
python frontend/app.py

# 使用 Flask CLI 啟動
flask run

# 使用自訂 CLI 啟動
python frontend/cli.py run --config development

# 使用 Makefile 啟動
make run-frontend
```

#### 方式三：手動分別啟動

```bash
# 終端 1：啟動 Flask 服務
python frontend/app.py

# 終端 2：啟動 FastAPI 服務
python -m uvicorn src.presentation.api.ft_eqc_api:app --host 0.0.0.0 --port 8010
```

### 4. 訪問 Web 介面

#### 服務端點

- **[E_MAIL] 郵件收件夾**: `http://localhost:5000`
- **[ROCKET] FT-EQC 處理**: `http://localhost:8010/ui`
- **[BOOKS] FT-EQC API 文檔**: `http://localhost:8010/docs`
- **[SEARCH] FT Summary 處理**: `http://localhost:8010/ft-summary-ui`

#### 整合功能

- 在郵件收件夾頁面點擊「FT-EQC 處理」按鈕直接跳轉到 FT-EQC 介面
- 在郵件詳情頁面點擊「啟動 EQC 處理」開始業務流程
- 統一的業務流程整合和狀態追蹤

## 使用說明

### 郵件同步

1. **手動同步**
   - 點擊頂部的「同步郵件」按鈕
   - 系統會從 POP3 伺服器獲取新郵件

2. **自動同步**
   - 點擊「自動同步」按鈕啟動
   - 默認每 5 分鐘同步一次
   - 可以隨時停止自動同步

### 郵件管理

1. **查看郵件**
   - 主介面顯示所有郵件列表
   - 可以按寄件者分頁查看
   - 點擊郵件查看詳情

2. **搜尋郵件**
   - 使用搜尋框搜尋主旨、內容或寄件者
   - 支援排序和篩選功能

3. **郵件操作**
   - [REFRESH] **重做** - 重新處理郵件
   - [ERROR] **刪[EXCEPT_CHAR]** - 刪[EXCEPT_CHAR]郵件
   - [ROCKET] **處理** - 啟動業務流程

### 批量操作

1. 使用複選框選擇多封郵件
2. 底部會顯示批量操作面板
3. 可以批量標記已讀、刪[EXCEPT_CHAR]或處理

## API 文檔

### 郵件 API

```bash
# 獲取郵件列表
GET /api/emails?limit=50&offset=0&sender=<EMAIL>

# 獲取郵件詳情
GET /api/emails/{email_id}

# 刪[EXCEPT_CHAR]郵件
DELETE /api/emails/{email_id}

# 搜尋郵件
GET /api/search?q=keyword&fields=subject,body,sender
```

### 同步 API

```bash
# 同步郵件
POST /api/sync
{
  "max_emails": 100
}

# 獲取同步狀態
GET /api/sync/status

# 啟動自動同步
POST /api/sync/auto/start
{
  "interval": 300
}

# 停止自動同步
POST /api/sync/auto/stop
```

### 統計 API

```bash
# 獲取統計資料
GET /api/statistics

# 獲取寄件者列表
GET /api/senders

# 測試連接
GET /api/connection/test

# 獲取連接狀態
GET /api/connection/status
```

## 資料庫結構

### 主要表格

1. **emails** - 郵件主表
   - id, message_id, sender, subject, body
   - received_time, is_read, has_attachments
   - created_at, updated_at

2. **senders** - 寄件者表
   - id, email_address, display_name
   - total_emails, unread_emails
   - last_email_time

3. **attachments** - 附件表
   - id, email_id, filename, content_type
   - size_bytes, content_data

4. **process_status** - 處理狀態表
   - id, email_id, step_name, status
   - started_at, completed_at, error_message

## 業務流程整合

### EQC 處理流程

1. **啟動 EQC 處理** - 開始 EQC 檢測流程
2. **生成報告** - 自動生成 EQCTOTALDATA.xlsx
3. **程式碼分析** - 執行程式碼區間檢測
4. **GTK 整合** - 開啟 GTK 介面進行操作
5. **FT Summary** - 整合 FT 總結功能
6. **一鍵完成** - 完整自動化流程

### 狀態追蹤

系統會追蹤每個郵件的處理狀態：
- pending - 待處理
- processing - 處理中
- completed - 已完成
- failed - 處理失敗

## 故障排[EXCEPT_CHAR]

### 常見問題

1. **無法連接 POP3 伺服器**
   - 檢查網路連接
   - 確認伺服器地址和端口正確
   - 驗證用戶名和密碼

2. **郵件同步失敗**
   - 查看日誌檔案
   - 檢查伺服器連接狀態
   - 確認郵件格式正確

3. **Web 介面無法訪問**
   - 檢查 Flask 伺服器是否正常啟動
   - 確認端口沒有被其他程式佔用
   - 檢查防火牆設定

### 日誌檔案

系統會自動記錄日誌，位置：
- 應用程式日誌：`logs/email_inbox_app.log`
- 同步服務日誌：`logs/email_sync.log`
- Web 服務日誌：`logs/web_api.log`

## 開發說明

### 自定義業務流程

1. 繼承 `EmailDetail` 類別
2. 實作業務邏輯方法
3. 註冊新的 API 端點
4. 更新前端介面

### 擴展功能

- 新增郵件伺服器類型 (IMAP、Exchange)
- 實作郵件發送功能
- 新增更多報告格式
- 整合其他業務系統

## 技術支援

如有問題，請檢查：
1. 系統日誌檔案
2. 網路連接狀態
3. 郵件伺服器設定
4. 資料庫檔案權限

---

© 2024 Email Inbox System - 郵件收件夾系統