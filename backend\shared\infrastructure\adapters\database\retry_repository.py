"""
Story 1.3: EmailDownloadRetryLogDB Repository
重試記錄倉庫層，提供完整的 CRUD 操作和高級查詢功能
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc, text, Integer
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from backend.shared.infrastructure.adapters.database.models import EmailDB
from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    EmailDownloadRetryLogDB, EmailDownloadStatusDB, RetryStrategy, RetryStatus
)


class RetryLogRepository:
    """重試記錄倉庫層 - 提供完整的 CRUD 操作"""

    def __init__(self, session: Session):
        """初始化倉庫"""
        self.session = session

    # =============== 基本 CRUD 操作 ===============

    def create_retry_log(self, retry_data: Dict[str, Any]) -> EmailDownloadRetryLogDB:
        """
        創建重試記錄
        
        Args:
            retry_data: 重試記錄數據
            
        Returns:
            EmailDownloadRetryLogDB: 創建的重試記錄
            
        Raises:
            ValueError: 資料驗證失敗
            IntegrityError: 資料庫約束違反
        """
        try:
            # 資料驗證
            self._validate_retry_data(retry_data)
            
            # 創建重試記錄
            retry_log = EmailDownloadRetryLogDB(**retry_data)
            
            # 業務規則驗證
            errors = retry_log.validate_business_rules()
            if errors:
                raise ValueError(f"業務規則驗證失敗: {', '.join(errors)}")
            
            self.session.add(retry_log)
            self.session.commit()
            
            return retry_log
        
        except IntegrityError as e:
            self.session.rollback()
            raise IntegrityError(f"資料庫約束違反: {str(e)}", None, None)
        except Exception as e:
            self.session.rollback()
            raise

    def get_retry_log_by_id(self, retry_id: int) -> Optional[EmailDownloadRetryLogDB]:
        """
        根據ID查詢重試記錄
        
        Args:
            retry_id: 重試記錄ID
            
        Returns:
            Optional[EmailDownloadRetryLogDB]: 重試記錄或None
        """
        return self.session.query(EmailDownloadRetryLogDB)\
            .options(joinedload(EmailDownloadRetryLogDB.email),
                    joinedload(EmailDownloadRetryLogDB.download_status))\
            .filter(EmailDownloadRetryLogDB.id == retry_id)\
            .first()

    def get_retry_logs_by_email(self, email_id: int, limit: int = 100) -> List[EmailDownloadRetryLogDB]:
        """
        根據郵件ID查詢重試記錄
        
        Args:
            email_id: 郵件ID
            limit: 限制返回數量
            
        Returns:
            List[EmailDownloadRetryLogDB]: 重試記錄列表
        """
        return self.session.query(EmailDownloadRetryLogDB)\
            .options(joinedload(EmailDownloadRetryLogDB.download_status))\
            .filter(EmailDownloadRetryLogDB.email_id == email_id)\
            .order_by(desc(EmailDownloadRetryLogDB.created_at))\
            .limit(limit)\
            .all()

    def get_retry_logs_by_download_status(self, download_status_id: int) -> List[EmailDownloadRetryLogDB]:
        """
        根據下載狀態ID查詢重試記錄
        
        Args:
            download_status_id: 下載狀態ID
            
        Returns:
            List[EmailDownloadRetryLogDB]: 重試記錄列表
        """
        return self.session.query(EmailDownloadRetryLogDB)\
            .filter(EmailDownloadRetryLogDB.download_status_id == download_status_id)\
            .order_by(asc(EmailDownloadRetryLogDB.retry_attempt))\
            .all()

    def update_retry_status(self, retry_id: int, status: RetryStatus, 
                          additional_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        更新重試狀態
        
        Args:
            retry_id: 重試記錄ID
            status: 新狀態
            additional_data: 附加更新數據
            
        Returns:
            bool: 更新是否成功
        """
        try:
            retry_log = self.get_retry_log_by_id(retry_id)
            if not retry_log:
                return False
            
            # 更新狀態
            retry_log.status = status
            retry_log.updated_at = datetime.utcnow()
            
            # 根據狀態設置時間戳
            if status == RetryStatus.RUNNING and not retry_log.started_at:
                retry_log.started_at = datetime.utcnow()
            elif status in [RetryStatus.SUCCESS, RetryStatus.FAILED, RetryStatus.CANCELLED, RetryStatus.TIMEOUT]:
                if not retry_log.completed_at:
                    retry_log.completed_at = datetime.utcnow()
                
                # 計算執行時間
                if retry_log.started_at:
                    duration = (retry_log.completed_at - retry_log.started_at).total_seconds() * 1000
                    retry_log.duration_ms = int(duration)
                
                # 設置成功標誌
                retry_log.success = (status == RetryStatus.SUCCESS)
            
            # 更新附加數據
            if additional_data:
                for key, value in additional_data.items():
                    if hasattr(retry_log, key):
                        setattr(retry_log, key, value)
            
            # 業務規則驗證
            errors = retry_log.validate_business_rules()
            if errors:
                self.session.rollback()
                raise ValueError(f"業務規則驗證失敗: {', '.join(errors)}")
            
            self.session.commit()
            return True
        
        except Exception:
            self.session.rollback()
            raise

    def cancel_retry_task(self, retry_id: int, reason: str = "手動取消") -> bool:
        """
        取消重試任務
        
        Args:
            retry_id: 重試記錄ID
            reason: 取消原因
            
        Returns:
            bool: 取消是否成功
        """
        additional_data = {
            'error_type': 'CANCELLED',
            'error_message': reason
        }
        return self.update_retry_status(retry_id, RetryStatus.CANCELLED, additional_data)

    def delete_retry_log(self, retry_id: int) -> bool:
        """
        刪除重試記錄
        
        Args:
            retry_id: 重試記錄ID
            
        Returns:
            bool: 刪除是否成功
        """
        try:
            retry_log = self.session.query(EmailDownloadRetryLogDB)\
                .filter(EmailDownloadRetryLogDB.id == retry_id)\
                .first()
            
            if retry_log:
                self.session.delete(retry_log)
                self.session.commit()
                return True
            return False
        
        except Exception:
            self.session.rollback()
            raise

    # =============== 高級查詢功能 ===============

    def get_retry_logs_by_status(self, status: RetryStatus, limit: int = 100) -> List[EmailDownloadRetryLogDB]:
        """
        根據狀態查詢重試記錄
        
        Args:
            status: 重試狀態
            limit: 限制返回數量
            
        Returns:
            List[EmailDownloadRetryLogDB]: 重試記錄列表
        """
        return self.session.query(EmailDownloadRetryLogDB)\
            .options(joinedload(EmailDownloadRetryLogDB.email),
                    joinedload(EmailDownloadRetryLogDB.download_status))\
            .filter(EmailDownloadRetryLogDB.status == status)\
            .order_by(asc(EmailDownloadRetryLogDB.scheduled_at))\
            .limit(limit)\
            .all()

    def get_scheduled_retries(self, before_time: Optional[datetime] = None) -> List[EmailDownloadRetryLogDB]:
        """
        獲取已調度的重試任務
        
        Args:
            before_time: 調度時間篩選（可選）
            
        Returns:
            List[EmailDownloadRetryLogDB]: 已調度的重試記錄列表
        """
        query = self.session.query(EmailDownloadRetryLogDB)\
            .options(joinedload(EmailDownloadRetryLogDB.email),
                    joinedload(EmailDownloadRetryLogDB.download_status))\
            .filter(EmailDownloadRetryLogDB.status == RetryStatus.SCHEDULED)
        
        if before_time:
            query = query.filter(EmailDownloadRetryLogDB.scheduled_at <= before_time)
        
        return query.order_by(asc(EmailDownloadRetryLogDB.scheduled_at)).all()

    def get_retry_statistics(self, email_id: Optional[int] = None) -> Dict[str, Any]:
        """
        獲取重試統計信息
        
        Args:
            email_id: 郵件ID（可選，不提供則獲取全局統計）
            
        Returns:
            Dict[str, Any]: 統計信息
        """
        query = self.session.query(EmailDownloadRetryLogDB)
        
        if email_id:
            query = query.filter(EmailDownloadRetryLogDB.email_id == email_id)
        
        # 狀態統計
        status_stats = self.session.query(
            EmailDownloadRetryLogDB.status,
            func.count(EmailDownloadRetryLogDB.id).label('count')
        )
        
        if email_id:
            status_stats = status_stats.filter(EmailDownloadRetryLogDB.email_id == email_id)
        
        status_stats = status_stats.group_by(EmailDownloadRetryLogDB.status).all()
        
        # 策略統計
        strategy_stats = self.session.query(
            EmailDownloadRetryLogDB.retry_strategy,
            func.count(EmailDownloadRetryLogDB.id).label('count')
        )
        
        if email_id:
            strategy_stats = strategy_stats.filter(EmailDownloadRetryLogDB.email_id == email_id)
        
        strategy_stats = strategy_stats.group_by(EmailDownloadRetryLogDB.retry_strategy).all()
        
        # 成功率統計
        success_rate_query = self.session.query(
            func.avg(func.cast(EmailDownloadRetryLogDB.success, Integer)).label('success_rate')
        )
        
        if email_id:
            success_rate_query = success_rate_query.filter(EmailDownloadRetryLogDB.email_id == email_id)
        
        success_rate = success_rate_query.scalar() or 0.0
        
        # 平均執行時間
        avg_duration = self.session.query(
            func.avg(EmailDownloadRetryLogDB.duration_ms).label('avg_duration')
        ).filter(EmailDownloadRetryLogDB.duration_ms.isnot(None))
        
        if email_id:
            avg_duration = avg_duration.filter(EmailDownloadRetryLogDB.email_id == email_id)
        
        avg_duration = avg_duration.scalar() or 0.0
        
        return {
            'status_distribution': {status.value: count for status, count in status_stats},
            'strategy_distribution': {strategy.value: count for strategy, count in strategy_stats},
            'success_rate': float(success_rate),
            'average_duration_ms': float(avg_duration),
            'total_retries': query.count()
        }

    def get_failed_retries_by_error_type(self, error_type: Optional[str] = None, 
                                       limit: int = 100) -> List[EmailDownloadRetryLogDB]:
        """
        獲取失敗的重試記錄，按錯誤類型篩選
        
        Args:
            error_type: 錯誤類型（可選）
            limit: 限制返回數量
            
        Returns:
            List[EmailDownloadRetryLogDB]: 失敗的重試記錄列表
        """
        query = self.session.query(EmailDownloadRetryLogDB)\
            .options(joinedload(EmailDownloadRetryLogDB.email),
                    joinedload(EmailDownloadRetryLogDB.download_status))\
            .filter(EmailDownloadRetryLogDB.status == RetryStatus.FAILED)
        
        if error_type:
            query = query.filter(EmailDownloadRetryLogDB.error_type == error_type)
        
        return query.order_by(desc(EmailDownloadRetryLogDB.completed_at))\
            .limit(limit)\
            .all()

    # =============== 批量操作 ===============

    def bulk_create_retry_logs(self, retry_logs_data: List[Dict[str, Any]]) -> List[EmailDownloadRetryLogDB]:
        """
        批量創建重試記錄
        
        Args:
            retry_logs_data: 重試記錄數據列表
            
        Returns:
            List[EmailDownloadRetryLogDB]: 創建的重試記錄列表
        """
        try:
            retry_logs = []
            
            for retry_data in retry_logs_data:
                # 資料驗證
                self._validate_retry_data(retry_data)
                
                # 創建重試記錄
                retry_log = EmailDownloadRetryLogDB(**retry_data)
                
                # 業務規則驗證
                errors = retry_log.validate_business_rules()
                if errors:
                    raise ValueError(f"業務規則驗證失敗: {', '.join(errors)}")
                
                retry_logs.append(retry_log)
            
            # 批量插入
            self.session.add_all(retry_logs)
            self.session.commit()
            
            return retry_logs
        
        except Exception:
            self.session.rollback()
            raise

    def bulk_update_status(self, retry_ids: List[int], status: RetryStatus) -> int:
        """
        批量更新重試狀態
        
        Args:
            retry_ids: 重試記錄ID列表
            status: 新狀態
            
        Returns:
            int: 更新的記錄數量
        """
        try:
            now = datetime.utcnow()
            
            # 構建更新數據
            update_data = {
                'status': status,
                'updated_at': now
            }
            
            # 根據狀態設置時間戳
            if status == RetryStatus.RUNNING:
                update_data['started_at'] = now
            elif status in [RetryStatus.SUCCESS, RetryStatus.FAILED, RetryStatus.CANCELLED, RetryStatus.TIMEOUT]:
                update_data['completed_at'] = now
                update_data['success'] = (status == RetryStatus.SUCCESS)
            
            # 批量更新
            result = self.session.query(EmailDownloadRetryLogDB)\
                .filter(EmailDownloadRetryLogDB.id.in_(retry_ids))\
                .update(update_data, synchronize_session=False)
            
            self.session.commit()
            return result
        
        except Exception:
            self.session.rollback()
            raise

    # =============== 清理和維護 ===============

    def cleanup_old_retry_logs(self, days_old: int = 30) -> int:
        """
        清理舊的重試記錄
        
        Args:
            days_old: 保留天數
            
        Returns:
            int: 清理的記錄數量
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            # 只清理已完成的記錄
            result = self.session.query(EmailDownloadRetryLogDB)\
                .filter(EmailDownloadRetryLogDB.created_at < cutoff_date)\
                .filter(EmailDownloadRetryLogDB.status.in_([
                    RetryStatus.SUCCESS, RetryStatus.FAILED, 
                    RetryStatus.CANCELLED, RetryStatus.TIMEOUT
                ]))\
                .delete(synchronize_session=False)
            
            self.session.commit()
            return result
        
        except Exception:
            self.session.rollback()
            raise

    # =============== 輔助方法 ===============

    def _validate_retry_data(self, retry_data: Dict[str, Any]) -> None:
        """
        驗證重試記錄數據
        
        Args:
            retry_data: 重試記錄數據
            
        Raises:
            ValueError: 資料驗證失敗
        """
        required_fields = [
            'download_status_id', 'email_id', 'retry_attempt', 
            'retry_strategy', 'scheduled_at', 'retry_delay_seconds'
        ]
        
        for field in required_fields:
            if field not in retry_data:
                raise ValueError(f"缺少必要欄位: {field}")
        
        # 驗證重試次數
        if not (1 <= retry_data['retry_attempt'] <= 10):
            raise ValueError("重試次數必須在 1-10 之間")
        
        # 驗證延遲時間
        if not (0 <= retry_data['retry_delay_seconds'] <= 3600):
            raise ValueError("重試延遲必須在 0-3600 秒之間")
        
        # 驗證策略
        if retry_data['retry_strategy'] not in RetryStrategy:
            raise ValueError(f"無效的重試策略: {retry_data['retry_strategy']}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        獲取性能指標
        
        Returns:
            Dict[str, Any]: 性能指標
        """
        try:
            # 使用原生 SQL 進行性能測試
            start_time = datetime.utcnow()
            
            # 測試查詢性能
            count_query = text("SELECT COUNT(*) FROM email_download_retry_log")
            total_count = self.session.execute(count_query).scalar()
            
            # 測試索引性能
            index_query = text("""
                SELECT COUNT(*) FROM email_download_retry_log 
                WHERE status = :status AND email_id = :email_id
            """)
            
            # 使用第一個可用的郵件ID進行測試
            email_id = self.session.query(EmailDB.id).first()
            email_id = email_id[0] if email_id else 1
            
            indexed_count = self.session.execute(
                index_query, 
                {'status': 'scheduled', 'email_id': email_id}
            ).scalar()
            
            end_time = datetime.utcnow()
            query_duration = (end_time - start_time).total_seconds() * 1000
            
            return {
                'total_retry_logs': total_count,
                'query_duration_ms': query_duration,
                'index_performance': 'GOOD' if query_duration < 150 else 'NEEDS_OPTIMIZATION'
            }
        
        except Exception as e:
            return {
                'error': str(e),
                'total_retry_logs': 0,
                'query_duration_ms': 0,
                'index_performance': 'ERROR'
            }