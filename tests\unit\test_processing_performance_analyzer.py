"""
測試 Epic-03 Story 3.4: ProcessingPerformanceAnalyzer
處理性能監控和分析的完整測試
"""

import pytest
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from typing import Dict, Any

from backend.shared.services.processing_performance_analyzer import (
    ProcessingPerformanceAnalyzer,
    ProcessingStageMetrics,
    PerformanceAlert,
    BottleneckAnalysis
)


class TestProcessingStageMetrics:
    """ProcessingStageMetrics 數據類測試"""
    
    def test_stage_metrics_initialization(self):
        """測試階段指標初始化"""
        metrics = ProcessingStageMetrics(stage_name="test_stage")
        
        assert metrics.stage_name == "test_stage"
        assert metrics.total_count == 0
        assert metrics.success_count == 0
        assert metrics.failed_count == 0
        assert metrics.total_duration == 0.0
        assert metrics.min_duration == float('inf')
        assert metrics.max_duration == 0.0
        assert len(metrics.durations) == 0
    
    def test_stage_metrics_calculations(self):
        """測試階段指標計算"""
        metrics = ProcessingStageMetrics(stage_name="test_stage")
        
        # 添加一些測試數據
        metrics.total_count = 10
        metrics.success_count = 8
        metrics.failed_count = 2
        metrics.total_duration = 100.0
        metrics.min_duration = 5.0
        metrics.max_duration = 20.0
        metrics.durations.extend([5.0, 8.0, 10.0, 12.0, 15.0, 18.0, 20.0])
        
        # 測試計算屬性
        assert metrics.average_duration == 10.0
        assert metrics.success_rate == 0.8
        assert metrics.median_duration == 12.0
        assert metrics.percentile_95_duration > 15.0
    
    def test_stage_metrics_edge_cases(self):
        """測試階段指標邊界情況"""
        metrics = ProcessingStageMetrics(stage_name="test_stage")
        
        # 空數據的情況
        assert metrics.average_duration == 0.0
        assert metrics.success_rate == 0.0
        assert metrics.median_duration == 0.0
        assert metrics.percentile_95_duration == 0.0


class TestProcessingPerformanceAnalyzer:
    """ProcessingPerformanceAnalyzer 主要功能測試"""
    
    @pytest.fixture
    def analyzer(self):
        """創建測試用的性能分析器"""
        return ProcessingPerformanceAnalyzer()
    
    @pytest.fixture
    def analyzer_with_custom_thresholds(self):
        """創建帶有自定義閾值的性能分析器"""
        custom_thresholds = {
            'avg_processing_time': 60.0,  # 1分鐘
            'success_rate': 0.9,          # 90%
            'stage_duration_multiplier': 2.0,  # 2倍
            'error_rate': 0.1,            # 10%
        }
        return ProcessingPerformanceAnalyzer(alert_thresholds=custom_thresholds)
    
    def test_analyzer_initialization(self, analyzer):
        """測試分析器初始化"""
        assert analyzer is not None
        assert len(analyzer.stage_metrics) == 0
        assert len(analyzer.email_processing_records) == 0
        assert len(analyzer.performance_alerts) == 0
        assert 'avg_processing_time' in analyzer.alert_thresholds
        assert 'email_parsing' in analyzer.baseline_metrics
    
    def test_start_email_processing(self, analyzer):
        """測試開始郵件處理追蹤"""
        email_id = 12345
        metadata = {'sender': '<EMAIL>', 'subject': 'Test Email'}
        
        analyzer.start_email_processing(email_id, metadata)
        
        assert email_id in analyzer.email_processing_records
        record = analyzer.email_processing_records[email_id]
        assert record['status'] == 'processing'
        assert record['metadata'] == metadata
        assert 'start_time' in record
        assert record['stages'] == {}
    
    def test_track_stage_lifecycle(self, analyzer):
        """測試階段生命週期追蹤"""
        email_id = 12345
        stage_name = "email_parsing"
        
        # 開始處理
        analyzer.start_email_processing(email_id)
        
        # 開始階段
        analyzer.track_stage_start(email_id, stage_name)
        
        assert stage_name in analyzer.email_processing_records[email_id]['stages']
        stage_info = analyzer.email_processing_records[email_id]['stages'][stage_name]
        assert stage_info['status'] == 'running'
        assert 'start_time' in stage_info
        
        # 模擬處理時間
        time.sleep(0.01)
        
        # 完成階段
        duration = analyzer.track_stage_completion(email_id, stage_name, success=True)
        
        assert duration > 0
        assert stage_info['status'] == 'completed'
        assert stage_info['success'] == True
        assert stage_info['duration'] == duration
        
        # 檢查階段指標是否更新
        assert stage_name in analyzer.stage_metrics
        metrics = analyzer.stage_metrics[stage_name]
        assert metrics.total_count == 1
        assert metrics.success_count == 1
        assert metrics.failed_count == 0
    
    def test_complete_email_processing(self, analyzer):
        """測試完成郵件處理"""
        email_id = 12345
        
        # 設置一個完整的處理流程
        analyzer.start_email_processing(email_id, {'test': 'data'})
        
        # 添加幾個處理階段
        stages = ['email_parsing', 'attachment_processing', 'database_update']
        for stage in stages:
            analyzer.track_stage_start(email_id, stage)
            time.sleep(0.01)  # 模擬處理時間
            analyzer.track_stage_completion(email_id, stage, success=True)
        
        # 完成處理
        final_metadata = {'result': 'success'}
        summary = analyzer.complete_email_processing(email_id, overall_success=True, 
                                                   final_metadata=final_metadata)
        
        # 驗證摘要
        assert summary['email_id'] == email_id
        assert summary['overall_success'] == True
        assert summary['total_duration'] > 0
        assert summary['stage_count'] == 3
        assert summary['successful_stages'] == 3
        assert 'stages_summary' in summary
        
        # 檢查記錄狀態
        record = analyzer.email_processing_records[email_id]
        assert record['status'] == 'completed'
        assert record['overall_success'] == True
        assert record['final_metadata'] == final_metadata\n    \n    def test_performance_alerts_slow_stage(self, analyzer_with_custom_thresholds):\n        \"\"\"測試慢階段性能警報\"\"\"\n        email_id = 12345\n        stage_name = \"email_parsing\"\n        \n        analyzer_with_custom_thresholds.start_email_processing(email_id)\n        analyzer_with_custom_thresholds.track_stage_start(email_id, stage_name)\n        \n        # 模擬長時間處理 (超過基準時間的2倍)\n        baseline = analyzer_with_custom_thresholds.baseline_metrics[stage_name]  # 5.0秒\n        long_duration = baseline * 3  # 15秒，超過2倍閾值\n        \n        # 手動設置持續時間\n        with patch('time.time') as mock_time:\n            start_time = 1000.0\n            end_time = start_time + long_duration\n            \n            # 設置開始時間\n            analyzer_with_custom_thresholds.email_processing_records[email_id]['stages'][stage_name]['start_time'] = start_time\n            \n            # 模擬結束時間\n            mock_time.return_value = end_time\n            \n            duration = analyzer_with_custom_thresholds.track_stage_completion(email_id, stage_name, success=True)\n            \n            assert duration == long_duration\n            \n            # 檢查是否產生了警報\n            alerts = list(analyzer_with_custom_thresholds.performance_alerts)\n            assert len(alerts) > 0\n            \n            slow_alert = next((alert for alert in alerts \n                             if alert.alert_type == 'slow_stage_processing'), None)\n            assert slow_alert is not None\n            assert slow_alert.stage_name == stage_name\n            assert slow_alert.email_id == email_id\n            assert slow_alert.severity in ['medium', 'high']\n    \n    def test_performance_alerts_high_error_rate(self, analyzer):\n        \"\"\"測試高錯誤率性能警報\"\"\"\n        stage_name = \"email_parsing\"\n        \n        # 模擬多次處理，其中大部分失敗\n        for i in range(15):  # 需要至少10次記錄才會檢查錯誤率\n            email_id = 10000 + i\n            analyzer.start_email_processing(email_id)\n            analyzer.track_stage_start(email_id, stage_name)\n            \n            # 前12次失敗，後3次成功 (80%錯誤率)\n            success = i >= 12\n            analyzer.track_stage_completion(email_id, stage_name, success=success)\n        \n        # 檢查是否產生了高錯誤率警報\n        alerts = list(analyzer.performance_alerts)\n        error_rate_alerts = [alert for alert in alerts \n                           if alert.alert_type == 'high_error_rate']\n        \n        assert len(error_rate_alerts) > 0\n        error_alert = error_rate_alerts[0]\n        assert error_alert.stage_name == stage_name\n        assert error_alert.severity == 'high'\n    \n    def test_get_real_time_metrics(self, analyzer):\n        \"\"\"測試獲取實時性能指標\"\"\"\n        # 添加一些測試數據\n        email_id = 12345\n        analyzer.start_email_processing(email_id)\n        analyzer.track_stage_start(email_id, \"email_parsing\")\n        analyzer.track_stage_completion(email_id, \"email_parsing\", success=True)\n        analyzer.complete_email_processing(email_id, overall_success=True)\n        \n        metrics = analyzer.get_real_time_metrics()\n        \n        assert 'timestamp' in metrics\n        assert 'active_processing_count' in metrics\n        assert 'hourly_stats' in metrics\n        assert 'stage_performance' in metrics\n        assert 'recent_alerts' in metrics\n        assert 'total_alerts_count' in metrics\n        \n        # 檢查階段性能數據\n        assert 'email_parsing' in metrics['stage_performance']\n        parsing_stats = metrics['stage_performance']['email_parsing']\n        assert parsing_stats['total_count'] == 1\n        assert parsing_stats['success_rate'] == 1.0\n    \n    def test_analyze_bottlenecks(self, analyzer):\n        \"\"\"測試瓶頸分析\"\"\"\n        stage_name = \"attachment_processing\"\n        baseline_duration = analyzer.baseline_metrics[stage_name]  # 15.0秒\n        \n        # 創建一個明顯的瓶頸：處理時間是基準的3倍\n        slow_duration = baseline_duration * 3\n        \n        # 模擬多次慢處理\n        for i in range(10):\n            email_id = 20000 + i\n            analyzer.start_email_processing(email_id)\n            analyzer.track_stage_start(email_id, stage_name)\n            \n            # 手動設置持續時間\n            with patch('time.time') as mock_time:\n                start_time = 1000.0 + i * 100\n                end_time = start_time + slow_duration\n                \n                analyzer.email_processing_records[email_id]['stages'][stage_name]['start_time'] = start_time\n                mock_time.return_value = end_time\n                \n                analyzer.track_stage_completion(email_id, stage_name, success=True)\n        \n        # 分析瓶頸\n        bottlenecks = analyzer.analyze_bottlenecks()\n        \n        assert len(bottlenecks) > 0\n        \n        # 找到附件處理瓶頸\n        attachment_bottleneck = next((b for b in bottlenecks \n                                    if b.bottleneck_stage == stage_name), None)\n        assert attachment_bottleneck is not None\n        assert attachment_bottleneck.impact_level in ['medium', 'high']\n        assert attachment_bottleneck.slowdown_factor >= 2.0\n        assert len(attachment_bottleneck.recommendations) > 0\n    \n    def test_get_performance_trends(self, analyzer):\n        \"\"\"測試性能趨勢分析\"\"\"\n        # 模擬不同時間的處理數據\n        current_time = time.time()\n        \n        with patch('time.time') as mock_time:\n            # 模擬2小時前的處理\n            mock_time.return_value = current_time - 7200  # 2小時前\n            \n            for i in range(5):\n                email_id = 30000 + i\n                analyzer.start_email_processing(email_id)\n                analyzer.complete_email_processing(email_id, overall_success=True)\n            \n            # 模擬1小時前的處理\n            mock_time.return_value = current_time - 3600  # 1小時前\n            \n            for i in range(8):\n                email_id = 30100 + i\n                analyzer.start_email_processing(email_id)\n                analyzer.complete_email_processing(email_id, overall_success=True)\n        \n        trends = analyzer.get_performance_trends(hours=3)\n        \n        assert 'time_range' in trends\n        assert 'hourly_trends' in trends\n        assert 'trend_analysis' in trends\n        \n        assert trends['time_range']['hours'] == 3\n        assert len(trends['hourly_trends']) == 3\n        \n        trend_analysis = trends['trend_analysis']\n        assert 'duration_trend' in trend_analysis\n        assert 'recent_avg_duration' in trend_analysis\n        assert 'performance_change_percent' in trend_analysis\n    \n    def test_clear_old_data(self, analyzer):\n        \"\"\"測試清理舊數據\"\"\"\n        # 添加一些測試數據\n        current_time = datetime.utcnow()\n        old_time = current_time - timedelta(days=10)\n        \n        # 手動添加舊數據\n        old_hour_key = old_time.strftime('%Y-%m-%d-%H')\n        analyzer.hourly_stats[old_hour_key] = {\n            'total_processed': 10,\n            'successful_count': 8,\n            'failed_count': 2\n        }\n        \n        # 添加舊的處理記錄\n        old_email_id = 99999\n        analyzer.email_processing_records[old_email_id] = {\n            'start_time': old_time.timestamp(),\n            'status': 'completed'\n        }\n        \n        # 添加舊的警報\n        old_alert = PerformanceAlert(\n            alert_type='test_alert',\n            severity='low',\n            message='Test old alert',\n            timestamp=old_time,\n            metric_value=1.0,\n            threshold_value=2.0\n        )\n        analyzer.performance_alerts.append(old_alert)\n        \n        # 執行清理\n        cleaned_counts = analyzer.clear_old_data(days_to_keep=7)\n        \n        # 驗證清理結果\n        assert cleaned_counts['hourly_stats_cleaned'] >= 0\n        assert cleaned_counts['processing_records_cleaned'] >= 0\n        assert cleaned_counts['alerts_cleaned'] >= 0\n        \n        # 確認舊數據已被清理\n        assert old_hour_key not in analyzer.hourly_stats\n        assert old_email_id not in analyzer.email_processing_records\n    \n    def test_export_performance_report(self, analyzer):\n        \"\"\"測試導出性能報告\"\"\"\n        # 添加一些測試數據\n        email_id = 40000\n        analyzer.start_email_processing(email_id)\n        analyzer.track_stage_start(email_id, \"email_parsing\")\n        analyzer.track_stage_completion(email_id, \"email_parsing\", success=True)\n        analyzer.complete_email_processing(email_id, overall_success=True)\n        \n        report = analyzer.export_performance_report(hours=24)\n        \n        # 檢查報告結構\n        assert 'report_metadata' in report\n        assert 'real_time_metrics' in report\n        assert 'bottleneck_analysis' in report\n        assert 'performance_trends' in report\n        assert 'performance_alerts' in report\n        assert 'stage_details' in report\n        \n        # 檢查元數據\n        metadata = report['report_metadata']\n        assert 'generated_at' in metadata\n        assert metadata['time_window_hours'] == 24\n        assert 'analyzer_version' in metadata\n        \n        # 檢查階段詳情\n        assert 'email_parsing' in report['stage_details']\n        parsing_details = report['stage_details']['email_parsing']\n        assert parsing_details['total_count'] == 1\n        assert parsing_details['success_rate'] == 1.0


class TestPerformanceIntegration:
    \"\"\"性能分析器整合測試\"\"\"\n    \n    def test_complete_email_processing_workflow(self):\n        \"\"\"測試完整的郵件處理工作流程\"\"\"\n        analyzer = ProcessingPerformanceAnalyzer()\n        \n        email_id = 50000\n        stages = [\n            'email_parsing',\n            'attachment_processing', \n            'vendor_files_processing',\n            'database_update',\n            'notification_sending'\n        ]\n        \n        # 開始處理\n        analyzer.start_email_processing(email_id, {\n            'sender': '<EMAIL>',\n            'subject': 'Test Email',\n            'attachment_count': 3\n        })\n        \n        # 模擬各個階段的處理\n        stage_durations = []\n        for stage in stages:\n            analyzer.track_stage_start(email_id, stage)\n            \n            # 模擬不同的處理時間\n            if stage == 'vendor_files_processing':\n                time.sleep(0.05)  # 較長的處理時間\n            else:\n                time.sleep(0.01)  # 正常處理時間\n            \n            duration = analyzer.track_stage_completion(email_id, stage, success=True)\n            stage_durations.append(duration)\n        \n        # 完成處理\n        summary = analyzer.complete_email_processing(email_id, overall_success=True)\n        \n        # 驗證處理摘要\n        assert summary['email_id'] == email_id\n        assert summary['overall_success'] == True\n        assert summary['stage_count'] == len(stages)\n        assert summary['successful_stages'] == len(stages)\n        assert summary['processing_efficiency'] > 0.0\n        \n        # 驗證所有階段都有指標\n        for stage in stages:\n            assert stage in analyzer.stage_metrics\n            metrics = analyzer.stage_metrics[stage]\n            assert metrics.total_count == 1\n            assert metrics.success_count == 1\n        \n        # 獲取實時指標\n        real_time_metrics = analyzer.get_real_time_metrics()\n        assert real_time_metrics['active_processing_count'] == 0  # 處理已完成\n        \n        # 檢查是否有vendor_files_processing的較慢警報\n        alerts = analyzer.get_performance_alerts()\n        # 可能有也可能沒有警報，取決於實際處理時間\n    \n    def test_multiple_concurrent_processing(self):\n        \"\"\"測試多個並發處理的監控\"\"\"\n        analyzer = ProcessingPerformanceAnalyzer()\n        \n        # 開始多個並發處理\n        email_ids = [60001, 60002, 60003, 60004, 60005]\n        \n        for email_id in email_ids:\n            analyzer.start_email_processing(email_id, {'concurrent_test': True})\n            analyzer.track_stage_start(email_id, 'email_parsing')\n        \n        # 檢查活躍處理數量\n        metrics = analyzer.get_real_time_metrics()\n        assert metrics['active_processing_count'] == len(email_ids)\n        \n        # 完成部分處理\n        for i, email_id in enumerate(email_ids[:3]):\n            analyzer.track_stage_completion(email_id, 'email_parsing', success=True)\n            analyzer.complete_email_processing(email_id, overall_success=True)\n        \n        # 檢查更新後的活躍處理數量\n        metrics = analyzer.get_real_time_metrics()\n        assert metrics['active_processing_count'] == 2  # 還有2個在處理\n        \n        # 完成剩餘處理\n        for email_id in email_ids[3:]:\n            analyzer.track_stage_completion(email_id, 'email_parsing', success=True)\n            analyzer.complete_email_processing(email_id, overall_success=True)\n        \n        # 最終檢查\n        final_metrics = analyzer.get_real_time_metrics()\n        assert final_metrics['active_processing_count'] == 0\n        \n        # 檢查統計數據\n        parsing_stats = final_metrics['stage_performance']['email_parsing']\n        assert parsing_stats['total_count'] == len(email_ids)\n        assert parsing_stats['success_rate'] == 1.0


if __name__ == \"__main__\":\n    # 運行性能分析器測試\n    pytest.main([__file__, \"-v\", \"--tb=short\"])