"""
Flask CLI 命令
提供應用程式管理和維護命令
"""

import os
import sys
import click
from pathlib import Path
from flask import Flask
from flask.cli import with_appcontext

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from frontend.app import create_app


@click.group()
def cli():
    """Flask 應用程式 CLI 命令"""
    pass


@cli.command()
@click.option('--config', default='development', help='配置環境 (development/testing/production)')
@click.option('--host', default='0.0.0.0', help='主機地址')
@click.option('--port', default=5000, help='端口號')
@click.option('--debug/--no-debug', default=None, help='除錯模式')
def run(config, host, port, debug):
    """啟動 Flask 應用程式"""
    app = create_app(config)
    
    if debug is None:
        debug = app.config.get('FLASK_DEBUG', False)
    
    click.echo(f"🌐 啟動 Flask 應用程式...")
    click.echo(f"   配置環境: {config}")
    click.echo(f"   主機地址: {host}")
    click.echo(f"   端口號: {port}")
    click.echo(f"   除錯模式: {debug}")
    
    app.run(host=host, port=port, debug=debug)


@cli.command()
@click.option('--config', default='development', help='配置環境')
def check_config(config):
    """檢查應用程式配置"""
    app = create_app(config)
    
    click.echo(f"📋 應用程式配置檢查 ({config}):")
    click.echo(f"   SECRET_KEY: {'已設定' if app.config.get('SECRET_KEY') else '未設定'}")
    click.echo(f"   DATABASE_URL: {app.config.get('DATABASE_URL', '未設定')}")
    click.echo(f"   UPLOAD_FOLDER: {app.config.get('UPLOAD_FOLDER', '未設定')}")
    click.echo(f"   LOG_LEVEL: {app.config.get('LOG_LEVEL', '未設定')}")
    
    # 檢查模組配置
    static_mapping = app.config.get('STATIC_FOLDER_MAPPING', {})
    template_mapping = app.config.get('TEMPLATE_FOLDER_MAPPING', {})
    
    click.echo(f"   靜態資源模組: {len(static_mapping)} 個")
    click.echo(f"   模板模組: {len(template_mapping)} 個")
    
    # 檢查目錄是否存在
    from pathlib import Path
    
    missing_dirs = []
    for module, path in static_mapping.items():
        if not Path(path).exists():
            missing_dirs.append(f"靜態資源: {module} -> {path}")
    
    for module, path in template_mapping.items():
        if not Path(path).exists():
            missing_dirs.append(f"模板: {module} -> {path}")
    
    if missing_dirs:
        click.echo("⚠️  缺少的目錄:")
        for missing in missing_dirs:
            click.echo(f"   - {missing}")
    else:
        click.echo("✅ 所有模組目錄都存在")


@cli.command()
@with_appcontext
def init_db():
    """初始化資料庫"""
    click.echo("🗄️  初始化資料庫...")
    # TODO: 實作資料庫初始化邏輯
    click.echo("✅ 資料庫初始化完成")


@cli.command()
@with_appcontext
def create_dirs():
    """創建必要的目錄"""
    from pathlib import Path
    
    click.echo("📁 創建必要的目錄...")
    
    # 創建基本目錄
    dirs_to_create = [
        'logs',
        'temp',
        'temp/uploads',
        'data'
    ]
    
    for dir_path in dirs_to_create:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        click.echo(f"   ✅ {dir_path}")
    
    click.echo("✅ 目錄創建完成")


@cli.command()
def test_modules():
    """測試所有模組是否正常載入"""
    click.echo("🧪 測試模組載入...")
    
    try:
        from frontend.email.routes.email_routes import email_bp
        click.echo("   ✅ Email 模組")
    except Exception as e:
        click.echo(f"   ❌ Email 模組: {e}")
    
    try:
        from frontend.analytics.routes.analytics_routes import analytics_bp
        click.echo("   ✅ Analytics 模組")
    except Exception as e:
        click.echo(f"   ❌ Analytics 模組: {e}")
    
    try:
        from frontend.eqc.routes.eqc_routes import eqc_bp
        click.echo("   ✅ EQC 模組")
    except Exception as e:
        click.echo(f"   ❌ EQC 模組: {e}")
    
    try:
        from frontend.tasks.routes.task_routes import task_bp
        click.echo("   ✅ Tasks 模組")
    except Exception as e:
        click.echo(f"   ❌ Tasks 模組: {e}")
    
    try:
        from frontend.monitoring.routes.monitoring_routes import monitoring_bp
        click.echo("   ✅ Monitoring 模組")
    except Exception as e:
        click.echo(f"   ❌ Monitoring 模組: {e}")
    
    try:
        from frontend.file_management.routes.file_routes import file_bp
        click.echo("   ✅ File Management 模組")
    except Exception as e:
        click.echo(f"   ❌ File Management 模組: {e}")
    
    click.echo("✅ 模組測試完成")


if __name__ == '__main__':
    cli()