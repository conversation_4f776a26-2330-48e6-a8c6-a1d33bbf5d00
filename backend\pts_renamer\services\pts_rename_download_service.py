"""
PTS Renamer Download Service

This service handles result packaging and download for the PTS Renamer module.
It integrates with existing Dramatiq compression tasks and provides secure
download URLs with automatic file cleanup.

Features:
- Auto-compress processed files using existing compression infrastructure
- Generate secure download URLs with expiration
- Automatic file cleanup based on retention policies
- Support for multiple result types (renamed files, QC files, directories)
- Integration with existing Dramatiq compression tasks
"""

import os
import uuid
import shutil
import zipfile
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import asyncio
import json

from loguru import logger

# Import existing infrastructure
from backend.shared.infrastructure.adapters.file_staging_service import FileStagingService
# Note: create_download_archive_task imported in method to avoid circular import
from backend.pts_renamer.models.pts_rename_models import (
    PTSRenameConfig, PTSErrorCodes, PTSJobStatus
)
from backend.pts_renamer.models.pts_rename_entities import PTSProcessingJob, PTSFile
from backend.pts_renamer.repositories.pts_rename_repository import IPTSRenameRepository


class PTSRenameDownloadService:
    """
    PTS Renamer Download Service
    
    Handles result compression, download URL generation, and file cleanup
    using existing Dramatiq compression infrastructure.
    """
    
    def __init__(
        self,
        repository: IPTSRenameRepository,
        config: PTSRenameConfig,
        result_storage_path: str = None,
        base_download_url: str = "http://localhost:5000/pts-renamer/api/download"
    ):
        """
        Initialize the download service
        
        Args:
            repository: PTS rename repository for data persistence
            config: PTS renamer configuration
            result_storage_path: Result storage path override
            base_download_url: Base URL for download endpoints
        """
        self.repository = repository
        self.config = config
        
        # Use configuration from file_management system (.env variables)
        # Priority: explicit override > config property > fallback default
        if result_storage_path:
            storage_path = result_storage_path
        elif config and hasattr(config, 'result_storage_path'):
            storage_path = config.result_storage_path  # Uses UploadConfig from file_management
        else:
            storage_path = "d:/temp/results"  # Fallback default
            
        self.result_storage_path = Path(storage_path)
        self.result_storage_path.mkdir(parents=True, exist_ok=True)
        self.base_download_url = base_download_url
        
        # Download URL expiration (default 24 hours)
        self.download_expiration_hours = config.cleanup_retention_hours
        
        logger.info(f"PTS Download Service initialized with result storage: {self.result_storage_path}")
        logger.info(f"Base download URL: {self.base_download_url}")
        logger.info(f"Download expiration: {self.download_expiration_hours} hours")
    
    async def compress_processed_files(
        self,
        job_id: str,
        processed_files: List[Path],
        include_original: bool = False,
        compression_level: int = None
    ) -> Tuple[bool, str]:
        """
        Compress all processed files into downloadable archive
        Uses existing compression infrastructure
        
        Args:
            job_id: Processing job ID
            processed_files: List of processed file paths
            include_original: Whether to include original files
            compression_level: Compression level (1-9)
            
        Returns:
            Tuple[bool, str]: (success, compressed_file_path or error_message)
        """
        logger.info(f"[COMPRESS] Starting compression for job: {job_id}")
        logger.info(f"   Files to compress: {len(processed_files)}")
        logger.info(f"   Include original: {include_original}")
        
        try:
            # Create job-specific result directory
            job_result_dir = self.result_storage_path / job_id
            job_result_dir.mkdir(parents=True, exist_ok=True)
            
            # Prepare files for compression
            compression_source_dir = job_result_dir / "compression_source"
            compression_source_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy processed files to compression source
            copied_files = await self._prepare_files_for_compression(
                processed_files, compression_source_dir, include_original
            )
            
            if not copied_files:
                return False, "No files to compress"
            
            # Generate archive name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archive_name = f"pts_results_{job_id}_{timestamp}"
            
            # Use existing Dramatiq compression task
            if self.config.enable_compression:
                try:
                    # Import here to avoid circular import
                    from backend.tasks.services.dramatiq_tasks import create_download_archive_task
                    
                    # Submit compression task to Dramatiq queue
                    compression_task = create_download_archive_task.send(
                        source_path=str(compression_source_dir),
                        archive_name=archive_name
                    )
                    
                    logger.info(f"[COMPRESS] Submitted Dramatiq compression task: {compression_task.message_id}")
                    
                    # For now, we'll also create a local backup compression
                    # In production, you might wait for the Dramatiq task result
                    local_archive_path = await self._create_local_archive(
                        compression_source_dir, job_result_dir, archive_name, compression_level
                    )
                    
                    # Update job record with compression info
                    await self._update_job_compression_info(job_id, local_archive_path, compression_task.message_id)
                    
                    logger.info(f"[COMPRESS] Compression completed: {local_archive_path}")
                    return True, str(local_archive_path)
                    
                except Exception as e:
                    logger.error(f"[COMPRESS] Dramatiq compression failed: {e}")
                    # Fallback to local compression
                    local_archive_path = await self._create_local_archive(
                        compression_source_dir, job_result_dir, archive_name, compression_level
                    )
                    return True, str(local_archive_path)
            else:
                # Direct local compression
                local_archive_path = await self._create_local_archive(
                    compression_source_dir, job_result_dir, archive_name, compression_level
                )
                return True, str(local_archive_path)
                
        except Exception as e:
            error_msg = f"Compression failed: {str(e)}"
            logger.error(f"[COMPRESS] {error_msg}")
            return False, error_msg
    
    async def _prepare_files_for_compression(
        self,
        processed_files: List[Path],
        target_dir: Path,
        include_original: bool
    ) -> List[Path]:
        """
        Prepare files for compression by copying to target directory
        
        Args:
            processed_files: List of processed file paths
            target_dir: Target directory for compression
            include_original: Whether to include original files
            
        Returns:
            List[Path]: List of copied file paths
        """
        copied_files = []
        
        try:
            for file_path in processed_files:
                if not file_path.exists():
                    logger.warning(f"[PREPARE] File not found: {file_path}")
                    continue
                
                try:
                    if file_path.is_file():
                        # Copy single file
                        target_file = target_dir / file_path.name
                        shutil.copy2(file_path, target_file)
                        copied_files.append(target_file)
                        
                    elif file_path.is_dir():
                        # Copy directory structure
                        target_subdir = target_dir / file_path.name
                        shutil.copytree(file_path, target_subdir, dirs_exist_ok=True)
                        copied_files.append(target_subdir)
                        
                except Exception as e:
                    logger.warning(f"[PREPARE] Failed to copy {file_path}: {e}")
            
            # Add processing report
            report_file = await self._create_processing_report(target_dir, processed_files)
            if report_file:
                copied_files.append(report_file)
            
            logger.info(f"[PREPARE] Prepared {len(copied_files)} items for compression")
            return copied_files
            
        except Exception as e:
            logger.error(f"[PREPARE] Error preparing files: {e}")
            return []
    
    async def _create_local_archive(
        self,
        source_dir: Path,
        result_dir: Path,
        archive_name: str,
        compression_level: int = None
    ) -> Path:
        """
        Create local ZIP archive
        
        Args:
            source_dir: Source directory to compress
            result_dir: Result directory for archive
            archive_name: Archive name (without extension)
            compression_level: Compression level (1-9)
            
        Returns:
            Path: Path to created archive
        """
        if compression_level is None:
            compression_level = self.config.compression_level
        
        archive_path = result_dir / f"{archive_name}.zip"
        
        def _create_zip():
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=compression_level) as zipf:
                for file_path in source_dir.rglob('*'):
                    if file_path.is_file():
                        # Calculate relative path for archive
                        arcname = file_path.relative_to(source_dir)
                        zipf.write(file_path, arcname)
        
        # Run compression in executor to avoid blocking
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, _create_zip)
        
        # Get archive size
        archive_size = archive_path.stat().st_size
        logger.info(f"[ARCHIVE] Created archive: {archive_path.name} ({archive_size / 1024 / 1024:.1f}MB)")
        
        return archive_path
    
    async def _create_processing_report(
        self,
        target_dir: Path,
        processed_files: List[Path]
    ) -> Optional[Path]:
        """
        Create processing report for inclusion in download package
        
        Args:
            target_dir: Target directory for report
            processed_files: List of processed files
            
        Returns:
            Optional[Path]: Path to report file or None
        """
        try:
            report_path = target_dir / "processing_report.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("PTS File Renamer - Processing Report\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total files processed: {len(processed_files)}\n\n")
                
                f.write("Processed Files:\n")
                f.write("-" * 20 + "\n")
                
                for i, file_path in enumerate(processed_files, 1):
                    f.write(f"{i:3d}. {file_path.name}\n")
                    if file_path.exists():
                        size = file_path.stat().st_size
                        f.write(f"     Size: {size:,} bytes\n")
                        if file_path.is_dir():
                            file_count = sum(1 for _ in file_path.rglob('*') if _.is_file())
                            f.write(f"     Contains: {file_count} files\n")
                    else:
                        f.write(f"     Status: File not found\n")
                    f.write("\n")
                
                f.write("\nProcessing Summary:\n")
                f.write("-" * 20 + "\n")
                
                # Count different file types
                pts_files = sum(1 for f in processed_files if f.suffix.lower() == '.pts')
                qc_files = sum(1 for f in processed_files if '_QC' in f.name)
                directories = sum(1 for f in processed_files if f.is_dir())
                
                f.write(f"PTS files: {pts_files}\n")
                f.write(f"QC files: {qc_files}\n")
                f.write(f"Directories: {directories}\n")
                f.write(f"Other files: {len(processed_files) - pts_files - qc_files - directories}\n")
                
                f.write("\n" + "=" * 50 + "\n")
                f.write("End of Report\n")
            
            logger.debug(f"[REPORT] Created processing report: {report_path}")
            return report_path
            
        except Exception as e:
            logger.warning(f"[REPORT] Failed to create processing report: {e}")
            return None
    
    async def create_download_package(
        self,
        job_id: str,
        results: Dict[str, Any]
    ) -> str:
        """
        Create complete download package with all results
        
        Args:
            job_id: Processing job ID
            results: Processing results dictionary
            
        Returns:
            str: Path to compressed download package
        """
        logger.info(f"[PACKAGE] Creating download package for job: {job_id}")
        
        try:
            # Extract file paths from results
            processed_files = []
            
            # Add renamed files
            if 'renamed_files' in results:
                for file_info in results['renamed_files']:
                    if 'new_path' in file_info:
                        processed_files.append(Path(file_info['new_path']))
            
            # Add QC files
            if 'qc_files' in results:
                for qc_file in results['qc_files']:
                    if 'qc_path' in qc_file:
                        processed_files.append(Path(qc_file['qc_path']))
            
            # Add created directories
            if 'created_directories' in results:
                for dir_info in results['created_directories']:
                    if 'directory_path' in dir_info:
                        processed_files.append(Path(dir_info['directory_path']))
            
            # Add any other result files
            if 'output_files' in results:
                for output_file in results['output_files']:
                    processed_files.append(Path(output_file))
            
            logger.info(f"[PACKAGE] Found {len(processed_files)} files to package")
            
            # Compress files
            success, result_path = await self.compress_processed_files(
                job_id, processed_files, include_original=False
            )
            
            if success:
                logger.info(f"[PACKAGE] Download package created: {result_path}")
                return result_path
            else:
                raise Exception(f"Compression failed: {result_path}")
                
        except Exception as e:
            error_msg = f"Failed to create download package: {str(e)}"
            logger.error(f"[PACKAGE] {error_msg}")
            raise Exception(error_msg)
    
    def generate_download_url(
        self,
        job_id: str,
        compressed_file_path: str,
        expires_in_hours: int = None
    ) -> Tuple[str, datetime]:
        """
        Generate secure download URL for compressed results
        
        Args:
            job_id: Processing job ID
            compressed_file_path: Path to compressed file
            expires_in_hours: URL expiration time in hours
            
        Returns:
            Tuple[str, datetime]: (download_url, expiration_time)
        """
        if expires_in_hours is None:
            expires_in_hours = self.download_expiration_hours
        
        # Generate secure download token
        download_token = f"{job_id}_{uuid.uuid4().hex[:16]}"
        expiration_time = datetime.now() + timedelta(hours=expires_in_hours)
        
        # Create download URL
        download_url = f"{self.base_download_url}/{download_token}"
        
        # Store download mapping (in production, this would be in database/cache)
        self._store_download_mapping(download_token, compressed_file_path, expiration_time, job_id)
        
        logger.info(f"[URL] Generated download URL for job {job_id}")
        logger.info(f"   Token: {download_token}")
        logger.info(f"   Expires: {expiration_time}")
        
        return download_url, expiration_time
    
    def _store_download_mapping(
        self,
        download_token: str,
        file_path: str,
        expiration_time: datetime,
        job_id: str
    ):
        """
        Store download token mapping
        
        Args:
            download_token: Download token
            file_path: Path to file
            expiration_time: Token expiration time
            job_id: Processing job ID
        """
        try:
            # Create download mappings directory
            mappings_dir = self.result_storage_path / "download_mappings"
            mappings_dir.mkdir(exist_ok=True)
            
            # Store mapping as JSON file
            mapping_file = mappings_dir / f"{download_token}.json"
            mapping_data = {
                'download_token': download_token,
                'file_path': file_path,
                'job_id': job_id,
                'created_at': datetime.now().isoformat(),
                'expires_at': expiration_time.isoformat(),
                'downloaded': False,
                'download_count': 0
            }
            
            with open(mapping_file, 'w') as f:
                json.dump(mapping_data, f, indent=2)
            
            logger.debug(f"[MAPPING] Stored download mapping: {download_token}")
            
        except Exception as e:
            logger.error(f"[MAPPING] Failed to store download mapping: {e}")
    
    def get_download_info(self, download_token: str) -> Optional[Dict[str, Any]]:
        """
        Get download information by token
        
        Args:
            download_token: Download token
            
        Returns:
            Optional[Dict]: Download information or None if not found/expired
        """
        try:
            mappings_dir = self.result_storage_path / "download_mappings"
            mapping_file = mappings_dir / f"{download_token}.json"
            
            if not mapping_file.exists():
                return None
            
            with open(mapping_file, 'r') as f:
                mapping_data = json.load(f)
            
            # Check expiration
            expires_at = datetime.fromisoformat(mapping_data['expires_at'])
            if datetime.now() > expires_at:
                logger.info(f"[DOWNLOAD] Token expired: {download_token}")
                return None
            
            # Check if file still exists
            file_path = Path(mapping_data['file_path'])
            if not file_path.exists():
                logger.warning(f"[DOWNLOAD] File not found: {file_path}")
                return None
            
            return mapping_data
            
        except Exception as e:
            logger.error(f"[DOWNLOAD] Error getting download info: {e}")
            return None
    
    def mark_download_accessed(self, download_token: str) -> bool:
        """
        Mark download as accessed
        
        Args:
            download_token: Download token
            
        Returns:
            bool: True if successfully marked
        """
        try:
            mappings_dir = self.result_storage_path / "download_mappings"
            mapping_file = mappings_dir / f"{download_token}.json"
            
            if not mapping_file.exists():
                return False
            
            with open(mapping_file, 'r') as f:
                mapping_data = json.load(f)
            
            # Update download info
            mapping_data['downloaded'] = True
            mapping_data['download_count'] = mapping_data.get('download_count', 0) + 1
            mapping_data['last_downloaded_at'] = datetime.now().isoformat()
            
            with open(mapping_file, 'w') as f:
                json.dump(mapping_data, f, indent=2)
            
            logger.info(f"[DOWNLOAD] Marked as accessed: {download_token}")
            return True
            
        except Exception as e:
            logger.error(f"[DOWNLOAD] Error marking download accessed: {e}")
            return False
    
    async def _update_job_compression_info(
        self,
        job_id: str,
        archive_path: str,
        dramatiq_task_id: str = None
    ):
        """
        Update job record with compression information
        
        Args:
            job_id: Processing job ID
            archive_path: Path to compressed archive
            dramatiq_task_id: Dramatiq task ID for compression
        """
        try:
            archive_path_obj = Path(archive_path)
            archive_size = archive_path_obj.stat().st_size if archive_path_obj.exists() else 0
            
            compression_info = {
                'compressed_file_path': str(archive_path),
                'compressed_file_name': archive_path_obj.name,
                'compressed_file_size': archive_size,
                'compression_completed_at': datetime.now().isoformat(),
                'dramatiq_task_id': dramatiq_task_id
            }
            
            await self.repository.update_job_compression_info(job_id, compression_info)
            logger.debug(f"[UPDATE] Updated job compression info: {job_id}")
            
        except Exception as e:
            logger.error(f"[UPDATE] Failed to update job compression info: {e}")
    
    async def cleanup_processed_files(
        self,
        job_id: str,
        retention_hours: int = None
    ) -> bool:
        """
        Clean up processed files after retention period
        
        Args:
            job_id: Processing job ID
            retention_hours: Retention period in hours
            
        Returns:
            bool: True if cleanup successful
        """
        if retention_hours is None:
            retention_hours = self.config.cleanup_retention_hours
        
        try:
            job_result_dir = self.result_storage_path / job_id
            
            if not job_result_dir.exists():
                return True
            
            # Check if retention period has passed
            dir_stat = job_result_dir.stat()
            created_time = datetime.fromtimestamp(dir_stat.st_ctime)
            cutoff_time = datetime.now() - timedelta(hours=retention_hours)
            
            if created_time > cutoff_time:
                logger.debug(f"[CLEANUP] Job {job_id} not yet eligible for cleanup")
                return True
            
            # Remove job result directory
            shutil.rmtree(job_result_dir)
            logger.info(f"[CLEANUP] Cleaned up job results: {job_id}")
            
            # Clean up download mappings
            await self._cleanup_job_download_mappings(job_id)
            
            return True
            
        except Exception as e:
            logger.error(f"[CLEANUP] Failed to cleanup job {job_id}: {e}")
            return False
    
    async def _cleanup_job_download_mappings(self, job_id: str):
        """
        Clean up download mappings for a job
        
        Args:
            job_id: Processing job ID
        """
        try:
            mappings_dir = self.result_storage_path / "download_mappings"
            
            if not mappings_dir.exists():
                return
            
            # Find and remove mappings for this job
            for mapping_file in mappings_dir.glob("*.json"):
                try:
                    with open(mapping_file, 'r') as f:
                        mapping_data = json.load(f)
                    
                    if mapping_data.get('job_id') == job_id:
                        mapping_file.unlink()
                        logger.debug(f"[CLEANUP] Removed download mapping: {mapping_file.name}")
                        
                except Exception as e:
                    logger.warning(f"[CLEANUP] Error processing mapping {mapping_file}: {e}")
            
        except Exception as e:
            logger.error(f"[CLEANUP] Error cleaning download mappings: {e}")
    
    async def cleanup_expired_downloads(self) -> int:
        """
        Clean up expired download mappings and files
        
        Returns:
            int: Number of expired downloads cleaned up
        """
        cleaned_count = 0
        
        try:
            mappings_dir = self.result_storage_path / "download_mappings"
            
            if not mappings_dir.exists():
                return 0
            
            current_time = datetime.now()
            
            for mapping_file in mappings_dir.glob("*.json"):
                try:
                    with open(mapping_file, 'r') as f:
                        mapping_data = json.load(f)
                    
                    expires_at = datetime.fromisoformat(mapping_data['expires_at'])
                    
                    if current_time > expires_at:
                        # Remove expired mapping
                        mapping_file.unlink()
                        cleaned_count += 1
                        logger.debug(f"[CLEANUP] Removed expired download: {mapping_file.name}")
                        
                except Exception as e:
                    logger.warning(f"[CLEANUP] Error processing expired mapping {mapping_file}: {e}")
            
            if cleaned_count > 0:
                logger.info(f"[CLEANUP] Cleaned up {cleaned_count} expired downloads")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"[CLEANUP] Error cleaning expired downloads: {e}")
            return 0
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """
        Get download service statistics
        
        Returns:
            Dict[str, Any]: Service statistics
        """
        try:
            # Count result directories
            result_dirs = [d for d in self.result_storage_path.iterdir() if d.is_dir() and d.name != "download_mappings"]
            
            # Count download mappings
            mappings_dir = self.result_storage_path / "download_mappings"
            active_downloads = 0
            expired_downloads = 0
            
            if mappings_dir.exists():
                current_time = datetime.now()
                
                for mapping_file in mappings_dir.glob("*.json"):
                    try:
                        with open(mapping_file, 'r') as f:
                            mapping_data = json.load(f)
                        
                        expires_at = datetime.fromisoformat(mapping_data['expires_at'])
                        
                        if current_time <= expires_at:
                            active_downloads += 1
                        else:
                            expired_downloads += 1
                            
                    except Exception:
                        expired_downloads += 1
            
            # Calculate total storage used
            total_storage_mb = 0
            for result_dir in result_dirs:
                try:
                    for file_path in result_dir.rglob('*'):
                        if file_path.is_file():
                            total_storage_mb += file_path.stat().st_size
                except Exception:
                    pass
            
            total_storage_mb = total_storage_mb / (1024 * 1024)
            
            return {
                'result_directories': len(result_dirs),
                'active_downloads': active_downloads,
                'expired_downloads': expired_downloads,
                'total_storage_mb': round(total_storage_mb, 2),
                'storage_path': str(self.result_storage_path),
                'retention_hours': self.config.cleanup_retention_hours,
                'compression_enabled': self.config.enable_compression
            }
            
        except Exception as e:
            logger.error(f"[STATS] Error getting service statistics: {e}")
            return {
                'error': str(e),
                'storage_path': str(self.result_storage_path)
            }