"""
Pytest configuration and shared fixtures for database field display testing

Provides:
- Shared fixtures for all test types (unit, integration, e2e)
- Test environment setup and teardown
- Database field display specific configuration
- Playwright configuration for E2E tests

Author: Test Automation Specialist
Date: 2025-08-19
"""

import pytest
import os
import sys
from typing import Generator, Dict, Any
from unittest.mock import Mock, patch
import tempfile
import shutil

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Import test fixtures
from tests.fixtures.database_field_display_fixtures import (
    DatabaseFieldDisplayFixtures,
    EmailDownloadStatusFactory,
    ColumnDefinitionFactory,
    MockAPIResponseFactory,
    create_playwright_test_data
)


# ============================================================================
# Test Environment Configuration
# ============================================================================

def pytest_configure(config):
    """Configure pytest with custom markers and settings"""
    config.addinivalue_line(
        "markers", 
        "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers",
        "e2e: marks tests as end-to-end requiring full system"
    )
    config.addinivalue_line(
        "markers", 
        "integration: marks tests as integration requiring API"
    )
    config.addinivalue_line(
        "markers",
        "playwright: marks tests requiring Playwright browser automation"
    )
    config.addinivalue_line(
        "markers",
        "story1_1: tests for Story 1.1 - Chinese field titles"
    )
    config.addinivalue_line(
        "markers",
        "story1_2: tests for Story 1.2 - Boolean visual tags"
    )
    config.addinivalue_line(
        "markers",
        "story1_3: tests for Story 1.3 - Field category organization"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically"""
    for item in items:
        # Mark slow tests
        if "performance" in item.nodeid or "load" in item.nodeid:
            item.add_marker(pytest.mark.slow)
        
        # Mark e2e tests
        if "e2e" in item.nodeid or "playwright" in item.nodeid:
            item.add_marker(pytest.mark.e2e)
            item.add_marker(pytest.mark.playwright)
        
        # Mark integration tests
        if "integration" in item.nodeid or "api" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Mark story-specific tests
        if "chinese_field" in item.nodeid or "field_title" in item.nodeid:
            item.add_marker(pytest.mark.story1_1)
        
        if "boolean" in item.nodeid or "visual_tag" in item.nodeid:
            item.add_marker(pytest.mark.story1_2)
        
        if "category" in item.nodeid or "organization" in item.nodeid:
            item.add_marker(pytest.mark.story1_3)


# ============================================================================
# Shared Test Fixtures
# ============================================================================

@pytest.fixture(scope="session")
def test_data_directory() -> str:
    """Create temporary directory for test data"""
    temp_dir = tempfile.mkdtemp(prefix="database_field_display_tests_")
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture(scope="session") 
def comprehensive_test_data() -> Dict[str, Any]:
    """Get comprehensive test data for all stories"""
    return DatabaseFieldDisplayFixtures.get_comprehensive_test_data()


@pytest.fixture
def test_records():
    """Get test records for Story 1.2 boolean visual tags testing"""
    return {
        'failure_case': EmailDownloadStatusFactory.build_failure_case(),
        'success_case': EmailDownloadStatusFactory.build_success_case(),
        'unknown_case': EmailDownloadStatusFactory.build_unknown_case(),
        'processing_failure': EmailDownloadStatusFactory.build_processing_failure_case(),
        'mixed_errors': EmailDownloadStatusFactory.build_mixed_error_case()
    }


@pytest.fixture
def chinese_field_mappings():
    """Get Chinese field title mappings for Story 1.1 testing"""
    return DatabaseFieldDisplayFixtures.get_chinese_title_mappings()


@pytest.fixture
def field_categories():
    """Get field categories for Story 1.3 testing"""
    return DatabaseFieldDisplayFixtures.get_field_categories()


@pytest.fixture
def email_download_status_columns():
    """Get email_download_status column definitions"""
    return ColumnDefinitionFactory.get_email_download_status_columns()


@pytest.fixture
def mock_api_responses():
    """Get mock API responses for integration testing"""
    return DatabaseFieldDisplayFixtures.get_mock_api_responses()


@pytest.fixture
def boolean_test_cases():
    """Get specific boolean test cases for Story 1.2"""
    return DatabaseFieldDisplayFixtures.get_boolean_test_cases()


# ============================================================================
# Database and API Mocking
# ============================================================================

@pytest.fixture
def mock_database_api():
    """Mock the database API for integration tests"""
    with patch('requests.get') as mock_get, \
         patch('requests.post') as mock_post:
        
        def mock_api_response(url, **kwargs):
            """Mock API responses based on URL"""
            if '/database/info' in url:
                return Mock(
                    json=lambda: MockAPIResponseFactory.build_database_info_response(),
                    status_code=200,
                    ok=True
                )
            elif '/database/table/email_download_status' in url:
                return Mock(
                    json=lambda: MockAPIResponseFactory.build_table_data_response(
                        'email_download_status',
                        EmailDownloadStatusFactory.build_batch(5)
                    ),
                    status_code=200,
                    ok=True
                )
            elif '/database/email_download_status/' in url:
                return Mock(
                    json=lambda: MockAPIResponseFactory.build_record_detail_response(1),
                    status_code=200,
                    ok=True
                )
            else:
                return Mock(
                    json=lambda: {"success": False, "error": "Not found"},
                    status_code=404,
                    ok=False
                )
        
        mock_get.side_effect = mock_api_response
        mock_post.side_effect = mock_api_response
        
        yield {
            'get': mock_get,
            'post': mock_post
        }


@pytest.fixture
def mock_frontend_javascript():
    """Mock JavaScript functions for unit testing"""
    class MockJavaScript:
        def __init__(self):
            self.chinese_mappings = DatabaseFieldDisplayFixtures.get_chinese_title_mappings()
            self.boolean_columns = ['is_remote_download_success', 'is_processing_success']
            self.categories = DatabaseFieldDisplayFixtures.get_field_categories()
        
        def get_column_display_name(self, column_name: str) -> str:
            """Mock JavaScript getColumnDisplayName function"""
            return self.chinese_mappings.get(column_name, column_name)
        
        def is_boolean_success_column(self, column_name: str) -> bool:
            """Mock JavaScript isBooleanSuccessColumn function"""
            return column_name in self.boolean_columns
        
        def to_boolean_value(self, value) -> bool:
            """Mock JavaScript toBooleanValue function"""
            if value is None or value == '' or str(value).lower() in ['null', 'undefined']:
                return None
            if isinstance(value, bool):
                return value
            
            str_value = str(value).lower()
            if str_value in ['true', '1']:
                return True
            if str_value in ['false', '0']:
                return False
            
            return None
        
        def format_boolean_success(self, value) -> str:
            """Mock JavaScript formatBooleanSuccess function"""
            bool_value = self.to_boolean_value(value)
            
            if bool_value is True:
                return '<span class="success-status-tag">成功</span>'
            elif bool_value is False:
                return '<span class="failed-status-tag">失敗</span>'  # Key test case
            else:
                return '<span class="unknown-status-tag">未知</span>'
        
        def group_columns_by_category(self, columns: list) -> dict:
            """Mock JavaScript groupColumnsByCategory function"""
            result = {}
            uncategorized = []
            
            for category, category_columns in self.categories.items():
                result[category] = []
            
            for col in columns:
                categorized = False
                for category, category_columns in self.categories.items():
                    if col in category_columns:
                        result[category].append(col)
                        categorized = True
                        break
                if not categorized:
                    uncategorized.append(col)
            
            if uncategorized:
                result['其他'] = uncategorized
            
            # Remove empty categories
            return {k: v for k, v in result.items() if v}
    
    return MockJavaScript()


# ============================================================================
# Playwright Configuration (for E2E tests)
# ============================================================================

@pytest.fixture(scope="session")
def browser_context_args():
    """Configure browser context for Playwright tests"""
    return {
        "viewport": {"width": 1280, "height": 720},
        "ignore_https_errors": True,
        "locale": "zh-TW",  # Chinese locale for testing Chinese titles
        "timezone_id": "Asia/Taipei"
    }


@pytest.fixture(scope="session") 
def playwright_test_data():
    """Get test data specifically for Playwright E2E tests"""
    return create_playwright_test_data()


@pytest.fixture
def playwright_setup_teardown(page):
    """Setup and teardown for Playwright tests"""
    # Setup: Navigate to base URL and wait for load
    base_url = "http://localhost:5000"
    page.goto(f"{base_url}/monitoring/database-manager")
    page.wait_for_load_state("networkidle")
    
    # Ensure JavaScript is loaded
    page.wait_for_function("typeof window.databaseManager !== 'undefined'")
    
    yield page
    
    # Teardown: Clear any test data or state
    # In a real scenario, this might clear test database records
    pass


# ============================================================================
# Test Data Validation
# ============================================================================

@pytest.fixture(scope="session", autouse=True)
def validate_test_environment():
    """Validate test environment setup"""
    # Check that key test files exist
    test_files = [
        'tests/unit/frontend/test_database_field_display.py',
        'tests/integration/test_database_api_field_display.py',
        'tests/e2e/test_database_field_display_e2e.py',
        'tests/fixtures/database_field_display_fixtures.py'
    ]
    
    for file_path in test_files:
        full_path = os.path.join(project_root, file_path)
        if not os.path.exists(full_path):
            pytest.skip(f"Required test file not found: {file_path}")
    
    # Check that test data is valid
    test_data = DatabaseFieldDisplayFixtures.get_comprehensive_test_data()
    
    # Validate Story 1.1 data
    assert 'column_mappings' in test_data
    assert 'is_remote_download_success' in test_data['column_mappings']
    assert test_data['column_mappings']['is_remote_download_success'] == '下載成功'
    
    # Validate Story 1.2 data
    assert 'boolean_test_cases' in test_data
    failure_cases = [case for case in test_data['boolean_test_cases'] if 'failure' in case['name']]
    assert len(failure_cases) > 0, "Must have failure test cases for Story 1.2"
    
    # Validate Story 1.3 data
    assert 'field_categories' in test_data
    assert '處理狀態' in test_data['field_categories']
    assert 'is_remote_download_success' in test_data['field_categories']['處理狀態']
    
    return True


# ============================================================================
# Performance Testing Fixtures
# ============================================================================

@pytest.fixture
def performance_test_data():
    """Get large dataset for performance testing"""
    return DatabaseFieldDisplayFixtures.get_performance_test_data(100)


@pytest.fixture
def timing_context():
    """Context manager for timing test operations"""
    import time
    from contextlib import contextmanager
    
    @contextmanager
    def timer():
        start_time = time.time()
        result = {}
        yield result
        end_time = time.time()
        result['duration'] = end_time - start_time
    
    return timer


# ============================================================================
# Error Injection for Robustness Testing
# ============================================================================

@pytest.fixture
def error_injection():
    """Fixture for injecting various error conditions"""
    class ErrorInjector:
        def inject_api_timeout(self):
            """Simulate API timeout"""
            import time
            time.sleep(5)  # Simulate timeout
        
        def inject_malformed_response(self):
            """Return malformed API response"""
            return {"invalid": "response", "missing_success_field": True}
        
        def inject_null_boolean_values(self):
            """Return record with null boolean values"""
            return EmailDownloadStatusFactory.build_unknown_case()
        
        def inject_large_dataset(self):
            """Return very large dataset"""
            return EmailDownloadStatusFactory.build_batch(1000)
    
    return ErrorInjector()


# ============================================================================
# Cleanup and Reporting
# ============================================================================

@pytest.fixture(scope="session", autouse=True)
def cleanup_test_artifacts():
    """Clean up test artifacts after session"""
    yield
    
    # Clean up any temporary files or test data
    temp_dirs = [
        'tests/screenshots',
        'tests/temp',
        'tests/reports'
    ]
    
    for temp_dir in temp_dirs:
        full_path = os.path.join(project_root, temp_dir)
        if os.path.exists(full_path):
            try:
                shutil.rmtree(full_path)
            except:
                pass  # Ignore cleanup errors


def pytest_sessionfinish(session, exitstatus):
    """Generate test report after session completes"""
    if exitstatus == 0:
        print("\nDatabase Field Display Test Results:")
        print("   Story 1.1: Chinese field titles - PASSED")
        print("   Story 1.2: Boolean visual tags - PASSED") 
        print("   Story 1.3: Field category organization - PASSED")
        print("   All tests passed successfully!")
    else:
        print(f"\nSome tests failed (exit code: {exitstatus})")
        print("   Check test output for details")


# ============================================================================
# Custom Assertions
# ============================================================================

def assert_chinese_title(actual_title: str, expected_english_field: str):
    """Custom assertion for Chinese title mapping"""
    mappings = DatabaseFieldDisplayFixtures.get_chinese_title_mappings()
    expected_chinese = mappings.get(expected_english_field, expected_english_field)
    
    assert actual_title == expected_chinese, \
        f"Expected Chinese title '{expected_chinese}' for field '{expected_english_field}', got '{actual_title}'"


def assert_boolean_tag(tag_html: str, expected_value: bool, expected_text: str = None):
    """Custom assertion for boolean visual tags"""
    if expected_value is True:
        assert 'success-status-tag' in tag_html
        if expected_text:
            assert expected_text in tag_html
        else:
            assert '成功' in tag_html
    elif expected_value is False:
        assert 'failed-status-tag' in tag_html
        if expected_text:
            assert expected_text in tag_html
        else:
            assert '失敗' in tag_html  # Key assertion for Story 1.2
    else:
        assert 'unknown-status-tag' in tag_html
        if expected_text:
            assert expected_text in tag_html
        else:
            assert '未知' in tag_html


def assert_field_in_category(field_name: str, expected_category: str):
    """Custom assertion for field categorization"""
    categories = DatabaseFieldDisplayFixtures.get_field_categories()
    
    found_category = None
    for category, fields in categories.items():
        if field_name in fields:
            found_category = category
            break
    
    assert found_category == expected_category, \
        f"Expected field '{field_name}' in category '{expected_category}', found in '{found_category}'"


# Make custom assertions available globally
pytest.assert_chinese_title = assert_chinese_title
pytest.assert_boolean_tag = assert_boolean_tag  
pytest.assert_field_in_category = assert_field_in_category