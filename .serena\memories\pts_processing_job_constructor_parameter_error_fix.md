# PTS Processing Job 構造函數參數錯誤修復

## 問題分析
從用戶提供的錯誤日誌可以看出：
```
Failed to process PTS files: PTSProcessingJob.__init__() got an unexpected keyword argument 'rename_config'
```

**根本原因**：
- `pts_rename_service.py:114` 行直接調用 `PTSProcessingJob()` 構造函數時傳遞了錯誤的參數名
- 傳遞了 `rename_config` 但 PTSProcessingJob 類中的字段是 `rename_pattern`

## 代碼錯誤位置
文件：`backend/pts_renamer/services/pts_rename_service.py`
行數：114-120

**當前錯誤代碼**：
```python
job = PTSProcessingJob(
    job_id=self._generate_job_id(),
    upload_id=job_request.upload_id,
    pts_files=pts_files,
    operations=[PTSOperationType(op.value) for op in job_request.operations],
    rename_config=job_request.rename_config,  # ❌ 錯誤：應該是 rename_pattern
    qc_enabled=job_request.qc_enabled,
    create_directories=job_request.create_directories
)
```

**正確代碼應該是**：
```python
job = PTSProcessingJob(
    job_id=self._generate_job_id(),
    upload_id=job_request.upload_id,
    pts_files=pts_files,
    operations=[PTSOperationType(op.value) for op in job_request.operations],
    rename_pattern=job_request.rename_config,  # ✅ 修正：使用 rename_pattern
    qc_enabled=job_request.qc_enabled,
    create_directories=job_request.create_directories
)
```

## 類字段對比
**PTSRenameJobRequest** (請求模型):
- `rename_config: Optional[Dict[str, str]]`

**PTSProcessingJob** (實體模型):
- `rename_pattern: Optional[RenamePattern]`

## 數據類型轉換需求
除了參數名稱錯誤外，還需要檢查數據類型是否匹配：
- `job_request.rename_config` 是 `Dict[str, str]`
- 但 `PTSProcessingJob.rename_pattern` 期望的是 `RenamePattern` 類型

## 解決方案
1. 修正參數名稱從 `rename_config` 到 `rename_pattern`
2. 檢查是否需要數據類型轉換
3. 添加路徑調試日誌以顯示重命名前後的路徑
4. 測試修復結果

## 相關檔案
- `backend/pts_renamer/services/pts_rename_service.py:114`
- `backend/pts_renamer/models/pts_rename_entities.py:404` (PTSProcessingJob.rename_pattern)
- `backend/pts_renamer/models/pts_rename_models.py:37` (PTSRenameJobRequest.rename_config)