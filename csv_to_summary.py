#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV to Summary 命令列工具
使用方式:
  python3 csv_to_summary.py <folder_path>           # 只產生 Summary
  python3 csv_to_summary.py <folder_path> --excel   # 產生 xlsx + Summary
  python3 csv_to_summary.py <archive_path>          # 支援壓縮檔輸入

範例:
  python3 csv_to_summary.py doc/20250523
  python3 csv_to_summary.py doc/20250523 --excel --verbose
  python3 csv_to_summary.py data.zip --excel        # 處理壓縮檔
"""

import os
import sys
import argparse
import time
from pathlib import Path
from datetime import datetime

# 修復 Windows cp950 編碼問題
def setup_unicode_environment():
    """設置 Unicode 環境，避免編碼錯誤"""
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'

    # 重新配置標準輸出編碼
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    if hasattr(sys.stderr, 'reconfigure'):
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')

# 在導入其他模組前先設置編碼環境
setup_unicode_environment()

# 重用現有的核心處理器
try:
    from batch_csv_to_excel_processor import BatchCsvToExcelProcessor, BatchProcessResult
except ImportError as e:
    print(f"[ERROR] 錯誤: 無法匯入核心處理器: {e}")
    print("請確認 batch_csv_to_excel_processor.py 檔案存在且可匯入")
    sys.exit(1)

# 新增 imports - 解壓縮功能
try:
    from backend.shared.infrastructure.adapters.excel.cta.cta_integrated_processor import extract_compressed_files
except ImportError as e:
    print(f"[WARNING] 警告: 無法匯入解壓縮模組: {e}")
    print("解壓縮功能將無法使用")
    extract_compressed_files = None

# 新增 imports - SPD 轉換功能
try:
    from backend.shared.infrastructure.adapters.excel.eqc.eqc_bin1_final_processor import FileConverter
except ImportError as e:
    print(f"[WARNING] 警告: 無法匯入 SPD 轉換模組: {e}")
    print("SPD 轉換功能將無法使用")
    FileConverter = None


def parse_arguments():
    """解析命令列參數"""
    parser = argparse.ArgumentParser(
        description='CSV to Summary 批量處理工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
範例:
  %(prog)s doc/20250523                    # 只產生 Summary (快速模式)
  %(prog)s doc/20250523 --excel            # 產生 xlsx + Summary (完整模式)
  %(prog)s doc/20250523 --excel --verbose  # 完整模式 + 詳細輸出
        """
    )
    
    parser.add_argument('folder_path',
                       help='CSV 檔案資料夾路徑或壓縮檔路徑')
    
    parser.add_argument('--excel', '--with-excel', 
                       action='store_true',
                       help='同時產生 Excel 檔案 (預設只產生 Summary)')
    
    parser.add_argument('--verbose', '-v', 
                       action='store_true',
                       help='詳細輸出模式')
    
    parser.add_argument('--version', 
                       action='version', 
                       version='CSV to Summary v1.0.0')
    
    return parser.parse_args()


def is_archive_file(path: str) -> bool:
    """判斷是否為支援的壓縮檔格式"""
    supported = ['.zip', '.7z', '.rar', '.tar', '.gz', '.bz2', '.tgz']
    path_obj = Path(path)

    # 檢查複合副檔名（如 .tar.gz, .tar.bz2）
    if path_obj.name.endswith('.tar.gz') or path_obj.name.endswith('.tar.bz2'):
        return True

    # 檢查單一副檔名
    return path_obj.suffix.lower() in supported


def validate_folder_path(folder_path):
    """驗證資料夾路徑"""
    path = Path(folder_path)

    if not path.exists():
        print(f"[ERROR] 錯誤: 資料夾不存在 '{folder_path}'")
        return False

    if not path.is_dir():
        print(f"[ERROR] 錯誤: '{folder_path}' 不是一個資料夾")
        return False

    # 檢查是否有 CSV 檔案
    csv_files = list(path.glob("*.csv"))
    if not csv_files:
        print(f"[WARNING]  警告: 資料夾 '{folder_path}' 中沒有找到 CSV 檔案")

    return True


def display_start_info(folder_path, mode, verbose=False):
    """顯示開始處理的資訊"""
    print("=" * 60)
    print("[ROCKET] CSV to Summary 批量處理工具")
    print("=" * 60)
    print(f"[FILE_FOLDER] 處理資料夾: {folder_path}")
    print(f"[TARGET] 處理模式: {'完整模式 (Excel + Summary)' if mode == 'full' else '快速模式 (僅 Summary)'}")
    print(f"[ALARM_CLOCK] 開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if verbose:
        print(f"[SEARCH] 詳細模式: 啟用")
    
    print("-" * 60)


def display_results(result: BatchProcessResult, processing_time: float):
    """顯示處理結果"""
    print("\n" + "=" * 60)
    
    if result.success:
        print("[OK] 處理完成！")
    else:
        print("[ERROR] 處理失敗！")
        if result.error_message:
            print(f"錯誤訊息: {result.error_message}")
    
    print("-" * 60)
    print("[CHART] 處理統計:")
    print(f"   總檔案數: {result.total_files}")
    print(f"   成功處理: {result.processed_files}")
    print(f"   跳過檔案: {result.skipped_files}")
    print(f"   失敗檔案: {result.failed_files}")
    print(f"   處理時間: {processing_time:.2f} 秒")
    
    # FT Summary 資訊
    if result.summary_files > 0:
        print(f"\n[BOARD] FT Summary:")
        print(f"   生成檔案數: {result.summary_files}")
        if result.ft_summary_file:
            print(f"   整合檔案: {result.ft_summary_file}")
    
    # EQC Summary 資訊
    if result.eqc_summary_files > 0:
        print(f"\n[SEARCH] EQC Summary:")
        print(f"   EQC 檔案數: {result.eqc_summary_files}")
        if result.eqc_summary_file:
            print(f"   EQC 摘要: {result.eqc_summary_file}")
        if result.eqc_all_pass_file:
            print(f"   全通過檔案: {result.eqc_all_pass_file}")
    
    # 檔案清單
    if result.ft_file_list:
        print(f"\n[PAGE_FACING_UP] 處理的 FT 檔案 ({len(result.ft_file_list)} 個):")
        for ft_file in result.ft_file_list[:5]:  # 最多顯示 5 個
            print(f"   • {ft_file}")
        if len(result.ft_file_list) > 5:
            print(f"   ... 還有 {len(result.ft_file_list) - 5} 個檔案")
    
    if result.eqc_file_list:
        print(f"\n[SEARCH] 處理的 EQC 檔案 ({len(result.eqc_file_list)} 個):")
        for eqc_file in result.eqc_file_list[:5]:  # 最多顯示 5 個
            print(f"   • {eqc_file}")
        if len(result.eqc_file_list) > 5:
            print(f"   ... 還有 {len(result.eqc_file_list) - 5} 個檔案")
    
    print("=" * 60)


def main():
    """主要函式"""
    try:
        # 解析參數
        args = parse_arguments()

        # === 新增：處理輸入路徑 ===
        input_path = args.folder_path  # 保持原有參數名稱

        # 判斷是壓縮檔還是資料夾
        if is_archive_file(input_path):
            # 解壓縮
            print("[PACKAGE] 偵測到壓縮檔，開始解壓縮...")

            # 解壓到壓縮檔所在目錄，使用壓縮檔名稱作為資料夾名
            archive_dir = os.path.dirname(input_path)
            archive_name = Path(input_path).stem
            folder_path = os.path.join(archive_dir, archive_name)

            # 建立目標資料夾
            os.makedirs(folder_path, exist_ok=True)

            # 直接解壓縮到目標資料夾
            try:
                # 匯入解壓縮相關模組
                from backend.shared.infrastructure.adapters.excel.cta.cta_integrated_processor import (
                    extract_zip, extract_7z, extract_rar, extract_tar
                )

                # 根據副檔名選擇解壓縮方法
                file_ext = Path(input_path).suffix.lower()
                if file_ext == '.zip':
                    extracted_files = extract_zip(input_path, folder_path)
                elif file_ext == '.7z':
                    extracted_files = extract_7z(input_path, folder_path)
                elif file_ext == '.rar':
                    extracted_files = extract_rar(input_path, folder_path)
                elif file_ext in ['.tar', '.gz', '.tgz', '.bz2']:
                    extracted_files = extract_tar(input_path, folder_path)
                else:
                    print(f"[ERROR] 不支援的壓縮格式: {file_ext}")
                    return 1

                if extracted_files:
                    print(f"[OK] 解壓縮完成: {len(extracted_files)} 個檔案")
                    # 刪除原始壓縮檔
                    os.remove(input_path)
                    print("[TRASH] 已刪除原始壓縮檔")
                else:
                    print("[ERROR] 解壓縮失敗")
                    return 1

            except Exception as e:
                print(f"[ERROR] 解壓縮失敗: {e}")
                return 1
        else:
            # 驗證資料夾路徑
            if not validate_folder_path(input_path):
                return 1
            folder_path = input_path

        # === 新增：遞迴解壓縮 ===
        if extract_compressed_files is not None:
            print("\n[SEARCH] 掃描並解壓縮資料夾內的壓縮檔...")
            extracted_files = extract_compressed_files(folder_path)
            if extracted_files:
                print(f"[OK] 解壓縮 {len(extracted_files)} 個檔案")

        # === 新增：SPD 轉換 ===
        if FileConverter is not None:
            print("\n[EDIT] 掃描並轉換 SPD 檔案為 CSV...")
            converted_count = FileConverter.convert_spd_files_to_csv(folder_path)
            if converted_count > 0:
                print(f"[OK] 成功轉換 {converted_count} 個 SPD 檔案")
            else:
                print("[INFO] 未發現需要轉換的 SPD 檔案")

        # 決定處理模式
        processing_mode = "full" if args.excel else "summary_only"

        # 顯示開始資訊
        display_start_info(folder_path, processing_mode, args.verbose)
        
        # 初始化處理器
        processor = BatchCsvToExcelProcessor(enable_logging=args.verbose)

        # 記錄開始時間
        start_time = time.perf_counter()

        # 執行核心處理
        result = processor.process_folder(
            input_folder=folder_path,
            force_overwrite=True,
            processing_mode=processing_mode
        )
        
        # 計算處理時間
        processing_time = time.perf_counter() - start_time
        
        # 顯示結果
        display_results(result, processing_time)
        
        # 返回結果
        return 0 if result.success else 1
        
    except KeyboardInterrupt:
        print("\n\n[WARNING]  用戶中斷處理")
        return 130
    except Exception as e:
        print(f"\n[ERROR] 未預期的錯誤: {e}")
        if args.verbose if 'args' in locals() else False:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())