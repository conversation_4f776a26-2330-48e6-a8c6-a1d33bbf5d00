"""
測試 Epic-03 Story 3.3: UnifiedEmailProcessor 狀態追蹤整合
完整的整合測試，驗證處理狀態服務在統一郵件處理器中的正確集成
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, MagicMock, patch, AsyncMock
from typing import Dict, Any

from backend.email.models.email_models import EmailData, EmailAttachment
from backend.shared.application.services.unified_email_processor import (
    UnifiedEmailProcessor, 
    EmailProcessingResult
)
from backend.shared.services.process_status_enums import ProcessStatus


class TestUnifiedEmailProcessorStatusIntegration:
    """UnifiedEmailProcessor 狀態追蹤整合測試"""
    
    @pytest.fixture
    def mock_email_data(self):
        """模擬郵件數據"""
        return EmailData(
            message_id="test_message_123",
            subject="測試郵件主旨",
            sender="<EMAIL>",
            body="測試郵件內容",
            received_time=datetime.now(),
            attachments=[]
        )
    
    @pytest.fixture
    def mock_processor(self):
        """模擬 UnifiedEmailProcessor"""
        with patch.multiple(
            'backend.shared.application.services.unified_email_processor',
            ParserFactory=Mock(),
            LineNotificationService=Mock(),
            EmailDatabase=Mock(),
            EmailFilter=Mock()
        ):
            processor = UnifiedEmailProcessor()
            
            # 模擬處理狀態服務
            processor.process_status_service = Mock()
            processor.process_status_service.start_processing.return_value = 12345
            processor.process_status_service.update_status = Mock()
            processor.process_status_service.complete_processing = Mock()
            processor.process_status_service.fail_processing = Mock()
            
            # 模擬其他組件
            processor.email_filter = Mock()
            processor.email_filter.filter_email.return_value = {
                'is_allowed': True,
                'reason': '白名單檢查通過'
            }
            
            # 模擬解析器工廠
            processor.parser_factory.parse_email.return_value = (
                Mock(
                    is_identified=True,
                    vendor_code="TEST_VENDOR",
                    vendor_name="測試廠商",
                    confidence_score=0.95,
                    matching_patterns=["test_pattern"],
                    identification_method="email_header"
                ),
                Mock(
                    is_success=True,
                    product_code="TEST_PD",
                    mo_number="TEST_MO",
                    lot_number="TEST_LOT",
                    extraction_method="llm_parsing",
                    error_message=None,
                    extracted_data={'yield_value': 95.5}
                )
            )
            
            # 禁用實際的檔案處理
            processor.attachment_handler = None
            processor.use_pipeline = False
            
            return processor
    
    @pytest.mark.asyncio
    async def test_processing_status_lifecycle_success(self, mock_processor, mock_email_data):
        """測試成功處理的完整狀態生命週期"""
        
        # 模擬數據庫更新成功
        with patch.object(mock_processor, '_update_database', new_callable=AsyncMock) as mock_update_db:
            mock_update_db.return_value = None
            
            # 模擬通知發送成功
            with patch.object(mock_processor, '_send_success_notification', new_callable=AsyncMock) as mock_notify:
                mock_notify.return_value = None
                
                # 執行處理
                result = await mock_processor.process_email_complete(
                    email_data=mock_email_data,
                    email_id="123"
                )
        
        # 驗證狀態追蹤調用
        status_service = mock_processor.process_status_service
        
        # 1. 檢查開始處理調用
        status_service.start_processing.assert_called_once_with(
            email_id=123,
            step_name="unified_email_processing"
        )
        
        # 2. 檢查狀態更新調用 (應該有多個更新)
        assert status_service.update_status.call_count >= 2
        
        # 檢查特定的狀態更新
        update_calls = status_service.update_status.call_args_list
        
        # 第一次更新: 解析階段
        first_call = update_calls[0]
        assert first_call[0][0] == 12345  # status_id
        assert first_call[0][1] == ProcessStatus.PROCESSING.value
        # 檢查元數據 (第三個參數)
        metadata = first_call[0][2]
        assert 'progress_percentage' in metadata
        assert metadata['progress_percentage'] == 20
        
        # 3. 檢查完成處理調用
        status_service.complete_processing.assert_called_once()
        complete_call = status_service.complete_processing.call_args
        assert complete_call[0][0] == 12345  # status_id
        
        # 檢查完成時的結果數據
        result_data = complete_call[0][1]
        assert result_data['vendor_code'] == "TEST_VENDOR"
        assert result_data['product_code'] == "TEST_PD"
        assert result_data['mo_number'] == "TEST_MO"
        assert result_data['extraction_method'] == "llm_parsing"
        assert 'lifecycle_events' in result_data
        
        # 4. 驗證最終結果
        assert result.is_success == True
        assert result.vendor_code == "TEST_VENDOR"
        assert result.status_id == 12345
        assert result.lifecycle_events is not None
    
    @pytest.mark.asyncio
    async def test_processing_status_failure_handling(self, mock_processor, mock_email_data):
        """測試處理失敗時的狀態處理"""
        
        # 模擬解析失敗
        mock_processor.parser_factory.parse_email.return_value = (
            Mock(
                is_identified=False,
                vendor_code=None,
                vendor_name=None,
                confidence_score=0.1,
                matching_patterns=[],
                identification_method="failed"
            ),
            Mock(
                is_success=False,
                product_code=None,
                mo_number=None,
                lot_number=None,
                extraction_method="failed",
                error_message="解析失敗測試",
                extracted_data=None
            )
        )
        
        # 模擬通知發送
        with patch.object(mock_processor, '_send_failure_notification', new_callable=AsyncMock) as mock_notify:
            mock_notify.return_value = None
            
            # 執行處理
            result = await mock_processor.process_email_complete(
                email_data=mock_email_data,
                email_id="456"
            )
        
        # 驗證失敗處理
        status_service = mock_processor.process_status_service
        
        # 1. 檢查開始處理調用
        status_service.start_processing.assert_called_once_with(
            email_id=456,
            step_name="unified_email_processing"
        )
        
        # 2. 檢查失敗處理調用
        status_service.fail_processing.assert_called_once_with(
            12345,
            "郵件解析失敗: 解析失敗測試"
        )
        
        # 3. 驗證最終結果
        assert result.is_success == False
        assert result.vendor_code is None
        assert result.status_id == 12345
        assert result.error_message == "解析失敗測試"
    
    @pytest.mark.asyncio
    async def test_processing_status_database_failure_handling(self, mock_processor, mock_email_data):
        """測試資料庫失敗情況下的狀態處理"""
        
        # 模擬資料庫更新異常
        with patch.object(mock_processor, '_update_database', new_callable=AsyncMock) as mock_update_db:
            mock_update_db.side_effect = Exception("資料庫連接失敗")
            
            # 模擬通知發送
            with patch.object(mock_processor, '_send_success_notification', new_callable=AsyncMock) as mock_notify:
                mock_notify.return_value = None
                
                # 執行處理
                result = await mock_processor.process_email_complete(
                    email_data=mock_email_data,
                    email_id="789"
                )
        
        # 驗證資料庫失敗處理
        status_service = mock_processor.process_status_service
        
        # 檢查失敗處理調用 (資料庫失敗時會調用這個)
        status_service.fail_processing.assert_called()
        fail_call = status_service.fail_processing.call_args[0]
        assert fail_call[0] == 12345  # status_id
        # 檢查失敗訊息包含資料庫錯誤
        error_message = fail_call[1]
        assert "資料庫" in error_message
        
        # 驗證結果 (資料庫失敗時會立即返回失敗結果)
        assert result.is_success == True  # 解析成功但資料庫更新失敗
        assert result.database_updated == False
        assert result.status_id == 12345
    
    def test_get_processing_status_method(self, mock_processor):
        """測試獲取處理狀態方法"""
        
        # 模擬狀態查詢結果
        mock_status_info = Mock()
        mock_status_info.id = 12345
        mock_status_info.email_id = 123
        mock_status_info.step_name = "unified_email_processing"
        mock_status_info.status = ProcessStatus.PROCESSING.value
        mock_status_info.progress_percentage = 50
        mock_status_info.started_at = datetime(2023, 1, 1, 10, 0, 0)
        mock_status_info.completed_at = None
        mock_status_info.error_message = None
        mock_status_info.metadata = {'test': 'data'}
        mock_status_info.result_data = None
        
        mock_processor.process_status_service.get_process_status.return_value = mock_status_info
        
        # 調用方法
        result = mock_processor.get_processing_status(email_id=123)
        
        # 驗證結果
        assert result is not None
        assert result['status_id'] == 12345
        assert result['email_id'] == 123
        assert result['status'] == ProcessStatus.PROCESSING.value
        assert result['progress_percentage'] == 50
        assert result['started_at'] == "2023-01-01T10:00:00"
        assert result['metadata'] == {'test': 'data'}
        
        # 驗證服務調用
        mock_processor.process_status_service.get_process_status.assert_called_once_with(
            email_id=123,
            step_name="unified_email_processing"
        )
    
    def test_get_processing_history_method(self, mock_processor):
        """測試獲取處理歷史方法"""
        
        # 模擬歷史記錄
        mock_records = [
            Mock(
                id=1,
                email_id=123,
                step_name="unified_email_processing",
                status=ProcessStatus.COMPLETED.value,
                progress_percentage=100,
                started_at=datetime(2023, 1, 1, 10, 0, 0),
                completed_at=datetime(2023, 1, 1, 10, 5, 0),
                error_message=None,
                processing_time=300.0,
                metadata={'test': 'data1'},
                result_data={'success': True}
            ),
            Mock(
                id=2,
                email_id=123,
                step_name="unified_email_processing",
                status=ProcessStatus.FAILED.value,
                progress_percentage=30,
                started_at=datetime(2023, 1, 1, 9, 0, 0),
                completed_at=datetime(2023, 1, 1, 9, 2, 0),
                error_message="測試錯誤",
                processing_time=120.0,
                metadata={'test': 'data2'},
                result_data=None
            )
        ]
        
        mock_processor.process_status_service.get_processing_history.return_value = mock_records
        
        # 調用方法
        result = mock_processor.get_processing_history(email_id=123)
        
        # 驗證結果
        assert len(result) == 2
        
        # 檢查第一條記錄
        first_record = result[0]
        assert first_record['status_id'] == 1
        assert first_record['status'] == ProcessStatus.COMPLETED.value
        assert first_record['progress_percentage'] == 100
        assert first_record['error_message'] is None
        
        # 檢查第二條記錄
        second_record = result[1]
        assert second_record['status_id'] == 2
        assert second_record['status'] == ProcessStatus.FAILED.value
        assert second_record['error_message'] == "測試錯誤"
    
    def test_get_processing_statistics_method(self, mock_processor):
        """測試獲取處理統計方法"""
        
        # 模擬統計數據
        mock_stats = {
            'total_processed': 100,
            'success_count': 85,
            'failed_count': 15,
            'success_rate': 0.85,
            'average_processing_time': 180.5,
            'daily_throughput': 20
        }
        
        mock_processor.process_status_service.get_processing_statistics.return_value = mock_stats
        
        # 調用方法
        result = mock_processor.get_processing_statistics(period="daily")
        
        # 驗證結果
        assert result['service_available'] == True
        assert result['total_processed'] == 100
        assert result['success_rate'] == 0.85
        
        # 驗證服務調用
        mock_processor.process_status_service.get_processing_statistics.assert_called_once_with("daily")
    
    def test_status_service_not_available(self, mock_processor):
        """測試狀態服務不可用時的處理"""
        
        # 設置狀態服務為 None
        mock_processor.process_status_service = None
        
        # 測試各個方法
        assert mock_processor.get_processing_status(123) is None
        assert mock_processor.get_processing_history(123) == []
        
        stats_result = mock_processor.get_processing_statistics()
        assert stats_result['service_available'] == False
        assert stats_result['message'] == '處理狀態服務不可用'
        
        cleanup_result = mock_processor.cleanup_old_processing_records()
        assert cleanup_result['success'] == False
        assert cleanup_result['cleaned_count'] == 0


class TestUnifiedEmailProcessorStatusProgressTracking:
    """狀態進度追蹤詳細測試"""
    
    @pytest.fixture
    def processor_with_detailed_mocks(self):
        """創建帶有詳細模擬的處理器"""
        with patch.multiple(
            'backend.shared.application.services.unified_email_processor',
            ParserFactory=Mock(),
            LineNotificationService=Mock(),
            EmailDatabase=Mock(),
            EmailFilter=Mock()
        ):
            processor = UnifiedEmailProcessor()
            
            # 詳細模擬處理狀態服務
            processor.process_status_service = Mock()
            processor.process_status_service.start_processing.return_value = 99999
            
            # 模擬成功的解析結果
            processor.parser_factory.parse_email.return_value = (
                Mock(
                    is_identified=True,
                    vendor_code="PROGRESS_TEST",
                    vendor_name="進度測試廠商",
                    confidence_score=0.98,
                    matching_patterns=["progress_pattern"]
                ),
                Mock(
                    is_success=True,
                    product_code="PROG_PD",
                    mo_number="PROG_MO",
                    lot_number="PROG_LOT",
                    extraction_method="detailed_parsing",
                    extracted_data={'yield_value': 97.3}
                )
            )
            
            # 啟用附件處理器
            processor.attachment_handler = Mock()
            processor.attachment_handler.process_email_attachments = AsyncMock(return_value={
                'success': True,
                'saved_count': 3,
                'processed_count': 3,
                'failed_count': 0
            })
            
            # 啟用郵件過濾器
            processor.email_filter = Mock()
            processor.email_filter.filter_email.return_value = {
                'is_allowed': True,
                'reason': '詳細測試通過'
            }
            
            return processor
    
    @pytest.mark.asyncio
    async def test_detailed_progress_tracking(self, processor_with_detailed_mocks):
        """測試詳細的進度追蹤"""
        
        # 準備測試數據
        test_attachment = EmailAttachment(
            filename="test.pdf",
            content_type="application/pdf",
            size_bytes=1024,
            content=b"test_content",
            file_path="/test/path/test.pdf"
        )
        
        email_data = EmailData(
            message_id="progress_test_123",
            subject="進度追蹤測試",
            sender="<EMAIL>",
            body="測試進度追蹤",
            received_time=datetime.now(),
            attachments=[test_attachment]
        )
        
        # 模擬所有異步操作
        with patch.object(processor_with_detailed_mocks, '_update_database', new_callable=AsyncMock) as mock_db:
            mock_db.return_value = None
            
            with patch.object(processor_with_detailed_mocks, '_send_success_notification', new_callable=AsyncMock) as mock_notify:
                mock_notify.return_value = None
                
                # 執行處理
                result = await processor_with_detailed_mocks.process_email_complete(
                    email_data=email_data,
                    email_id="999",
                    process_attachments=True,
                    process_vendor_files=False,  # 簡化測試
                    send_notifications=True,
                    update_database=True
                )
        
        # 詳細驗證進度更新
        status_service = processor_with_detailed_mocks.process_status_service
        update_calls = status_service.update_status.call_args_list
        
        # 驗證至少有以下進度更新:
        # 1. 解析階段 (20%)
        # 2. 附件處理階段 (40%)  
        # 3. 資料庫更新階段 (80%)
        # 4. 通知發送階段 (90%)
        
        assert len(update_calls) >= 3
        
        # 檢查進度百分比遞增
        progress_values = []
        for call in update_calls:
            if len(call[0]) >= 3:  # 確保有元數據參數
                metadata = call[0][2]
                if 'progress_percentage' in metadata:
                    progress_values.append(metadata['progress_percentage'])
        
        # 驗證進度遞增
        assert all(progress_values[i] <= progress_values[i+1] for i in range(len(progress_values)-1))
        
        # 驗證特定階段
        stage_descriptions = []
        for call in update_calls:
            if len(call[0]) >= 3:  # 確保有元數據參數
                metadata = call[0][2]
                if 'stage_description' in metadata:
                    stage_descriptions.append(metadata['stage_description'])
        
        # 確保包含了關鍵處理階段
        expected_stages = ['正在處理郵件附件', '正在更新資料庫記錄', '正在發送LINE通知']
        for expected_stage in expected_stages:
            assert any(expected_stage in desc for desc in stage_descriptions)


if __name__ == "__main__":
    # 運行整合測試
    pytest.main([__file__, "-v", "--tb=short"])