#!/usr/bin/env python3
"""
手動處理功能 - 錯誤處理測試
測試各種錯誤情況的處理和響應
"""

import pytest
import requests
import json
import os
import sqlite3
import time
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path


class TestErrorHandling:
    """錯誤處理測試類"""
    
    def __init__(self):
        self.base_url = os.getenv('API_BASE_URL', 'http://localhost:5000')
        self.api_key = os.getenv('PARSER_API_KEY', 'dev-parser-key-12345')
        self.headers = {
            'Content-Type': 'application/json',
            'X-API-Key': self.api_key
        }
        self.test_results = []
        self.db_path = self._find_database_path()
    
    def _find_database_path(self) -> str:
        """尋找資料庫檔案路徑"""
        possible_paths = [
            'D:/project/python/outlook_summary/data/email_inbox.db',
            './data/email_inbox.db',
            './email_inbox.db'
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                return path
        
        return 'D:/project/python/outlook_summary/data/email_inbox.db'
    
    def log_test_result(self, test_name: str, passed: bool, details: str = "", error: str = ""):
        """記錄測試結果"""
        result = {
            'test_name': test_name,
            'passed': passed,
            'timestamp': datetime.now().isoformat(),
            'details': details,
            'error': error
        }
        self.test_results.append(result)
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"[{status}] {test_name}")
        if details:
            print(f"    詳細: {details}")
        if error:
            print(f"    錯誤: {error}")
    
    def test_invalid_email_id(self):
        """測試無效的 emailId"""
        test_cases = [
            {
                'name': '不存在的 emailId',
                'email_id': 999999,
                'expected_status': 404
            },
            {
                'name': '負數 emailId',
                'email_id': -1,
                'expected_status': 400
            },
            {
                'name': '零 emailId',
                'email_id': 0,
                'expected_status': 400
            }
        ]
        
        test_data = {
            'vendor_code': 'TEST',
            'pd': 'TEST_PD',
            'lot': 'TEST_LOT'
        }
        
        for test_case in test_cases:
            try:
                endpoint = f"/api/parser/emails/{test_case['email_id']}/manual-input"
                url = f"{self.base_url}{endpoint}"
                
                response = requests.post(url, headers=self.headers, json=test_data, timeout=10)
                
                if response.status_code == test_case['expected_status']:
                    try:
                        data = response.json()
                        if 'success' in data and not data['success']:
                            self.log_test_result(
                                f"無效 emailId - {test_case['name']}",
                                True,
                                f"正確返回錯誤狀態碼 {response.status_code}"
                            )
                        else:
                            self.log_test_result(
                                f"無效 emailId - {test_case['name']}",
                                False,
                                f"狀態碼正確但響應格式不正確",
                                json.dumps(data, ensure_ascii=False)
                            )
                    except json.JSONDecodeError:
                        self.log_test_result(
                            f"無效 emailId - {test_case['name']}",
                            False,
                            f"狀態碼正確但響應不是有效 JSON",
                            response.text[:200]
                        )
                else:
                    self.log_test_result(
                        f"無效 emailId - {test_case['name']}",
                        False,
                        f"期望狀態碼 {test_case['expected_status']}，實際 {response.status_code}",
                        response.text[:200]
                    )
                    
            except Exception as e:
                self.log_test_result(
                    f"無效 emailId - {test_case['name']}",
                    False,
                    "",
                    str(e)
                )
    
    def test_missing_required_fields(self):
        """測試缺少必填欄位"""
        # 先建立一個測試郵件
        test_email_id = self._create_test_email()
        if not test_email_id:
            self.log_test_result(
                "缺少必填欄位測試",
                False,
                "",
                "無法建立測試郵件"
            )
            return
        
        endpoint = f"/api/parser/emails/{test_email_id}/manual-input"
        url = f"{self.base_url}{endpoint}"
        
        test_cases = [
            {
                'name': '完全空白資料',
                'data': {},
                'expected_status': 400
            },
            {
                'name': '只有 vendor_code',
                'data': {'vendor_code': 'TEST'},
                'expected_status': 400
            },
            {
                'name': '缺少 vendor_code',
                'data': {'pd': 'TEST_PD', 'lot': 'TEST_LOT'},
                'expected_status': 400
            },
            {
                'name': '空字串 vendor_code',
                'data': {'vendor_code': '', 'pd': 'TEST_PD', 'lot': 'TEST_LOT'},
                'expected_status': 400
            },
            {
                'name': 'null vendor_code',
                'data': {'vendor_code': None, 'pd': 'TEST_PD', 'lot': 'TEST_LOT'},
                'expected_status': 400
            }
        ]
        
        for test_case in test_cases:
            try:
                response = requests.post(url, headers=self.headers, json=test_case['data'], timeout=10)
                
                if response.status_code == test_case['expected_status']:
                    try:
                        data = response.json()
                        if 'success' in data and not data['success'] and 'error' in data:
                            self.log_test_result(
                                f"缺少必填欄位 - {test_case['name']}",
                                True,
                                f"正確拒絕無效資料，錯誤訊息: {data['error']}"
                            )
                        else:
                            self.log_test_result(
                                f"缺少必填欄位 - {test_case['name']}",
                                False,
                                "狀態碼正確但響應格式不正確",
                                json.dumps(data, ensure_ascii=False)
                            )
                    except json.JSONDecodeError:
                        self.log_test_result(
                            f"缺少必填欄位 - {test_case['name']}",
                            False,
                            "響應不是有效 JSON",
                            response.text[:200]
                        )
                else:
                    self.log_test_result(
                        f"缺少必填欄位 - {test_case['name']}",
                        False,
                        f"期望狀態碼 {test_case['expected_status']}，實際 {response.status_code}",
                        response.text[:200]
                    )
                    
            except Exception as e:
                self.log_test_result(
                    f"缺少必填欄位 - {test_case['name']}",
                    False,
                    "",
                    str(e)
                )
        
        # 清理測試資料
        self._cleanup_test_email(test_email_id)
    
    def test_format_validation_errors(self):
        """測試格式驗證錯誤"""
        test_email_id = self._create_test_email()
        if not test_email_id:
            return
        
        endpoint = f"/api/parser/emails/{test_email_id}/manual-input"
        url = f"{self.base_url}{endpoint}"
        
        test_cases = [
            {
                'name': '無效的廠商代碼（包含空格）',
                'data': {
                    'vendor_code': 'TEST VENDOR',
                    'pd': 'TEST_PD',
                    'lot': 'TEST_LOT'
                },
                'expected_status': 400
            },
            {
                'name': '無效的廠商代碼（特殊字符）',
                'data': {
                    'vendor_code': 'TEST@VENDOR',
                    'pd': 'TEST_PD',
                    'lot': 'TEST_LOT'
                },
                'expected_status': 400
            },
            {
                'name': '良率超出上限',
                'data': {
                    'vendor_code': 'TEST',
                    'pd': 'TEST_PD',
                    'lot': 'TEST_LOT',
                    'yield_value': 150.0
                },
                'expected_status': 400
            },
            {
                'name': '良率為負數',
                'data': {
                    'vendor_code': 'TEST',
                    'pd': 'TEST_PD',
                    'lot': 'TEST_LOT',
                    'yield_value': -5.0
                },
                'expected_status': 400
            },
            {
                'name': '良率為非數字字串',
                'data': {
                    'vendor_code': 'TEST',
                    'pd': 'TEST_PD',
                    'lot': 'TEST_LOT',
                    'yield_value': 'not_a_number'
                },
                'expected_status': 400
            }
        ]
        
        for test_case in test_cases:
            try:
                response = requests.post(url, headers=self.headers, json=test_case['data'], timeout=10)
                
                if response.status_code == test_case['expected_status']:
                    try:
                        data = response.json()
                        if 'success' in data and not data['success']:
                            self.log_test_result(
                                f"格式驗證錯誤 - {test_case['name']}",
                                True,
                                f"正確拒絕無效格式，錯誤: {data.get('error', 'N/A')}"
                            )
                        else:
                            self.log_test_result(
                                f"格式驗證錯誤 - {test_case['name']}",
                                False,
                                "狀態碼正確但響應格式不正確",
                                json.dumps(data, ensure_ascii=False)
                            )
                    except json.JSONDecodeError:
                        self.log_test_result(
                            f"格式驗證錯誤 - {test_case['name']}",
                            False,
                            "響應不是有效 JSON",
                            response.text[:200]
                        )
                else:
                    self.log_test_result(
                        f"格式驗證錯誤 - {test_case['name']}",
                        False,
                        f"期望狀態碼 {test_case['expected_status']}，實際 {response.status_code}",
                        response.text[:200]
                    )
                    
            except Exception as e:
                self.log_test_result(
                    f"格式驗證錯誤 - {test_case['name']}",
                    False,
                    "",
                    str(e)
                )
        
        self._cleanup_test_email(test_email_id)
    
    def test_api_key_errors(self):
        """測試 API Key 錯誤"""
        test_email_id = self._create_test_email()
        if not test_email_id:
            return
        
        endpoint = f"/api/parser/emails/{test_email_id}/manual-input"
        url = f"{self.base_url}{endpoint}"
        
        test_data = {
            'vendor_code': 'TEST',
            'pd': 'TEST_PD',
            'lot': 'TEST_LOT'
        }
        
        test_cases = [
            {
                'name': '缺少 API Key',
                'headers': {'Content-Type': 'application/json'},
                'expected_status': 401
            },
            {
                'name': '無效 API Key',
                'headers': {
                    'Content-Type': 'application/json',
                    'X-API-Key': 'invalid-key'
                },
                'expected_status': 401
            },
            {
                'name': '空白 API Key',
                'headers': {
                    'Content-Type': 'application/json',
                    'X-API-Key': ''
                },
                'expected_status': 401
            }
        ]
        
        for test_case in test_cases:
            try:
                response = requests.post(url, headers=test_case['headers'], json=test_data, timeout=10)
                
                if response.status_code == test_case['expected_status']:
                    try:
                        data = response.json()
                        if 'success' in data and not data['success']:
                            self.log_test_result(
                                f"API Key 錯誤 - {test_case['name']}",
                                True,
                                f"正確拒絕無效認證，錯誤: {data.get('error', 'N/A')}"
                            )
                        else:
                            self.log_test_result(
                                f"API Key 錯誤 - {test_case['name']}",
                                False,
                                "狀態碼正確但響應格式不正確",
                                json.dumps(data, ensure_ascii=False)
                            )
                    except json.JSONDecodeError:
                        self.log_test_result(
                            f"API Key 錯誤 - {test_case['name']}",
                            False,
                            "響應不是有效 JSON",
                            response.text[:200]
                        )
                else:
                    self.log_test_result(
                        f"API Key 錯誤 - {test_case['name']}",
                        False,
                        f"期望狀態碼 {test_case['expected_status']}，實際 {response.status_code}",
                        response.text[:200]
                    )
                    
            except Exception as e:
                self.log_test_result(
                    f"API Key 錯誤 - {test_case['name']}",
                    False,
                    "",
                    str(e)
                )
        
        self._cleanup_test_email(test_email_id)
    
    def test_network_timeout_handling(self):
        """測試網路超時處理"""
        test_email_id = self._create_test_email()
        if not test_email_id:
            return
        
        endpoint = f"/api/parser/emails/{test_email_id}/manual-input"
        url = f"{self.base_url}{endpoint}"
        
        test_data = {
            'vendor_code': 'TEST',
            'pd': 'TEST_PD',
            'lot': 'TEST_LOT'
        }
        
        try:
            # 使用極短的超時時間來模擬網路問題
            response = requests.post(url, headers=self.headers, json=test_data, timeout=0.001)
            
            # 如果請求成功完成，這表示服務很快，這也是正常的
            self.log_test_result(
                "網路超時處理",
                True,
                "服務響應迅速，未觸發超時"
            )
            
        except requests.exceptions.Timeout:
            # 超時是預期的行為
            self.log_test_result(
                "網路超時處理",
                True,
                "正確處理超時異常"
            )
            
        except Exception as e:
            # 其他異常也是可以接受的，因為我們在測試異常處理
            self.log_test_result(
                "網路超時處理",
                True,
                f"處理了其他網路異常: {type(e).__name__}"
            )
        
        self._cleanup_test_email(test_email_id)
    
    def test_malformed_json_handling(self):
        """測試格式錯誤的 JSON 處理"""
        test_email_id = self._create_test_email()
        if not test_email_id:
            return
        
        endpoint = f"/api/parser/emails/{test_email_id}/manual-input"
        url = f"{self.base_url}{endpoint}"
        
        # 發送格式錯誤的 JSON
        malformed_json = '{"vendor_code": "TEST", "pd": "TEST_PD", "lot": "TEST_LOT"'  # 缺少結束括號
        
        try:
            response = requests.post(
                url,
                headers=self.headers,
                data=malformed_json,
                timeout=10
            )
            
            if response.status_code == 400:
                try:
                    data = response.json()
                    if 'success' in data and not data['success']:
                        self.log_test_result(
                            "格式錯誤 JSON 處理",
                            True,
                            f"正確拒絕格式錯誤的 JSON，錯誤: {data.get('error', 'N/A')}"
                        )
                    else:
                        self.log_test_result(
                            "格式錯誤 JSON 處理",
                            False,
                            "狀態碼正確但響應格式不正確",
                            json.dumps(data, ensure_ascii=False)
                        )
                except json.JSONDecodeError:
                    # 如果連錯誤響應都不是有效 JSON，這可能表示有問題
                    self.log_test_result(
                        "格式錯誤 JSON 處理",
                        False,
                        "錯誤響應不是有效 JSON",
                        response.text[:200]
                    )
            else:
                self.log_test_result(
                    "格式錯誤 JSON 處理",
                    False,
                    f"期望狀態碼 400，實際 {response.status_code}",
                    response.text[:200]
                )
                
        except Exception as e:
            self.log_test_result(
                "格式錯誤 JSON 處理",
                False,
                "",
                str(e)
            )
        
        self._cleanup_test_email(test_email_id)
    
    def _create_test_email(self) -> Optional[int]:
        """建立測試郵件並返回 ID"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO emails (subject, sender, body, parse_status, received_time, message_id)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                f'Test Error Handling Email {datetime.now().timestamp()}',
                '<EMAIL>',
                'Test body for error handling',
                'pending',
                datetime.now().isoformat(),
                f"test-error-{datetime.now().timestamp()}"
            ))
            
            email_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return email_id
            
        except Exception as e:
            print(f"建立測試郵件失敗: {e}")
            return None
    
    def _cleanup_test_email(self, email_id: int):
        """清理測試郵件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM emails WHERE id = ?", (email_id,))
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"清理測試郵件失敗: {e}")
    
    def run_all_tests(self):
        """執行所有測試"""
        print("=" * 60)
        print("手動處理功能 - 錯誤處理測試")
        print("=" * 60)
        print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"基礎 URL: {self.base_url}")
        print(f"資料庫路徑: {self.db_path}")
        print("-" * 60)
        
        # 執行所有測試
        self.test_invalid_email_id()
        self.test_missing_required_fields()
        self.test_format_validation_errors()
        self.test_api_key_errors()
        self.test_network_timeout_handling()
        self.test_malformed_json_handling()
        
        # 輸出測試總結
        print("-" * 60)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        total_tests = len(self.test_results)
        
        print(f"測試總結: {passed_tests}/{total_tests} 通過")
        
        if passed_tests == total_tests:
            print("🎉 所有錯誤處理測試通過！")
            return True
        else:
            print("⚠️  部分測試失敗，請檢查詳細結果")
            return False
    
    def generate_report(self, output_file: str = None):
        """生成測試報告"""
        if output_file is None:
            output_file = f"error_handling_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report = {
            'test_type': 'Error Handling Test',
            'timestamp': datetime.now().isoformat(),
            'base_url': self.base_url,
            'database_path': self.db_path,
            'total_tests': len(self.test_results),
            'passed_tests': sum(1 for result in self.test_results if result['passed']),
            'failed_tests': sum(1 for result in self.test_results if not result['passed']),
            'test_results': self.test_results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"測試報告已儲存至: {output_file}")
        return output_file


if __name__ == "__main__":
    # 執行測試
    test_runner = TestErrorHandling()
    success = test_runner.run_all_tests()
    test_runner.generate_report()
    
    # 返回適當的退出碼
    exit(0 if success else 1)