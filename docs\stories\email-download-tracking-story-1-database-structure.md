# Story 1: 資料庫結構創建 - 郵件下載追蹤系統

## 📋 Story 概要

**Story ID**: EDTS-001  
**Story 名稱**: 資料庫結構創建  
**Epic**: 郵件下載狀態追蹤  
**優先級**: P0 (Critical)  
**估計工作量**: 16 Story Points (2-3 工作日)

### User Story
**As a** 系統架構師  
**I want** 建立專用的郵件下載追蹤資料庫表結構  
**So that** 系統能夠精確記錄和管理每個郵件的下載狀態和重試歷史

---

## 🎯 驗收標準

### 必須完成的功能 (Must Have)
- [ ] ✅ 創建 `email_download_status` 主追蹤表
- [ ] ✅ 創建 `email_download_retry_log` 重試日誌表
- [ ] ✅ 建立適當的外鍵關聯關係
- [ ] ✅ 創建所有必要的資料庫索引
- [ ] ✅ 實現向後兼容的遷移腳本
- [ ] ✅ 為現有郵件創建初始狀態記錄

### 應該有的功能 (Should Have)
- [ ] ✅ 資料庫遷移回滾機制
- [ ] ✅ 遷移過程的詳細日誌記錄
- [ ] ✅ 資料完整性驗證腳本

### 期望的功能 (Could Have)
- [ ] ✅ 自動備份機制
- [ ] ✅ 性能基準測試腳本

---

## 🗄️ 技術實現詳情

### 資料庫表結構設計

#### 1. EmailDownloadStatus 主追蹤表

```sql
CREATE TABLE email_download_status (
    -- 主鍵
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email_id INTEGER NOT NULL,
    
    -- 下載狀態追蹤
    status VARCHAR(20) DEFAULT 'pending',  -- pending, downloading, completed, failed, retry_scheduled
    download_attempt INTEGER DEFAULT 1,   -- 當前嘗試次數
    max_retry_count INTEGER DEFAULT 3,    -- 最大重試次數
    
    -- 時間追蹤
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,                   -- 開始下載時間
    completed_at DATETIME,                 -- 完成時間
    last_retry_at DATETIME,                -- 上次重試時間
    next_retry_at DATETIME,                -- 下次重試時間
    
    -- 錯誤追蹤
    error_type VARCHAR(50),                -- 錯誤類型
    error_message TEXT,                    -- 詳細錯誤信息
    error_details TEXT,                    -- JSON 格式的詳細錯誤信息
    
    -- 下載詳情
    download_size_bytes INTEGER,           -- 下載大小
    download_duration_seconds REAL,       -- 下載耗時
    server_response_code VARCHAR(10),      -- 服務器響應代碼
    
    -- 重試策略
    retry_strategy VARCHAR(20) DEFAULT 'exponential', -- exponential, linear, fixed
    retry_interval_seconds INTEGER DEFAULT 60,        -- 重試間隔
    
    -- 外鍵約束
    FOREIGN KEY (email_id) REFERENCES emails (id) ON DELETE CASCADE
);
```

#### 2. EmailDownloadRetryLog 重試日誌表

```sql
CREATE TABLE email_download_retry_log (
    -- 主鍵
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    download_status_id INTEGER NOT NULL,
    
    -- 重試記錄
    retry_attempt INTEGER NOT NULL,
    retry_reason VARCHAR(100),
    attempted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 重試結果
    result VARCHAR(20),  -- success, failed, timeout, server_error
    error_message TEXT,
    duration_seconds REAL,
    
    -- 重試配置
    retry_strategy_used VARCHAR(20),
    retry_interval_used INTEGER,
    
    -- 外鍵約束
    FOREIGN KEY (download_status_id) REFERENCES email_download_status (id) ON DELETE CASCADE
);
```

#### 3. 索引設計

```sql
-- email_download_status 表索引
CREATE INDEX idx_email_download_status_email_id ON email_download_status (email_id);
CREATE INDEX idx_email_download_status_status ON email_download_status (status);
CREATE INDEX idx_email_download_status_next_retry ON email_download_status (next_retry_at);
CREATE INDEX idx_email_download_status_created ON email_download_status (created_at);

-- email_download_retry_log 表索引
CREATE INDEX idx_retry_log_download_status ON email_download_retry_log (download_status_id);
CREATE INDEX idx_retry_log_attempted_at ON email_download_retry_log (attempted_at);
```

### 實現文件結構

```
backend/
├── shared/
│   └── infrastructure/
│       └── adapters/
│           └── database/
│               ├── migrations/
│               │   └── add_download_tracking.py      # 主遷移腳本
│               ├── models.py                         # 更新現有模型
│               └── download_tracking_models.py       # 新追蹤模型
└── email/
    └── models/
        └── download_tracking_models.py               # Pydantic 模型
```

---

## 🔧 詳細實現步驟

### Step 1: 創建 SQLAlchemy 模型 (1天)

**檔案位置**: `backend/shared/infrastructure/adapters/database/download_tracking_models.py`

```python
"""
郵件下載追蹤資料庫模型
"""

from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, DateTime, Float, Text, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from backend.shared.infrastructure.adapters.database.models import Base

class DownloadStatus(str, Enum):
    PENDING = "pending"
    DOWNLOADING = "downloading"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRY_SCHEDULED = "retry_scheduled"

class RetryStrategy(str, Enum):
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    FIXED = "fixed"

class EmailDownloadStatusDB(Base):
    __tablename__ = 'email_download_status'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    email_id = Column(Integer, ForeignKey('emails.id'), nullable=False, index=True)
    
    status = Column(SQLEnum(DownloadStatus), default=DownloadStatus.PENDING, nullable=False)
    download_attempt = Column(Integer, default=1, nullable=False)
    max_retry_count = Column(Integer, default=3, nullable=False)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    last_retry_at = Column(DateTime)
    next_retry_at = Column(DateTime, index=True)
    
    error_type = Column(String(50))
    error_message = Column(Text)
    error_details = Column(Text)
    
    download_size_bytes = Column(Integer)
    download_duration_seconds = Column(Float)
    server_response_code = Column(String(10))
    
    retry_strategy = Column(SQLEnum(RetryStrategy), default=RetryStrategy.EXPONENTIAL)
    retry_interval_seconds = Column(Integer, default=60)
    
    # 關聯
    email = relationship("EmailDB", back_populates="download_status")
    retry_logs = relationship("EmailDownloadRetryLogDB", back_populates="download_status", cascade="all, delete-orphan")

class EmailDownloadRetryLogDB(Base):
    __tablename__ = 'email_download_retry_log'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    download_status_id = Column(Integer, ForeignKey('email_download_status.id'), nullable=False, index=True)
    
    retry_attempt = Column(Integer, nullable=False)
    retry_reason = Column(String(100))
    attempted_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    result = Column(String(20))
    error_message = Column(Text)
    duration_seconds = Column(Float)
    
    retry_strategy_used = Column(String(20))
    retry_interval_used = Column(Integer)
    
    # 關聯
    download_status = relationship("EmailDownloadStatusDB", back_populates="retry_logs")
```

### Step 2: 更新現有郵件模型 (0.5天)

**修改檔案**: `backend/shared/infrastructure/adapters/database/models.py`

在 `EmailDB` 類中添加關聯：

```python
class EmailDB(Base):
    # ... 現有欄位 ...
    
    # 新增關聯
    download_status = relationship("EmailDownloadStatusDB", back_populates="email", cascade="all, delete-orphan")
```

### Step 3: 創建 Pydantic 響應模型 (0.5天)

**檔案位置**: `backend/email/models/download_tracking_models.py`

```python
"""
郵件下載追蹤 Pydantic 模型
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class DownloadStatus(str, Enum):
    PENDING = "pending"
    DOWNLOADING = "downloading" 
    COMPLETED = "completed"
    FAILED = "failed"
    RETRY_SCHEDULED = "retry_scheduled"

class RetryStrategy(str, Enum):
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    FIXED = "fixed"

class DownloadStatusResponse(BaseModel):
    id: int
    email_id: int
    status: DownloadStatus
    download_attempt: int
    max_retry_count: int
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    last_retry_at: Optional[datetime]
    next_retry_at: Optional[datetime]
    error_type: Optional[str]
    error_message: Optional[str]
    download_size_bytes: Optional[int]
    download_duration_seconds: Optional[float]
    retry_strategy: RetryStrategy

class DownloadStatisticsResponse(BaseModel):
    total_downloads: int
    successful_downloads: int
    failed_downloads: int
    pending_retries: int
    success_rate: float
    error_types: List[Dict[str, Any]]
    period_hours: int
    last_updated: str

class ManualRetryRequest(BaseModel):
    email_ids: List[int]
    reason: Optional[str] = "manual_retry"

class RetryResult(BaseModel):
    email_id: int
    tracking_id: int
    status: str

class ManualRetryResponse(BaseModel):
    success: bool
    message: str
    retry_results: List[RetryResult]
```

### Step 4: 實現遷移腳本 (1天)

**檔案位置**: `backend/shared/infrastructure/adapters/database/migrations/add_download_tracking.py`

```python
"""
郵件下載追蹤表遷移腳本
"""

from sqlalchemy import text
from backend.shared.infrastructure.adapters.database.models import Base, db_engine
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def migrate_add_download_tracking():
    """添加郵件下載追蹤表"""
    try:
        with db_engine.get_session() as session:
            # 檢查表是否已存在
            result = session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='email_download_status'
            """))
            
            if result.fetchone():
                logger.info("email_download_status 表已存在，跳過遷移")
                return True
            
            logger.info("開始創建郵件下載追蹤表...")
            
            # 創建 email_download_status 表
            session.execute(text("""
                CREATE TABLE email_download_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email_id INTEGER NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending',
                    download_attempt INTEGER DEFAULT 1,
                    max_retry_count INTEGER DEFAULT 3,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    started_at DATETIME,
                    completed_at DATETIME,
                    last_retry_at DATETIME,
                    next_retry_at DATETIME,
                    error_type VARCHAR(50),
                    error_message TEXT,
                    error_details TEXT,
                    download_size_bytes INTEGER,
                    download_duration_seconds REAL,
                    server_response_code VARCHAR(10),
                    retry_strategy VARCHAR(20) DEFAULT 'exponential',
                    retry_interval_seconds INTEGER DEFAULT 60,
                    FOREIGN KEY (email_id) REFERENCES emails (id) ON DELETE CASCADE
                )
            """))
            
            # 創建索引
            session.execute(text("CREATE INDEX idx_email_download_status_email_id ON email_download_status (email_id)"))
            session.execute(text("CREATE INDEX idx_email_download_status_status ON email_download_status (status)"))
            session.execute(text("CREATE INDEX idx_email_download_status_next_retry ON email_download_status (next_retry_at)"))
            session.execute(text("CREATE INDEX idx_email_download_status_created ON email_download_status (created_at)"))
            
            # 創建 email_download_retry_log 表
            session.execute(text("""
                CREATE TABLE email_download_retry_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    download_status_id INTEGER NOT NULL,
                    retry_attempt INTEGER NOT NULL,
                    retry_reason VARCHAR(100),
                    attempted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    result VARCHAR(20),
                    error_message TEXT,
                    duration_seconds REAL,
                    retry_strategy_used VARCHAR(20),
                    retry_interval_used INTEGER,
                    FOREIGN KEY (download_status_id) REFERENCES email_download_status (id) ON DELETE CASCADE
                )
            """))
            
            # 創建索引
            session.execute(text("CREATE INDEX idx_retry_log_download_status ON email_download_retry_log (download_status_id)"))
            session.execute(text("CREATE INDEX idx_retry_log_attempted_at ON email_download_retry_log (attempted_at)"))
            
            session.commit()
            logger.info("郵件下載追蹤表創建成功")
            
            # 為現有郵件創建初始狀態記錄
            create_initial_download_status(session)
            
            return True
            
    except Exception as e:
        logger.error(f"遷移失敗: {e}")
        return False

def create_initial_download_status(session):
    """為現有郵件創建初始下載狀態"""
    try:
        # 為所有現有郵件創建 'completed' 狀態記錄
        result = session.execute(text("""
            INSERT INTO email_download_status (email_id, status, download_attempt, completed_at)
            SELECT id, 'completed', 1, created_at 
            FROM emails 
            WHERE id NOT IN (SELECT email_id FROM email_download_status)
        """))
        
        affected_rows = result.rowcount
        session.commit()
        logger.info(f"為 {affected_rows} 封現有郵件創建初始下載狀態完成")
        
    except Exception as e:
        logger.error(f"創建初始下載狀態失敗: {e}")
        session.rollback()
        raise

def rollback_download_tracking_migration():
    """回滾遷移"""
    try:
        with db_engine.get_session() as session:
            logger.info("開始回滾郵件下載追蹤表...")
            
            # 刪除表（順序很重要，先刪除有外鍵的表）
            session.execute(text("DROP TABLE IF EXISTS email_download_retry_log"))
            session.execute(text("DROP TABLE IF EXISTS email_download_status"))
            
            session.commit()
            logger.info("郵件下載追蹤表回滾完成")
            
    except Exception as e:
        logger.error(f"回滾失敗: {e}")
        raise

def verify_migration():
    """驗證遷移結果"""
    try:
        with db_engine.get_session() as session:
            # 檢查表是否存在
            tables_result = session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ('email_download_status', 'email_download_retry_log')
                ORDER BY name
            """))
            
            tables = [row[0] for row in tables_result.fetchall()]
            expected_tables = ['email_download_retry_log', 'email_download_status']
            
            if set(tables) != set(expected_tables):
                raise Exception(f"表創建不完整: 期望 {expected_tables}, 實際 {tables}")
            
            # 檢查索引是否存在
            indexes_result = session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND name LIKE 'idx_%download%'
                ORDER BY name
            """))
            
            indexes = [row[0] for row in indexes_result.fetchall()]
            logger.info(f"創建的索引: {indexes}")
            
            # 檢查外鍵約束
            pragma_result = session.execute(text("PRAGMA foreign_key_check"))
            fk_errors = pragma_result.fetchall()
            
            if fk_errors:
                raise Exception(f"外鍵約束檢查失敗: {fk_errors}")
            
            # 檢查初始數據
            count_result = session.execute(text("SELECT COUNT(*) FROM email_download_status"))
            initial_count = count_result.fetchone()[0]
            
            email_count_result = session.execute(text("SELECT COUNT(*) FROM emails"))
            email_count = email_count_result.fetchone()[0]
            
            logger.info(f"遷移驗證完成: 郵件總數={email_count}, 初始追蹤記錄={initial_count}")
            
            return True
            
    except Exception as e:
        logger.error(f"遷移驗證失敗: {e}")
        return False

if __name__ == "__main__":
    # 執行遷移
    if migrate_add_download_tracking():
        print("✅ 遷移成功")
        
        # 驗證遷移
        if verify_migration():
            print("✅ 遷移驗證通過")
        else:
            print("❌ 遷移驗證失敗")
    else:
        print("❌ 遷移失敗")
```

### Step 5: 創建遷移執行器 (0.5天)

**檔案位置**: `backend/shared/infrastructure/adapters/database/migration_runner.py`

```python
"""
資料庫遷移執行器
"""

import logging
from typing import List, Dict, Any
from datetime import datetime
import json

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.shared.infrastructure.adapters.database.migrations.add_download_tracking import (
    migrate_add_download_tracking, 
    rollback_download_tracking_migration,
    verify_migration
)

class MigrationRunner:
    """資料庫遷移執行器"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("MigrationRunner")
        self.migration_history = []
    
    def run_download_tracking_migration(self) -> Dict[str, Any]:
        """運行郵件下載追蹤遷移"""
        migration_info = {
            'migration_name': 'add_download_tracking',
            'started_at': datetime.utcnow().isoformat(),
            'success': False,
            'error_message': None,
            'verification_passed': False
        }
        
        try:
            self.logger.info("開始執行郵件下載追蹤遷移")
            
            # 執行遷移
            success = migrate_add_download_tracking()
            migration_info['success'] = success
            
            if success:
                # 驗證遷移
                verification_passed = verify_migration()
                migration_info['verification_passed'] = verification_passed
                
                if verification_passed:
                    self.logger.info("郵件下載追蹤遷移執行並驗證成功")
                else:
                    self.logger.error("郵件下載追蹤遷移驗證失敗")
            else:
                self.logger.error("郵件下載追蹤遷移執行失敗")
            
        except Exception as e:
            migration_info['error_message'] = str(e)
            self.logger.error(f"郵件下載追蹤遷移異常: {e}")
        
        finally:
            migration_info['completed_at'] = datetime.utcnow().isoformat()
            self.migration_history.append(migration_info)
        
        return migration_info
    
    def rollback_download_tracking_migration(self) -> Dict[str, Any]:
        """回滾郵件下載追蹤遷移"""
        rollback_info = {
            'rollback_name': 'rollback_download_tracking',
            'started_at': datetime.utcnow().isoformat(),
            'success': False,
            'error_message': None
        }
        
        try:
            self.logger.info("開始回滾郵件下載追蹤遷移")
            
            rollback_download_tracking_migration()
            rollback_info['success'] = True
            
            self.logger.info("郵件下載追蹤遷移回滾成功")
            
        except Exception as e:
            rollback_info['error_message'] = str(e)
            self.logger.error(f"郵件下載追蹤遷移回滾失敗: {e}")
        
        finally:
            rollback_info['completed_at'] = datetime.utcnow().isoformat()
            self.migration_history.append(rollback_info)
        
        return rollback_info
    
    def get_migration_history(self) -> List[Dict[str, Any]]:
        """獲取遷移歷史"""
        return self.migration_history.copy()
    
    def save_migration_log(self, log_file: str = "data/migration_log.json"):
        """保存遷移日誌"""
        try:
            import os
            from pathlib import Path
            
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(log_path, 'w', encoding='utf-8') as f:
                json.dump(self.migration_history, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"遷移日誌已保存: {log_path}")
            
        except Exception as e:
            self.logger.error(f"保存遷移日誌失敗: {e}")
```

---

## 🧪 測試策略

### 單元測試

**檔案位置**: `tests/unit/database/test_download_tracking_migration.py`

```python
"""
郵件下載追蹤遷移測試
"""

import pytest
from unittest.mock import Mock, patch
from backend.shared.infrastructure.adapters.database.migrations.add_download_tracking import (
    migrate_add_download_tracking,
    verify_migration,
    rollback_download_tracking_migration
)

class TestDownloadTrackingMigration:
    """郵件下載追蹤遷移測試"""
    
    def test_migration_creates_tables(self):
        """測試遷移創建表"""
        # 實現測試邏輯
        pass
    
    def test_migration_creates_indexes(self):
        """測試遷移創建索引"""
        # 實現測試邏輯
        pass
    
    def test_initial_data_creation(self):
        """測試初始數據創建"""
        # 實現測試邏輯
        pass
    
    def test_migration_rollback(self):
        """測試遷移回滾"""
        # 實現測試邏輯
        pass
```

### 整合測試

**檔案位置**: `tests/integration/database/test_download_tracking_integration.py`

```python
"""
郵件下載追蹤整合測試
"""

import pytest
from backend.shared.infrastructure.adapters.database.migration_runner import MigrationRunner

class TestDownloadTrackingIntegration:
    """郵件下載追蹤整合測試"""
    
    def test_full_migration_cycle(self):
        """測試完整遷移週期"""
        runner = MigrationRunner()
        
        # 執行遷移
        migration_result = runner.run_download_tracking_migration()
        assert migration_result['success'] == True
        assert migration_result['verification_passed'] == True
        
        # 回滾遷移
        rollback_result = runner.rollback_download_tracking_migration()
        assert rollback_result['success'] == True
```

---

## 📊 部署和驗證

### 部署腳本

**檔案位置**: `scripts/deploy_download_tracking_migration.py`

```python
"""
部署郵件下載追蹤遷移
"""

import sys
import logging
from pathlib import Path

# 添加項目根目錄到路徑
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from backend.shared.infrastructure.adapters.database.migration_runner import MigrationRunner

def main():
    """主函數"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    runner = MigrationRunner()
    
    try:
        # 執行遷移
        result = runner.run_download_tracking_migration()
        
        if result['success'] and result['verification_passed']:
            logger.info("✅ 郵件下載追蹤遷移部署成功")
            
            # 保存遷移日誌
            runner.save_migration_log()
            
            return 0
        else:
            logger.error("❌ 郵件下載追蹤遷移部署失敗")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 遷移部署異常: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
```

### 驗證檢查清單

#### 遷移後檢查項目
1. **表結構檢查**
   - [ ] `email_download_status` 表已創建
   - [ ] `email_download_retry_log` 表已創建
   - [ ] 所有欄位類型正確
   - [ ] 外鍵約束正確

2. **索引檢查**
   - [ ] 所有計劃的索引已創建
   - [ ] 索引命名符合規範
   - [ ] 查詢性能符合預期

3. **數據完整性檢查**
   - [ ] 現有郵件已有初始追蹤記錄
   - [ ] 外鍵約束正常工作
   - [ ] 沒有孤立數據

4. **系統兼容性檢查**
   - [ ] 現有功能不受影響
   - [ ] 應用程序正常啟動
   - [ ] 日誌無錯誤信息

---

## 🎯 成功標準

### 功能成功標準
- ✅ 所有資料庫表和索引創建成功
- ✅ 遷移腳本執行無錯誤
- ✅ 現有郵件數據完整遷移
- ✅ 回滾機制正常工作

### 性能成功標準
- ✅ 遷移過程 < 5分鐘（對於10萬封郵件）
- ✅ 查詢性能無明顯降低
- ✅ 資料庫文件大小增長 < 20%

### 質量成功標準
- ✅ 單元測試覆蓋率 > 90%
- ✅ 整合測試全部通過
- ✅ 代碼審查通過

---

## ⚠️ 風險評估

### 高風險項目
1. **資料庫損壞風險**
   - **緩解措施**: 自動備份 + 回滾機制
   - **應急計畫**: 立即回滾並恢復備份

2. **遷移時間過長**
   - **緩解措施**: 分批處理 + 進度監控
   - **應急計畫**: 優化 SQL 查詢

### 中風險項目
1. **外鍵約束衝突**
   - **緩解措施**: 詳細測試 + 驗證腳本
   - **應急計畫**: 手動修復數據

---

## 📋 交付清單

### 代碼文件
- [ ] `download_tracking_models.py` - SQLAlchemy 模型
- [ ] `download_tracking_models.py` - Pydantic 模型 
- [ ] `add_download_tracking.py` - 遷移腳本
- [ ] `migration_runner.py` - 遷移執行器

### 文檔
- [ ] 遷移指南
- [ ] 回滾程序
- [ ] 故障排除指南

### 測試
- [ ] 單元測試套件
- [ ] 整合測試套件
- [ ] 性能測試結果

### 部署資源
- [ ] 部署腳本
- [ ] 驗證腳本
- [ ] 監控配置

---

**Story 完成標準**: 所有驗收標準通過 ✅ 且交付清單項目完成 📋 且風險評估通過 ⚠️

**下一個 Story**: [Story 2: 核心追蹤服務實現](./email-download-tracking-story-2-core-tracker.md)