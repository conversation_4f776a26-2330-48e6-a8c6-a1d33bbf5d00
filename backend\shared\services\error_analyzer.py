"""
Epic-04 Story 4.3: 錯誤分析和重試決策實現
TDD GREEN PHASE: 最小實現通過測試

實現智能錯誤分析系統：
- 6種錯誤類型分類
- 智能重試決策邏輯
- 失敗模式分析
- 重試適宜性判斷
"""

import re
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum

from backend.shared.infrastructure.adapters.database.download_tracking_models import (
    RetryStrategy, RetryStatus, EmailDownloadRetryLogDB
)
from backend.shared.infrastructure.logging.logger_manager import LoggerManager


class ErrorCategory:
    """錯誤類別常數"""
    NETWORK_ERROR = "network"          # 網路錯誤 - 適合重試
    TIMEOUT_ERROR = "timeout"          # 超時錯誤 - 適合重試
    RATE_LIMIT_ERROR = "rate_limit"    # 頻率限制 - 延遲重試
    AUTH_ERROR = "authentication"      # 認證錯誤 - 不適合重試
    DATA_ERROR = "data_format"         # 資料格式錯誤 - 不適合重試
    SYSTEM_ERROR = "system"            # 系統錯誤 - 根據具體情況決定


@dataclass
class ErrorPattern:
    """錯誤模式"""
    error_type: str
    frequency: int
    success_rate: float
    avg_delay: int
    last_occurrence: datetime
    common_messages: List[str]


@dataclass
class RetryDecision:
    """重試決策"""
    should_retry: bool
    recommended_strategy: RetryStrategy
    recommended_delay: int
    reason: str
    confidence: float = 1.0
    max_attempts: int = 5


class ErrorAnalyzer:
    """
    錯誤分析器 - GREEN PHASE 實現
    
    提供智能錯誤分析和重試決策：
    - 錯誤類型分類
    - 重試適宜性判斷
    - 失敗模式分析
    - 智能重試決策
    """
    
    def __init__(self, database=None):
        """
        初始化錯誤分析器
        
        Args:
            database: 資料庫連接（可選）
        """
        self.database = database
        self.logger = LoggerManager().get_logger("ErrorAnalyzer")
        
        # 錯誤分類規則
        self._error_patterns = {
            ErrorCategory.NETWORK_ERROR: [
                r'connection.*timeout',
                r'network.*unreachable',
                r'connection.*refused',
                r'host.*not.*found',
                r'dns.*resolution.*failed',
                r'socket.*timeout'
            ],
            ErrorCategory.TIMEOUT_ERROR: [
                r'timeout',
                r'timed.*out',
                r'request.*timeout',
                r'operation.*timeout'
            ],
            ErrorCategory.RATE_LIMIT_ERROR: [
                r'too.*many.*requests',
                r'rate.*limit.*exceeded',
                r'quota.*exceeded',
                r'throttled',
                r'retry.*after'
            ],
            ErrorCategory.AUTH_ERROR: [
                r'authentication.*failed',
                r'invalid.*credentials',
                r'unauthorized',
                r'access.*denied',
                r'forbidden',
                r'login.*failed'
            ],
            ErrorCategory.DATA_ERROR: [
                r'invalid.*format',
                r'parse.*error',
                r'malformed.*data',
                r'json.*decode.*error',
                r'xml.*parse.*error',
                r'corrupt.*data'
            ]
        }
        
        # 重試配置
        self._retry_config = {
            ErrorCategory.NETWORK_ERROR: {
                'retryable': True,
                'max_attempts': 5,
                'strategy': RetryStrategy.EXPONENTIAL,
                'base_delay': 60
            },
            ErrorCategory.TIMEOUT_ERROR: {
                'retryable': True,
                'max_attempts': 3,
                'strategy': RetryStrategy.LINEAR,
                'base_delay': 120
            },
            ErrorCategory.RATE_LIMIT_ERROR: {
                'retryable': True,
                'max_attempts': 3,
                'strategy': RetryStrategy.FIXED_DELAY,
                'base_delay': 300
            },
            ErrorCategory.AUTH_ERROR: {
                'retryable': False,
                'max_attempts': 0,
                'strategy': None,
                'base_delay': 0
            },
            ErrorCategory.DATA_ERROR: {
                'retryable': False,
                'max_attempts': 0,
                'strategy': None,
                'base_delay': 0
            },
            ErrorCategory.SYSTEM_ERROR: {
                'retryable': True,
                'max_attempts': 2,
                'strategy': RetryStrategy.ADAPTIVE,
                'base_delay': 180
            }
        }
    
    def categorize_error(self, error: Exception) -> str:
        """
        分析錯誤類型 - GREEN PHASE 實現
        
        Args:
            error: 錯誤對象
            
        Returns:
            錯誤類別字符串
        """
        try:
            # 獲取錯誤信息
            error_message = str(error).lower()
            error_type = type(error).__name__.lower()
            
            # 基於錯誤類型名稱的快速分類
            if 'timeout' in error_type:
                return ErrorCategory.TIMEOUT_ERROR
            elif 'auth' in error_type or 'credential' in error_type:
                return ErrorCategory.AUTH_ERROR
            elif 'ratelimit' in error_type or 'quota' in error_type:
                return ErrorCategory.RATE_LIMIT_ERROR
            elif 'network' in error_type or 'connection' in error_type:
                return ErrorCategory.NETWORK_ERROR
            elif 'data' in error_type or 'format' in error_type or 'parse' in error_type:
                return ErrorCategory.DATA_ERROR
            
            # 基於錯誤信息的模式匹配
            for category, patterns in self._error_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, error_message, re.IGNORECASE):
                        self.logger.debug(f"錯誤分類: {error_type} -> {category} (匹配模式: {pattern})")
                        return category
            
            # 預設分類為系統錯誤
            self.logger.info(f"未知錯誤類型，歸類為系統錯誤: {error_type}")
            return ErrorCategory.SYSTEM_ERROR
            
        except Exception as e:
            self.logger.error(f"錯誤分類失敗: {e}")
            return ErrorCategory.SYSTEM_ERROR
    
    def should_retry(self, error: Exception, attempt_count: int) -> bool:
        """
        判斷是否應該重試 - GREEN PHASE 實現
        
        Args:
            error: 錯誤對象
            attempt_count: 已嘗試次數
            
        Returns:
            是否應該重試
        """
        try:
            # 分析錯誤類型
            error_category = self.categorize_error(error)
            
            # 獲取重試配置
            config = self._retry_config.get(error_category, self._retry_config[ErrorCategory.SYSTEM_ERROR])
            
            # 檢查是否可重試
            if not config['retryable']:
                self.logger.info(f"錯誤類型 {error_category} 不適合重試")
                return False
            
            # 檢查重試次數限制
            max_attempts = config['max_attempts']
            if attempt_count >= max_attempts:
                self.logger.info(f"重試次數 {attempt_count} 已達到最大限制 {max_attempts}")
                return False
            
            self.logger.debug(f"錯誤 {error_category} 可以重試 (嘗試 {attempt_count}/{max_attempts})")
            return True
            
        except Exception as e:
            self.logger.error(f"重試決策失敗: {e}")
            # 失敗時保守決策：少量重試
            return attempt_count < 2
    
    def analyze_failure_patterns(self, email_id: int) -> Dict[str, Any]:
        """
        分析失敗模式 - GREEN PHASE 實現
        
        Args:
            email_id: 郵件ID
            
        Returns:
            失敗模式分析結果
        """
        try:
            # GREEN PHASE: 模擬分析結果
            # 在 REFACTOR PHASE 中會整合真實的資料庫查詢
            
            patterns = {
                'email_id': email_id,
                'error_frequency': {
                    ErrorCategory.NETWORK_ERROR: 3,
                    ErrorCategory.TIMEOUT_ERROR: 2,
                    ErrorCategory.RATE_LIMIT_ERROR: 1,
                    ErrorCategory.AUTH_ERROR: 0,
                    ErrorCategory.DATA_ERROR: 0,
                    ErrorCategory.SYSTEM_ERROR: 1
                },
                'common_errors': [
                    'Connection timeout after 30 seconds',
                    'Network unreachable',
                    'Too many requests'
                ],
                'retry_success_rate': 0.7,
                'avg_retry_delay': 120,
                'last_failure_time': datetime.utcnow().isoformat(),
                'failure_trend': 'decreasing',  # increasing, stable, decreasing
                'recommended_strategy': RetryStrategy.EXPONENTIAL
            }
            
            self.logger.debug(f"分析郵件 {email_id} 的失敗模式: {patterns}")
            return patterns
            
        except Exception as e:
            self.logger.error(f"分析失敗模式失敗: {e}")
            return {
                'email_id': email_id,
                'error_frequency': {},
                'common_errors': [],
                'retry_success_rate': 0.0,
                'analysis_error': str(e)
            }
    
    def make_retry_decision(self, error: Exception, attempt_count: int, 
                          email_id: Optional[int] = None) -> RetryDecision:
        """
        做出綜合重試決策 - GREEN PHASE 實現
        
        Args:
            error: 錯誤對象
            attempt_count: 已嘗試次數
            email_id: 郵件ID（可選）
            
        Returns:
            重試決策
        """
        try:
            # 分析錯誤類型
            error_category = self.categorize_error(error)
            
            # 基本重試判斷
            should_retry = self.should_retry(error, attempt_count)
            
            # 獲取重試配置
            config = self._retry_config.get(error_category, self._retry_config[ErrorCategory.SYSTEM_ERROR])
            
            if not should_retry:
                return RetryDecision(
                    should_retry=False,
                    recommended_strategy=RetryStrategy.EXPONENTIAL,  # 預設值
                    recommended_delay=0,
                    reason=f"錯誤類型 {error_category} 不適合重試或已達最大重試次數",
                    confidence=1.0,
                    max_attempts=config['max_attempts']
                )
            
            # 分析失敗模式（如果提供了 email_id）
            if email_id:
                patterns = self.analyze_failure_patterns(email_id)
                success_rate = patterns.get('retry_success_rate', 0.5)
                
                # 根據成功率調整信心度
                confidence = min(1.0, success_rate + 0.3)
            else:
                confidence = 0.8  # 預設信心度
            
            # 推薦策略和延遲
            recommended_strategy = config['strategy']
            base_delay = config['base_delay']
            
            # 根據重試次數調整延遲
            if recommended_strategy == RetryStrategy.LINEAR:
                recommended_delay = base_delay * attempt_count
            elif recommended_strategy == RetryStrategy.EXPONENTIAL:
                recommended_delay = base_delay * (2 ** (attempt_count - 1))
            elif recommended_strategy == RetryStrategy.FIXED_DELAY:
                recommended_delay = base_delay
            else:  # ADAPTIVE
                if attempt_count <= 2:
                    recommended_delay = base_delay * attempt_count
                else:
                    recommended_delay = base_delay * (2 ** (attempt_count - 2))
            
            # 限制最大延遲
            recommended_delay = min(recommended_delay, 3600)  # 最多1小時
            
            decision = RetryDecision(
                should_retry=True,
                recommended_strategy=recommended_strategy,
                recommended_delay=recommended_delay,
                reason=f"錯誤類型 {error_category} 適合重試，使用 {recommended_strategy} 策略",
                confidence=confidence,
                max_attempts=config['max_attempts']
            )
            
            self.logger.info(f"重試決策: {decision}")
            return decision
            
        except Exception as e:
            self.logger.error(f"做出重試決策失敗: {e}")
            # 失敗時返回保守決策
            return RetryDecision(
                should_retry=attempt_count < 2,
                recommended_strategy=RetryStrategy.EXPONENTIAL,
                recommended_delay=60,
                reason=f"決策失敗，使用預設策略: {e}",
                confidence=0.5,
                max_attempts=2
            )
    
    def get_error_statistics(self, time_period: timedelta) -> Dict[str, Any]:
        """
        獲取錯誤統計 - GREEN PHASE 實現
        
        Args:
            time_period: 統計時間週期
            
        Returns:
            錯誤統計數據
        """
        try:
            # GREEN PHASE: 模擬統計數據
            # 在 REFACTOR PHASE 中會整合真實的資料庫查詢
            
            stats = {
                'period': str(time_period),
                'total_errors': 45,
                'error_distribution': {
                    ErrorCategory.NETWORK_ERROR: 20,
                    ErrorCategory.TIMEOUT_ERROR: 12,
                    ErrorCategory.RATE_LIMIT_ERROR: 5,
                    ErrorCategory.AUTH_ERROR: 3,
                    ErrorCategory.DATA_ERROR: 2,
                    ErrorCategory.SYSTEM_ERROR: 3
                },
                'retry_success_rates': {
                    ErrorCategory.NETWORK_ERROR: 0.8,
                    ErrorCategory.TIMEOUT_ERROR: 0.6,
                    ErrorCategory.RATE_LIMIT_ERROR: 0.9,
                    ErrorCategory.SYSTEM_ERROR: 0.5
                },
                'most_common_errors': [
                    'Connection timeout after 30 seconds',
                    'Network unreachable',
                    'Too many requests, retry after 60 seconds'
                ],
                'trends': {
                    'network_errors': 'stable',
                    'timeout_errors': 'increasing',
                    'rate_limit_errors': 'decreasing'
                },
                'generated_at': datetime.utcnow().isoformat()
            }
            
            self.logger.debug(f"錯誤統計: {stats}")
            return stats
            
        except Exception as e:
            self.logger.error(f"獲取錯誤統計失敗: {e}")
            return {
                'period': str(time_period),
                'total_errors': 0,
                'error_distribution': {},
                'statistics_error': str(e)
            }
    
    def validate_error_classification(self, test_cases: List[tuple]) -> Dict[str, float]:
        """
        驗證錯誤分類準確率 - GREEN PHASE 實現
        
        Args:
            test_cases: 測試用例列表 [(error, expected_category), ...]
            
        Returns:
            分類準確率統計
        """
        try:
            total_cases = len(test_cases)
            correct_classifications = 0
            category_stats = {}
            
            for error, expected_category in test_cases:
                predicted_category = self.categorize_error(error)
                
                if predicted_category == expected_category:
                    correct_classifications += 1
                
                # 統計每個類別的準確率
                if expected_category not in category_stats:
                    category_stats[expected_category] = {'total': 0, 'correct': 0}
                
                category_stats[expected_category]['total'] += 1
                if predicted_category == expected_category:
                    category_stats[expected_category]['correct'] += 1
            
            # 計算總體準確率
            overall_accuracy = correct_classifications / total_cases if total_cases > 0 else 0.0
            
            # 計算各類別準確率
            category_accuracies = {}
            for category, stats in category_stats.items():
                accuracy = stats['correct'] / stats['total'] if stats['total'] > 0 else 0.0
                category_accuracies[category] = accuracy
            
            results = {
                'overall_accuracy': overall_accuracy,
                'category_accuracies': category_accuracies,
                'total_cases': total_cases,
                'correct_classifications': correct_classifications,
                'validation_time': datetime.utcnow().isoformat()
            }
            
            self.logger.info(f"錯誤分類驗證結果: {results}")
            return results
            
        except Exception as e:
            self.logger.error(f"驗證錯誤分類失敗: {e}")
            return {
                'overall_accuracy': 0.0,
                'category_accuracies': {},
                'validation_error': str(e)
            }
    
    def get_retry_recommendations(self, error_history: List[Exception]) -> Dict[str, Any]:
        """
        基於錯誤歷史獲取重試建議 - GREEN PHASE 實現
        
        Args:
            error_history: 錯誤歷史列表
            
        Returns:
            重試建議
        """
        try:
            if not error_history:
                return {
                    'recommendation': 'no_history',
                    'suggested_strategy': RetryStrategy.EXPONENTIAL,
                    'reason': '無錯誤歷史，使用預設策略'
                }
            
            # 分析錯誤模式
            error_categories = [self.categorize_error(error) for error in error_history]
            category_counts = {}
            
            for category in error_categories:
                category_counts[category] = category_counts.get(category, 0) + 1
            
            # 找出最常見的錯誤類型
            most_common_category = max(category_counts.items(), key=lambda x: x[1])[0]
            
            # 基於最常見錯誤類型推薦策略
            config = self._retry_config.get(most_common_category, self._retry_config[ErrorCategory.SYSTEM_ERROR])
            
            recommendations = {
                'most_common_error': most_common_category,
                'error_distribution': category_counts,
                'recommended_strategy': config['strategy'],
                'recommended_max_attempts': config['max_attempts'],
                'recommended_base_delay': config['base_delay'],
                'reason': f"基於最常見錯誤類型 {most_common_category} 的建議",
                'confidence': 0.8
            }
            
            self.logger.debug(f"重試建議: {recommendations}")
            return recommendations
            
        except Exception as e:
            self.logger.error(f"獲取重試建議失敗: {e}")
            return {
                'recommendation': 'error',
                'suggested_strategy': RetryStrategy.EXPONENTIAL,
                'reason': f'分析失敗: {e}'
            }