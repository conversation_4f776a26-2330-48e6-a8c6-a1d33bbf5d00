"""
Unit tests for PTS Rename Upload Service

Tests the upload service functionality including file validation,
compression handling, and integration with existing infrastructure.
"""

import pytest
import tempfile
import zipfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from backend.pts_renamer.services.pts_rename_upload_service import PTSRenameUploadService
from backend.pts_renamer.models.pts_rename_models import PTSRenameConfig, PTSFileUploadInfo
from backend.pts_renamer.repositories.pts_rename_repository import IPTSRenameRepository
from backend.shared.infrastructure.adapters.file_staging_service import FileStagingService


class TestPTSRenameUploadService:
    """Test cases for PTS Rename Upload Service"""
    
    @pytest.fixture
    def mock_repository(self):
        """Mock repository for testing"""
        repo = Mock(spec=IPTSRenameRepository)
        repo.save_upload_record = AsyncMock(return_value=True)
        return repo
    
    @pytest.fixture
    def mock_staging_service(self):
        """Mock staging service for testing"""
        staging = Mock(spec=FileStagingService)
        return staging
    
    @pytest.fixture
    def test_config(self):
        """Test configuration"""
        return PTSRenameConfig(
            max_file_size_mb=10,
            max_files_per_upload=5,
            max_total_upload_size_mb=50,
            supported_formats=[".pts", ".zip", ".7z", ".rar"],
            enable_security_scanning=True,
            temp_storage_path="/tmp/pts_test"
        )
    
    @pytest.fixture
    def upload_service(self, mock_repository, mock_staging_service, test_config):
        """Upload service instance for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = test_config
            config.temp_storage_path = temp_dir
            
            service = PTSRenameUploadService(
                repository=mock_repository,
                staging_service=mock_staging_service,
                config=config,
                temp_storage_path=temp_dir
            )
            return service
    
    @pytest.mark.asyncio
    async def test_validate_uploaded_files_success(self, upload_service):
        """Test successful file validation"""
        # Create mock files
        mock_files = [
            Mock(size=1024 * 1024, filename="test.pts"),  # 1MB
            Mock(size=2 * 1024 * 1024, filename="test.zip")  # 2MB
        ]
        
        is_valid, message = await upload_service.validate_uploaded_files(mock_files)
        
        assert is_valid is True
        assert "Validation passed" in message
    
    @pytest.mark.asyncio
    async def test_validate_uploaded_files_too_many(self, upload_service):
        """Test validation failure for too many files"""
        # Create more files than allowed
        mock_files = [Mock(size=1024, filename=f"test{i}.pts") for i in range(10)]
        
        is_valid, message = await upload_service.validate_uploaded_files(mock_files)
        
        assert is_valid is False
        assert "Too many files" in message
    
    @pytest.mark.asyncio
    async def test_validate_uploaded_files_too_large(self, upload_service):
        """Test validation failure for files too large"""
        # Create files that exceed total size limit
        mock_files = [
            Mock(size=30 * 1024 * 1024, filename="large1.pts"),  # 30MB
            Mock(size=30 * 1024 * 1024, filename="large2.pts")   # 30MB
        ]
        
        is_valid, message = await upload_service.validate_uploaded_files(mock_files)
        
        assert is_valid is False
        assert "Total upload size too large" in message
    
    def test_validate_archive_header_zip(self, upload_service):
        """Test ZIP file header validation"""
        # Valid ZIP header
        zip_header = b'PK\x03\x04' + b'\x00' * 6
        assert upload_service._validate_archive_header(zip_header, "test.zip") is True
        
        # Invalid ZIP header
        invalid_header = b'INVALID' + b'\x00' * 3
        assert upload_service._validate_archive_header(invalid_header, "test.zip") is False
    
    def test_validate_archive_header_7z(self, upload_service):
        """Test 7Z file header validation"""
        # Valid 7Z header
        sevenzip_header = b'7z\xbc\xaf\x27\x1c' + b'\x00' * 4
        assert upload_service._validate_archive_header(sevenzip_header, "test.7z") is True
        
        # Invalid 7Z header
        invalid_header = b'INVALID' + b'\x00' * 3
        assert upload_service._validate_archive_header(invalid_header, "test.7z") is False
    
    def test_validate_single_pts_file_valid(self, upload_service):
        """Test validation of valid PTS file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.pts', delete=False) as f:
            f.write("Parameter,Test1,Limit1\n")
            f.write("Bin Definition,1,Pass\n")
            f.write("Test,Result,QA\n")
            temp_path = Path(f.name)
        
        try:
            assert upload_service._validate_single_pts_file(temp_path) is True
        finally:
            temp_path.unlink()
    
    def test_validate_single_pts_file_invalid(self, upload_service):
        """Test validation of invalid PTS file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.pts', delete=False) as f:
            f.write("This is not a PTS file\n")
            f.write("Just some random text\n")
            temp_path = Path(f.name)
        
        try:
            assert upload_service._validate_single_pts_file(temp_path) is False
        finally:
            temp_path.unlink()
    
    def test_validate_single_pts_file_empty(self, upload_service):
        """Test validation of empty PTS file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.pts', delete=False) as f:
            temp_path = Path(f.name)
        
        try:
            assert upload_service._validate_single_pts_file(temp_path) is False
        finally:
            temp_path.unlink()
    
    @pytest.mark.asyncio
    async def test_security_validation_safe_file(self, upload_service):
        """Test security validation for safe file"""
        filename = "test.pts"
        content_type = "text/plain"
        file_size = 1024
        checksum = "abc123"
        file_content = b"Parameter,Test1,Limit1\nBin Definition,1,Pass\n"
        
        validation = await upload_service._validate_file_security(
            filename, content_type, file_size, checksum, file_content
        )
        
        assert validation.is_safe is True
        assert len(validation.threats_detected) == 0
    
    @pytest.mark.asyncio
    async def test_security_validation_malicious_content(self, upload_service):
        """Test security validation for malicious content"""
        filename = "test.pts"
        content_type = "text/plain"
        file_size = 1024
        checksum = "abc123"
        file_content = b"<script>alert('malicious')</script>"
        
        validation = await upload_service._validate_file_security(
            filename, content_type, file_size, checksum, file_content
        )
        
        assert validation.is_safe is False
        assert len(validation.threats_detected) > 0
        assert any("script" in threat.lower() for threat in validation.threats_detected)
    
    @pytest.mark.asyncio
    async def test_security_validation_path_traversal(self, upload_service):
        """Test security validation for path traversal attack"""
        filename = "../../../etc/passwd"
        content_type = "text/plain"
        file_size = 1024
        checksum = "abc123"
        file_content = b"safe content"
        
        validation = await upload_service._validate_file_security(
            filename, content_type, file_size, checksum, file_content
        )
        
        assert validation.is_safe is False
        assert any("dangerous filename" in threat.lower() for threat in validation.threats_detected)
    
    @pytest.mark.asyncio
    async def test_security_validation_unsupported_extension(self, upload_service):
        """Test security validation for unsupported file extension"""
        filename = "test.exe"
        content_type = "application/octet-stream"
        file_size = 1024
        checksum = "abc123"
        file_content = b"safe content"
        
        validation = await upload_service._validate_file_security(
            filename, content_type, file_size, checksum, file_content
        )
        
        assert validation.is_safe is False
        assert any("unsupported file extension" in threat.lower() for threat in validation.threats_detected)
    
    def test_get_upload_info_existing(self, upload_service):
        """Test getting upload info for existing upload"""
        # Create a temporary upload directory
        upload_id = "test_upload_123"
        upload_dir = upload_service.temp_storage_path / upload_id
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # Create some test files
        (upload_dir / "test1.pts").write_text("test content 1")
        (upload_dir / "test2.pts").write_text("test content 2")
        (upload_dir / "other.txt").write_text("other content")
        
        try:
            info = upload_service.get_upload_info(upload_id)
            
            assert info is not None
            assert info['upload_id'] == upload_id
            assert info['file_count'] == 3
            assert info['pts_file_count'] == 2
            assert info['exists'] is True
            
        finally:
            # Cleanup
            import shutil
            shutil.rmtree(upload_dir)
    
    def test_get_upload_info_nonexistent(self, upload_service):
        """Test getting upload info for non-existent upload"""
        info = upload_service.get_upload_info("nonexistent_upload")
        assert info is None
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_uploads(self, upload_service):
        """Test cleanup of expired uploads"""
        # Create old upload directory
        old_upload_id = "pts_upload_old123"
        old_upload_dir = upload_service.temp_storage_path / old_upload_id
        old_upload_dir.mkdir(parents=True, exist_ok=True)
        (old_upload_dir / "test.pts").write_text("test")
        
        # Create recent upload directory
        new_upload_id = "pts_upload_new456"
        new_upload_dir = upload_service.temp_storage_path / new_upload_id
        new_upload_dir.mkdir(parents=True, exist_ok=True)
        (new_upload_dir / "test.pts").write_text("test")
        
        try:
            # Cleanup with very short retention (should clean old uploads)
            cleaned_count = await upload_service.cleanup_expired_uploads(max_age_hours=0)
            
            # Should have cleaned at least the old upload
            assert cleaned_count >= 0  # Depends on timing
            
        finally:
            # Cleanup any remaining directories
            import shutil
            for upload_dir in [old_upload_dir, new_upload_dir]:
                if upload_dir.exists():
                    shutil.rmtree(upload_dir)


if __name__ == "__main__":
    pytest.main([__file__])