"""
DownloadPerformanceMonitor - 下載性能監控和分析
Epic-02 Story 2.4: 下載性能監控和分析

延續Epic-01的卓越標準：
- 企業級性能監控
- 實時性能分析
- 記憶體效率優化
- 完整的性能指標
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from collections import defaultdict, deque
from threading import Lock
import statistics

from backend.shared.infrastructure.logging.logger_manager import LoggerManager


class PerformanceMetrics:
    """
    性能指標數據結構
    """
    
    def __init__(self):
        self.download_start_time: Optional[float] = None
        self.download_end_time: Optional[float] = None
        self.total_bytes: int = 0
        self.progress_updates: List[Dict[str, Any]] = []
        self.error_count: int = 0
        self.retry_count: int = 0
        self.peak_speed_bps: float = 0.0
        
    def add_progress_update(self, timestamp: float, bytes_downloaded: int, speed_bps: float):
        """添加進度更新"""
        self.progress_updates.append({
            'timestamp': timestamp,
            'bytes_downloaded': bytes_downloaded,
            'speed_bps': speed_bps
        })
        self.peak_speed_bps = max(self.peak_speed_bps, speed_bps)
    
    def get_duration(self) -> Optional[float]:
        """獲取下載持續時間"""
        if self.download_start_time and self.download_end_time:
            return self.download_end_time - self.download_start_time
        return None
    
    def get_average_speed(self) -> float:
        """獲取平均下載速度"""
        duration = self.get_duration()
        if duration and duration > 0:
            return self.total_bytes / duration
        return 0.0


class DownloadPerformanceMonitor:
    """
    下載性能監控器 - Epic-02 Story 2.4 完整實現
    
    功能：
    - 實時性能追蹤
    - 性能指標計算
    - 性能分析和報告
    - 異常檢測和告警
    """
    
    def __init__(self, max_history_size: int = 1000):
        """
        初始化性能監控器
        
        Args:
            max_history_size: 最大歷史記錄數量
        """
        self.logger = LoggerManager().get_logger("DownloadPerformanceMonitor")
        
        # 線程安全鎖
        self._lock = Lock()
        
        # 性能數據存儲
        self.metrics: Dict[int, PerformanceMetrics] = {}
        self.global_stats = {
            'total_downloads': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'total_bytes_downloaded': 0,
            'total_download_time': 0.0,
            'peak_concurrent_downloads': 0,
            'current_concurrent_downloads': 0
        }
        
        # 歷史記錄（使用deque實現固定大小的環形緩衝區）
        self.performance_history: deque = deque(maxlen=max_history_size)
        
        # 性能閾值設定
        self.performance_thresholds = {
            'max_download_time': 300.0,  # 5分鐘
            'min_speed_bps': 1024,  # 1KB/s
            'max_concurrent_downloads': 100
        }
        
        self.logger.info("DownloadPerformanceMonitor 已初始化")
    
    # ============================================================================
    # 性能追蹤核心方法
    # ============================================================================
    
    def track_download_start(self, email_id: int, expected_size: Optional[int] = None):
        """
        追蹤下載開始
        
        Args:
            email_id: 郵件ID
            expected_size: 預期檔案大小（可選）
        """
        try:
            with self._lock:
                current_time = time.time()
                
                # 創建性能指標記錄
                self.metrics[email_id] = PerformanceMetrics()
                self.metrics[email_id].download_start_time = current_time
                
                # 更新全局統計
                self.global_stats['current_concurrent_downloads'] += 1
                self.global_stats['peak_concurrent_downloads'] = max(
                    self.global_stats['peak_concurrent_downloads'],
                    self.global_stats['current_concurrent_downloads']
                )
                
                self.logger.debug(f"開始追蹤下載性能: email_id={email_id}, expected_size={expected_size}")
                
        except Exception as e:
            self.logger.error(f"追蹤下載開始失敗: email_id={email_id}, error={e}")
    
    def track_download_progress(self, email_id: int, bytes_downloaded: int, speed_bps: float):
        """
        追蹤下載進度
        
        Args:
            email_id: 郵件ID
            bytes_downloaded: 已下載位元組數
            speed_bps: 當前下載速度（位元組/秒）
        """
        try:
            with self._lock:
                if email_id not in self.metrics:
                    self.logger.warning(f"未找到下載性能記錄: email_id={email_id}")
                    return
                
                current_time = time.time()
                metrics = self.metrics[email_id]
                
                # 添加進度更新
                metrics.add_progress_update(current_time, bytes_downloaded, speed_bps)
                
                # 檢查性能閾值
                if speed_bps < self.performance_thresholds['min_speed_bps']:
                    self.logger.warning(f"下載速度過慢: email_id={email_id}, speed={speed_bps:.2f} B/s")
                
                self.logger.debug(f"更新下載進度: email_id={email_id}, bytes={bytes_downloaded}, speed={speed_bps:.2f} B/s")
                
        except Exception as e:
            self.logger.error(f"追蹤下載進度失敗: email_id={email_id}, error={e}")
    
    def track_download_completion(self, email_id: int, total_bytes: int, success: bool = True):
        """
        追蹤下載完成
        
        Args:
            email_id: 郵件ID
            total_bytes: 總下載位元組數
            success: 是否成功完成
        """
        try:
            with self._lock:
                if email_id not in self.metrics:
                    self.logger.warning(f"未找到下載性能記錄: email_id={email_id}")
                    return
                
                current_time = time.time()
                metrics = self.metrics[email_id]
                
                # 記錄完成時間和總位元組數
                metrics.download_end_time = current_time
                metrics.total_bytes = total_bytes
                
                # 計算性能指標
                duration = metrics.get_duration()
                average_speed = metrics.get_average_speed()
                
                # 更新全局統計
                self.global_stats['total_downloads'] += 1
                self.global_stats['current_concurrent_downloads'] -= 1
                
                if success:
                    self.global_stats['successful_downloads'] += 1
                    self.global_stats['total_bytes_downloaded'] += total_bytes
                    if duration:
                        self.global_stats['total_download_time'] += duration
                else:
                    self.global_stats['failed_downloads'] += 1
                    metrics.error_count += 1
                
                # 保存到歷史記錄
                self._save_to_history(email_id, metrics, success)
                
                # 檢查性能閾值
                if duration and duration > self.performance_thresholds['max_download_time']:
                    self.logger.warning(f"下載時間過長: email_id={email_id}, duration={duration:.2f}s")
                
                self.logger.info(
                    f"下載完成追蹤: email_id={email_id}, success={success}, "
                    f"bytes={total_bytes}, duration={duration:.2f}s, speed={average_speed:.2f} B/s"
                )
                
                # 清理當前記錄
                del self.metrics[email_id]
                
        except Exception as e:
            self.logger.error(f"追蹤下載完成失敗: email_id={email_id}, error={e}")
    
    def track_download_error(self, email_id: int, error_message: str):
        """
        追蹤下載錯誤
        
        Args:
            email_id: 郵件ID
            error_message: 錯誤信息
        """
        try:
            with self._lock:
                if email_id in self.metrics:
                    self.metrics[email_id].error_count += 1
                
                self.logger.warning(f"下載錯誤: email_id={email_id}, error={error_message}")
                
        except Exception as e:
            self.logger.error(f"追蹤下載錯誤失敗: email_id={email_id}, error={e}")
    
    def track_download_retry(self, email_id: int):
        """
        追蹤下載重試
        
        Args:
            email_id: 郵件ID
        """
        try:
            with self._lock:
                if email_id in self.metrics:
                    self.metrics[email_id].retry_count += 1
                
                self.logger.debug(f"下載重試: email_id={email_id}")
                
        except Exception as e:
            self.logger.error(f"追蹤下載重試失敗: email_id={email_id}, error={e}")
    
    # ============================================================================
    # 性能分析和報告
    # ============================================================================
    
    def get_performance_metrics(self, period: str = "hourly") -> Dict[str, Any]:
        """
        獲取性能指標
        
        Args:
            period: 統計週期 ("hourly", "daily", "weekly")
            
        Returns:
            性能指標字典
        """
        try:
            with self._lock:
                # 計算成功率
                total_downloads = self.global_stats['total_downloads']
                success_rate = 0.0
                if total_downloads > 0:
                    success_rate = (self.global_stats['successful_downloads'] / total_downloads) * 100
                
                # 計算平均下載速度
                average_download_speed = 0.0
                if self.global_stats['total_download_time'] > 0:
                    average_download_speed = (
                        self.global_stats['total_bytes_downloaded'] / 
                        self.global_stats['total_download_time']
                    )
                
                # 計算平均下載時間
                average_completion_time = 0.0
                successful_downloads = self.global_stats['successful_downloads']
                if successful_downloads > 0:
                    average_completion_time = (
                        self.global_stats['total_download_time'] / successful_downloads
                    )
                
                return {
                    'period': period,
                    'timestamp': datetime.utcnow().isoformat(),
                    'total_downloads': total_downloads,
                    'successful_downloads': self.global_stats['successful_downloads'],
                    'failed_downloads': self.global_stats['failed_downloads'],
                    'success_rate': round(success_rate, 2),
                    'average_download_speed': round(average_download_speed, 2),
                    'average_completion_time': round(average_completion_time, 2),
                    'peak_concurrent_downloads': self.global_stats['peak_concurrent_downloads'],
                    'current_concurrent_downloads': self.global_stats['current_concurrent_downloads'],
                    'total_bytes_downloaded': self.global_stats['total_bytes_downloaded']
                }
                
        except Exception as e:
            self.logger.error(f"獲取性能指標失敗: {e}")
            return {
                'period': period,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def get_detailed_analytics(self, last_hours: int = 24) -> Dict[str, Any]:
        """
        獲取詳細分析數據
        
        Args:
            last_hours: 分析最近小時數
            
        Returns:
            詳細分析結果
        """
        try:
            cutoff_time = time.time() - (last_hours * 3600)
            
            # 過濾歷史記錄
            recent_history = [
                record for record in self.performance_history
                if record['completion_time'] >= cutoff_time
            ]
            
            if not recent_history:
                return {
                    'period_hours': last_hours,
                    'message': 'No data available for the specified period'
                }
            
            # 計算統計數據
            download_times = [r['duration'] for r in recent_history if r['duration']]
            download_speeds = [r['average_speed'] for r in recent_history if r['average_speed']]
            file_sizes = [r['total_bytes'] for r in recent_history]
            
            analytics = {
                'period_hours': last_hours,
                'sample_count': len(recent_history),
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # 下載時間統計
            if download_times:
                analytics['download_time_stats'] = {
                    'min': round(min(download_times), 2),
                    'max': round(max(download_times), 2),
                    'mean': round(statistics.mean(download_times), 2),
                    'median': round(statistics.median(download_times), 2)
                }
                if len(download_times) > 1:
                    analytics['download_time_stats']['stddev'] = round(statistics.stdev(download_times), 2)
            
            # 下載速度統計
            if download_speeds:
                analytics['download_speed_stats'] = {
                    'min': round(min(download_speeds), 2),
                    'max': round(max(download_speeds), 2),
                    'mean': round(statistics.mean(download_speeds), 2),
                    'median': round(statistics.median(download_speeds), 2)
                }
                if len(download_speeds) > 1:
                    analytics['download_speed_stats']['stddev'] = round(statistics.stdev(download_speeds), 2)
            
            # 檔案大小統計
            if file_sizes:
                analytics['file_size_stats'] = {
                    'min': min(file_sizes),
                    'max': max(file_sizes),
                    'mean': round(statistics.mean(file_sizes), 2),
                    'median': round(statistics.median(file_sizes), 2),
                    'total': sum(file_sizes)
                }
            
            return analytics
            
        except Exception as e:
            self.logger.error(f"獲取詳細分析失敗: {e}")
            return {
                'period_hours': last_hours,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    # ============================================================================
    # 輔助方法
    # ============================================================================
    
    def _save_to_history(self, email_id: int, metrics: PerformanceMetrics, success: bool):
        """
        保存性能記錄到歷史
        
        Args:
            email_id: 郵件ID
            metrics: 性能指標
            success: 是否成功
        """
        try:
            record = {
                'email_id': email_id,
                'completion_time': time.time(),
                'success': success,
                'duration': metrics.get_duration(),
                'total_bytes': metrics.total_bytes,
                'average_speed': metrics.get_average_speed(),
                'peak_speed': metrics.peak_speed_bps,
                'progress_updates_count': len(metrics.progress_updates),
                'error_count': metrics.error_count,
                'retry_count': metrics.retry_count
            }
            
            self.performance_history.append(record)
            
        except Exception as e:
            self.logger.error(f"保存歷史記錄失敗: {e}")
    
    def get_current_downloads(self) -> List[Dict[str, Any]]:
        """
        獲取當前進行中的下載
        
        Returns:
            當前下載列表
        """
        try:
            with self._lock:
                current_downloads = []
                current_time = time.time()
                
                for email_id, metrics in self.metrics.items():
                    if metrics.download_start_time:
                        elapsed_time = current_time - metrics.download_start_time
                        current_speed = 0.0
                        
                        if metrics.progress_updates:
                            latest_update = metrics.progress_updates[-1]
                            current_speed = latest_update['speed_bps']
                        
                        current_downloads.append({
                            'email_id': email_id,
                            'elapsed_time': round(elapsed_time, 2),
                            'total_bytes': metrics.total_bytes,
                            'current_speed': round(current_speed, 2),
                            'peak_speed': round(metrics.peak_speed_bps, 2),
                            'progress_updates': len(metrics.progress_updates),
                            'error_count': metrics.error_count,
                            'retry_count': metrics.retry_count
                        })
                
                return current_downloads
                
        except Exception as e:
            self.logger.error(f"獲取當前下載失敗: {e}")
            return []
    
    def reset_statistics(self):
        """重置統計數據"""
        try:
            with self._lock:
                self.global_stats = {
                    'total_downloads': 0,
                    'successful_downloads': 0,
                    'failed_downloads': 0,
                    'total_bytes_downloaded': 0,
                    'total_download_time': 0.0,
                    'peak_concurrent_downloads': 0,
                    'current_concurrent_downloads': len(self.metrics)
                }
                self.performance_history.clear()
                
                self.logger.info("性能統計數據已重置")
                
        except Exception as e:
            self.logger.error(f"重置統計數據失敗: {e}")
    
    def get_monitor_health(self) -> Dict[str, Any]:
        """
        獲取監控器健康狀態
        
        Returns:
            健康狀態信息
        """
        try:
            with self._lock:
                return {
                    'status': 'healthy',
                    'active_downloads': len(self.metrics),
                    'history_records': len(self.performance_history),
                    'memory_usage': 'normal',
                    'performance_thresholds': self.performance_thresholds.copy(),
                    'timestamp': datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"獲取監控器健康狀態失敗: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }