# Email Download Status Tracking System Architecture

## 🏗️ 系統概況

### 項目範圍
基於現有郵件處理系統，設計並實現郵件下載狀態追蹤機制，提供完整的下載生命週期監控、智能重試機制和可視化管理界面。

### 核心目標
- **即時追蹤**: 提供郵件下載過程的精細狀態監控
- **自動重試**: 實現智能的下載失敗重試機制
- **可視化管理**: 提供 Web 界面進行下載狀態管理和監控
- **無縫整合**: 與現有系統完全兼容，不影響現有功能

---

## 🎯 架構設計原則

### 技術約束
- **現有技術棧**: Python Flask + SQLAlchemy + Vue.js + Element UI
- **資料庫**: SQLite (email_inbox.db)
- **任務隊列**: Dramatiq (已有基礎設施)
- **相容性**: 與現有 EmailSyncService 完全兼容

### 設計原則
1. **最小侵入**: 對現有系統的修改最小化
2. **向後兼容**: 不破壞現有功能
3. **可擴展性**: 為未來功能擴展預留接口
4. **性能優先**: 不影響現有郵件處理性能

---

## 🗄️ 資料庫架構設計

### 新增資料表結構

#### EmailDownloadStatus 表
```sql
CREATE TABLE email_download_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email_id INTEGER NOT NULL,
    
    -- 下載狀態追蹤
    status VARCHAR(20) DEFAULT 'pending',  -- pending, downloading, completed, failed, retry_scheduled
    download_attempt INTEGER DEFAULT 1,   -- 當前嘗試次數
    max_retry_count INTEGER DEFAULT 3,    -- 最大重試次數
    
    -- 時間追蹤
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,                   -- 開始下載時間
    completed_at DATETIME,                 -- 完成時間
    last_retry_at DATETIME,                -- 上次重試時間
    next_retry_at DATETIME,                -- 下次重試時間
    
    -- 錯誤追蹤
    error_type VARCHAR(50),                -- 錯誤類型 (connection, authentication, server_error, timeout)
    error_message TEXT,                    -- 詳細錯誤信息
    error_details TEXT,                    -- JSON 格式的詳細錯誤信息
    
    -- 下載詳情
    download_size_bytes INTEGER,           -- 下載大小
    download_duration_seconds REAL,       -- 下載耗時
    server_response_code VARCHAR(10),      -- 服務器響應代碼
    
    -- 重試策略
    retry_strategy VARCHAR(20) DEFAULT 'exponential', -- exponential, linear, fixed
    retry_interval_seconds INTEGER DEFAULT 60,        -- 重試間隔
    
    -- 外鍵約束
    FOREIGN KEY (email_id) REFERENCES emails (id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_email_download_status_email_id ON email_download_status (email_id);
CREATE INDEX idx_email_download_status_status ON email_download_status (status);
CREATE INDEX idx_email_download_status_next_retry ON email_download_status (next_retry_at);
CREATE INDEX idx_email_download_status_created ON email_download_status (created_at);
```

#### EmailDownloadRetryLog 表
```sql
CREATE TABLE email_download_retry_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    download_status_id INTEGER NOT NULL,
    
    -- 重試記錄
    retry_attempt INTEGER NOT NULL,
    retry_reason VARCHAR(100),
    attempted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 重試結果
    result VARCHAR(20),  -- success, failed, timeout, server_error
    error_message TEXT,
    duration_seconds REAL,
    
    -- 重試配置
    retry_strategy_used VARCHAR(20),
    retry_interval_used INTEGER,
    
    -- 外鍵約束
    FOREIGN KEY (download_status_id) REFERENCES email_download_status (id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_retry_log_download_status ON email_download_retry_log (download_status_id);
CREATE INDEX idx_retry_log_attempted_at ON email_download_retry_log (attempted_at);
```

### 資料庫遷移策略

#### 遷移腳本設計
```python
# backend/shared/infrastructure/adapters/database/migrations/add_download_tracking.py

from sqlalchemy import text
from backend.shared.infrastructure.adapters.database.models import Base, db_engine
import logging

logger = logging.getLogger(__name__)

def migrate_add_download_tracking():
    """添加郵件下載追蹤表"""
    try:
        with db_engine.get_session() as session:
            # 檢查表是否已存在
            result = session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='email_download_status'
            """))
            
            if result.fetchone():
                logger.info("email_download_status 表已存在，跳過遷移")
                return True
            
            # 創建 email_download_status 表
            session.execute(text("""
                CREATE TABLE email_download_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email_id INTEGER NOT NULL,
                    status VARCHAR(20) DEFAULT 'pending',
                    download_attempt INTEGER DEFAULT 1,
                    max_retry_count INTEGER DEFAULT 3,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    started_at DATETIME,
                    completed_at DATETIME,
                    last_retry_at DATETIME,
                    next_retry_at DATETIME,
                    error_type VARCHAR(50),
                    error_message TEXT,
                    error_details TEXT,
                    download_size_bytes INTEGER,
                    download_duration_seconds REAL,
                    server_response_code VARCHAR(10),
                    retry_strategy VARCHAR(20) DEFAULT 'exponential',
                    retry_interval_seconds INTEGER DEFAULT 60,
                    FOREIGN KEY (email_id) REFERENCES emails (id) ON DELETE CASCADE
                )
            """))
            
            # 創建索引
            session.execute(text("CREATE INDEX idx_email_download_status_email_id ON email_download_status (email_id)"))
            session.execute(text("CREATE INDEX idx_email_download_status_status ON email_download_status (status)"))
            session.execute(text("CREATE INDEX idx_email_download_status_next_retry ON email_download_status (next_retry_at)"))
            session.execute(text("CREATE INDEX idx_email_download_status_created ON email_download_status (created_at)"))
            
            # 創建 email_download_retry_log 表
            session.execute(text("""
                CREATE TABLE email_download_retry_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    download_status_id INTEGER NOT NULL,
                    retry_attempt INTEGER NOT NULL,
                    retry_reason VARCHAR(100),
                    attempted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    result VARCHAR(20),
                    error_message TEXT,
                    duration_seconds REAL,
                    retry_strategy_used VARCHAR(20),
                    retry_interval_used INTEGER,
                    FOREIGN KEY (download_status_id) REFERENCES email_download_status (id) ON DELETE CASCADE
                )
            """))
            
            # 創建索引
            session.execute(text("CREATE INDEX idx_retry_log_download_status ON email_download_retry_log (download_status_id)"))
            session.execute(text("CREATE INDEX idx_retry_log_attempted_at ON email_download_retry_log (attempted_at)"))
            
            session.commit()
            logger.info("郵件下載追蹤表創建成功")
            
            # 為現有郵件創建初始狀態記錄
            create_initial_download_status(session)
            
            return True
            
    except Exception as e:
        logger.error(f"遷移失敗: {e}")
        return False

def create_initial_download_status(session):
    """為現有郵件創建初始下載狀態"""
    try:
        # 為所有現有郵件創建 'completed' 狀態記錄
        session.execute(text("""
            INSERT INTO email_download_status (email_id, status, download_attempt, completed_at)
            SELECT id, 'completed', 1, created_at 
            FROM emails 
            WHERE id NOT IN (SELECT email_id FROM email_download_status)
        """))
        
        session.commit()
        logger.info("為現有郵件創建初始下載狀態完成")
        
    except Exception as e:
        logger.error(f"創建初始下載狀態失敗: {e}")
```

---

## 🔧 服務層架構設計

### EmailDownloadTracker 服務

#### 核心服務類設計
```python
# backend/email/services/email_download_tracker.py

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from enum import Enum
import json
import math

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.shared.infrastructure.adapters.database.models import db_engine
from backend.email.models.download_tracking_models import (
    EmailDownloadStatusDB, EmailDownloadRetryLogDB, DownloadStatus, RetryStrategy
)

class EmailDownloadTracker:
    """郵件下載狀態追蹤服務"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("EmailDownloadTracker")
        
    def start_download_tracking(self, email_id: int, max_retry_count: int = 3) -> int:
        """開始下載追蹤
        
        Args:
            email_id: 郵件 ID
            max_retry_count: 最大重試次數
            
        Returns:
            下載狀態記錄 ID
        """
        try:
            with db_engine.get_session() as session:
                # 檢查是否已有追蹤記錄
                existing = session.query(EmailDownloadStatusDB).filter_by(email_id=email_id).first()
                
                if existing:
                    # 重置現有記錄
                    existing.status = DownloadStatus.DOWNLOADING
                    existing.download_attempt += 1
                    existing.started_at = datetime.utcnow()
                    existing.error_type = None
                    existing.error_message = None
                    session.commit()
                    
                    self.logger.info(f"重置郵件下載追蹤: email_id={email_id}, attempt={existing.download_attempt}")
                    return existing.id
                else:
                    # 創建新記錄
                    download_status = EmailDownloadStatusDB(
                        email_id=email_id,
                        status=DownloadStatus.DOWNLOADING,
                        max_retry_count=max_retry_count,
                        started_at=datetime.utcnow()
                    )
                    session.add(download_status)
                    session.commit()
                    
                    self.logger.info(f"開始郵件下載追蹤: email_id={email_id}, tracking_id={download_status.id}")
                    return download_status.id
                    
        except Exception as e:
            self.logger.error(f"開始下載追蹤失敗: email_id={email_id}, error={e}")
            raise
    
    def update_download_success(self, tracking_id: int, download_size: int = None, 
                              duration: float = None) -> bool:
        """更新下載成功狀態"""
        try:
            with db_engine.get_session() as session:
                status = session.query(EmailDownloadStatusDB).filter_by(id=tracking_id).first()
                
                if not status:
                    self.logger.warning(f"找不到下載追蹤記錄: tracking_id={tracking_id}")
                    return False
                
                status.status = DownloadStatus.COMPLETED
                status.completed_at = datetime.utcnow()
                status.download_size_bytes = download_size
                status.download_duration_seconds = duration
                
                session.commit()
                
                self.logger.info(f"下載成功: tracking_id={tracking_id}, email_id={status.email_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新下載成功狀態失敗: tracking_id={tracking_id}, error={e}")
            return False
    
    def update_download_failure(self, tracking_id: int, error_type: str, 
                              error_message: str, error_details: Dict = None) -> bool:
        """更新下載失敗狀態並安排重試"""
        try:
            with db_engine.get_session() as session:
                status = session.query(EmailDownloadStatusDB).filter_by(id=tracking_id).first()
                
                if not status:
                    self.logger.warning(f"找不到下載追蹤記錄: tracking_id={tracking_id}")
                    return False
                
                # 更新失敗信息
                status.error_type = error_type
                status.error_message = error_message
                status.error_details = json.dumps(error_details) if error_details else None
                status.last_retry_at = datetime.utcnow()
                
                # 記錄重試日誌
                retry_log = EmailDownloadRetryLogDB(
                    download_status_id=tracking_id,
                    retry_attempt=status.download_attempt,
                    retry_reason=error_type,
                    result='failed',
                    error_message=error_message,
                    retry_strategy_used=status.retry_strategy
                )
                session.add(retry_log)
                
                # 判斷是否需要重試
                if status.download_attempt < status.max_retry_count:
                    # 計算下次重試時間
                    next_retry_interval = self._calculate_retry_interval(
                        status.retry_strategy, 
                        status.download_attempt, 
                        status.retry_interval_seconds
                    )
                    
                    status.status = DownloadStatus.RETRY_SCHEDULED
                    status.next_retry_at = datetime.utcnow() + timedelta(seconds=next_retry_interval)
                    status.download_attempt += 1
                    
                    self.logger.warning(f"下載失敗，安排重試: tracking_id={tracking_id}, attempt={status.download_attempt}, next_retry={status.next_retry_at}")
                else:
                    # 超過最大重試次數
                    status.status = DownloadStatus.FAILED
                    
                    self.logger.error(f"下載最終失敗: tracking_id={tracking_id}, email_id={status.email_id}, attempts={status.download_attempt}")
                
                session.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"更新下載失敗狀態失敗: tracking_id={tracking_id}, error={e}")
            return False
    
    def get_pending_retries(self, limit: int = 50) -> List[Dict[str, Any]]:
        """獲取待重試的下載任務"""
        try:
            with db_engine.get_session() as session:
                current_time = datetime.utcnow()
                
                pending_retries = session.query(EmailDownloadStatusDB).filter(
                    EmailDownloadStatusDB.status == DownloadStatus.RETRY_SCHEDULED,
                    EmailDownloadStatusDB.next_retry_at <= current_time
                ).limit(limit).all()
                
                results = []
                for status in pending_retries:
                    results.append({
                        'tracking_id': status.id,
                        'email_id': status.email_id,
                        'attempt': status.download_attempt,
                        'next_retry_at': status.next_retry_at.isoformat(),
                        'error_type': status.error_type,
                        'error_message': status.error_message
                    })
                
                self.logger.info(f"獲取待重試任務: {len(results)} 個")
                return results
                
        except Exception as e:
            self.logger.error(f"獲取待重試任務失敗: {e}")
            return []
    
    def get_download_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """獲取下載統計信息"""
        try:
            with db_engine.get_session() as session:
                since_time = datetime.utcnow() - timedelta(hours=hours)
                
                # 基本統計
                total_downloads = session.query(EmailDownloadStatusDB).filter(
                    EmailDownloadStatusDB.created_at >= since_time
                ).count()
                
                successful_downloads = session.query(EmailDownloadStatusDB).filter(
                    EmailDownloadStatusDB.created_at >= since_time,
                    EmailDownloadStatusDB.status == DownloadStatus.COMPLETED
                ).count()
                
                failed_downloads = session.query(EmailDownloadStatusDB).filter(
                    EmailDownloadStatusDB.created_at >= since_time,
                    EmailDownloadStatusDB.status == DownloadStatus.FAILED
                ).count()
                
                pending_retries = session.query(EmailDownloadStatusDB).filter(
                    EmailDownloadStatusDB.status == DownloadStatus.RETRY_SCHEDULED
                ).count()
                
                # 錯誤類型統計
                error_stats = session.execute(text("""
                    SELECT error_type, COUNT(*) as count
                    FROM email_download_status 
                    WHERE created_at >= :since_time AND error_type IS NOT NULL
                    GROUP BY error_type
                    ORDER BY count DESC
                """), {'since_time': since_time}).fetchall()
                
                return {
                    'total_downloads': total_downloads,
                    'successful_downloads': successful_downloads,
                    'failed_downloads': failed_downloads,
                    'pending_retries': pending_retries,
                    'success_rate': (successful_downloads / total_downloads * 100) if total_downloads > 0 else 0,
                    'error_types': [{'type': row[0], 'count': row[1]} for row in error_stats],
                    'period_hours': hours,
                    'last_updated': datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"獲取下載統計失敗: {e}")
            return {}
    
    def _calculate_retry_interval(self, strategy: str, attempt: int, base_interval: int) -> int:
        """計算重試間隔"""
        if strategy == RetryStrategy.EXPONENTIAL:
            # 指數退避: base * (2 ^ attempt)
            return min(base_interval * (2 ** (attempt - 1)), 3600)  # 最大1小時
        elif strategy == RetryStrategy.LINEAR:
            # 線性增長: base * attempt
            return min(base_interval * attempt, 1800)  # 最大30分鐘
        else:  # FIXED
            # 固定間隔
            return base_interval
```

### EmailDownloadRetryService 服務

#### 重試服務設計
```python
# backend/email/services/email_download_retry_service.py

from typing import Dict, Any, List, Optional
import asyncio
from datetime import datetime

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.email.services.email_download_tracker import EmailDownloadTracker
from backend.shared.infrastructure.adapters.email_inbox.email_sync_service import EmailSyncService
from backend.tasks.services.dramatiq_tasks import dramatiq_actor

class EmailDownloadRetryService:
    """郵件下載重試服務"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("EmailDownloadRetryService")
        self.download_tracker = EmailDownloadTracker()
        self.email_sync_service = None
        
    async def initialize(self):
        """初始化服務"""
        try:
            self.email_sync_service = EmailSyncService()
            await self.email_sync_service.initialize_email_reader()
            self.logger.info("EmailDownloadRetryService 初始化完成")
        except Exception as e:
            self.logger.error(f"EmailDownloadRetryService 初始化失敗: {e}")
            raise
    
    async def process_pending_retries(self, batch_size: int = 10) -> Dict[str, Any]:
        """處理待重試的下載任務"""
        try:
            # 獲取待重試任務
            pending_retries = self.download_tracker.get_pending_retries(limit=batch_size)
            
            if not pending_retries:
                return {
                    'success': True,
                    'message': '沒有待重試任務',
                    'processed_count': 0
                }
            
            success_count = 0
            failed_count = 0
            results = []
            
            for retry_task in pending_retries:
                try:
                    result = await self._retry_email_download(retry_task)
                    
                    if result['success']:
                        success_count += 1
                    else:
                        failed_count += 1
                    
                    results.append({
                        'email_id': retry_task['email_id'],
                        'tracking_id': retry_task['tracking_id'],
                        'success': result['success'],
                        'message': result['message']
                    })
                    
                except Exception as e:
                    failed_count += 1
                    error_msg = f"重試下載異常: {e}"
                    self.logger.error(error_msg)
                    
                    results.append({
                        'email_id': retry_task['email_id'],
                        'tracking_id': retry_task['tracking_id'],
                        'success': False,
                        'message': error_msg
                    })
            
            self.logger.info(f"重試處理完成: 成功={success_count}, 失敗={failed_count}")
            
            return {
                'success': True,
                'message': f'重試處理完成: 成功={success_count}, 失敗={failed_count}',
                'processed_count': len(pending_retries),
                'success_count': success_count,
                'failed_count': failed_count,
                'results': results
            }
            
        except Exception as e:
            self.logger.error(f"處理重試任務失敗: {e}")
            return {
                'success': False,
                'message': f'處理重試任務失敗: {e}',
                'processed_count': 0
            }
    
    async def _retry_email_download(self, retry_task: Dict[str, Any]) -> Dict[str, Any]:
        """重試單個郵件下載"""
        tracking_id = retry_task['tracking_id']
        email_id = retry_task['email_id']
        
        try:
            # 更新狀態為正在重試
            self.download_tracker.start_download_tracking(email_id)
            
            # 執行郵件重新下載（這裡簡化為重新同步）
            sync_result = await self.email_sync_service.sync_emails_once(max_emails=1)
            
            if sync_result['success'] and sync_result['data']['sync_count'] > 0:
                # 下載成功
                self.download_tracker.update_download_success(tracking_id)
                
                return {
                    'success': True,
                    'message': '重試下載成功'
                }
            else:
                # 下載失敗
                error_msg = sync_result.get('message', '重試下載失敗')
                self.download_tracker.update_download_failure(
                    tracking_id, 
                    'retry_failed', 
                    error_msg
                )
                
                return {
                    'success': False,
                    'message': error_msg
                }
                
        except Exception as e:
            # 重試異常
            self.download_tracker.update_download_failure(
                tracking_id, 
                'retry_exception', 
                str(e)
            )
            
            return {
                'success': False,
                'message': f'重試下載異常: {e}'
            }
    
    def trigger_manual_retry(self, email_ids: List[int]) -> Dict[str, Any]:
        """觸發手動重試"""
        try:
            results = []
            
            for email_id in email_ids:
                # 創建新的重試記錄
                tracking_id = self.download_tracker.start_download_tracking(email_id)
                
                # 安排重試任務
                retry_email_download_task.send(email_id, tracking_id)
                
                results.append({
                    'email_id': email_id,
                    'tracking_id': tracking_id,
                    'status': 'scheduled'
                })
            
            self.logger.info(f"手動重試已安排: {len(email_ids)} 個郵件")
            
            return {
                'success': True,
                'message': f'已安排 {len(email_ids)} 個郵件重試',
                'results': results
            }
            
        except Exception as e:
            self.logger.error(f"觸發手動重試失敗: {e}")
            return {
                'success': False,
                'message': f'觸發手動重試失敗: {e}'
            }

# Dramatiq 任務
@dramatiq_actor(queue_name="email_retry_queue", max_retries=0)
def retry_email_download_task(email_id: int, tracking_id: int):
    """重試郵件下載任務"""
    import asyncio
    
    retry_service = EmailDownloadRetryService()
    
    async def run_retry():
        await retry_service.initialize()
        return await retry_service._retry_email_download({
            'email_id': email_id,
            'tracking_id': tracking_id
        })
    
    return asyncio.run(run_retry())
```

---

## 🌐 API 層架構設計

### REST API 端點設計

#### 郵件下載狀態管理 API
```python
# backend/email/routes/download_status_api.py

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
from datetime import datetime

from backend.email.services.email_download_tracker import EmailDownloadTracker
from backend.email.services.email_download_retry_service import EmailDownloadRetryService
from backend.email.models.download_tracking_models import (
    DownloadStatusResponse, DownloadStatisticsResponse,
    ManualRetryRequest, ManualRetryResponse
)

router = APIRouter(prefix="/api/v1/email/download", tags=["email-download"])

# 依賴注入
async def get_download_tracker() -> EmailDownloadTracker:
    return EmailDownloadTracker()

async def get_retry_service() -> EmailDownloadRetryService:
    service = EmailDownloadRetryService()
    await service.initialize()
    return service

@router.get("/status/{email_id}", response_model=DownloadStatusResponse)
async def get_email_download_status(
    email_id: int,
    tracker: EmailDownloadTracker = Depends(get_download_tracker)
):
    """獲取特定郵件的下載狀態"""
    try:
        status = tracker.get_email_download_status(email_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="郵件下載狀態未找到")
        
        return DownloadStatusResponse(**status)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取下載狀態失敗: {e}")

@router.get("/status", response_model=List[DownloadStatusResponse])
async def get_download_status_list(
    status: Optional[str] = Query(None, description="狀態過濾"),
    limit: int = Query(50, ge=1, le=100, description="返回數量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    tracker: EmailDownloadTracker = Depends(get_download_tracker)
):
    """獲取下載狀態列表"""
    try:
        statuses = tracker.get_download_status_list(
            status_filter=status,
            limit=limit,
            offset=offset
        )
        
        return [DownloadStatusResponse(**status) for status in statuses]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取下載狀態列表失敗: {e}")

@router.get("/statistics", response_model=DownloadStatisticsResponse)
async def get_download_statistics(
    hours: int = Query(24, ge=1, le=168, description="統計時間範圍（小時）"),
    tracker: EmailDownloadTracker = Depends(get_download_tracker)
):
    """獲取下載統計信息"""
    try:
        stats = tracker.get_download_statistics(hours=hours)
        return DownloadStatisticsResponse(**stats)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取下載統計失敗: {e}")

@router.post("/retry/manual", response_model=ManualRetryResponse)
async def trigger_manual_retry(
    request: ManualRetryRequest,
    retry_service: EmailDownloadRetryService = Depends(get_retry_service)
):
    """觸發手動重試"""
    try:
        result = retry_service.trigger_manual_retry(request.email_ids)
        
        return ManualRetryResponse(
            success=result['success'],
            message=result['message'],
            retry_results=result.get('results', [])
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"觸發手動重試失敗: {e}")

@router.post("/retry/process")
async def process_pending_retries(
    batch_size: int = Query(10, ge=1, le=50, description="批處理大小"),
    retry_service: EmailDownloadRetryService = Depends(get_retry_service)
):
    """處理待重試任務"""
    try:
        result = await retry_service.process_pending_retries(batch_size=batch_size)
        
        return {
            "success": result['success'],
            "message": result['message'],
            "processed_count": result['processed_count'],
            "success_count": result.get('success_count', 0),
            "failed_count": result.get('failed_count', 0)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"處理重試任務失敗: {e}")

@router.delete("/status/{email_id}")
async def reset_download_status(
    email_id: int,
    tracker: EmailDownloadTracker = Depends(get_download_tracker)
):
    """重置郵件下載狀態"""
    try:
        success = tracker.reset_download_status(email_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="郵件下載狀態未找到")
        
        return {"success": True, "message": "下載狀態已重置"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置下載狀態失敗: {e}")
```

#### 數據模型定義
```python
# backend/email/models/download_tracking_models.py

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class DownloadStatus(str, Enum):
    PENDING = "pending"
    DOWNLOADING = "downloading"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRY_SCHEDULED = "retry_scheduled"

class RetryStrategy(str, Enum):
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    FIXED = "fixed"

class DownloadStatusResponse(BaseModel):
    id: int
    email_id: int
    status: DownloadStatus
    download_attempt: int
    max_retry_count: int
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    last_retry_at: Optional[datetime]
    next_retry_at: Optional[datetime]
    error_type: Optional[str]
    error_message: Optional[str]
    download_size_bytes: Optional[int]
    download_duration_seconds: Optional[float]
    retry_strategy: RetryStrategy

class DownloadStatisticsResponse(BaseModel):
    total_downloads: int
    successful_downloads: int
    failed_downloads: int
    pending_retries: int
    success_rate: float
    error_types: List[Dict[str, Any]]
    period_hours: int
    last_updated: str

class ManualRetryRequest(BaseModel):
    email_ids: List[int]
    reason: Optional[str] = "manual_retry"

class RetryResult(BaseModel):
    email_id: int
    tracking_id: int
    status: str

class ManualRetryResponse(BaseModel):
    success: bool
    message: str
    retry_results: List[RetryResult]

# SQLAlchemy 模型
from sqlalchemy import Column, Integer, String, DateTime, Float, Text, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from backend.shared.infrastructure.adapters.database.models import Base

class EmailDownloadStatusDB(Base):
    __tablename__ = 'email_download_status'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    email_id = Column(Integer, ForeignKey('emails.id'), nullable=False, index=True)
    
    status = Column(SQLEnum(DownloadStatus), default=DownloadStatus.PENDING, nullable=False)
    download_attempt = Column(Integer, default=1, nullable=False)
    max_retry_count = Column(Integer, default=3, nullable=False)
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    last_retry_at = Column(DateTime)
    next_retry_at = Column(DateTime, index=True)
    
    error_type = Column(String(50))
    error_message = Column(Text)
    error_details = Column(Text)
    
    download_size_bytes = Column(Integer)
    download_duration_seconds = Column(Float)
    server_response_code = Column(String(10))
    
    retry_strategy = Column(SQLEnum(RetryStrategy), default=RetryStrategy.EXPONENTIAL)
    retry_interval_seconds = Column(Integer, default=60)
    
    # 關聯
    email = relationship("EmailDB", back_populates="download_status")
    retry_logs = relationship("EmailDownloadRetryLogDB", back_populates="download_status", cascade="all, delete-orphan")

class EmailDownloadRetryLogDB(Base):
    __tablename__ = 'email_download_retry_log'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    download_status_id = Column(Integer, ForeignKey('email_download_status.id'), nullable=False, index=True)
    
    retry_attempt = Column(Integer, nullable=False)
    retry_reason = Column(String(100))
    attempted_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    result = Column(String(20))
    error_message = Column(Text)
    duration_seconds = Column(Float)
    
    retry_strategy_used = Column(String(20))
    retry_interval_used = Column(Integer)
    
    # 關聯
    download_status = relationship("EmailDownloadStatusDB", back_populates="retry_logs")

# 更新現有 EmailDB 模型
# 在 backend/shared/infrastructure/adapters/database/models.py 中添加:
# download_status = relationship("EmailDownloadStatusDB", back_populates="email", cascade="all, delete-orphan")
```

---

## 🎨 前端架構設計

### Vue.js 組件設計

#### 郵件下載狀態儀表板組件
```vue
<!-- frontend/email/templates/components/EmailDownloadDashboard.vue -->
<template>
  <div class="email-download-dashboard">
    <!-- 統計卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.total_downloads }}</div>
              <div class="stat-label">總下載數</div>
            </div>
            <i class="el-icon-download stat-icon"></i>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card success">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.successful_downloads }}</div>
              <div class="stat-label">成功下載</div>
            </div>
            <i class="el-icon-check stat-icon"></i>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card warning">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.pending_retries }}</div>
              <div class="stat-label">待重試</div>
            </div>
            <i class="el-icon-refresh stat-icon"></i>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="stat-card danger">
            <div class="stat-content">
              <div class="stat-number">{{ statistics.failed_downloads }}</div>
              <div class="stat-label">失敗下載</div>
            </div>
            <i class="el-icon-close stat-icon"></i>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 成功率顯示 -->
    <div class="success-rate-section">
      <el-card>
        <div slot="header">
          <span>下載成功率</span>
          <el-badge :value="statistics.success_rate.toFixed(1) + '%'" class="success-rate-badge">
            <el-progress 
              :percentage="statistics.success_rate" 
              :color="getSuccessRateColor(statistics.success_rate)"
              :stroke-width="20">
            </el-progress>
          </el-badge>
        </div>
      </el-card>
    </div>
    
    <!-- 錯誤類型統計 -->
    <div class="error-types-section">
      <el-card>
        <div slot="header">
          <span>錯誤類型分佈</span>
        </div>
        <div class="error-types-chart">
          <div v-for="errorType in statistics.error_types" :key="errorType.type" class="error-type-item">
            <span class="error-type-name">{{ getErrorTypeLabel(errorType.type) }}</span>
            <el-progress 
              :percentage="(errorType.count / statistics.failed_downloads * 100)" 
              :show-text="false">
            </el-progress>
            <span class="error-type-count">{{ errorType.count }}</span>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 操作按鈕 -->
    <div class="action-buttons">
      <el-button 
        type="primary" 
        icon="el-icon-refresh" 
        @click="refreshData"
        :loading="loading">
        刷新數據
      </el-button>
      
      <el-button 
        type="warning" 
        icon="el-icon-refresh-right" 
        @click="processRetries"
        :loading="processingRetries">
        處理重試
      </el-button>
      
      <el-button 
        type="info" 
        icon="el-icon-view" 
        @click="showDetailDialog = true">
        查看詳情
      </el-button>
    </div>
    
    <!-- 詳情對話框 -->
    <el-dialog 
      title="下載狀態詳情" 
      :visible.sync="showDetailDialog"
      width="80%"
      :before-close="handleDetailClose">
      <email-download-detail-table 
        v-if="showDetailDialog"
        @manual-retry="handleManualRetry">
      </email-download-detail-table>
    </el-dialog>
  </div>
</template>

<script>
import EmailDownloadDetailTable from './EmailDownloadDetailTable.vue'

export default {
  name: 'EmailDownloadDashboard',
  components: {
    EmailDownloadDetailTable
  },
  data() {
    return {
      statistics: {
        total_downloads: 0,
        successful_downloads: 0,
        failed_downloads: 0,
        pending_retries: 0,
        success_rate: 0,
        error_types: []
      },
      loading: false,
      processingRetries: false,
      showDetailDialog: false,
      refreshInterval: null
    }
  },
  mounted() {
    this.loadStatistics()
    this.startAutoRefresh()
  },
  beforeDestroy() {
    this.stopAutoRefresh()
  },
  methods: {
    async loadStatistics() {
      this.loading = true
      try {
        const response = await this.$http.get('/api/v1/email/download/statistics')
        this.statistics = response.data
      } catch (error) {
        this.$message.error('載入統計數據失敗: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    async refreshData() {
      await this.loadStatistics()
      this.$message.success('數據已刷新')
    },
    
    async processRetries() {
      this.processingRetries = true
      try {
        const response = await this.$http.post('/api/v1/email/download/retry/process')
        
        if (response.data.success) {
          this.$message.success(
            `重試處理完成: 成功=${response.data.success_count}, 失敗=${response.data.failed_count}`
          )
          await this.loadStatistics()
        } else {
          this.$message.error('重試處理失敗: ' + response.data.message)
        }
      } catch (error) {
        this.$message.error('重試處理失敗: ' + error.message)
      } finally {
        this.processingRetries = false
      }
    },
    
    getSuccessRateColor(rate) {
      if (rate >= 95) return '#67c23a'
      if (rate >= 85) return '#e6a23c'
      return '#f56c6c'
    },
    
    getErrorTypeLabel(type) {
      const labels = {
        'connection': '連接錯誤',
        'authentication': '認證失敗',
        'server_error': '服務器錯誤',
        'timeout': '超時',
        'unknown': '未知錯誤'
      }
      return labels[type] || type
    },
    
    startAutoRefresh() {
      this.refreshInterval = setInterval(() => {
        this.loadStatistics()
      }, 30000) // 30秒刷新一次
    },
    
    stopAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval)
        this.refreshInterval = null
      }
    },
    
    handleDetailClose() {
      this.showDetailDialog = false
    },
    
    async handleManualRetry(emailIds) {
      try {
        const response = await this.$http.post('/api/v1/email/download/retry/manual', {
          email_ids: emailIds
        })
        
        if (response.data.success) {
          this.$message.success('手動重試已安排')
          await this.loadStatistics()
        } else {
          this.$message.error('手動重試失敗: ' + response.data.message)
        }
      } catch (error) {
        this.$message.error('手動重試失敗: ' + error.message)
      }
    }
  }
}
</script>

<style scoped>
.email-download-dashboard {
  padding: 20px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-card.success {
  border-left: 4px solid #67c23a;
}

.stat-card.warning {
  border-left: 4px solid #e6a23c;
}

.stat-card.danger {
  border-left: 4px solid #f56c6c;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  color: #dcdfe6;
}

.success-rate-section,
.error-types-section {
  margin-bottom: 20px;
}

.success-rate-badge {
  float: right;
}

.error-types-chart {
  max-height: 300px;
  overflow-y: auto;
}

.error-type-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.error-type-name {
  width: 120px;
  font-size: 14px;
}

.error-type-count {
  width: 50px;
  text-align: right;
  font-weight: bold;
  margin-left: 10px;
}

.action-buttons {
  text-align: center;
  margin-top: 20px;
}

.action-buttons .el-button {
  margin: 0 10px;
}
</style>
```

#### 郵件下載詳情表格組件
```vue
<!-- frontend/email/templates/components/EmailDownloadDetailTable.vue -->
<template>
  <div class="email-download-detail">
    <!-- 過濾器 -->
    <div class="filter-section">
      <el-form :inline="true" size="small">
        <el-form-item label="狀態:">
          <el-select v-model="filters.status" placeholder="選擇狀態" clearable @change="loadData">
            <el-option label="全部" value=""></el-option>
            <el-option label="下載中" value="downloading"></el-option>
            <el-option label="已完成" value="completed"></el-option>
            <el-option label="失敗" value="failed"></el-option>
            <el-option label="待重試" value="retry_scheduled"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="loadData">查詢</el-button>
          <el-button type="success" icon="el-icon-refresh" @click="batchRetry" :disabled="selectedRows.length === 0">
            批量重試 ({{ selectedRows.length }})
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 數據表格 -->
    <el-table 
      :data="tableData" 
      v-loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      style="width: 100%">
      
      <el-table-column type="selection" width="55"></el-table-column>
      
      <el-table-column prop="email_id" label="郵件ID" width="80"></el-table-column>
      
      <el-table-column label="狀態" width="120">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="download_attempt" label="嘗試次數" width="90">
        <template slot-scope="scope">
          {{ scope.row.download_attempt }} / {{ scope.row.max_retry_count }}
        </template>
      </el-table-column>
      
      <el-table-column label="創建時間" width="160">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      
      <el-table-column label="完成時間" width="160">
        <template slot-scope="scope">
          {{ scope.row.completed_at ? formatDateTime(scope.row.completed_at) : '-' }}
        </template>
      </el-table-column>
      
      <el-table-column label="下次重試" width="160">
        <template slot-scope="scope">
          {{ scope.row.next_retry_at ? formatDateTime(scope.row.next_retry_at) : '-' }}
        </template>
      </el-table-column>
      
      <el-table-column label="錯誤信息" min-width="200">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.error_message" :content="scope.row.error_message" placement="top">
            <span class="error-message">{{ scope.row.error_message.substring(0, 50) }}...</span>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button 
            size="mini" 
            type="warning" 
            @click="retryEmail(scope.row)"
            :disabled="scope.row.status === 'downloading'">
            重試
          </el-button>
          
          <el-button 
            size="mini" 
            type="danger" 
            @click="resetStatus(scope.row)">
            重置
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分頁 -->
    <div class="pagination-section">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.current"
        :page-sizes="[20, 50, 100]"
        :page-size="pagination.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmailDownloadDetailTable',
  data() {
    return {
      tableData: [],
      selectedRows: [],
      loading: false,
      filters: {
        status: ''
      },
      pagination: {
        current: 1,
        size: 20,
        total: 0
      }
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        const params = {
          limit: this.pagination.size,
          offset: (this.pagination.current - 1) * this.pagination.size
        }
        
        if (this.filters.status) {
          params.status = this.filters.status
        }
        
        const response = await this.$http.get('/api/v1/email/download/status', { params })
        this.tableData = response.data
        
        // 獲取總數（這裡簡化處理，實際應該從API返回）
        this.pagination.total = response.data.length >= this.pagination.size ? 
          (this.pagination.current * this.pagination.size + 1) : 
          ((this.pagination.current - 1) * this.pagination.size + response.data.length)
          
      } catch (error) {
        this.$message.error('載入數據失敗: ' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.loadData()
    },
    
    handleCurrentChange(page) {
      this.pagination.current = page
      this.loadData()
    },
    
    async retryEmail(row) {
      try {
        const response = await this.$http.post('/api/v1/email/download/retry/manual', {
          email_ids: [row.email_id]
        })
        
        if (response.data.success) {
          this.$message.success('重試已安排')
          this.loadData()
          this.$emit('manual-retry', [row.email_id])
        } else {
          this.$message.error('重試失敗: ' + response.data.message)
        }
      } catch (error) {
        this.$message.error('重試失敗: ' + error.message)
      }
    },
    
    async batchRetry() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('請選擇要重試的郵件')
        return
      }
      
      const emailIds = this.selectedRows.map(row => row.email_id)
      
      try {
        const response = await this.$http.post('/api/v1/email/download/retry/manual', {
          email_ids: emailIds
        })
        
        if (response.data.success) {
          this.$message.success(`已安排 ${emailIds.length} 個郵件重試`)
          this.loadData()
          this.$emit('manual-retry', emailIds)
        } else {
          this.$message.error('批量重試失敗: ' + response.data.message)
        }
      } catch (error) {
        this.$message.error('批量重試失敗: ' + error.message)
      }
    },
    
    async resetStatus(row) {
      try {
        await this.$confirm('確定要重置此郵件的下載狀態嗎？', '確認', {
          confirmButtonText: '確定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await this.$http.delete(`/api/v1/email/download/status/${row.email_id}`)
        
        if (response.data.success) {
          this.$message.success('狀態已重置')
          this.loadData()
        } else {
          this.$message.error('重置失敗: ' + response.data.message)
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('重置失敗: ' + error.message)
        }
      }
    },
    
    getStatusTagType(status) {
      const types = {
        'pending': 'info',
        'downloading': 'primary',
        'completed': 'success',
        'failed': 'danger',
        'retry_scheduled': 'warning'
      }
      return types[status] || 'info'
    },
    
    getStatusLabel(status) {
      const labels = {
        'pending': '待處理',
        'downloading': '下載中',
        'completed': '已完成',
        'failed': '失敗',
        'retry_scheduled': '待重試'
      }
      return labels[status] || status
    },
    
    formatDateTime(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleString('zh-TW')
    }
  }
}
</script>

<style scoped>
.email-download-detail {
  padding: 20px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.error-message {
  color: #f56c6c;
  cursor: help;
}

.pagination-section {
  margin-top: 20px;
  text-align: center;
}
</style>
```

### 前端路由整合

#### Flask 路由整合
```python
# frontend/email/routes/email_routes.py 中添加

@email_bp.route('/download-tracking')
def download_tracking():
    """郵件下載追蹤頁面"""
    return render_template('email/download_tracking.html',
                         title='郵件下載追蹤',
                         api_base_url='/api/v1/email/download')
```

#### HTML 模板
```html
<!-- frontend/email/templates/download_tracking.html -->
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - 郵件處理系統</title>
    
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    
    <style>
        body {
            margin: 0;
            font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        }
        
        .content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>{{ title }}</h1>
                <p>監控郵件下載狀態，管理重試機制和查看詳細統計信息</p>
            </div>
            
            <div class="content">
                <email-download-dashboard></email-download-dashboard>
            </div>
        </div>
    </div>

    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- Element UI JS -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- Axios -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <script>
        // 註冊組件
        Vue.component('email-download-dashboard', {
            template: `<!-- EmailDownloadDashboard 組件內容 -->`
        });
        
        Vue.component('email-download-detail-table', {
            template: `<!-- EmailDownloadDetailTable 組件內容 -->`
        });
        
        // 創建 Vue 實例
        new Vue({
            el: '#app',
            data: {
                apiBaseUrl: '{{ api_base_url }}'
            },
            created() {
                // 設置 axios 基礎配置
                this.$http = axios.create({
                    baseURL: this.apiBaseUrl,
                    timeout: 10000
                });
            }
        });
    </script>
</body>
</html>
```

---

## 🔄 系統整合設計

### 與現有 EmailSyncService 整合

#### 修改 EmailSyncService 集成下載追蹤
```python
# 在 backend/shared/infrastructure/adapters/email_inbox/email_sync_service.py 中修改

from backend.email.services.email_download_tracker import EmailDownloadTracker

class EmailSyncService:
    def __init__(self, database: EmailDatabase = None):
        # ... 現有初始化代碼 ...
        
        # 添加下載追蹤器
        self.download_tracker = EmailDownloadTracker()
    
    async def sync_emails_once(self, max_emails: int = 100) -> Dict[str, Any]:
        """執行一次郵件同步（集成下載追蹤）"""
        # ... 現有代碼 ...
        
        try:
            # 連接到 POP3 服務器
            if not await self.email_reader.connect():
                raise Exception("無法連接到 POP3 服務器")
            
            try:
                # 讀取郵件
                emails = await self.email_reader.read_emails(count=max_emails)
                
                # ... 現有郵件處理代碼 ...
                
                for i, email in enumerate(emails):
                    tracking_id = None
                    try:
                        # 開始下載追蹤
                        tracking_id = self.download_tracker.start_download_tracking(
                            email_id=None,  # 此時還沒有 email_id
                            max_retry_count=3
                        )
                        
                        # 儲存郵件到資料庫
                        email_id = self.database.save_email(email)
                        
                        if email_id:
                            # 更新追蹤記錄的 email_id
                            self.download_tracker.update_email_id(tracking_id, email_id)
                            
                            # 標記下載成功
                            self.download_tracker.update_download_success(
                                tracking_id,
                                download_size=len(email.body.encode('utf-8')) if email.body else 0,
                                duration=None  # 可以記錄實際下載時間
                            )
                            
                            sync_count += 1
                            # ... 其他處理邏輯 ...
                            
                        else:
                            # 下載失敗
                            if tracking_id:
                                self.download_tracker.update_download_failure(
                                    tracking_id,
                                    'save_failed',
                                    'Database save returned None'
                                )
                            sync_errors += 1
                            
                    except Exception as e:
                        # 下載異常
                        if tracking_id:
                            self.download_tracker.update_download_failure(
                                tracking_id,
                                'download_exception',
                                str(e),
                                {'email_index': i, 'total_emails': len(emails)}
                            )
                        sync_errors += 1
                        # ... 現有錯誤處理 ...
                
                # ... 其餘現有代碼 ...
```

### Dramatiq 任務整合

#### 定期重試任務
```python
# backend/tasks/email_download_tasks.py

import dramatiq
from backend.email.services.email_download_retry_service import EmailDownloadRetryService
from backend.shared.infrastructure.logging.logger_manager import LoggerManager

logger = LoggerManager().get_logger("EmailDownloadTasks")

@dramatiq.actor(queue_name="email_download_queue", max_retries=0)
def process_email_download_retries():
    """處理郵件下載重試任務"""
    try:
        import asyncio
        
        async def run_retry_processing():
            retry_service = EmailDownloadRetryService()
            await retry_service.initialize()
            
            result = await retry_service.process_pending_retries(batch_size=20)
            
            logger.info(f"重試任務處理完成: {result}")
            return result
        
        return asyncio.run(run_retry_processing())
        
    except Exception as e:
        logger.error(f"重試任務處理失敗: {e}")
        raise

@dramatiq.actor(queue_name="email_download_queue", max_retries=2)
def single_email_download_retry(email_id: int, tracking_id: int):
    """單個郵件下載重試任務"""
    try:
        import asyncio
        
        async def run_single_retry():
            retry_service = EmailDownloadRetryService()
            await retry_service.initialize()
            
            result = await retry_service._retry_email_download({
                'email_id': email_id,
                'tracking_id': tracking_id
            })
            
            logger.info(f"單個郵件重試完成: email_id={email_id}, result={result}")
            return result
        
        return asyncio.run(run_single_retry())
        
    except Exception as e:
        logger.error(f"單個郵件重試失敗: email_id={email_id}, error={e}")
        raise

# 定期任務調度
@dramatiq.actor(queue_name="scheduler_queue")
def schedule_retry_processing():
    """調度重試處理任務"""
    try:
        # 每5分鐘執行一次重試處理
        process_email_download_retries.send()
        logger.info("重試處理任務已調度")
        
    except Exception as e:
        logger.error(f"調度重試處理任務失敗: {e}")
```

#### 在現有 dramatiq_tasks.py 中添加調度
```python
# backend/tasks/services/dramatiq_tasks.py 中添加

from backend.tasks.email_download_tasks import schedule_retry_processing

# 在適當位置添加定期調度
@dramatiq_actor(queue_name="scheduler_queue")
def run_periodic_tasks():
    """運行定期任務"""
    try:
        # ... 現有定期任務 ...
        
        # 添加下載重試處理
        schedule_retry_processing.send()
        
    except Exception as e:
        logger.error(f"定期任務執行失敗: {e}")
```

---

## 📊 監控與報告設計

### 監控指標定義

#### 關鍵性能指標 (KPIs)
1. **下載成功率**: 成功下載數 / 總下載數 × 100%
2. **平均重試次數**: 總重試次數 / 失敗下載數
3. **平均下載時間**: 總下載時間 / 成功下載數
4. **錯誤分佈**: 各類錯誤的百分比分佈
5. **重試效率**: 重試成功數 / 重試總數 × 100%

#### 監控數據收集
```python
# backend/email/services/email_download_monitor.py

from typing import Dict, Any, List
from datetime import datetime, timedelta
import json

from backend.shared.infrastructure.logging.logger_manager import LoggerManager
from backend.email.services.email_download_tracker import EmailDownloadTracker

class EmailDownloadMonitor:
    """郵件下載監控服務"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("EmailDownloadMonitor")
        self.tracker = EmailDownloadTracker()
    
    def generate_daily_report(self, date: datetime = None) -> Dict[str, Any]:
        """生成日報告"""
        if not date:
            date = datetime.now().date()
        
        start_time = datetime.combine(date, datetime.min.time())
        end_time = start_time + timedelta(days=1)
        
        try:
            # 基礎統計
            stats = self.tracker.get_download_statistics(hours=24)
            
            # 詳細分析
            hourly_stats = self._get_hourly_breakdown(start_time, end_time)
            retry_analysis = self._get_retry_analysis(start_time, end_time)
            performance_metrics = self._get_performance_metrics(start_time, end_time)
            
            report = {
                'report_type': 'daily',
                'report_date': date.isoformat(),
                'generated_at': datetime.utcnow().isoformat(),
                'summary': {
                    'total_downloads': stats['total_downloads'],
                    'successful_downloads': stats['successful_downloads'],
                    'failed_downloads': stats['failed_downloads'],
                    'success_rate': stats['success_rate'],
                    'pending_retries': stats['pending_retries']
                },
                'hourly_breakdown': hourly_stats,
                'retry_analysis': retry_analysis,
                'performance_metrics': performance_metrics,
                'error_distribution': stats['error_types'],
                'recommendations': self._generate_recommendations(stats)
            }
            
            # 保存報告
            self._save_report(report)
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成日報告失敗: {e}")
            return {}
    
    def _get_hourly_breakdown(self, start_time: datetime, end_time: datetime) -> List[Dict]:
        """獲取小時級別的統計數據"""
        # 實現小時級別的數據統計
        # 這裡簡化實現
        return []
    
    def _get_retry_analysis(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """重試分析"""
        # 實現重試分析邏輯
        return {
            'total_retries': 0,
            'successful_retries': 0,
            'retry_success_rate': 0,
            'avg_retries_per_email': 0
        }
    
    def _get_performance_metrics(self, start_time: datetime, end_time: datetime) -> Dict[str, Any]:
        """性能指標"""
        return {
            'avg_download_time': 0,
            'max_download_time': 0,
            'min_download_time': 0,
            'avg_download_size': 0
        }
    
    def _generate_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """生成改進建議"""
        recommendations = []
        
        if stats['success_rate'] < 95:
            recommendations.append("下載成功率低於95%，建議檢查網絡連接和服務器配置")
        
        if stats['pending_retries'] > 10:
            recommendations.append("待重試任務過多，建議增加重試處理頻率")
        
        # 根據錯誤類型給出建議
        for error_type in stats['error_types']:
            if error_type['type'] == 'timeout' and error_type['count'] > 5:
                recommendations.append("超時錯誤較多，建議增加超時時間或檢查網絡狀況")
        
        return recommendations
    
    def _save_report(self, report: Dict[str, Any]):
        """保存報告到文件系統"""
        try:
            import os
            from pathlib import Path
            
            reports_dir = Path("reports/email_download")
            reports_dir.mkdir(parents=True, exist_ok=True)
            
            filename = f"daily_report_{report['report_date']}.json"
            filepath = reports_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"報告已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存報告失敗: {e}")
```

### 監控 API 設計

#### 監控數據 API
```python
# backend/email/routes/download_monitoring_api.py

from fastapi import APIRouter, HTTPException, Query
from datetime import datetime, date
from typing import Optional

from backend.email.services.email_download_monitor import EmailDownloadMonitor

router = APIRouter(prefix="/api/v1/email/download/monitoring", tags=["email-download-monitoring"])

@router.get("/report/daily")
async def get_daily_report(
    report_date: Optional[date] = Query(None, description="報告日期，默認為今天")
):
    """獲取日報告"""
    try:
        monitor = EmailDownloadMonitor()
        report = monitor.generate_daily_report(report_date)
        
        if not report:
            raise HTTPException(status_code=404, detail="報告生成失敗")
        
        return report
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取日報告失敗: {e}")

@router.get("/metrics/realtime")
async def get_realtime_metrics():
    """獲取實時監控指標"""
    try:
        monitor = EmailDownloadMonitor()
        
        # 獲取最近1小時的統計
        stats = monitor.tracker.get_download_statistics(hours=1)
        
        # 計算實時指標
        realtime_metrics = {
            'current_time': datetime.utcnow().isoformat(),
            'downloads_last_hour': stats['total_downloads'],
            'success_rate_last_hour': stats['success_rate'],
            'pending_retries': stats['pending_retries'],
            'active_downloads': 0,  # 可以從狀態表查詢正在下載的數量
            'system_health': 'healthy' if stats['success_rate'] > 95 else 'warning'
        }
        
        return realtime_metrics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取實時指標失敗: {e}")

@router.get("/alerts")
async def get_download_alerts(
    hours: int = Query(24, ge=1, le=168, description="檢查時間範圍（小時）")
):
    """獲取下載相關告警"""
    try:
        monitor = EmailDownloadMonitor()
        stats = monitor.tracker.get_download_statistics(hours=hours)
        
        alerts = []
        
        # 檢查各種告警條件
        if stats['success_rate'] < 90:
            alerts.append({
                'level': 'critical',
                'type': 'low_success_rate',
                'message': f"下載成功率過低: {stats['success_rate']:.1f}%",
                'threshold': '90%',
                'current_value': f"{stats['success_rate']:.1f}%"
            })
        
        if stats['pending_retries'] > 20:
            alerts.append({
                'level': 'warning',
                'type': 'high_retry_queue',
                'message': f"待重試隊列過長: {stats['pending_retries']} 個",
                'threshold': '20',
                'current_value': str(stats['pending_retries'])
            })
        
        return {
            'alerts': alerts,
            'alert_count': len(alerts),
            'last_checked': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取告警失敗: {e}")
```

---

## 🚀 部署策略

### 部署清單

#### 1. 資料庫遷移
```bash
# 1. 備份現有資料庫
cp data/email_inbox.db data/email_inbox_backup_$(date +%Y%m%d_%H%M%S).db

# 2. 執行遷移
python -c "
from backend.shared.infrastructure.adapters.database.migrations.add_download_tracking import migrate_add_download_tracking
migrate_add_download_tracking()
"
```

#### 2. 服務部署
```bash
# 1. 更新依賴
pip install -r requirements.txt

# 2. 重啟服務
systemctl restart outlook-summary

# 3. 檢查服務狀態
systemctl status outlook-summary
```

#### 3. 前端資源部署
```bash
# 1. 更新前端文件
# 新增的 Vue 組件和 HTML 模板會自動被 Flask 識別

# 2. 清理瀏覽器快取
echo "請清理瀏覽器快取以確保載入最新前端資源"
```

### 配置檢查清單

#### 必要配置項
1. **資料庫路徑**: 確認 `email_inbox.db` 可寫入
2. **API 路由**: 確認新 API 端點可訪問
3. **Dramatiq 隊列**: 確認重試任務隊列正常工作
4. **日誌配置**: 確認下載追蹤相關日誌正常輸出

#### 性能調優建議
1. **資料庫索引**: 確保新表的索引創建正確
2. **記憶體使用**: 監控新服務的記憶體佔用
3. **API 回應時間**: 監控新 API 的性能表現

---

## ✅ 驗收標準

### 功能驗收

#### Epic 1: 郵件下載狀態追蹤
- [ ] ✅ `email_download_status` 表創建成功
- [ ] ✅ 新郵件下載自動創建追蹤記錄
- [ ] ✅ 下載狀態正確更新（pending → downloading → completed/failed）
- [ ] ✅ 下載時間和大小正確記錄
- [ ] ✅ 錯誤信息詳細記錄

#### Epic 2: 下載重試機制
- [ ] ✅ 下載失敗自動安排重試
- [ ] ✅ 重試策略正確執行（指數退避、線性、固定）
- [ ] ✅ 最大重試次數限制生效
- [ ] ✅ 重試歷史完整記錄
- [ ] ✅ 手動重試功能正常

#### Epic 3: 下載狀態可視化
- [ ] ✅ 儀表板正確顯示統計數據
- [ ] ✅ 下載狀態列表功能正常
- [ ] ✅ 過濾和分頁功能正常
- [ ] ✅ 手動重試操作成功
- [ ] ✅ 實時數據刷新正常

### 性能驗收

#### 回應時間要求
- [ ] ✅ 下載狀態查詢 < 200ms
- [ ] ✅ 統計數據載入 < 500ms
- [ ] ✅ 手動重試操作 < 1s
- [ ] ✅ 批量重試操作 < 5s

#### 並發性能
- [ ] ✅ 支援同時 5 個管理員訪問
- [ ] ✅ 支援每分鐘 100 次 API 調用
- [ ] ✅ 重試處理不影響正常下載

### 相容性驗收

#### 系統相容性
- [ ] ✅ 現有郵件同步功能不受影響
- [ ] ✅ 現有 API 端點正常工作
- [ ] ✅ 現有前端頁面正常訪問
- [ ] ✅ Dramatiq 任務隊列正常運行

#### 瀏覽器相容性
- [ ] ✅ Chrome（最新版本）
- [ ] ✅ Firefox（最新版本）
- [ ] ✅ Safari（最新版本）
- [ ] ✅ Edge（最新版本）

---

## 📋 實施計劃

### Phase 1: 核心基礎設施（第1-2週）
1. **資料庫設計和遷移**
   - 創建追蹤表結構
   - 編寫遷移腳本
   - 測試資料完整性

2. **核心服務開發**
   - EmailDownloadTracker 服務
   - 基礎 API 端點
   - 單元測試

### Phase 2: 重試機制（第3-4週）
1. **重試服務開發**
   - EmailDownloadRetryService
   - Dramatiq 任務整合
   - 重試策略實現

2. **系統整合**
   - EmailSyncService 整合
   - 錯誤處理優化
   - 整合測試

### Phase 3: 前端界面（第5-6週）
1. **Vue.js 組件開發**
   - 儀表板組件
   - 詳情表格組件
   - 狀態可視化

2. **用戶體驗優化**
   - 響應式設計
   - 錯誤提示優化
   - 性能優化

### Phase 4: 監控和部署（第7-8週）
1. **監控系統**
   - 監控指標收集
   - 報告生成
   - 告警機制

2. **生產部署**
   - 部署腳本準備
   - 生產環境測試
   - 性能調優

---

## 🔧 技術決策記錄

### 決策 1: 使用現有 SQLite 資料庫
**背景**: 需要選擇資料存儲方案
**決策**: 繼續使用現有 SQLite 資料庫
**理由**: 
- 與現有系統完全兼容
- 避免額外的資料庫維護成本
- 滿足當前性能需求

### 決策 2: 採用 REST API 而非 GraphQL
**背景**: 需要選擇 API 設計風格
**決策**: 使用 REST API
**理由**:
- 與現有系統 API 風格一致
- 團隊熟悉 REST 開發
- 簡化前端整合複雜度

### 決策 3: 前端使用 Vue.js 組件
**背景**: 需要選擇前端技術方案
**決策**: 使用 Vue.js + Element UI
**理由**:
- 與現有前端技術棧一致
- 豐富的 UI 組件庫
- 學習成本低

### 決策 4: 重試策略採用可配置設計
**背景**: 需要設計重試機制
**決策**: 支援多種重試策略（指數退避、線性、固定）
**理由**:
- 靈活應對不同錯誤類型
- 預留未來優化空間
- 滿足不同場景需求

---

## 📈 未來擴展計劃

### 短期擴展（3個月內）
1. **AI 異常檢測**: 使用機器學習識別異常下載模式
2. **更多郵件協議**: 支援 IMAP、Exchange 等協議
3. **移動端支援**: 響應式設計優化

### 中期擴展（6個月內）
1. **分散式處理**: 支援多節點郵件下載
2. **高級監控**: 集成 Prometheus + Grafana
3. **自動修復**: 基於錯誤模式的自動修復機制

### 長期規劃（1年內）
1. **微服務拆分**: 將下載追蹤獨立為微服務
2. **雲端部署**: 支援 Docker + Kubernetes 部署
3. **企業級功能**: 多租戶、角色管理、審計日誌

---

**文檔版本**: 1.0  
**創建日期**: 2025-08-19  
**負責人**: Backend Architect Agent  
**狀態**: 設計完成，Ready for Implementation