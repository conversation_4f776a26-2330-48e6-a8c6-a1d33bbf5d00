# 3.1 FT-EQC 檔案配對機制實作規格

## [BOARD] 文檔資訊
- **建立日期**: 2025-06-07
- **版本**: v1.2 (專項實作規格 + 檔案發現系統整合 + 智能過濾機制)
- **層級**: 第三層級技術文件
- **基於**: `REF/module2.txt` Compare_Onlineqc 函數逐行深度分析
- **分析範圍**: 第197-374行，重點關注檔案分類與配對邏輯
- **目的**: 精確理解 FT 與 Online EQC 的配對判斷機制並提供 Python 實作規格
- **關聯**: 隸屬於 `0.專案流程管理總覽.md` 階層式管理架構

## [SEARCH] 核心問題解答

### 問題1: 如何判斷有多少 CSV 檔案類型？
### 問題2: FT 和哪一個 Online EQC 配成一組？
### 問題3: CTA 和一般檔案有什麼不同？

**2025-06-08 更新說明**：
本文檔已整合最新的檔案發現與分類系統分析，包含完整的 VBA 四大核心函數 Python 實作對照。詳細的函數功能分析請參考 `3.Online_EQC_系統深度實作規格.md` 第0章節。

**v1.2 更新**：新增智能過濾機制，解決 Correlation 檔案和 RG_ 前綴檔案的過濾問題，確保 EQC RT PASS 統計準確性。

---

## [CHART] 檔案分類流程深度解析

### 1. CSV 檔案發現與去重 (235-237行)

```vba
csvFilestemp = FindALLCSVFiles(folderPath)
csvFiles = RemoveDuplicateCSVFiles(csvFilestemp)
csvFileseqc = FindALLEQCFILE(csvFiles)
csvFilesft = FindALLFTFILE(csvFiles)
```

**Python 完整實作對照**：
```python
class CSVFileDiscovery:
    """
    檔案發現與分類系統 - 對應 VBA 四大核心函數
    詳細分析請參考: 3.Online_EQC_系統深度實作規格.md 第0章節
    """
    
    def find_all_csv_files(self, folder_path: str) -> List[str]:
        """對應 FindALLCSVFiles - 遞迴掃描資料夾找所有CSV檔案"""
        csv_files = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith('.csv'):
                    file_path = os.path.join(root, file)
                    if not any(excluded in file.lower() 
                              for excluded in self.excluded_files):
                        csv_files.append(file_path)
        return self._remove_duplicates(csv_files)
    
    def _remove_duplicates(self, csv_files: List[str]) -> List[str]:
        """對應 RemoveDuplicateCSVFiles - 基於檔案名稱去重"""
        seen_names = set()
        unique_files = []
        for file_path in csv_files:
            filename = os.path.basename(file_path)
            if filename not in seen_names:
                seen_names.add(filename)
                unique_files.append(file_path)
        return unique_files
    
    def classify_eqc_files(self, csv_files: List[str]) -> List[str]:
        """對應 FindALLEQCFILE - 多層檢測分類EQC檔案"""
        eqc_files = []
        for file_path in csv_files:
            if self._is_eqc_file(file_path):
                eqc_files.append(file_path)
        return eqc_files
    
    def classify_ft_files(self, csv_files: List[str]) -> List[str]:
        """對應 FindALLFTFILE - 多標記支援分類FT檔案"""
        ft_files = []
        for file_path in csv_files:
            if self._is_ft_file(file_path):
                ft_files.append(file_path)
        return ft_files

# 實際使用流程 - 完全對應VBA四步驟
discovery = CSVFileDiscovery()
csv_files = discovery.find_all_csv_files(folder_path)    # 步驟1+2: 掃描並去重
eqc_files = discovery.classify_eqc_files(csv_files)      # 步驟3: EQC分類  
ft_files = discovery.classify_ft_files(csv_files)        # 步驟4: FT分類
```

**檔案分類統計範例**：
```python
# 基於 doc/20250523 的實際統計
統計結果:
├── 總CSV檔案: 28個
├── EQC檔案: 15個
│   ├── Online EQC: 7個 (包含 onlieEQC 標記)
│   └── 一般EQC: 8個 (包含 EQC1R, TE_EQC 等)
├── FT檔案: 7個 (包含 FT1R 系列)
└── 其他檔案: 6個 (Summary, Correlation等)
```

**分析要點**:
- `FindALLCSVFiles`: 遞迴掃描資料夾，找出所有 .csv 檔案
- `RemoveDuplicateCSVFiles`: 去[EXCEPT_CHAR]重複檔案名稱，只保留第一個遇到的檔案
- `FindALLEQCFILE`: 多層檢測篩選 EQC 檔案 (檔案名稱 → 內容標記 → 備用規則)
- `FindALLFTFILE`: 多標記支援篩選 FT 檔案 ((ft) 和 (auto_qc) 標記)

**核心優化**：
- Python 版本整合了掃描和去重為單一函數，提升效能
- 增加了異常處理和編碼檢測，提高穩定性
- 支援配置化的排[EXCEPT_CHAR]檔案清單，避免處理系統生成檔案

## [NO_ENTRY] 智能過濾機制實作 (v1.2 新增)

### 問題解決：RG_ 檔案和 Correlation 資料夾過濾

基於對 `RG_F2550176A_EQC_2_S1_L19D1S3.spd_20250523035527.csv` 和 `RG_F2550176A_EQC_2_S2_L19D2S4.spd_20250523035527.csv` 的深度分析，發現這些檔案會被誤認為 EQC 檔案並影響統計準確性。

### .env 配置檔案設定

```bash
# 檔案過濾機制設定
# ==================

# 排[EXCEPT_CHAR]的資料夾名稱 (逗號分隔，不區分大小寫)
EXCLUDED_FOLDERS=correlation,ctacsv,backup,temp

# 排[EXCEPT_CHAR]的檔案前綴 (逗號分隔，不區分大小寫)
EXCLUDED_FILE_PREFIXES=RG_,TEST_,BACKUP_,TEMP_

# 排[EXCEPT_CHAR]的檔案後綴 (逗號分隔，不區分大小寫)  
EXCLUDED_FILE_SUFFIXES=_old,_backup,_temp,_test

# 排[EXCEPT_CHAR]的檔案名稱關鍵字 (逗號分隔，不區分大小寫)
EXCLUDED_FILE_KEYWORDS=eqctotaldata,eqcfaildata,summary,correlation
```

### FileFilterConfig 類別實作

```python
class FileFilterConfig:
    """檔案過濾配置管理器 - 從 .env 讀取配置"""
    
    def __init__(self):
        self.excluded_folders = self._get_env_list('EXCLUDED_FOLDERS', 'correlation,ctacsv,backup,temp')
        self.excluded_file_prefixes = self._get_env_list('EXCLUDED_FILE_PREFIXES', 'RG_,TEST_,BACKUP_,TEMP_')
        self.excluded_file_suffixes = self._get_env_list('EXCLUDED_FILE_SUFFIXES', '_old,_backup,_temp,_test')
        self.excluded_file_keywords = self._get_env_list('EXCLUDED_FILE_KEYWORDS', 'eqctotaldata,eqcfaildata,summary,correlation')
        
    def _get_env_list(self, env_name: str, default_value: str) -> List[str]:
        """從環境變數獲取逗號分隔的列表"""
        import os
        env_value = os.getenv(env_name, default_value)
        return [item.strip().lower() for item in env_value.split(',') if item.strip()]
    
    def should_exclude_folder(self, folder_path: str) -> bool:
        """檢查是否應排[EXCEPT_CHAR]該資料夾"""
        folder_path_lower = folder_path.lower()
        return any(excluded in folder_path_lower for excluded in self.excluded_folders)
    
    def should_exclude_file(self, file_path: str) -> bool:
        """檢查是否應排[EXCEPT_CHAR]該檔案"""
        filename = os.path.basename(file_path).lower()
        
        # 檢查前綴 (解決 RG_ 檔案問題)
        if any(filename.startswith(prefix) for prefix in self.excluded_file_prefixes):
            return True
            
        # 檢查後綴  
        if any(filename.endswith(suffix) for suffix in self.excluded_file_suffixes):
            return True
            
        # 檢查關鍵字
        if any(keyword in filename for keyword in self.excluded_file_keywords):
            return True
            
        return False
```

### 增強的檔案掃描邏輯

```python
def find_all_csv_files(self, folder_path: str) -> List[str]:
    """遞迴掃描資料夾，找出所有 CSV 檔案 - 整合智能過濾機制"""
    csv_files = []
    excluded_count = 0
    folder_excluded_count = 0
    
    for root, dirs, files in os.walk(folder_path):
        # 檢查是否應排[EXCEPT_CHAR]整個資料夾 (解決 Correlation 資料夾問題)
        if self.filter_config.should_exclude_folder(root):
            folder_excluded_count += len([f for f in files if f.lower().endswith('.csv')])
            continue
            
        for file in files:
            if file.lower().endswith('.csv'):
                file_path = os.path.join(root, file)
                
                # 檢查是否應排[EXCEPT_CHAR]該檔案
                if self.filter_config.should_exclude_file(file_path):
                    excluded_count += 1
                    continue
                    
                csv_files.append(file_path)
    
    print(f"[CHART] 檔案掃描統計:")
    print(f"   [OK] 有效CSV檔案: {len(csv_files)} 個")
    print(f"   [NO_ENTRY] 排[EXCEPT_CHAR]檔案: {excluded_count} 個")
    print(f"   [FILE_FOLDER] 排[EXCEPT_CHAR]資料夾檔案: {folder_excluded_count} 個")
    
    return self._remove_duplicates(csv_files)
```

### 增強的 EQC 檔案檢測

```python
def _is_eqc_file(self, file_path: str) -> bool:
    """檢測是否為有效的生產 EQC 檔案 - 增強過濾邏輯"""
    try:
        # 預先檢查：確保檔案沒有被過濾配置排[EXCEPT_CHAR]
        if self.filter_config.should_exclude_file(file_path):
            return False
            
        # 預先檢查：確保檔案不在排[EXCEPT_CHAR]的資料夾中
        if self.filter_config.should_exclude_folder(file_path):
            return False
            
        filename_lower = file_path.lower()
        
        # 檢查檔案路徑是否包含特定標記
        if 'onlineeqc' in filename_lower or '.qa' in filename_lower:
            return True
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
            # 檢查是否為 Correlation 測試檔案
            if len(lines) > 1:
                test_program = lines[1].lower()
                if '(qc)' in test_program and 'correlation' in file_path.lower():
                    return False  # 排[EXCEPT_CHAR]相關性測試檔案
            
            # 其他檢測邏輯...
            
    except Exception:
        return False
```

### 過濾效果預期

實施後的過濾效果：

```
原始掃描: 30個CSV檔案
├── [FILE_FOLDER] 排[EXCEPT_CHAR] Correlation 資料夾: 2個檔案 (RG_S1, RG_S2)
├── [NO_ENTRY] 排[EXCEPT_CHAR] RG_ 前綴檔案: 0個 (已在資料夾層級過濾)
├── [NO_ENTRY] 排[EXCEPT_CHAR]其他系統檔案: 3個
└── [OK] 有效生產檔案: 25個

統計準確性提升:
- EQC RT PASS 統計不再包含 Correlation 測試數據
- 避免 RG_ 檔案的 (QC) 標記誤判
- 保持與 VBA 系統一致的過濾邏輯
```

### 2. 檔案分類的詳細機制

#### 2.1 EQC 檔案識別邏輯

基於 `module1.txt` 中的 `CheckEQCCSVFile` 函數分析：

```vba
Function CheckEQCCSVFile(ByVal filePath As String) As Boolean
    ' 檢查前兩行內容
    For i = 0 To 1
        Line Input #1, fileLine
        If InStr(1, LCase(fileLine), "(qc)", vbTextCompare) > 0 Or 
           InStr(1, filePath, ".qa", vbTextCompare) > 0 Then
            CheckEQCCSVFile = True
            Exit Function
        End If
    Next i
    
    ' 檢查第三行
    Line Input #1, fileLine
    If InStr(1, fileLine, "qa", vbTextCompare) > 0 Then
        CheckEQCCSVFile = True
    End If
End Function
```

**EQC 檔案判斷條件**:
1. 檔案前兩行包含 `(qc)` 標記
2. 檔案路徑包含 `.qa` 擴展名
3. 第三行包含 `qa` 文字

#### 2.2 FT 檔案識別邏輯

基於 `module1.txt` 中的 `CheckFTCSVFile` 函數分析：

```vba
Function CheckFTCSVFile(ByVal filePath As String) As Boolean
    ' 排[EXCEPT_CHAR]特定檔案類型
    If InStr(1, LCase(filePath), "eqctotaldata", vbTextCompare) = 0 And 
       InStr(1, LCase(filePath), "eqcfaildata", vbTextCompare) = 0 And 
       InStr(1, filePath, "qa", vbTextCompare) = 0 Or 
       InStr(1, filePath, "1621qa", vbTextCompare) > 0 Then
        
        ' 檢查前兩行內容
        For i = 0 To 1
            Line Input #1, fileLine
            If InStr(1, LCase(fileLine), "(ft)", vbTextCompare) > 0 Or 
               InStr(1, LCase(fileLine), "(auto_qc)", vbTextCompare) > 0 Then
                CheckFTCSVFile = True
            End If
        Next i
    End If
End Function
```

**FT 檔案判斷條件**:
1. 排[EXCEPT_CHAR] `eqctotaldata`, `eqcfaildata` 檔案
2. 排[EXCEPT_CHAR]包含 `qa` 的檔案（[EXCEPT_CHAR]非是 `1621qa`）
3. 前兩行包含 `(ft)` 或 `(auto_qc)` 標記

#### 2.3 Online EQC 檔案識別

基於 `module1.txt` 中的 `CheckEQCBIN1CSVFile` 函數：

```vba
Function CheckEQCBIN1CSVFile(ByVal filePath As String) As Boolean
    ' 檢查整個檔案內容
    Do While Not EOF(fileNumber)
        Line Input #fileNumber, fileLine
        If InStr(1, LCase(fileLine), "(qc)", vbTextCompare) > 0 Or 
           InStr(1, LCase(filePath), "onlineeqc", vbTextCompare) > 0 Then
            CheckEQCBIN1CSVFile = True
            Exit Function
        End If
    Loop
End Function
```

**Online EQC 檔案判斷條件**:
1. 檔案內容包含 `(qc)` 標記
2. 檔案路徑包含 `onlineeqc` 字串

---

## [LINK] FT-EQC 配對機制核心分析

### 1. 配對流程總覽 (240-242行)

```vba
If csvFileseqc(1)(1) <> "" Then
    matchedCSV = FindmatchedCSV(csvFileseqc, csvFilesft, ctafile)
    If matchedCSV(0, 1) <> "" Then
        csvFileseqcrt = FindunmatchedCSV(csvFileseqc, matchedCSV)
```

**關鍵配對步驟**:
1. 檢查是否有有效的 EQC 檔案
2. 執行 `FindmatchedCSV` 進行 FT-EQC 配對
3. 找出未配對的 EQC 檔案 (EQC RT 檔案)

### 2. 配對邏輯的精確分析

#### 2.1 時間戳配對機制

基於 `module2.txt` 中的時間比對函數：

```vba
Function FindCSVFileByTime2x(ByVal fileNameft As String, ByVal fileNameqc As String) As Boolean
    ' 轉換檔案日期為數字格式
    Dim fileNameWithoutExtft As String
    fileNameWithoutExtft = Left(fileNameft, Len(fileNameft) - 4) ' 去[EXCEPT_CHAR] .csv
    
    Dim fileNameWithoutExtqc As String  
    fileNameWithoutExtqc = Left(fileNameqc, Len(fileNameqc) - 4) ' 去[EXCEPT_CHAR] .csv
    
    ' 提取時間戳 (檔案名稱最後8位數字)
    strNumft = Mid(fileNameWithoutExtft, Len(fileNameWithoutExtft) - 7, 8)
    strNumqct = Mid(fileNameWithoutExtqc, Len(fileNameWithoutExtqc) - 7, 8)
    
    If IsNumeric(strNumft) And IsNumeric(strNumqct) Then
        fileDateft = CLng(strNumft)
        fileDateqc = CLng(strNumqct)
        
        ' 時間差小於400視為同組
        If Abs(fileDateft - fileDateqc) < 400 Then
            FindCSVFileByTime2x = True
        End If
    End If
End Function
```

**時間戳配對規則**:
1. 提取檔案名稱最後8位數字作為時間戳
2. 計算 FT 和 EQC 檔案的時間差
3. **關鍵閾值**: 時間差 < 400 視為同組檔案

#### 2.2 檔案修改時間配對

```vba
Function FindCSVFileByTime(folderPath As String, fileName As String) As String
    ' 轉換檔案日期為 Date 格式
    Dim fileDate As Date
    fileDate = FileDateTime(folderPath & "\" & fileName)
    
    For Each file In folder.files
        ' 檢查是否為 CSV 檔案且檔名不同
        If LCase(Right(file.name, 4)) = ".csv" And Not LCase(file.name) = LCase(fileName) Then
            ' 檢查檔案日期是否符合條件
            If Abs(file.DateLastModified - fileDate) < TimeSerial(0, 0, 60) Then ' 時間誤差小於 60 秒
                FindCSVFileByTime = file.path
                Exit Function
            End If
        End If
    Next file
End Function
```

**檔案時間配對規則**:
1. 使用檔案系統的修改時間
2. **關鍵閾值**: 時間差 < 60秒視為同組檔案
3. 作為檔案名稱時間戳配對的備用方案

---

## [TOOL] CTA 檔案處理的特殊機制

### 1. CTA 檔案檢測邏輯 (226行)

```vba
ctafile = ReadCSVsInDirectory(folderPath)
```

### 2. CTA 格式的深度分析

基於 `module2.txt` CopyRowsToNewFile 函數中的 CTA 檢測邏輯：

#### 2.1 CTA 格式標記檢測

```vba
' 第7行檢測 (index 6)
lineElementsA = Split(fileLine, ",")
If InStr(1, lineElementsA(2), "Serial_No", vbTextCompare) > 0 Then
    cta_file8290 = True
ElseIf InStr(1, lineElementsA(2), "Index_No", vbTextCompare) > 0 Then
    cta_file8280 = True
End If

' 第8行檢測CTA標記 (index 7)  
If InStr(1, lineElementsA(0), "cta", vbTextCompare) > 0 Then
    If cta_file8290 = True Then
        cta_file = 8290
    ElseIf cta_file8280 = True Then
        cta_file = 8280
    End If
End If
```

#### 2.2 CTA 配對的三種模式

**模式0: 一般檔案配對**
```vba
If lineElementsA(0) = valueInColumnS And cta_file = 0 Then
    ' 使用序號 (第0欄) 直接匹配
End If
```

**模式8290: CTA 8290 格式配對**
```vba
If lineElementsA(3) = cta_Part_No And lineElementsA(4) = cta_Dut_No And lineElementsA(5) = cta_Site_No And cta_file = 8290 Then
    ' 使用 Part_No + Dut_No + Site_No 三欄位匹配
End If
```

**模式8280: CTA 8280 格式配對**
```vba
If lineElementsA(3) = cta_Part_No And lineElementsA(4) = cta_Dut_No And lineElementsA(5) = cta_Site_No And cta_file = 8280 Then
    ' 使用 Part_No + Dut_No + Site_No 三欄位匹配 + 座標欄位過濾
End If
```

### 3. CTA 與一般檔案的關鍵差異

| 特徵 | 一般檔案 | CTA 8290 | CTA 8280 |
|------|----------|----------|----------|
| **匹配依據** | 序號 (第0欄) | Part_No + Dut_No + Site_No | Part_No + Dut_No + Site_No |
| **格式標記** | 無 | Serial_No (第7行第2欄) | Index_No (第7行第2欄) |
| **特殊處理** | 無 | 無 | 過濾 X_COORD, Y_COORD, Alarm |
| **配對複雜度** | 低 | 中 | 高 |

---

## [TARGET] 配對判斷的完整演算法

### 1. FT-EQC 配對的完整流程

```mermaid
graph TD
    A[掃描資料夾所有CSV檔案] --> B[檔案類型分類]
    B --> C{檢查檔案內容標記}
    C -->|包含(ft)或(auto_qc)| D[歸類為FT檔案]
    C -->|包含(qc)或.qa| E[歸類為EQC檔案]
    C -->|包含onlineeqc| F[歸類為Online EQC]
    
    D --> G[時間戳配對]
    E --> G
    F --> G
    
    G --> H{檔案名稱時間戳比對}
    H -->|時間差<400| I[配對成功]
    H -->|時間差>=400| J[檔案修改時間比對]
    J -->|時間差<60秒| I
    J -->|時間差>=60秒| K[配對失敗]
    
    I --> L{檢查CTA格式}
    L -->|一般格式| M[序號匹配]
    L -->|CTA 8290| N[三欄位匹配]
    L -->|CTA 8280| O[三欄位匹配+欄位過濾]
    
    M --> P[生成配對結果]
    N --> P
    O --> P
```

### 2. 配對成功的關鍵條件

#### 條件1: 時間相近性
- **檔案名稱時間戳**: 差異 < 400
- **檔案修改時間**: 差異 < 60秒

#### 條件2: 檔案類型正確
- FT 檔案: 包含 `(ft)` 或 `(auto_qc)`
- EQC 檔案: 包含 `(qc)` 或 `.qa`

#### 條件3: 資料內容匹配
- **一般格式**: 序號匹配
- **CTA 格式**: Part_No + Dut_No + Site_No 匹配

### 3. EQC RT 檔案的識別

```vba
csvFileseqcrt = FindunmatchedCSV(csvFileseqc, matchedCSV)
```

**EQC RT 檔案定義**:
- 屬於 EQC 類型檔案
- 但沒有對應的 FT 檔案配對
- 通常是額外的品質檢測檔案

---

## [TOOL] Python 實作規格

### 1. 檔案分類器實作

```python
class CSVFileClassifier:
    """CSV 檔案分類器"""
    
    def __init__(self):
        self.ft_markers = ['(ft)', '(auto_qc)']
        self.eqc_markers = ['(qc)', '.qa', 'qa']
        self.online_eqc_markers = ['(qc)', 'onlineeqc']
    
    def classify_files(self, folder_path: str) -> Dict[str, List]:
        """檔案分類主函數"""
        all_csv_files = self.find_all_csv_files(folder_path)
        deduplicated_files = self.remove_duplicate_csv_files(all_csv_files)
        
        return {
            'ft_files': self.find_ft_files(deduplicated_files),
            'eqc_files': self.find_eqc_files(deduplicated_files),
            'online_eqc_files': self.find_online_eqc_files(deduplicated_files)
        }
    
    def _detect_file_type_by_content(self, lines: List[str], file_path: str) -> str:
        """第一層級：基於內容的檔案類型檢測"""
        file_path_lower = file_path.lower()
        
        # 排[EXCEPT_CHAR]特定檔案類型
        if any(x in file_path_lower for x in ['eqctotaldata', 'eqcfaildata']):
            return 'processed'
        
        # 檢查前兩行內容
        for i in range(min(2, len(lines))):
            line_lower = lines[i].lower()
            
            # FT 檔案檢測
            if any(marker in line_lower for marker in ['(ft)', '(auto_qc)']):
                # 額外檢查：確保不含 'qa'
                if 'qa' not in file_path_lower or '1621qa' in file_path_lower:
                    return 'ft'
            
            # EQC 檔案檢測
            if '(qc)' in line_lower or '.qa' in file_path_lower:
                return 'eqc'
        
        # 檢查第三行
        if len(lines) > 2 and 'qa' in lines[2].lower():
            return 'eqc'
        
        # Online EQC 檔案檢測（檢查整個檔案）
        if 'onlineeqc' in file_path_lower:
            return 'online_eqc'
            
        for line in lines:
            if '(qc)' in line.lower():
                return 'online_eqc'
        
        return 'unknown'
```

### 2. 配對器實作

```python
class FTEQCMatcher:
    """FT-EQC 檔案配對器"""
    
    def __init__(self):
        self.time_threshold_filename = 400      # 檔案名稱時間戳閾值
        self.time_threshold_modified = 60       # 檔案修改時間閾值 (秒)
    
    def match_files(self, ft_files: List[str], eqc_files: List[str]) -> List[Tuple]:
        """檔案配對主函數"""
        matched_pairs = []
        
        for eqc_file in eqc_files:
            best_ft_match = self.find_best_ft_match(eqc_file, ft_files)
            if best_ft_match:
                matched_pairs.append((best_ft_match, eqc_file))
        
        return matched_pairs
    
    def find_best_ft_match(self, eqc_file: str, ft_files: List[str]) -> Optional[str]:
        """找到最佳的 FT 檔案匹配"""
        # 1. 檔案名稱時間戳比對
        for ft_file in ft_files:
            if self.compare_filename_timestamps(ft_file, eqc_file, self.time_threshold_filename):
                return ft_file
        
        # 2. 檔案修改時間比對
        for ft_file in ft_files:
            if self.compare_file_modification_times(ft_file, eqc_file, self.time_threshold_modified):
                return ft_file
        
        return None
    
    def compare_filename_timestamps(self, ft_file: str, eqc_file: str, threshold: int) -> bool:
        """檔案名稱時間戳比對 (對應 FindCSVFileByTime2x)"""
        ft_timestamp = self._extract_filename_timestamp(ft_file)
        eqc_timestamp = self._extract_filename_timestamp(eqc_file)
        
        if ft_timestamp is not None and eqc_timestamp is not None:
            return abs(ft_timestamp - eqc_timestamp) < threshold
        
        return False
    
    def _extract_filename_timestamp(self, file_path: str) -> Optional[int]:
        """提取檔案名稱最後8位數字作為時間戳"""
        try:
            filename = os.path.splitext(os.path.basename(file_path))[0]
            if len(filename) >= 8:
                timestamp_str = filename[-8:]  # 取最後8位
                if timestamp_str.isdigit():
                    return int(timestamp_str)
        except Exception:
            pass
        return None
    
    def compare_file_modification_times(self, ft_file: str, eqc_file: str, threshold: int) -> bool:
        """檔案修改時間比對 (對應 FindCSVFileByTime)"""
        try:
            ft_mtime = os.path.getmtime(ft_file)
            eqc_mtime = os.path.getmtime(eqc_file)
            return abs(ft_mtime - eqc_mtime) < threshold
        except Exception:
            return False
```

### 3. CTA 格式處理器

```python
class CTAProcessor:
    """CTA 格式處理器"""
    
    def detect_cta_format(self, file_path: str) -> Dict:
        """檢測 CTA 格式"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        format_info = {
            'format': 0,  # 0=一般, 8290, 8280
            'match_mode': 'serial_number',
            'bypass_columns': []
        }
        
        # 第7行檢測 (index 6)
        if len(lines) > 6:
            line_7_elements = lines[6].strip().split(',')
            if len(line_7_elements) > 2:
                if 'Serial_No' in line_7_elements[2]:
                    format_info['format'] = 8290
                    format_info['match_mode'] = 'three_column'
                elif 'Index_No' in line_7_elements[2]:
                    format_info['format'] = 8280
                    format_info['match_mode'] = 'three_column_filtered'
                    
                    # 檢測需要過濾的座標欄位
                    if len(line_7_elements) > 8 and 'X_COORD' in line_7_elements[8]:
                        format_info['bypass_columns'].append(8)
                    if len(line_7_elements) > 9 and 'Y_COORD' in line_7_elements[9]:
                        format_info['bypass_columns'].append(9)
                    if len(line_7_elements) > 14 and 'Alarm' in line_7_elements[14]:
                        format_info['bypass_columns'].append(14)
        
        # 第8行檢測CTA標記 (index 7)
        if len(lines) > 7:
            line_8_elements = lines[7].strip().split(',')
            if line_8_elements and 'cta' in line_8_elements[0].lower():
                # 確認CTA格式
                pass
        
        return format_info
    
    def match_data_row(self, ft_row: List[str], eqc_fail_row: List[str], 
                      format_info: Dict) -> bool:
        """資料行匹配邏輯"""
        cta_format = format_info['format']
        
        # 模式0: 一般格式 - 序號匹配
        if cta_format == 0:
            return ft_row[0] == eqc_fail_row[0]
        
        # 模式8290&8280: CTA格式 - Part_No + Dut_No + Site_No 匹配
        elif cta_format in [8290, 8280]:
            return (len(ft_row) > 5 and len(eqc_fail_row) > 5 and
                    ft_row[3] == eqc_fail_row[3] and  # Part_No
                    ft_row[4] == eqc_fail_row[4] and  # Dut_No
                    ft_row[5] == eqc_fail_row[5])     # Site_No
        
        return False
```

### 4. 完整配對流程實作

```python
class FTEQCPairingEngine:
    """完整的 FT-EQC 配對引擎"""
    
    def __init__(self):
        self.classifier = CSVFileClassifier()
        self.matcher = FTEQCMatcher()
        self.cta_processor = CTAProcessor()
    
    def execute_pairing_process(self, folder_path: str) -> Dict[str, Any]:
        """執行完整的配對流程"""
        
        # 步驟1: 檔案分類
        classification_result = self.classifier.classify_files(folder_path)
        
        # 步驟2: FT-EQC 配對
        matched_pairs = self.matcher.match_files(
            classification_result['ft_files'],
            classification_result['eqc_files']
        )
        
        # 步驟3: 找出未配對的 EQC 檔案 (EQC RT)
        unmatched_eqc = self._find_unmatched_eqc(
            classification_result['eqc_files'], matched_pairs
        )
        
        # 步驟4: CTA 格式處理
        cta_files = []
        for ft_file in classification_result['ft_files']:
            cta_info = self.cta_processor.detect_cta_format(ft_file)
            if cta_info['format'] > 0:
                cta_files.append({'file': ft_file, 'format': cta_info})
        
        return {
            'matched_pairs': matched_pairs,
            'unmatched_eqc': unmatched_eqc,
            'cta_files': cta_files,
            'statistics': {
                'total_ft_files': len(classification_result['ft_files']),
                'total_eqc_files': len(classification_result['eqc_files']),
                'successful_matches': len(matched_pairs),
                'unmatched_eqc_count': len(unmatched_eqc)
            }
        }
    
    def _find_unmatched_eqc(self, all_eqc_files: List[str], 
                           matched_pairs: List[Tuple]) -> List[str]:
        """找出未配對的 EQC 檔案"""
        matched_eqc_paths = {pair[1] for pair in matched_pairs}
        return [eqc_file for eqc_file in all_eqc_files 
                if eqc_file not in matched_eqc_paths]
```

---

## [BOARD] 實作檢查清單

### Phase 1: 檔案分類檢測 (100% 精確度要求)
- [ ] **FT 檔案**: 前兩行包含 `(ft)` 或 `(auto_qc)` 標記
- [ ] **EQC 檔案**: 前兩行包含 `(qc)` 或檔案路徑包含 `.qa`
- [ ] **Online EQC**: 檔案內容包含 `(qc)` 或路徑包含 `onlineeqc`
- [ ] **排[EXCEPT_CHAR]檔案**: `eqctotaldata`, `eqcfaildata` 自動排[EXCEPT_CHAR]

### Phase 2: 時間戳配對機制 (雙重保險)
- [ ] **檔案名稱時間戳**: 最後8位數字，差異 < 400
- [ ] **檔案修改時間**: 系統時間戳，差異 < 60秒
- [ ] **配對優先級**: 檔案名稱時間戳 > 檔案修改時間

### Phase 3: CTA 格式特殊處理
- [ ] **CTA 檢測**: 第7行第2欄的 "Serial_No" vs "Index_No"
- [ ] **一般匹配**: 序號 (第0欄) 直接對應
- [ ] **CTA 匹配**: Part_No + Dut_No + Site_No 三欄位匹配
- [ ] **8280 過濾**: X_COORD, Y_COORD, Alarm 欄位移[EXCEPT_CHAR]

### Phase 4: 配對結果處理
- [ ] **已配對檔案**: FT-EQC 成功配對的檔案對
- [ ] **未配對 EQC**: 無對應 FT 檔案的 EQC (EQC RT)
- [ ] **配對統計**: 配對成功率和失敗原因分析

---

## [LAB] 結論

**FT-EQC 配對的核心判斷依據**:

1. **時間相近性** (最重要)
   - 檔案名稱時間戳差異 < 400
   - 檔案修改時間差異 < 60秒

2. **檔案類型正確性**
   - FT: `(ft)` 或 `(auto_qc)` 標記
   - EQC: `(qc)` 或 `.qa` 標記

3. **資料內容匹配**
   - 一般格式: 序號匹配
   - CTA 格式: 三欄位匹配 (Part_No + Dut_No + Site_No)

4. **特殊處理**
   - CTA 8280: 額外的座標欄位過濾
   - Online EQC: 整個檔案內容掃描

**關鍵發現**: 系統使用**雙重時間檢查機制**確保配對準確性，並根據 CTA 格式採用不同的資料匹配策略。這種多層級的配對機制確保了在複雜的製造環境中能夠準確地將測試資料配對。

**技術亮點**: 
- **400 vs 60秒閾值設計**: 檔案名稱時間戳使用較大閾值 (400)，檔案修改時間使用較小閾值 (60秒)
- **三層級檔案檢測**: 路徑檢測 → 內容標記檢測 → CTA格式檢測
- **三種匹配模式**: 序號匹配 → 三欄位匹配 → 三欄位匹配+欄位過濾

*專項實作規格版本: v1.0 | 完成日期: 2025-06-07 | 基於 module2.txt 逐行深度分析*