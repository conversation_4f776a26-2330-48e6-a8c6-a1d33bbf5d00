@echo off
chcp 65001 >nul 2>&1

REM 設定中文編碼環境變數
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set LANG=zh_TW.UTF-8
REM Dramatiq Worker Startup Script - Production Grade Async Task Processing
REM
REM Functions:
REM   - Start Dramatiq Worker for async task processing
REM   - Support multi-process and multi-thread configuration
REM   - Auto-detect Redis connection
REM   - Provide health check and monitoring
REM
REM Usage:
REM   start_dramatiq.bat [processes] [threads]
REM
REM   Examples:
REM   start_dramatiq.bat 4 8    # 4 processes, 8 threads each
REM   start_dramatiq.bat        # Use default configuration

echo.
echo ========================================
echo   Dramatiq Worker Startup Script
echo   Production Grade Async Task Processing
echo ========================================
echo.

REM Set default parameters
set PROCESSES=%1
set THREADS=%2

if "%PROCESSES%"=="" set PROCESSES=4
if "%THREADS%"=="" set THREADS=8

echo Configuration Parameters:
echo   - Processes: %PROCESSES%
echo   - Threads: %THREADS%
echo   - Task Module: dramatiq_tasks
echo   - Environment: Production Mode (Redis)
echo.

REM Check virtual environment
if not exist "venv_win_3_11_12\Scripts\python.exe" (
    echo ERROR: Virtual environment venv_win_3_11_12 not found
    echo Please ensure virtual environment is properly installed
    pause
    exit /b 1
)

REM Check Redis connection
echo Checking Redis connection...
venv_win_3_11_12\Scripts\python.exe -c "import redis; r=redis.Redis(host='localhost', port=6379, db=0); r.ping(); print('Redis connection OK')" 2>nul
if errorlevel 1 (
    echo WARNING: Redis connection failed, using memory mode
    set USE_MEMORY_BROKER=true
) else (
    echo Redis connection OK, using production mode
    set USE_MEMORY_BROKER=false
)

echo.
echo Starting Dramatiq Worker...
echo.

REM Set environment variables
set DRAMATIQ_PROCESSES=%PROCESSES%
set DRAMATIQ_THREADS=%THREADS%

REM Set Python path
set PYTHONPATH=%CD%;%PYTHONPATH%

REM Start Dramatiq Worker
venv_win_3_11_12\Scripts\python.exe -m dramatiq dramatiq_tasks ^
    --processes %PROCESSES% ^
    --threads %THREADS% ^
    --verbose

REM Check startup result
if errorlevel 1 (
    echo.
    echo Dramatiq Worker startup failed
    echo.
    echo Troubleshooting suggestions:
    echo   1. Check if Redis service is running
    echo   2. Confirm virtual environment dependencies are installed
    echo   3. Check dramatiq_tasks.py syntax
    echo   4. Review error messages above
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo Dramatiq Worker closed normally
)

pause
