# BMAD 智能程式重啟配置
# 此配置檔案定義了不同檔案修改時需要重啟的服務

## 專案配置
PROJECT_ROOT="D:\project\python\outlook_summary"
MAIN_SERVICE="start_integrated_services.py"  # 主要整合服務
SERVICE_CONFIG={
    "integrated": {
        "script": "start_integrated_services.py",
        "ports": [5000, 8010],  # Flask + FastAPI
        "process_pattern": "python.*start_integrated_services.py"
    }
}

## 檔案路徑 → 服務對應表
RESTART_RULES={
    # 任何 Python 檔案修改 → 重啟整合服務
    "**/*.py": ["integrated"],
    
    # 静態檔案修改 → 無需重啟
    "frontend/email/static/css/*.css": [],
    "frontend/email/static/js/*.js": [],
    "frontend/email/templates/*.html": [],
    
    # 配置檔案 → 重啟整合服務
    "*.yaml": ["integrated"],
    "*.json": ["integrated"],
    "requirements.txt": ["integrated"],
    
    # 特殊情況：只修改前端靜態檔案時無需重啟
    "static_only": []
}

## 自動檢測腳本範例
```python
import os
import subprocess
import time
import psutil

def detect_affected_services(modified_files):
    """根據修改的檔案檢測需要重啟的服務"""
    affected_services = set()
    
    for file_path in modified_files:
        # 標準化路徑
        rel_path = os.path.relpath(file_path, PROJECT_ROOT)
        
        # 檢查規則
        for pattern, services in RESTART_RULES.items():
            if matches_pattern(rel_path, pattern):
                affected_services.update(services)
                
    return list(affected_services)

def restart_service(service_name):
    """重啟指定服務"""
    if service_name == "integrated":
        service_config = SERVICE_CONFIG["integrated"]
        
        # 1. 停止現有進程
        stop_processes(service_config["process_pattern"])
        
        # 2. 等待完全關閉
        time.sleep(5)
        
        # 3. 重新啟動整合服務
        start_service(service_config["script"])
        
        # 4. 驗證多個端口啟動成功
        for port in service_config["ports"]:
            if not verify_service(port):
                print(f"警告: 端口 {port} 未正常啟動")
                return False
        return True

def stop_processes(pattern):
    """停止符合模式的進程"""
    for proc in psutil.process_iter(['pid', 'cmdline']):
        cmdline = ' '.join(proc.info['cmdline'] or [])
        if pattern in cmdline:
            proc.terminate()
            print(f"停止進程: {proc.info['pid']} - {cmdline}")

def start_service(script):
    """啟動服務"""
    os.chdir(PROJECT_ROOT)
    subprocess.Popen([sys.executable, script])
    print(f"啟動整合服務: {script}")

def verify_service(port):
    """驗證服務是否正常運行"""
    import requests
    try:
        response = requests.get(f"http://localhost:{port}/", timeout=10)
        return response.status_code == 200
    except:
        return False

def verify_and_shutdown_service(service_name="integrated"):
    """驗證服務後關閉"""
    if service_name == "integrated":
        service_config = SERVICE_CONFIG["integrated"]
        
        # 1. 最後一次驗證服務正常
        all_ports_ok = True
        for port in service_config["ports"]:
            if not verify_service(port):
                print(f"警告: 端口 {port} 異常")
                all_ports_ok = False
        
        if all_ports_ok:
            print("✓ 所有服務驗證通過，開始關閉...")
        
        # 2. 強制關閉服務
        stop_processes(service_config["process_pattern"])
        
        # 3. 等待完全關閉
        time.sleep(3)
        
        # 4. 確認關閉成功
        shutdown_confirmed = True
        for port in service_config["ports"]:
            if verify_service(port):
                print(f"錯誤: 端口 {port} 仍在運行")
                shutdown_confirmed = False
        
        if shutdown_confirmed:
            print("✓ start_integrated_services.py 已成功關閉")
            print("✓ 所有端口已釋放")
        else:
            print("⚠️ 服務關閉不完全，可能需要手動關閉")
            
        return shutdown_confirmed
```

## 使用說明
1. **自動檢測**: 系統會根據修改的檔案自動判斷需要重啟哪些服務
2. **手動指定**: 在 Phase 3 修復過程中可以明確指出需要重啟的服務
3. **驗證機制**: 重啟後會自動驗證服務是否正常運行

## 範例用法
```yaml
# 修改了 frontend/email/routes/email_routes.py
→ 自動檢測: 需要重啟 start_integrated_services.py
→ 執行: 停止 python start_integrated_services.py → 等待 5 秒 → 重啟 → 驗證 port 5000, 8010
→ Phase 4 結束: 驗證通過後強制關閉 start_integrated_services.py

# 修改了 backend/shared/models/email_models.py  
→ 自動檢測: 需要重啟 start_integrated_services.py
→ 執行: 停止整合服務 → 重啟 → 驗證 Flask 和 FastAPI 都正常
→ Phase 4 結束: 確認所有端口已釋放

# 修改了 frontend/static/js/email-operations.js
→ 自動檢測: 無需重啟，只需刷新頁面
→ 執行: 通知驗證階段刷新瀏覽器即可
→ Phase 4 結束: 無服務運行，無需關閉

# 手動指定範例
"這個修改需要重啟 start_integrated_services.py"
"無需重啟，只需刷新瀏覽器"
"需要完全重啟所有服務"

# 完整流程範例
1. Phase 3: 修改 email_routes.py
2. Phase 3.5: 自動重啟 start_integrated_services.py
3. Phase 4: Playwright 驗證 + 自動關閉服務
4. Phase 5: 生成交付報告（無服務運行）
```
```
