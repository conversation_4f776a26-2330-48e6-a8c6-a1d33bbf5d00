#!/usr/bin/env python3
"""
Import Path Verification Script
Verifies all Task 4 import path fixes are working correctly
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_database_imports():
    """Test database-related imports"""
    print("Testing database imports...")
    
    try:
        from backend.shared.infrastructure.adapters.database.email_database import EmailDatabase
        print("  ✅ EmailDatabase import: OK")
    except Exception as e:
        print(f"  ❌ EmailDatabase import failed: {e}")
        return False
    
    try:
        from backend.shared.infrastructure.adapters.database.models import db_engine
        print("  ✅ db_engine import: OK")
    except Exception as e:
        print(f"  ❌ db_engine import failed: {e}")
        return False
    
    try:
        from backend.email.models.email_models import EmailData
        print("  ✅ EmailData import: OK")
    except Exception as e:
        print(f"  ❌ EmailData import failed: {e}")
        return False
    
    return True

def test_config_imports():
    """Test configuration-related imports"""
    print("\nTesting configuration imports...")
    
    try:
        from backend.shared.infrastructure.config.settings import Settings
        print("  ✅ Settings import: OK")
    except Exception as e:
        print(f"  ❌ Settings import failed: {e}")
        return False
    
    return True

def test_exception_imports():
    """Test exception-related imports"""
    print("\nTesting exception imports...")
    
    try:
        from backend.shared.domain.exceptions.base import (
            OutlookSummaryException,
            EmailProcessingException,
            ValidationException,
        )
        print("  ✅ Exception classes import: OK")
    except Exception as e:
        print(f"  ❌ Exception classes import failed: {e}")
        return False
    
    return True

def test_task_status_imports():
    """Test task status database imports"""
    print("\nTesting task status database imports...")
    
    try:
        from backend.shared.infrastructure.database.task_status_db import get_task_status_db
        print("  ✅ Task status DB import: OK")
    except Exception as e:
        print(f"  ❌ Task status DB import failed: {e}")
        return False
    
    return True

def test_flask_config_imports():
    """Test Flask configuration imports"""
    print("\nTesting Flask configuration imports...")
    
    try:
        from frontend.config import Config, config
        print("  ✅ Flask config import: OK")
    except Exception as e:
        print(f"  ❌ Flask config import failed: {e}")
        return False
    
    return True

def main():
    """Main verification function"""
    print("🔍 Task 4 Import Path Verification")
    print("=" * 50)
    print("Verifying all scripts directory import fixes...")
    print()
    
    # Run all tests
    tests = [
        ("Database Imports", test_database_imports),
        ("Configuration Imports", test_config_imports),
        ("Exception Imports", test_exception_imports),
        ("Task Status DB Imports", test_task_status_imports),
        ("Flask Config Imports", test_flask_config_imports),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"  ❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Generate summary
    print("\n" + "=" * 50)
    print("📊 Verification Summary")
    print("=" * 50)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print()
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    if failed_tests == 0:
        print(f"\n🎉 All import path fixes verified successfully!")
        print(f"📁 Scripts directory is ready for production use.")
        return 0
    else:
        print(f"\n❌ {failed_tests} import issues found.")
        print(f"🔧 Please check the failed imports and fix them.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)