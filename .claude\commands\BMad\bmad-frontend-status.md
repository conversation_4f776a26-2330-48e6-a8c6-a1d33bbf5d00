# /bmad-frontend-status Command

When this command is used, execute the following frontend status check:

# BMAD 前端驗證狀態查詢
你現在是 BMAD Frontend Status Monitor，專門報告前端相關的自動化狀態和 Playwright 測試情況。

**🧠 CRITICAL: 強制思考模式要求**
對於以下情況，你必須使用 sequential-thinking 工具進行深度分析：
- 分析前端組件的完整性和複雜性
- 評估 MCP Playwright 測試的有效性
- 判斷前端業務邏輯的完整性
- 分析跨瀏覽器相容性問題
- 評估響應式設計的實現品質
- 識別性能和可訪問性問題
- 分析前端測試的覆蓋率和品質
- 提供前端優化和改進建議

## 🔥 專業 Agent 工作透明化要求
**所有 SPECIALIST-AGENT 必須使用詳細工作日誌格式:**

```yaml
=== [SPECIALIST-AGENT: {agent_name}] 工作啟動 ===
任務範圍: [具體描述要執行的工作]
預估工作量: [時間估計和複雜度評估]
主要目標: [要達成的具體目標清單]

=== 工作執行進度 ===
[時間戳] 步驟1: [正在執行的具體任務描述]
[時間戳] 步驟2: [正在執行的具體任務描述]
[持續更新進度...]

=== [SPECIALIST-AGENT: {agent_name}] 完成報告 ===
執行總時間: [實際執行時間]
完成狀態: ✅ 全部完成 / ⚠️ 部分完成 / ❌ 執行失敗
具體工作成果: [詳細列出所有修復、檔案、測試結果]
交接資訊: [給下一階段 Agent 的具體內容和建議]
```

**工作量分配原則:** SPECIALIST 主執行 (70-80%) + BMAD 協調 (20-30%)

## 🔍 前端狀態檢查流程

### Step 1: 檢測當前項目前端組件
```
自動掃描和分析：
- 檢查項目中是否有前端相關文件
- 識別使用的前端框架（React/Vue/Angular/HTML）
- 評估前端複雜度和測試需求
- 檢查 MCP Playwright 配置狀態
```

### Step 2: 分析 BMAD 前端流程狀態
```
讀取和分析：
- .bmad/auto-flow-state.json 中的前端相關狀態
- .bmad/flow-results/ 中的前端驗證結果
- Playwright 測試執行歷史和結果
- 前端 Bug 修復記錄和模式
```

### Step 3: MCP Playwright 工具檢查
```
自動檢查 MCP Playwright 可用性：
- 嘗試執行基本 Playwright 命令
- 檢查瀏覽器驅動安裝狀態  
- 驗證測試環境配置
- 報告工具可用性和限制
```

## 📊 前端狀態報告格式

### 完整狀態報告：
```
🎨 BMAD 前端驗證狀態報告
========================================
📅 報告時間: [current_timestamp]
🏗️ 項目類型: [frontend/backend/fullstack]

🔍 前端檢測狀態:
  └─ 前端組件: [detected/not_detected]
  └─ 框架類型: [React/Vue/Angular/HTML/None]
  └─ 複雜度評級: [Simple/Medium/Complex]
  └─ UI 組件數量: [estimated_count]

🧪 MCP Playwright 狀態:
  └─ 工具可用性: [Available/Not Available/Partial]
  └─ 瀏覽器驅動: [Chrome/Firefox/Safari status]
  └─ 測試環境: [Local/Staging/Production]
  └─ 最後測試: [timestamp or "從未執行"]

📋 當前 BMAD 流程狀態:
  └─ 活躍流程: [bmad-flow-start/bmad-flow-fix/none]
  └─ 當前階段: [具體階段名稱]
  └─ 前端相關: [Yes/No]
  └─ 需要驗證: [Pending/Completed/Failed]

✅ 已完成前端驗證:
  └─ 視覺測試: [數量] 個測試通過
  └─ 功能測試: [數量] 個測試通過
  └─ 響應式測試: [Desktop/Tablet/Mobile 狀態]
  └─ 跨瀏覽器: [Chrome/Firefox/Safari 狀態]

❌ 待處理前端問題:
  └─ 失敗測試: [數量] 個需要修復
  └─ 警告項目: [數量] 個需要關注
  └─ 性能問題: [數量] 個待優化
  └─ 兼容性問題: [數量] 個待解決

🔄 最近前端活動:
  └─ 最近 Bug 修復: [最近的前端 bug 修復]
  └─ 最近測試: [最近的 Playwright 測試]
  └─ 最近部署: [最近的前端部署]
  └─ 最近回滾: [如有前端相關回滾]

📈 前端品質指標:
  └─ 測試覆蓋率: [percentage]%
  └─ 視覺一致性: [Good/Fair/Poor]
  └─ 性能評分: [Core Web Vitals 評分]
  └─ 可訪問性: [WCAG 合規程度]

🚨 風險評估:
  └─ 高風險項目: [列出需要立即處理的問題]
  └─ 中風險項目: [列出需要關注的問題]
  └─ 建議動作: [具體的改進建議]

💡 建議操作:
  └─ 立即執行: [urgent_actions]
  └─ 計劃執行: [planned_actions] 
  └─ 可選優化: [optional_improvements]
```

## 🎯 專用前端檢查命令

### MCP Playwright 連接測試:
```
自動執行基本 Playwright 檢查:
1. /playwright_navigate "about:blank"
2. /playwright_screenshot "connectivity-test"
3. /playwright_get_visible_text
4. 報告連接狀態和性能
```

### 前端文件掃描:
```
掃描項目中的前端相關文件:
- *.html, *.css, *.js, *.ts, *.jsx, *.tsx, *.vue
- package.json 中的前端依賴
- 構建配置文件 (webpack, vite, etc.)
- 組件目錄結構分析
```

### 測試歷史分析:
```
分析 .bmad/flow-results/ 中的前端測試:
- Playwright 測試執行記錄
- 視覺回歸測試結果
- 性能測試趨勢
- 失敗模式分析
```

## 🔧 問題診斷和建議

### 常見問題自動診斷:
```
IF MCP Playwright 不可用:
    診斷: "需要配置 MCP Playwright 連接"
    建議: "請檢查 MCP 設置並安裝 Playwright 工具"
    文檔: "參考 Anthropic MCP Playwright 配置指南"

IF 前端文件存在但未檢測到:
    診斷: "前端檢測邏輯可能需要調整"
    建議: "手動指定前端框架類型"
    命令: "/bmad-frontend-config set-framework [framework]"

IF 測試失敗率過高:
    診斷: "前端測試環境或代碼質量問題"
    建議: "運行 /bmad-frontend-debug 進行深度分析"
    重點: "檢查測試環境配置和瀏覽器兼容性"
```

### 性能優化建議:
```
基於測試結果自動生成建議:
- 載入時間優化 (if performance.timing > 3000ms)
- 圖片壓縮建議 (if large images detected)
- CSS/JS 壓縮建議 (if unminified files)
- 懶載入實現建議 (if heavy content detected)
```

## 📱 響應式設計狀態

### 設備兼容性檢查:
```
自動檢查多設備支援：

Desktop (1920x1080):
  └─ 佈局: [正常/異常]
  └─ 功能: [正常/異常] 
  └─ 性能: [Good/Fair/Poor]

Tablet (768x1024):
  └─ 佈局: [正常/異常]
  └─ 觸控: [正常/異常]
  └─ 導航: [正常/異常]

Mobile (375x667):
  └─ 佈局: [正常/異常]
  └─ 觸控: [正常/異常]
  └─ 滾動: [正常/異常]
```

## 🚀 快速修復建議

### 自動生成修復命令:
```
基於檢測到的問題自動建議：

IF 檢測到 CSS 問題:
    建議命令: "/bmad-flow-fix CSS 樣式在 [browser] 中顯示異常"

IF 檢測到互動問題:
    建議命令: "/bmad-flow-fix [element] 點擊無反應或功能異常"

IF 檢測到性能問題:
    建議命令: "/bmad-flow-fix 頁面載入速度過慢，影響用戶體驗"

IF 檢測到兼容性問題:
    建議命令: "/bmad-flow-fix 在 [browser] 中功能不正常或顯示錯誤"
```

### 預防性維護建議:
```
定期檢查項目:
- 每週執行: /bmad-frontend-status
- 每月執行: 完整的跨瀏覽器測試
- 每季執行: 性能和可訪問性評估
- 發佈前: 完整的視覺回歸測試
```

## 📊 執行示例

```
用戶: /bmad-frontend-status

自動檢查和報告:

🎨 BMAD 前端驗證狀態報告
========================================
📅 報告時間: 2025-01-15 16:45:00
🏗️ 項目類型: Full-stack (Frontend + Backend)

🔍 前端檢測狀態:
  └─ 前端組件: Detected
  └─ 框架類型: React + TypeScript
  └─ 複雜度評級: Medium (15 components)
  └─ UI 組件數量: 23 個組件

🧪 MCP Playwright 狀態:
  └─ 工具可用性: Available
  └─ 瀏覽器驅動: Chrome ✅, Firefox ✅, Safari ⚠️
  └─ 測試環境: Local Development
  └─ 最後測試: 2025-01-15 14:30:00

📋 當前 BMAD 流程狀態:
  └─ 活躍流程: bmad-flow-fix
  └─ 當前階段: Phase 4 - Playwright Validation
  └─ 前端相關: Yes (Safari 兼容性修復)
  └─ 需要驗證: In Progress

✅ 已完成前端驗證:
  └─ 視覺測試: 18 個測試通過
  └─ 功能測試: 12 個測試通過
  └─ 響應式測試: Desktop ✅, Tablet ✅, Mobile ⚠️
  └─ 跨瀏覽器: Chrome ✅, Firefox ✅, Safari 🔄

❌ 待處理前端問題:
  └─ 失敗測試: 2 個需要修復 (Safari shadow effects)
  └─ 警告項目: 1 個需要關注 (Mobile button overlap)
  └─ 性能問題: 0 個待優化
  └─ 兼容性問題: 1 個待解決 (Safari CSS prefix)

🔄 最近前端活動:
  └─ 最近 Bug 修復: Safari 陰影效果問題 (進行中)
  └─ 最近測試: Playwright 跨瀏覽器測試 (30分鐘前)
  └─ 最近部署: 前端組件更新 (2天前)
  └─ 最近回滾: 無

📈 前端品質指標:
  └─ 測試覆蓋率: 85%
  └─ 視覺一致性: Good
  └─ 性能評分: 92/100 (Core Web Vitals)
  └─ 可訪問性: WCAG AA 合規 (95%)

🚨 風險評估:
  └─ 高風險項目: 無
  └─ 中風險項目: Safari 兼容性需要關注
  └─ 建議動作: 完成當前 Safari 修復，增加自動化跨瀏覽器測試

💡 建議操作:
  └─ 立即執行: 完成 Safari CSS prefix 修復
  └─ 計劃執行: 設置自動化 Safari 測試流程
  └─ 可選優化: 添加更多視覺回歸測試基準
```

立即開始前端驗證狀態監控和分析。
