"""
API 超時保護中間件
統一處理所有 API 端點的超時和錯誤響應
"""

import asyncio
import time
from typing import Callable, Dict, Any
from fastapi import Request, Response, HTTPException
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger


class TimeoutProtectionMiddleware(BaseHTTPMiddleware):
    """超時保護中間件"""
    
    def __init__(
        self, 
        app, 
        global_timeout: int = 30,
        enable_logging: bool = True
    ):
        super().__init__(app)
        self.global_timeout = global_timeout
        self.enable_logging = enable_logging
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """處理請求的超時保護"""
        
        start_time = time.time()
        method = request.method
        url_path = request.url.path
        
        if self.enable_logging:
            logger.debug(f"🔄 {method} {url_path} - 開始處理")
        
        try:
            # 使用全局超時保護
            async with asyncio.timeout(self.global_timeout):
                response = await call_next(request)
                
            elapsed_time = time.time() - start_time
            
            if self.enable_logging:
                logger.debug(f"✅ {method} {url_path} - 完成 ({elapsed_time:.2f}s)")
                
            return response
            
        except asyncio.TimeoutError:
            elapsed_time = time.time() - start_time
            
            if self.enable_logging:
                logger.warning(f"⏰ {method} {url_path} - 全局超時 ({elapsed_time:.2f}s)")
            
            return JSONResponse(
                status_code=408,
                content={
                    "status": "timeout",
                    "message": f"請求處理超時（{self.global_timeout}秒），請稍後重試",
                    "method": method,
                    "path": url_path,
                    "elapsed_time": round(elapsed_time, 2),
                    "timestamp": time.time()
                }
            )
            
        except HTTPException as e:
            elapsed_time = time.time() - start_time
            
            if self.enable_logging:
                logger.warning(f"⚠️ {method} {url_path} - HTTP異常: {e.status_code}")
            
            # 保持原始 HTTPException 的處理方式
            raise e
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            
            if self.enable_logging:
                logger.error(f"❌ {method} {url_path} - 未處理異常: {e}")
            
            return JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "message": "服務器內部錯誤",
                    "method": method,
                    "path": url_path,
                    "elapsed_time": round(elapsed_time, 2),
                    "timestamp": time.time()
                }
            )


class RequestTimeoutLogger:
    """請求超時日誌記錄器"""
    
    def __init__(self):
        self.timeout_stats: Dict[str, Any] = {
            "total_requests": 0,
            "timeout_requests": 0,
            "average_response_time": 0.0,
            "max_response_time": 0.0,
            "endpoint_stats": {}
        }
    
    def log_request(
        self, 
        method: str, 
        path: str, 
        elapsed_time: float, 
        is_timeout: bool = False
    ):
        """記錄請求統計"""
        
        self.timeout_stats["total_requests"] += 1
        
        if is_timeout:
            self.timeout_stats["timeout_requests"] += 1
        
        # 更新平均響應時間
        total = self.timeout_stats["total_requests"]
        current_avg = self.timeout_stats["average_response_time"]
        self.timeout_stats["average_response_time"] = (
            (current_avg * (total - 1) + elapsed_time) / total
        )
        
        # 更新最大響應時間
        if elapsed_time > self.timeout_stats["max_response_time"]:
            self.timeout_stats["max_response_time"] = elapsed_time
        
        # 端點統計
        endpoint_key = f"{method} {path}"
        if endpoint_key not in self.timeout_stats["endpoint_stats"]:
            self.timeout_stats["endpoint_stats"][endpoint_key] = {
                "count": 0,
                "timeouts": 0,
                "avg_time": 0.0,
                "max_time": 0.0
            }
        
        endpoint_stat = self.timeout_stats["endpoint_stats"][endpoint_key]
        endpoint_stat["count"] += 1
        
        if is_timeout:
            endpoint_stat["timeouts"] += 1
        
        # 更新端點平均時間
        count = endpoint_stat["count"]
        current_avg = endpoint_stat["avg_time"]
        endpoint_stat["avg_time"] = (
            (current_avg * (count - 1) + elapsed_time) / count
        )
        
        if elapsed_time > endpoint_stat["max_time"]:
            endpoint_stat["max_time"] = elapsed_time
    
    def get_timeout_report(self) -> Dict[str, Any]:
        """獲取超時統計報告"""
        
        total = self.timeout_stats["total_requests"]
        timeouts = self.timeout_stats["timeout_requests"]
        
        return {
            "summary": {
                "total_requests": total,
                "timeout_requests": timeouts,
                "timeout_rate": (timeouts / total * 100) if total > 0 else 0,
                "average_response_time": round(self.timeout_stats["average_response_time"], 2),
                "max_response_time": round(self.timeout_stats["max_response_time"], 2)
            },
            "endpoint_details": {
                endpoint: {
                    "count": stats["count"],
                    "timeouts": stats["timeouts"],
                    "timeout_rate": (stats["timeouts"] / stats["count"] * 100) if stats["count"] > 0 else 0,
                    "avg_time": round(stats["avg_time"], 2),
                    "max_time": round(stats["max_time"], 2)
                }
                for endpoint, stats in self.timeout_stats["endpoint_stats"].items()
            }
        }
    
    def log_summary(self):
        """輸出統計摘要到日誌"""
        
        report = self.get_timeout_report()
        summary = report["summary"]
        
        logger.info("📊 API 超時統計摘要:")
        logger.info(f"  總請求數: {summary['total_requests']}")
        logger.info(f"  超時請求數: {summary['timeout_requests']}")
        logger.info(f"  超時率: {summary['timeout_rate']:.2f}%")
        logger.info(f"  平均響應時間: {summary['average_response_time']}秒")
        logger.info(f"  最大響應時間: {summary['max_response_time']}秒")
        
        # 顯示超時最多的端點
        endpoint_details = report["endpoint_details"]
        high_timeout_endpoints = [
            (endpoint, stats) 
            for endpoint, stats in endpoint_details.items() 
            if stats["timeout_rate"] > 0
        ]
        
        if high_timeout_endpoints:
            logger.warning("⚠️ 有超時記錄的端點:")
            for endpoint, stats in sorted(high_timeout_endpoints, key=lambda x: x[1]["timeout_rate"], reverse=True):
                logger.warning(
                    f"  {endpoint}: {stats['timeout_rate']:.1f}% "
                    f"({stats['timeouts']}/{stats['count']} 請求)"
                )


# 全局統計記錄器實例
timeout_logger = RequestTimeoutLogger()


def get_timeout_statistics() -> Dict[str, Any]:
    """獲取全局超時統計信息"""
    return timeout_logger.get_timeout_report()


def log_timeout_summary():
    """輸出超時統計摘要"""
    timeout_logger.log_summary()


# 導出公共接口
__all__ = [
    'TimeoutProtectionMiddleware',
    'RequestTimeoutLogger',
    'timeout_logger',
    'get_timeout_statistics',
    'log_timeout_summary'
]