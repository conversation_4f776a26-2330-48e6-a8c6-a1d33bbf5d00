#!/usr/bin/env python3
"""
功能驗證測試腳本
測試所有現有頁面是否正常載入，驗證所有現有功能是否正常運作
"""

import os
import sys
import time
import requests
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any
from urllib.parse import urljoin

# 設定中文編碼環境變數（Windows 兼容性）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['LANG'] = 'zh_TW.UTF-8'

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class FunctionalVerificationTester:
    """功能驗證測試器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化測試器
        
        Args:
            base_url: Flask 應用程式的基礎 URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.failed_tests = []
        
        # 設定請求超時
        self.session.timeout = 10
        
        # 設定請求標頭
        self.session.headers.update({
            'User-Agent': 'FunctionalVerificationTester/1.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-TW,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        })
    
    def log_test_result(self, test_name: str, success: bool, message: str = "", 
                       response_time: float = 0, status_code: int = None):
        """
        記錄測試結果
        
        Args:
            test_name: 測試名稱
            success: 是否成功
            message: 測試訊息
            response_time: 回應時間（毫秒）
            status_code: HTTP 狀態碼
        """
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'response_time_ms': round(response_time * 1000, 2),
            'status_code': status_code,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self.test_results.append(result)
        
        if not success:
            self.failed_tests.append(result)
        
        # 即時輸出測試結果
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        if response_time > 0:
            print(f"    回應時間: {result['response_time_ms']}ms")
        if status_code:
            print(f"    狀態碼: {status_code}")
        print()
    
    def test_page_load(self, path: str, expected_status: int = 200, 
                      test_name: str = None) -> Tuple[bool, requests.Response]:
        """
        測試頁面載入
        
        Args:
            path: 頁面路徑
            expected_status: 預期的 HTTP 狀態碼
            test_name: 測試名稱（可選）
            
        Returns:
            Tuple[bool, requests.Response]: (是否成功, 回應物件)
        """
        if test_name is None:
            test_name = f"頁面載入測試: {path}"
        
        url = urljoin(self.base_url, path)
        
        try:
            start_time = time.time()
            response = self.session.get(url)
            response_time = time.time() - start_time
            
            success = response.status_code == expected_status
            
            if success:
                message = f"頁面成功載入 - URL: {url}"
            else:
                message = f"頁面載入失敗 - URL: {url}, 預期狀態碼: {expected_status}, 實際狀態碼: {response.status_code}"
            
            self.log_test_result(test_name, success, message, response_time, response.status_code)
            
            return success, response
            
        except requests.exceptions.RequestException as e:
            self.log_test_result(test_name, False, f"請求異常: {str(e)}")
            return False, None
    
    def test_health_endpoint(self) -> bool:
        """測試健康檢查端點"""
        success, response = self.test_page_load('/health', test_name="健康檢查端點測試")
        
        if success and response:
            try:
                health_data = response.json()
                
                # 檢查健康檢查回應格式
                required_fields = ['status', 'timestamp', 'modules']
                missing_fields = [field for field in required_fields if field not in health_data]
                
                if missing_fields:
                    self.log_test_result("健康檢查格式驗證", False, 
                                       f"缺少必要欄位: {missing_fields}")
                    return False
                
                # 檢查模組狀態
                modules = health_data.get('modules', {})
                expected_modules = ['email', 'analytics', 'file_management', 'eqc', 'tasks', 'monitoring']
                
                for module in expected_modules:
                    if module not in modules:
                        self.log_test_result(f"模組檢查: {module}", False, f"模組 {module} 未在健康檢查中報告")
                        return False
                    elif not modules[module]:
                        self.log_test_result(f"模組檢查: {module}", False, f"模組 {module} 狀態為不健康")
                        return False
                    else:
                        self.log_test_result(f"模組檢查: {module}", True, f"模組 {module} 狀態正常")
                
                self.log_test_result("健康檢查格式驗證", True, "健康檢查回應格式正確")
                return True
                
            except json.JSONDecodeError:
                self.log_test_result("健康檢查格式驗證", False, "健康檢查回應不是有效的 JSON")
                return False
        
        return False
    
    def test_static_resources(self) -> bool:
        """測試靜態資源載入"""
        static_resources = [
            '/favicon.ico',
            '/static/css/base.css',
            '/static/js/main.js',
            '/static/images/favicon.ico'
        ]
        
        all_success = True
        
        for resource in static_resources:
            success, _ = self.test_page_load(resource, test_name=f"靜態資源測試: {resource}")
            if not success:
                all_success = False
        
        return all_success
    
    def test_module_pages(self) -> bool:
        """測試各模組的主要頁面"""
        module_pages = {
            'email': [
                '/email/',
                '/email/inbox',
                '/email/compose',
                '/email/settings'
            ],
            'analytics': [
                '/analytics/',
                '/analytics/dashboard',
                '/analytics/reports',
                '/analytics/vendor_analysis'
            ],
            'file_management': [
                '/files/',
                '/files/manager',
                '/files/upload',
                '/files/browser'
            ],
            'eqc': [
                '/eqc/',
                '/eqc/dashboard',
                '/eqc/history',
                '/eqc/quality_check'
            ],
            'tasks': [
                '/tasks/',
                '/tasks/dashboard',
                '/tasks/queue',
                '/tasks/scheduler'
            ],
            'monitoring': [
                '/monitoring/',
                '/monitoring/dashboard',
                '/monitoring/health',
                '/monitoring/database'
            ]
        }
        
        all_success = True
        
        for module_name, pages in module_pages.items():
            print(f"\n=== 測試 {module_name} 模組 ===")
            
            for page in pages:
                success, _ = self.test_page_load(page, test_name=f"{module_name} 模組頁面: {page}")
                if not success:
                    all_success = False
        
        return all_success
    
    def test_main_routes(self) -> bool:
        """測試主要路由"""
        main_routes = [
            ('/', 302, "主頁重定向測試"),  # 應該重定向到 /email/inbox
            ('/test', 200, "測試頁面"),
            ('/debug_sender_display.html', 200, "調試頁面")
        ]
        
        all_success = True
        
        for route, expected_status, test_name in main_routes:
            success, _ = self.test_page_load(route, expected_status, test_name)
            if not success:
                all_success = False
        
        return all_success
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """
        執行全面的功能驗證測試
        
        Returns:
            Dict[str, Any]: 測試結果摘要
        """
        print("🚀 開始執行功能驗證測試...")
        print(f"測試目標: {self.base_url}")
        print("=" * 60)
        
        start_time = time.time()
        
        # 1. 測試健康檢查端點
        print("\n📋 1. 健康檢查端點測試")
        health_success = self.test_health_endpoint()
        
        # 2. 測試主要路由
        print("\n🏠 2. 主要路由測試")
        main_routes_success = self.test_main_routes()
        
        # 3. 測試靜態資源
        print("\n📁 3. 靜態資源測試")
        static_success = self.test_static_resources()
        
        # 4. 測試各模組頁面
        print("\n🔧 4. 模組頁面測試")
        modules_success = self.test_module_pages()
        
        total_time = time.time() - start_time
        
        # 生成測試摘要
        total_tests = len(self.test_results)
        passed_tests = total_tests - len(self.failed_tests)
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        summary = {
            'overall_success': len(self.failed_tests) == 0,
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': len(self.failed_tests),
            'success_rate': round(success_rate, 2),
            'total_time_seconds': round(total_time, 2),
            'test_categories': {
                'health_check': health_success,
                'main_routes': main_routes_success,
                'static_resources': static_success,
                'module_pages': modules_success
            },
            'failed_test_details': self.failed_tests
        }
        
        return summary
    
    def print_summary(self, summary: Dict[str, Any]):
        """
        列印測試摘要
        
        Args:
            summary: 測試結果摘要
        """
        print("\n" + "=" * 60)
        print("📊 功能驗證測試結果摘要")
        print("=" * 60)
        
        overall_status = "✅ 全部通過" if summary['overall_success'] else "❌ 有測試失敗"
        print(f"整體狀態: {overall_status}")
        print(f"總測試數: {summary['total_tests']}")
        print(f"通過測試: {summary['passed_tests']}")
        print(f"失敗測試: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']}%")
        print(f"總執行時間: {summary['total_time_seconds']} 秒")
        
        print("\n📋 測試類別結果:")
        for category, success in summary['test_categories'].items():
            status = "✅" if success else "❌"
            print(f"  {status} {category}")
        
        if summary['failed_tests']:
            print("\n❌ 失敗的測試詳情:")
            for failed_test in summary['failed_tests']:
                print(f"  - {failed_test['test_name']}: {failed_test['message']}")
        
        print("\n" + "=" * 60)
    
    def save_results(self, filename: str = "functional_verification_results.json"):
        """
        儲存測試結果到檔案
        
        Args:
            filename: 結果檔案名稱
        """
        results_data = {
            'test_metadata': {
                'base_url': self.base_url,
                'test_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_tests': len(self.test_results),
                'failed_tests': len(self.failed_tests)
            },
            'test_results': self.test_results
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, ensure_ascii=False, indent=2)
            print(f"📄 測試結果已儲存到: {filename}")
        except Exception as e:
            print(f"❌ 儲存測試結果失敗: {str(e)}")


def main():
    """主函數"""
    # 檢查 Flask 應用程式是否正在運行
    base_url = "http://localhost:8000"
    
    print("🔍 檢查 Flask 應用程式是否正在運行...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Flask 應用程式正在運行")
        else:
            print(f"⚠️  Flask 應用程式回應異常，狀態碼: {response.status_code}")
    except requests.exceptions.RequestException:
        print("❌ 無法連接到 Flask 應用程式")
        print("請確保 Flask 應用程式正在 http://localhost:8000 運行")
        print("可以使用以下命令啟動:")
        print("  cd frontend && python app.py")
        return False
    
    # 執行功能驗證測試
    tester = FunctionalVerificationTester(base_url)
    summary = tester.run_comprehensive_test()
    
    # 列印摘要
    tester.print_summary(summary)
    
    # 儲存結果
    tester.save_results()
    
    return summary['overall_success']


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)