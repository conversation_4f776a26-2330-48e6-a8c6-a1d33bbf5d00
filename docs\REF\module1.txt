
' 宣告 GetCursorPos 函式

Private Type POINTAPI
    x As Long
    y As Long
End Type
Public Const maxRowN As Long = 165536
Public Const maxColumnN As Integer = 6810 '2019/01/14
Public Const maxPassBinN As Integer = 4
Public Const maxSiteN As Integer = 32 '2018/10/03
Dim mySiteColumnN As Integer, myMismatch(256) As Integer, myMaxPassBinN As Integer, myTesterType As Integer
Dim myHideMsg As Boolean, myToCheckLimitsOfSubLots As Boolean
Dim myStartTimer, myFinishTimer, myTotalTimer
Dim myQaOnly '2021/12/14
Public Const sourcePath As String = "\\************\test_log"
Public Const sourcePath_temp As String = "\\************\test_log_temp"

Public Const tempPath As String = "d:\temp"
Public Const netpath As String = "\\************\temp_7days"
Public Const mailnetpath As String = "\\************\te\tong.wu\AutoLy\mail.xlsx"
Dim CC_FLAG As Boolean
Public Const CC_FLAG_Set As Boolean = True
Public NextRun As Double

Sub StartTimerDual()
    NextRun = Now + TimeValue("00:00:37")
    Application.OnTime NextRun, "CheckFile"
End Sub

Sub CheckFile()
    Dim strContent As String
    Dim strFileName As String: strFileName = "\\************\te\tong.wu\AutoLy\Dual_Flag.txt"
    
    If Dir(strFileName) <> "" Then ' Check if file exists
        ' Read file content
        Open strFileName For Input As #1
        Input #1, strContent
        Close #1
        
        ' Check content
        If Trim(strContent) = "1" Then
            ReadtemplotFile
            'MsgBox "Content is 1"
            ' You can also return 1 here or do other operations
        End If
    End If
    
    StartTimerDual  ' Reschedule the procedure
End Sub

Sub abc()
    Dim inputString As String
    'inputString = "G753T11U(金???) LOT：RPM8A.G ??批次 C22121345 ??低良反?" 'ChuzhouInfoFromStrings
    'inputString = "台?致新G2262BTR1U，lot：NTK65.2，??批?：TS23031812.1，??良率：95.51%" 'SuqianInfoFromStrings
    'inputString = "FW: 台?致新G2892K21D(CA)(??)，lot：YHW0019.4，??批?：TS23035654.3，??良率?96.87%。" 'SuqianInfoFromStrings
    'inputString = "FW: 台?致新G2262BTR1U，lot：NTK65.2，??批?：TS23031812.1，??良率：95.51%" 'SuqianInfoFromStrings
    'inputString = "[ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D LowYield = 95.09% < 98.5%" 'LINGSENInfoFromStrings
    'inputString = "FW: [LowYield] GMT WQFN0403565L G2518KK1U(G2518XXXXDB1KK1451) Run#333124 Lot#Q2VC62.1M LowYield =96.85% < 97%" 'LINGSENInfoFromStrings
    'inputString = "FW: [ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D LowYield = 95.09% < 98.5%" 'LINGSENInfoFromStrings
    ' inputString = "G693L293_ZG032303025結批lowyield 98.97%" 'Nanotech
    'inputString = "ZG032203026 G693L293"
    'inputString = "FW: TW083_G529A1TB1U(BP)_SOT236230419299   低良率/OS超?" 'TSHT
    'inputString = "FW: Low yield hold lot G2732PK81D-K(AD) PGM23070007 DMCSK.1U" 'MSECZD
    'inputString = "FW: G5619RZ1U-H4XU50.1F??OS超?" 'NFME
    'inputString = "FW: KUI  Q48W91.1 B802-1V KUIC31N001-D003量?批次??良率98.4%<98.5%(指?)，主要不良?BIN6,???据已上?至FTP,??考?理，??" 'JCET
    'inputString = "FW: TW083-G2732PK81D-K(AD-CTAXP)-WA330L30201工?低良率" xaht
    inputString = "FW: TW083_?天西安_G2794AK11U-K(AB)_KD29114.8_WB070W50102_Hold Lot反?" 'xaht
    
    Dim product As String
    Dim moString As String
    Dim lotString As String
    Dim yieldString As String
    'Call RenameFolders("D:\temp\G2566")
    'CopyFolderCreateIfNotExist "M8321U31U(AC1)", "F2390324A1"
    'CopyFolderCreateIfNotExist2 "M8321U31U(AC1)", "F2390324A1"
    'NFMEInfoFromStrings inputString, product, moString, lotString, yieldString
    'SendSummaryEmail
'    XAHTInfoFromStrings inputString, product, moString, lotString, yieldString
'    filedownloadok = CopyFilesGTK(moString, tempPath & "\" & product & "\" & UCase(moString), product, lot)
    'CopyFilesJCET moString, tempPath & "\" & product & "\" & UCase(moString), product, lotString
'    CopyFilesXAHT moString, tempPath & "\" & product & "\" & UCase(moString), product, lotString
'    Dim fileB As Object
'    Set fileB = CreateObject("Scripting.FileSystemObject").GetFile("D:\temp\G2772HJ1U-A\YNT872A415859" & "\" & "EQCTOTALDATA.csv")
'    fileBContents = ""
''    On Error Resume Next
''        Open fileB For Input As #1
''        fileBContents = Input$(LOF(1), 1)
''        Close #1
'
'    Set fileStream = fileB.OpenAsTextStream(1, -2) ' 1 = ForReading, -2 = Default encoding
'
'    fileBContents = fileStream.ReadAll
   ' fileStream.Close
   XAHTInfoFromStrings2 inputString, product, moString, lotString, yieldString
End Sub
Function CopyFilesXAHT(ByVal fileName As String, ByVal filetemp As String, Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathJCET As String
    Dim destinationPath As String
    Dim file As String
    Dim newestFile As String
    Dim tempfilepatch As String
    Dim newestDate As Date
    Dim copyMsgBox As Object
    Dim uf As UserForm
    Dim startTime As Double
    Dim elapsedTime As Double
    Dim lotfile() As String
    Dim i  As Integer
    ReDim lotfile(0)
    i = 0
    sourcePathXAHT = sourcePath & "\XAHT\temp\"
    destinationPath = filetemp
    If CreateFolders(pd, UCase(fileName)) = False Then
        sourcePathXAHT = False
        Exit Function
    End If
    
    file = Dir(sourcePathXAHT & "*" & fileName & "*") 'MO
    Do While file <> ""
        If Right(file, 4) = ".zip" Or Right(file, 4) = ".rar" Or Right(file, 3) = ".7z" Then
            If FileDateTime(sourcePathXAHT & file) > newestDate Then
                newestDate = FileDateTime(sourcePathXAHT & file)
                newestFile = file
            End If
        End If
        file = Dir()
    Loop
    
    If newestFile <> "" Then
        If Dir(destinationPath & "\" & newestFile) <> "" Then
            If FileLen(sourcePathXAHT & newestFile) = FileLen(destinationPath & "\" & newestFile) Then
                ' File already exists and is the same size, do not copy
'                Set copyMsgBox = CreateObject("WScript.Shell")
'                copyMsgBox.Popup "Copying file is ok By youself,", 3, "Copy File", vbInformation + vbSystemModal
'                Set copyMsgBox = Nothing
                CopyFilesXAHT = True
                Exit Function
            End If
        End If

        'Set copyMsgBox = CreateObject("WScript.Shell")
        ' File does not exist or is a different size, copy the file
        'copyMsgBox.Popup "Copying file, please wait...", 2, "Copy File", vbInformation + vbSystemModal
        'startTime = Timer
        DelayOneSecond
        FileCopy sourcePathXAHT & newestFile, destinationPath & "\" & newestFile
        'If FileLen(destinationPath & "\" & newestFile) > 0 Then
        If FileLen(sourcePathXAHT & newestFile) = FileLen(destinationPath & "\" & newestFile) Then
            CopyFilesXAHT = True
            'elapsedTime = Timer - startTime
           ' copyMsgBox.Popup "XAHT Copy file OK. Time taken: " & elapsedTime & " seconds.", 3, "Copy File", vbInformation + vbSystemModal
        Else
           
            CopyFilesXAHT = False
            'copyMsgBox.Popup "XAHT Copy file Fail. Time taken: " & elapsedTime & " seconds.", 3, "Copy File", vbInformation + vbSystemModal
        End If

    End If

End Function
Function XAHTInfoFromStrings(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String, ByRef yieldString As String)
    Dim productString As String
    Dim product_temp As String
    Dim first_word As Integer
    first_word = 0
    Dim words() As String
    words = Split(inputString, " ")
    For i = 0 To UBound(words)
        If Len(words(i)) > 4 Then
            If InStr(1, LCase(words(i)), "wa", vbTextCompare) > 0 Or InStr(1, LCase(words(i)), "GYC", vbTextCompare) > 0 Then
                moString = Left(words(i), 11)
                first_word = i
                Exit For
            End If
        Else
            If InStr(1, LCase(words(i)), "wa", vbTextCompare) > 0 And first_word = 0 Then
                first_word = i
            End If
       
        End If
        
    Next i

    If (Len(words(first_word)) > 0) Then
        product = words(i - 1)
    End If
 
End Function
Function XAHTInfoFromStrings2(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String, ByRef yieldString As String)
    ' 初始化返回值
    product = ""
    moString = ""
    lotString = ""
    yieldString = ""
    
    ' 去[EXCEPT_CHAR]可能的前綴 "FW: "
    Dim cleanString As String
    If InStr(inputString, "FW: ") = 1 Then
        cleanString = Mid(inputString, 5)
    Else
        cleanString = inputString
    End If
    
    ' 使用下劃線分割字符串
    Dim parts() As String
    parts = Split(cleanString, "_")
    
    ' 檢查是否有足夠的部分可以提取
    If UBound(parts) >= 4 Then
        ' 產品型號總是在第三個部分 (索引 2)
        product = parts(2)
        
        ' Lot 編號總是在第四個部分 (索引 3)
        lotString = parts(3)
        
        ' Yield 字符串總是在第五個部分 (索引 4)
        moString = parts(4)
    End If
    
    ' 返回成功
    XAHTInfoFromStrings2 = True
End Function
Function CopyFilesJCET(ByVal fileName As String, ByVal filetemp As String, Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathJCET As String
    Dim destinationPath As String
    Dim file As String
    Dim newestFile As String
    Dim tempfilepatch As String
    Dim newestDate As Date
    Dim copyMsgBox As Object
    Dim uf As UserForm
    Dim startTime As Double
    Dim elapsedTime As Double
    Dim lotfile() As String
    Dim i  As Integer
    ReDim lotfile(0)
    i = 0
    sourcePathJCET = sourcePath & "\JCET\JCET\"
    destinationPath = filetemp
    If CreateFolders(pd, UCase(fileName)) = False Then
        sourcePathJCET = False
        Exit Function
    End If
    
    file = Dir(sourcePathJCET & "*" & fileName & "*") 'MO
    Do While file <> ""
        If Right(file, 4) = ".zip" Or Right(file, 4) = ".rar" Or Right(file, 3) = ".7z" Then
            If FileDateTime(sourcePathJCET & file) > newestDate Then
                newestDate = FileDateTime(sourcePathJCET & file)
                newestFile = file
            End If
        End If
        file = Dir()
    Loop
    
    If newestFile <> "" Then
        If Dir(destinationPath & "\" & newestFile) <> "" Then
            If FileLen(sourcePathJCET & newestFile) = FileLen(destinationPath & "\" & newestFile) Then
                ' File already exists and is the same size, do not copy
'                Set copyMsgBox = CreateObject("WScript.Shell")
'                copyMsgBox.Popup "Copying file is ok By youself,", 3, "Copy File", vbInformation + vbSystemModal
'                Set copyMsgBox = Nothing
                CopyFilesJCET = True
                Exit Function
            End If
        End If

        'Set copyMsgBox = CreateObject("WScript.Shell")
        ' File does not exist or is a different size, copy the file
        'copyMsgBox.Popup "Copying file, please wait...", 2, "Copy File", vbInformation + vbSystemModal
        'startTime = Timer
        DelayOneSecond
        FileCopy sourcePathJCET & newestFile, destinationPath & "\" & newestFile
        'If FileLen(destinationPath & "\" & newestFile) > 0 Then
        If FileLen(sourcePathJCET & newestFile) = FileLen(destinationPath & "\" & newestFile) Then
            CopyFilesJCET = True
            'elapsedTime = Timer - startTime
           ' copyMsgBox.Popup "JCET Copy file OK. Time taken: " & elapsedTime & " seconds.", 3, "Copy File", vbInformation + vbSystemModal
        Else
           
            CopyFilesJCET = False
            'copyMsgBox.Popup "JCET Copy file Fail. Time taken: " & elapsedTime & " seconds.", 3, "Copy File", vbInformation + vbSystemModal
        End If

    End If

End Function

Function JCETInfoFromStrings(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String, ByRef yieldString As String)
    Dim productString As String
    Dim product_temp As String
    Dim first_word As Integer
    first_word = 0
    Dim words() As String
    words = Split(inputString, " ")
    For i = 0 To UBound(words)
        If Len(words(i)) > 4 Then
            If InStr(1, LCase(words(i)), "KUI", vbTextCompare) > 0 Or InStr(1, LCase(words(i)), "GYC", vbTextCompare) > 0 Then
                moString = Left(words(i), 15)
                Exit For
            End If
        Else
            If InStr(1, LCase(words(i)), "KUI", vbTextCompare) > 0 Or InStr(1, LCase(words(i)), "GYC", vbTextCompare) > 0 And first_word = 0 Then
                first_word = i
            End If
       
        End If
        
    Next i

    If (UBound(words) > 0) Then
        For i = 0 To UBound(words)
            If i > first_word And Len(words(i)) > 0 Then
                lotString = words(i)
                product = words(i + 1)
                Exit For
            End If
        Next i
    End If
 
End Function
Function CopyFilesNFME(ByVal fileName As String, ByVal filetemp As String, Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathNFME As String
    Dim destinationPath As String
    Dim file As String
    Dim newestFile As String
    Dim tempfilepatch As String
    Dim newestDate As Date
    Dim copyMsgBox As Object
    Dim uf As UserForm
    Dim startTime As Double
    Dim elapsedTime As Double
    Dim lotfile() As String
    Dim i  As Integer
    ReDim lotfile(0)
    i = 0
    sourcePathNFME = sourcePath & "\NFME\FT\" & pd & "\"
    destinationPath = filetemp
    If CreateFolders(pd, UCase(fileName)) = False Then
        CopyFilesNFME = False
        Exit Function
    End If
       
    file = Dir(sourcePathNFME & fileName & "\*.*")   'mo
    Do While file <> ""
        ReDim Preserve lotfile(UBound(lotfile) + 1)
        lotfile(UBound(lotfile) - 1) = file
        file = Dir()
        i = i + 1
    Loop
    If i > 0 Then
        ReDim Preserve lotfile(UBound(lotfile) - 1)
        For i = LBound(lotfile) To UBound(lotfile)
            tempfilepatch = destinationPath & lotfile(i)
            If InStr(1, LCase(lotfile(i)), "lsr", vbTextCompare) > 0 Or InStr(1, LCase(lotfile(i)), "data.csv", vbTextCompare) > 0 Then
                FileCopy sourcePathNFME & fileName & "\" & lotfile(i), destinationPath & "\" & lotfile(i)
                CopyFilesNFME = True
            End If
        Next i
    End If

End Function
Function NFMEInfoFromStrings(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String, ByRef yieldString As String)
    Dim productString As String
    Dim product_temp As String
    productString = "\b(G|M|AT)\d{1}"
    Dim productMatch As Object
    Set productMatch = CreateObject("VBScript.RegExp")
    productMatch.pattern = productString
    productMatch.Global = True
    productMatch.IgnoreCase = True
    productMatch.MultiLine = True
    Set productMatch = productMatch.Execute(inputString)

    Dim words() As String
    words = Split(inputString, " ")

    If productMatch.count > 0 Then
        For i = 0 To UBound(words)
            If InStr(1, words(i), productMatch.Item(0)) > 0 Then
                product = words(i)
                Exit For
            End If
        Next i
    End If
    Dim words2() As String
    If (Len(product) > 0) Then
        words2 = Split(product, "-")
        product = words2(0)
    End If
    If (UBound(words) > 0) Then
        If UBound(words2) > 0 Then
            moString = Left(words2(1), 9)
            If (Len(moString) = 0) Then
                moString = Left(words(2), 9)
                If (Len(moString) = 0) Then
                    moString = Left(words(1), 9)
                End If
            End If
        Else
            moString = Left(words(3), 9)
        End If
    End If
 
End Function

Function CopyFolderCreateIfNotExist(product As String, moString As String)
    Dim fso As Object
    Dim sourceFolder As String, targetFolder As String, tempFolder As String
    
    ' 初始化 FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    If Len(product) = 0 Then
        Exit Function
    End If
    ' 指定源文件夾和目標文件夾
    sourceFolder = tempPath & "\" & product
    'tempFolder = "\\************\temp_7days\temp"
    'targetFolder = "\\************\temp_7days\temp\M8321U31U(AC1)"
    tempFolder = netpath & "\temp"
    targetFolder = tempFolder & "\" & product
    
    ' 檢查網絡路徑是否存在
    If Not fso.FolderExists(tempFolder) Then
        ' 如果不存在，創建它
        fso.CreateFolder tempFolder '\\************\temp_7days\temp
    End If
    ' 檢查網絡路徑是否存在
    If Not fso.FolderExists(tempFolder) Then
        ' 如果不存在，創建它
        fso.CreateFolder targetFolder '\\************\temp_7days\temp\M8321U31U(AC1)
    End If
    
    ' 檢查路徑是否存在
    If Not fso.FolderExists(sourceFolder) Then
        ' 如果不存在，創建它
        fso.CreateFolder sourceFolder '\\************\temp_7days\temp\M8321U31U(AC1)
    End If
    
    ' 覆制源文件夾到目標文件夾
    fso.CopyFolder sourceFolder, targetFolder
    

End Function
Function CopyFolderCreateIfNotExist2(product As String, moString As String)
    Dim fso As Object
    Dim sourceFolder As String, targetFolder As String, tempFolder As String
    
    ' 初始化 FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' 指定源文件夾和目標文件夾
    
    'tempFolder = "\\************\temp_7days\temp"
    'targetFolder = "d:\temp\M8321U31U(AC1)"
    tempFolder = netpath & "\temp"
    sourceFolder = tempFolder & "\" & product '\\************\temp_7days\temp\M8321U31U(AC1)
    targetFolder = tempPath & "\" & product

    ' 檢查網絡路徑是否存在
    If Not fso.FolderExists(tempFolder) Then
        ' 如果不存在，創建它
        fso.CreateFolder targetFolder 'd:\temp\M8321U31U(AC1)
    End If
    
    ' 覆制源文件夾到目標文件夾
    fso.CopyFolder sourceFolder, targetFolder
    

End Function

'重命名指定路徑下的子文件夾
Sub RenameFolders(targetPath As String)

Dim fso As Object '定義FileSystemObject對象
Dim folder As Object '定義folder對象
Dim subfolder As Object '定義subfolder對象

Set fso = CreateObject("Scripting.FileSystemObject")

If Not fso.FolderExists(targetPath) Then
  Exit Sub '如果目標路徑不存在,退出子程序
End If

Set folder = fso.GetFolder(targetPath) '獲取目標路徑文件夾

'遍歷所有子文件夾
For Each subfolder In folder.Subfolders

'  '如果子文件夾名為"生產資料" 20231031 fix
'  If subfolder.name = "生產資料" Then
'
'    '檢查"pd_run"文件夾是否存在
'    If Not fso.FolderExists(targetPath & "\pd_run") Then
'
'      '如果不存在,重命名該子文件夾為"pd_run"
'      subfolder.name = "pd_run"
'
'    End If
'
'  End If

  '重複上述邏輯,檢查並重命名"pd_check","eng_check"
  
  If subfolder.name = "產線驗證" Or InStr(1, subfolder.name, "產線驗證", vbTextCompare) > 0 Then

    If Not fso.FolderExists(targetPath & "\pd_check") Then

      subfolder.name = "pd_check"

    End If
    
  End If

  If subfolder.name = "工程驗證" Or InStr(1, subfolder.name, "工程驗證", vbTextCompare) > 0 Then

    If Not fso.FolderExists(targetPath & "\eng_check") Then

      subfolder.name = "eng_check"

    End If

  End If

Next subfolder

End Sub

Function MSECZDInfoFromStrings(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String, ByRef yieldString As String)
    Dim productString As String
    Dim product_temp As String
    productString = "\b(G|M|AT)\d{1}"
    Dim productMatch As Object
    Set productMatch = CreateObject("VBScript.RegExp")
    productMatch.pattern = productString
    productMatch.Global = True
    productMatch.IgnoreCase = True
    productMatch.MultiLine = True
    Set productMatch = productMatch.Execute(inputString)
    
    Dim words() As String
    words = Split(inputString, " ")

    If productMatch.count > 0 Then
        For i = 0 To UBound(words)
            If InStr(1, words(i), productMatch.Item(0)) > 0 Then
                product = words(i)
                Exit For
            End If
        Next i
    End If
    If UBound(words) >= i + 2 Then
        moString = words(i + 1)
        lotString = words(i + 2)
    End If
End Function
Function CopyFilesMSECZD(ByVal fileName As String, ByVal filetemp As String, Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathMSECZD As String
    Dim destinationPath As String
    Dim file As String
    Dim newestFile As String
    Dim tempfilepatch As String
    Dim newestDate As Date
    Dim copyMsgBox As Object
    Dim uf As UserForm
    Dim startTime As Double
    Dim elapsedTime As Double
    Dim lotfile() As String
    Dim i  As Integer
    ReDim lotfile(0)
    i = 0
    'sourcePathMSECZD = sourcePath & "\MSECZD\FT\" & pd & "\"
'    destinationPath = filetemp
'    If CreateFolders(pd, UCase(fileName)) = False Then
'        CopyFilesMSECZD = False
'        Exit Function
'    End If
'
'    'file = Dir(sourcePathMSECZD & "*" & fileName & "*") 'mo
'
'    Do While file <> ""
'        ReDim Preserve lotfile(UBound(lotfile) + 1)
'        lotfile(UBound(lotfile) - 1) = file
'        file = Dir()
'        i = i + 1
'    Loop
'    If i > 0 Then
'        ReDim Preserve lotfile(UBound(lotfile) - 1)
'        For i = LBound(lotfile) To UBound(lotfile)
'            tempfilepatch = destinationPath & lotfile(i)
'            FileCopy sourcePathMSECZD & lotfile(i), destinationPath & "\" & lotfile(i)
'            CopyFilesMSECZD = True
'        Next i
'    End If
    '20240830
    
'    If CreateFolders(pd, UCase(fileName)) = False Then
'        CopyFilesMSECZD = False
'        Exit Function
'    End If
'
'    Dim fso As Object
'    Set fso = CreateObject("Scripting.FileSystemObject")
'    sourcePathMSECZD = sourcePath_temp & "\" & "MSECZD\FT\" & pd & "\" & lot
'     destinationPath = filetemp
'    If Len(Dir(sourcePathMSECZD, vbDirectory)) > 0 Then
'        fso.CopyFolder sourcePathMSECZD, destinationPath
'        CopyFilesMSECZD = True
'        Exit Function
'    Else
'        CopyFilesMSECZD = False
'    End If

    '20240903
    
    If CreateFolders(pd, UCase(fileName)) = False Then
        CopyFilesMSECZD = False
        Exit Function
    End If

    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    sourcePathMSECZD = sourcePath & "\" & "MSECZD\FT\" & pd & "\" & lot
     destinationPath = filetemp
    If Len(Dir(sourcePathMSECZD, vbDirectory)) > 0 Then
        fso.CopyFolder sourcePathMSECZD, destinationPath
        CopyFilesMSECZD = True
        Exit Function
    Else
        CopyFilesMSECZD = False
    End If


End Function
Function CheckFileExists(line As Integer, filePath As String) As Boolean
    Dim fso As Object
    Dim file As Object
    Dim excelApp As Object
    Dim Workbook As Object
    Dim line_ColumnWidth As Integer
    Dim rng As Range
    
    ' 建立 FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' 檢查檔案是否存在
    If fso.fileExists(filePath) Then
        ' 開啟現有的 Excel 檔案
        Set excelApp = CreateObject("Excel.Application")
        Set Workbook = excelApp.Workbooks.Open(filePath)
          ' 在最後一行寫入資料
        Dim lastRow As Long
        lastRow = Workbook.Sheets(1).Cells(rows.count, 1).End(xlUp).row + 1
        
        For i = 1 To 15
            Workbook.Sheets(1).Cells(lastRow, i).value = Sheet1.Cells(line, i).value
        Next i
        
        Set rng = Workbook.Sheets(1).Range("I2:I" & lastRow)
        
        ' 設定儲存格格式為百分比，並顯示兩位小數
        rng.NumberFormat = "0.00%"
        ' 關閉工作簿與 Excel 應用程式
        Workbook.Close True
        excelApp.Quit
        
        ' 釋放物件
        Set Workbook = Nothing
        Set excelApp = Nothing
        CheckFileExists = True
    Else
        ' 創建 Excel 應用程式
        Set excelApp = CreateObject("Excel.Application")
        excelApp.Visible = False
        excelApp.DisplayAlerts = False
        
        ' 建立新的工作簿
        Set Workbook = excelApp.Workbooks.Add
        Dim firstRowData As Variant
        firstRowData = Array("Date", "Testing House", "收件人", "Device Name", "MO", "Lot ID", "檔案處理完成", "數量", "Yield", "處理時間", "Analyzation", "Decision", "Finish Date", "資料已整理完成")
        
        ' 在第一列寫入資料
        For i = 1 To UBound(firstRowData)
            Workbook.Sheets(1).Cells(1, i).value = firstRowData(i - 1)
            line_ColumnWidth = Len(Workbook.Sheets(1).Cells(1, i).value)
            If i = 7 Then '檔案處理完成
                line_ColumnWidth = line_ColumnWidth * 2
            End If
            Workbook.Sheets(1).Cells(1, i).ColumnWidth = line_ColumnWidth + 1
        Next i
        
        ' 在第二列寫入資料
        For i = 1 To 15
            Workbook.Sheets(1).Cells(2, i).value = Sheet1.Cells(line, i).value
            line_ColumnWidth = Len(Sheet1.Cells(line, i).value)
            If ((line_ColumnWidth + 1) > Workbook.Sheets(1).Cells(1, i).ColumnWidth) Then
                Workbook.Sheets(1).Cells(2, i).ColumnWidth = line_ColumnWidth + 1
            End If
        Next i

        Set rng = Workbook.Sheets(1).Range("I2:I3")
        
        ' 設定儲存格格式為百分比，並顯示兩位小數
        rng.NumberFormat = "0.00%"
        
        ' 儲存工作簿為指定的檔案格式
        Workbook.SaveAs filePath, 51
        
        ' 關閉工作簿與 Excel 應用程式
        Workbook.Close False
        excelApp.Quit
        
        ' 釋放物件
        Set Workbook = Nothing
        Set excelApp = Nothing
        
        CheckFileExists = False
    End If
End Function
Function CheckNetMailAddress(mailAddress As String) As String
    Dim r As Long
    mailAddress = Replace(mailAddress, " ", "")
    r = Sheet2.Cells(rows.count, 1).End(xlUp).row + 1
    For i = 2 To r
        If Len(Sheet2.Cells(i, 1).value) > 0 Then
            If InStr(1, Sheet2.Cells(i, 1).value, mailAddress, vbTextCompare) > 0 Then
                CheckNetMailAddress = Sheet2.Cells(i, 3).value
                Exit Function
            End If
        End If
    Next i
    CheckNetMailAddress = ""
End Function
Function WriteFileToNet(line As Integer, mailAddress As String)
    Dim name  As String
    Dim netpatfc  As String
    Dim file_exist  As Boolean
    name = CheckNetMailAddress(mailAddress)
    If Len(name) > 0 Then
        netpatfc = "\\************\te\" + name + "\\history.xlsx"
        file_exist = CheckFileExists(line, netpatfc)
    End If
End Function
Function FindTw083(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String)
    Dim tw083Pos As Integer
    Dim spacePos As Integer
    Dim words() As String
    
    tw083Pos = InStr(inputString, "TW083")
    If tw083Pos > 0 Then
        spacePos = InStr(tw083Pos, inputString, " ")
        If spacePos > 0 Then
            inputString = Mid(inputString, tw083Pos, spacePos - tw083Pos)
            If Len(inputString) > 0 Then
                words = Split(inputString, "_")
                If Len(words(1)) > 0 Then
                    product = words(1)
                End If
                If Len(words(2)) > 0 Then
                    moString = words(2)
                End If
            End If
        End If
    End If
End Function
Function CopyFilesTSHT(ByVal fileName As String, ByVal filetemp As String, Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathTSHT As String
    Dim destinationPath As String
    Dim file As String
    Dim newestFile As String
    Dim tempfilepatch As String
    Dim newestDate As Date
    Dim copyMsgBox As Object
    Dim uf As UserForm
    Dim startTime As Double
    Dim elapsedTime As Double
    Dim lotfile() As String
    Dim i  As Integer
    ReDim lotfile(0)
    i = 0
    sourcePathTSHT = sourcePath & "\TSHT\FT\"
    destinationPath = filetemp
    If CreateFolders(pd, UCase(fileName)) = False Then
        CopyFilesTSHT = False
        Exit Function
    End If
       
    file = Dir(sourcePathTSHT & "*" & fileName & "*") 'mo
    Do While file <> ""
        ReDim Preserve lotfile(UBound(lotfile) + 1)
        lotfile(UBound(lotfile) - 1) = file
        file = Dir()
        i = i + 1
    Loop
    If i > 0 Then
        ReDim Preserve lotfile(UBound(lotfile) - 1)
        For i = LBound(lotfile) To UBound(lotfile)
            tempfilepatch = destinationPath & lotfile(i)
            FileCopy sourcePathTSHT & lotfile(i), destinationPath & "\" & lotfile(i)
            CopyFilesTSHT = True
        Next i
    End If

End Function
Function ReadtemplotFile() '1
    Dim nowprocess  As Integer
    Dim one_line As Boolean
    Dim Final_Str As String
    Dim ccname_temp() As String
    Dim ccnamex As String
    Dim r As Integer
    one_line = False
    Final_Str = ""
    nowprocess = 0
    nowprocess = ReadProcessFile
    If nowprocess = 1 Then
        ScheduleSubx
        Exit Function
    Else
        On Error Resume Next
        SendSummaryEmail
        On Error GoTo 0
    End If
    r = Sheet1.Cells(rows.count, 1).End(xlUp).row + 1 '找到最後一行記錄
    For i = 1 To r - 1
        If InStr(1, Sheet1.Cells(i, 7).value, "FALSE", vbTextCompare) > 0 Then
            If Check_download_fail_file = 0 Then
                ScheduleSubx 'Application.OnTime Now + TimeValue("00:10:00"), "Module1.ReadtemplotFile"
                Exit Function
            End If
        End If
    Next i
    ' 設置文件路徑
    filePath = "\\************\te\tong.wu\AutoLy\templot.txt"
    ' 打開文件
    If Dir(filePath) <> "" Then
        Set fileA = CreateObject("Scripting.FileSystemObject").GetFile(filePath)
        Open fileA For Input As #1
        Do Until EOF(1)
            Line Input #1, fileLine
            If Len(fileLine) > 20 Then
                If one_line = False Then
                    lineElementsA = Split(fileLine, vbTab)
                    r = Sheet1.Cells(rows.count, 1).End(xlUp).row + 1 '找到最後一行記錄
                    For i = 1 To 9
                        Sheet1.Cells(r, i).value = lineElementsA(i - 1)
                    Next i
                    Sheet1.Cells(r, 15).value = lineElementsA(9)
                    one_line = True
                Else
                    Final_Str = Final_Str + fileLine + vbCrLf
                End If
            End If
        Loop
        Close #1

        If Len(Final_Str) > 0 Then
            Open filePath For Output As #89
            Print #89, Final_Str
            ' 關閉文件
            Close #89
            
            ' 設置文件路徑
            filePath = "\\************\te\tong.wu\AutoLy\Dual_Flag.txt"
            ' 打開文件以供寫入
            Open filePath For Output As #90
            Print #90, 1
            Close #90
        Else
            Dim fso As Object
            Set fso = CreateObject("Scripting.FileSystemObject")
            If fso.fileExists("\\************\te\tong.wu\AutoLy\templot.txt") Then
                fso.DeleteFile "\\************\te\tong.wu\AutoLy\templot.txt", True
            End If
            Set fso = Nothing
         ' 設置文件路徑
            filePath = "\\************\te\tong.wu\AutoLy\Dual_Flag.txt"
            ' 打開文件以供寫入
            Open filePath For Output As #90
            Print #90, 0
            Close #90
        End If
        
        On Error Resume Next
         'Application.OnTime Now + TimeValue("00:00:05"), "Module1.SendProcessingEmail"
            Application.OnTime Now + TimeValue("00:00:10"), "Module1.Check_download_fail_file"
        On Error GoTo 0

    End If
End Function
Function WritetemplotFile(ByVal aa As String)
    Dim filePath As String
    Dim fileNumber As Integer
    Dim numberedTime As String
    
    ' 設置文件路徑
    filePath = "\\************\te\tong.wu\AutoLy\templot.txt"
    
    ' 打開文件以供寫入
    fileNumber = FreeFile()
    Open filePath For Append As #fileNumber
    
    Print #fileNumber, aa
    
    ' 關閉文件
    Close #fileNumber
End Function
Function ExtractProduct(inputString As String) As String
    Dim i As Long
    For i = 1 To Len(inputString)
        Dim c As String
        c = Mid(inputString, i, 1)
        If Not (c Like "[A-Z0-9()]") Then
            If i > 3 Then
                ExtractProduct = Mid(inputString, 1, i - 2)
            End If
            Exit Function
        End If
    Next i
    ExtractProduct = inputString
End Function
Function Nanotechbody(body As String) As String
    Dim moStringPattern As String
    moStringPattern = "\b[A-Z]{2}\d{9}\b"
    Dim moStringMatch As Object
    Set moStringMatch = CreateObject("VBScript.RegExp")
    moStringMatch.pattern = moStringPattern
    moStringMatch.Global = True
    moStringMatch.IgnoreCase = True
    moStringMatch.MultiLine = True
    Set moStringMatch = moStringMatch.Execute(body)
    
    ' 取得符合製令編號的第一個字串
    If moStringMatch.count > 0 Then
        Nanotechbody = moStringMatch.Item(0)
        Exit Function
    End If
    Nanotechbody = ""
End Function
Function NanotechInfoFromStrings(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String, ByRef yieldString As String)
    'G693L293_ZG032203026結批lowyield 98.97%
    ' 找到產品代碼
    ' 找到包含產品代碼的字串
    Dim productString As String
    Dim producttemp As String
    
    productString = "\b(G|M|AT)\d{1}"
    Dim productMatch As Object
    Set productMatch = CreateObject("VBScript.RegExp")
    productMatch.pattern = productString
    productMatch.Global = True
    productMatch.IgnoreCase = True
    productMatch.MultiLine = True
    
    Dim matches As Object
    Set matches = productMatch.Execute(inputString)
    
    Dim match As Object
    For Each match In matches
        If Not Left(match.value, 1) = "Z" Then
            producttemp = match.value
            Exit For
        End If
    Next match

'
'    Dim words() As String
'    words = Split(inputString, vbCrLf)
'    If UBound(words) = 0 Then
'        words = Split(inputString, " ")
'    End If
'
'    For i = 0 To UBound(words)
'        If InStr(1, words(i), producttemp) > 0 And Len(words(i)) > 4 Then
'            product = words(i)
'            Exit For
'        End If
'    Next i
'
    Dim startpos  As Integer
    Dim endpos  As Integer
    startpos = 0
    endpos = 0
    startpos = InStr(1, inputString, producttemp)
    endpos = InStr(1, inputString, "_")
    
    If startpos > 0 And endpos > 4 Then
        product = Mid(inputString, startpos, endpos - 1)
    End If
    
    Dim moStringString As String
    moStringString = "(ZG)\d{9}"
    Dim moStringMatch As Object
    Set moStringMatch = CreateObject("VBScript.RegExp")
    moStringMatch.pattern = moStringString
    moStringMatch.Global = True
    moStringMatch.IgnoreCase = True
    moStringMatch.MultiLine = True
    Set moStringMatch = moStringMatch.Execute(inputString)

    ' 若有找到ZG字串，則取得第一個MO字串
    If moStringMatch.count > 0 Then
        moString = moStringMatch.Item(0)
    End If
    
     ' 找到以 % 結尾的字串
    Dim yieldStringPattern As String
    yieldStringPattern = "(\d+\.\d+%)[^%]*$"
    Dim yieldStringMatch As Object
    Set yieldStringMatch = CreateObject("VBScript.RegExp")
    yieldStringMatch.pattern = yieldStringPattern
    yieldStringMatch.Global = True
    yieldStringMatch.IgnoreCase = True
    yieldStringMatch.MultiLine = True
    Set yieldStringMatch = yieldStringMatch.Execute(inputString)
    
    ' 若有找到以 % 結尾的字串，則取得第一個符合的字串
    If yieldStringMatch.count > 0 Then
        yieldString = yieldStringMatch.Item(0)
    End If
    
End Function
Function LINGSENInfoFromStrings(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String, ByRef yieldString As String)
    '[ LowYield] GMT SOT-25M G509H25RT11G Run#320833 Lot#KB32750D2.D LowYield = 95.09% < 98.5%
    ' 找到產品代碼
    ' 找到包含產品代碼的字串
    Dim productString As String
    Dim product_temp As String
    productString = "\b(G|M|AT)\d{1}"
    Dim productMatch As Object
    Set productMatch = CreateObject("VBScript.RegExp")
    productMatch.pattern = productString
    productMatch.Global = True
    productMatch.IgnoreCase = True
    productMatch.MultiLine = True
    Set productMatch = productMatch.Execute(inputString)
    
    Dim words() As String
    words = Split(inputString, " ")

    If productMatch.count > 0 Then
        For i = 0 To UBound(words)
            If InStr(1, words(i), productMatch.Item(0)) > 0 Then
                product = words(i)
                Exit For
            End If
        Next i
    End If
    '找到 Run#
    For i = 0 To UBound(words)
        If InStr(1, words(i), "run", vbTextCompare) > 0 Then
            moString = Mid(words(i), InStr(1, words(i), "#") + 1) '找到#
            Exit For
        End If
    Next i
    '找到 Lot#
    For i = 0 To UBound(words)
        If InStr(1, words(i), "Lot", vbTextCompare) > 0 Then
            lotString = Mid(words(i), InStr(1, words(i), "#") + 1) '找到#
            Exit For
        End If
    Next i
'    ' 找到 Run# 和 Lot#
'    Dim runLotString As String
'    Dim runLotPattern As String
'    Dim runLotMatch As Object
'    runLotString = inputString
'    runLotPattern = "Run#[0-9]+ Lot#[A-Z0-9\.]+"
'    Set runLotMatch = CreateObject("VBScript.RegExp")
'    runLotMatch.Pattern = runLotPattern
'    runLotMatch.Global = True
'    runLotMatch.IgnoreCase = True
'    runLotMatch.MultiLine = True
'    Set runLotMatch = runLotMatch.Execute(runLotString)
'
'     Dim startpos As Integer
'
'    ' 取得 Run# 和 Lot#
'    If runLotMatch.count > 0 Then
'        Dim runLot() As String
'        runLot = Split(runLotMatch.Item(0), " ")
'         'startPos =
'        If UBound(runLot) = 1 Then
'            moString = Mid(runLot(0), InStr(1, runLot(0), "#") + 1) '找到#
'            lotString = Mid(runLot(1), InStr(1, runLot(1), "#") + 1) '找到#
'        End If
'    End If
    
    ' 找到 Yield%
    Dim yieldPattern As String
    Dim yieldMatch As Object
    yieldPattern = "LowYield\s*=\s*(\d+\.\d+%)"
    Set yieldMatch = CreateObject("VBScript.RegExp")
    yieldMatch.pattern = yieldPattern
    yieldMatch.Global = True
    yieldMatch.IgnoreCase = True
    yieldMatch.MultiLine = True
    Set yieldMatch = yieldMatch.Execute(inputString)
    
    ' 取得 Yield%
    If yieldMatch.count > 0 Then
        yieldString = yieldMatch.Item(0).SubMatches(0)
    End If
End Function
Function SuqianInfoFromStrings(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String, ByRef yieldString As String)
    '台?致新G2262BTR1U，lot：NTK65.2，??批?：TS23031812.1，??良率：95.51%
   ' 找到包含產品代碼的字串
    Dim productString As String
    productString = "\b(G|M|AT)\d{1}"
    Dim productMatch As Object
    Set productMatch = CreateObject("VBScript.RegExp")
    productMatch.pattern = productString
    productMatch.Global = True
    productMatch.IgnoreCase = True
    productMatch.MultiLine = True
    Set productMatch = productMatch.Execute(inputString)
    
    ' 若有找到產品代碼，則取得第一個產品代碼
    Dim words() As String
    words = Split(inputString, ",")
    If UBound(words) = 0 Then
        words = Split(inputString, "，")
    End If
    If UBound(words) = 0 Then
        words = Split(inputString, " ")
    End If
    If productMatch.count > 0 Then
        For i = 0 To UBound(words)
            If InStr(1, words(i), productMatch.Item(0)) > 0 Then
                product = Mid(words(i), productMatch.Item(0).FirstIndex + 1)
                Exit For
            End If
        Next i
    End If
    
'    Dim startpos  As Integer
'    Dim endpos  As Integer
'    startpos = InStr(1, product, "?")
'    If startpos > 0 Then
'         product = Mid(product, 1, startpos - 2)
'    End If
    product = ExtractProduct(product)
    
     ' 找到包含MO字串的字串
    Dim moStringString As String
    moStringString = "\b[A-Z]{2}\d{8}\.\d\b"
    Dim moStringMatch As Object
    Set moStringMatch = CreateObject("VBScript.RegExp")
    moStringMatch.pattern = moStringString
    moStringMatch.Global = True
    moStringMatch.IgnoreCase = True
    moStringMatch.MultiLine = True
    Set moStringMatch = moStringMatch.Execute(inputString)

    ' 若有找到MO字串，則取得第一個MO字串
    If moStringMatch.count > 0 Then
        moString = Replace(moStringMatch.Item(0), ".", "")
    End If
    
    ' 取得 lot 字串
    Dim lotStringString As String
    lotStringString = "lot[:：]([A-Z0-9\.]+)"
    Dim lotMatch As Object
    Set lotMatch = CreateObject("VBScript.RegExp")
    lotMatch.pattern = lotStringString
    lotMatch.Global = True
    lotMatch.IgnoreCase = True
    lotMatch.MultiLine = True
    Set lotMatch = lotMatch.Execute(inputString)

    ' 若有找到 lot 字串，則取得第一個 lot 字串
    If lotMatch.count > 0 Then
        lotString = lotMatch.Item(0).SubMatches(0)
    End If
    
    ' 取得良率字串
    Dim yieldStringString As String
    yieldStringString = "良率：([\d\.]+%)"
    Dim yieldMatch As Object
    Set yieldMatch = CreateObject("VBScript.RegExp")
    yieldMatch.pattern = yieldStringString
    yieldMatch.Global = True
    yieldMatch.IgnoreCase = True
    yieldMatch.MultiLine = True
    Set yieldMatch = yieldMatch.Execute(inputString)

    ' 若有找到良率字串，則取得第一個良率字串
    If yieldMatch.count > 0 Then
        yieldString = yieldMatch.Item(0).SubMatches(0)
    End If
End Function
Function ChuzhouInfoFromStrings(inputString As String, ByRef product As String, ByRef moString As String, ByRef lotString As String)
    '"G753T11U(金???) LOT：RPM8A.G ??批次 C22121345 ??低良反?
    ' 初始化產品、MO編號、批號
    
    ' 找到包含產品代碼的字串
    Dim productString As String
    'productString = "\b[GAM]\d{3}[A-Z]{1,2}\d{0,2}[A-Z]?\b"
    productString = "\b(G|M|AT)\d{1}"
    Dim productMatch As Object
    Set productMatch = CreateObject("VBScript.RegExp")
    productMatch.pattern = productString
    productMatch.Global = True
    productMatch.IgnoreCase = True
    productMatch.MultiLine = True
    Set productMatch = productMatch.Execute(inputString)
    
    ' 若有找到產品代碼，則取得第一個產品代碼
    Dim words() As String
    Dim startpos As Integer
    Dim endpos As Integer
    words = Split(inputString, " ")
    If productMatch.count > 0 Then
        For i = 0 To UBound(words)
            startpos = InStr(1, words(i), productMatch.Item(0))
            endpos = InStr(1, words(i), "(") - startpos
            If InStr(1, words(i), productMatch.Item(0)) > 0 Then
                If endpos > 0 Then
                    product = Mid(words(i), startpos, endpos)
                Else
                    product = Mid(words(i), startpos)
                End If
                Exit For
            End If
        Next i
    End If
    
    
    ' 找到包含MO編號的字串
    Dim moStringPattern As String
    moStringPattern = "C\d{6,8}"
    Dim moStringMatch As Object
    Set moStringMatch = CreateObject("VBScript.RegExp")
    moStringMatch.pattern = moStringPattern
    moStringMatch.Global = True
    moStringMatch.IgnoreCase = True
    moStringMatch.MultiLine = True
    Set moStringMatch = moStringMatch.Execute(inputString)
    
    ' 若有找到MO編號，則取得第一個MO編號
    If moStringMatch.count > 0 Then
        For i = 0 To UBound(words)
            startpos = InStr(1, words(i), moStringMatch.Item(0))
            endpos = InStr(1, words(i), " ") - startpos
            
            If InStr(1, words(i), moStringMatch.Item(0)) > 0 Then
                If endpos > startpos Then
                    moString = Mid(words(i), startpos, endpos)
                Else
                    moString = Mid(words(i), startpos)
                End If
                Exit For
            End If
        Next i
    End If
    
    ' 找到包含批號的字串
    Dim lotStringPattern As String
    lotStringPattern = "LOT[:：][A-Z]"
    Dim lotStringMatch As Object
    Set lotStringMatch = CreateObject("VBScript.RegExp")
    lotStringMatch.pattern = lotStringPattern
    lotStringMatch.Global = True
    lotStringMatch.IgnoreCase = True
    lotStringMatch.MultiLine = True
     Set lotStringMatch = lotStringMatch.Execute(inputString)
    
    ' 若有找到批號，則取得第一個批號
    For i = 0 To UBound(words)
        startpos = InStr(1, words(i), lotStringMatch.Item(0))
        endpos = InStr(1, words(i), " ") - startpos
        If InStr(1, words(i), lotStringMatch.Item(0)) > 0 Then
            If endpos > startpos Then
                lotString = Mid(words(i), startpos + 4, endpos)
            Else
                lotString = Mid(words(i), startpos + 4)
            End If
            Exit For
        End If
    Next i
End Function
Function CheckMailAddress(mailAddress As String) As Boolean
    Dim r As Long
    r = Sheet2.Cells(rows.count, 1).End(xlUp).row + 1
    For i = 2 To r
        If Sheet2.Cells(i, 1).value = mailAddress Then
            CheckMailAddress = True
            Exit Function
        End If
    Next i
    CheckMailAddress = False
End Function
Function CheckCCMailAddress(mailAddress As String) As String
    Dim r As Long
    mailAddress = Replace(mailAddress, " ", "")
    r = Sheet2.Cells(rows.count, 1).End(xlUp).row + 1
    For i = 2 To r
        If Len(Sheet2.Cells(i, 1).value) > 0 Then
            If InStr(1, Sheet2.Cells(i, 1).value, mailAddress, vbTextCompare) > 0 Then
                CheckCCMailAddress = Sheet2.Cells(i, 2).value
                Exit Function
            End If
        End If
    Next i
    CheckCCMailAddress = ""
End Function

Function processFile(ByVal aa As Integer, ByVal fun As String)
    Dim filePath As String
    Dim fileNumber As Integer
    Dim numberedTime As String
    
    ' 設置文件路徑
    filePath = "D:\Temp\processFile.txt"
    
    ' 打開文件以供寫入
    fileNumber = FreeFile()
    Open filePath For Output As #fileNumber
    
    Print #fileNumber, aa
    
    ' 關閉文件
    Close #fileNumber
    
    ' 設置文件路徑
    filePath = "D:\Temp\" + fun + ".txt"
    
    ' 打開文件以供寫入
    fileNumber = FreeFile()
    Open filePath For Output As #fileNumber
    
    Print #fileNumber, aa
    
    ' 關閉文件
    Close #fileNumber
 
End Function
Function ReadProcessFile() As Integer
    Dim filePath As String
    Dim fileNumber As Integer
    Dim numberedTime As String
    
    filePath = "D:\Temp\processFile.txt"
    
    fileNumber = FreeFile()
    Open filePath For Input As #fileNumber
    
    Input #fileNumber, ReadProcessFile
    
    Close #fileNumber
End Function
Function WriteNumberedTimeToFile(ByVal aa As Long)
    Dim filePath As String
    Dim fileNumber As Integer
    Dim numberedTime As String
    
    ' 設置文件路徑
    filePath = "D:\Temp\numbered_time.txt"
    
    ' 打開文件以供寫入
    fileNumber = FreeFile()
    Open filePath For Append As #fileNumber
    
    Print #fileNumber, Format(Now(), "yyyy-mm-dd hh:mm:ss")
    Print #fileNumber, aa
    
    ' 關閉文件
    Close #fileNumber
End Function
Function WritStringTimeToFile(ByVal aa As String)
    Dim filePath As String
    Dim fileNumber As Integer
    Dim numberedTime As String
    
    ' 設置文件路徑
    filePath = "D:\Temp\numbered_time.txt"
    
    ' 打開文件以供寫入
    fileNumber = FreeFile()
    Open filePath For Append As #fileNumber
    
    Print #fileNumber, Format(Now(), "yyyy-mm-dd hh:mm:ss")
    Print #fileNumber, aa
    
    ' 關閉文件
    Close #fileNumber
End Function
Sub ScheduleSubx()
   On Error Resume Next
    Application.OnTime Now + TimeValue("00:10:00"), "Module1.ReadtemplotFile"
    On Error GoTo 0
End Sub
Sub ScheduleSub()
    On Error Resume Next
    Application.OnTime Now + TimeValue("00:00:10"), "Module1.Check_download_fail_file"
    On Error GoTo 0
End Sub

Sub StartTimer2()
    On Error Resume Next
    Application.OnTime Now + TimeValue("00:00:05"), "Module1.SendProcessingEmail"
    On Error GoTo 0
End Sub

Function ReadDataFromFile(Optional i_line As Integer = 0) As Variant
    Dim wb As Object
    Dim ws As Object
    Dim lastRow As Long
    Dim i As Long
    Dim j As Long
    Dim result() As Variant
    
    j = 1
    ' 創建Excel對象
    Dim xlApp As Object
    Set xlApp = CreateObject("Excel.Application")
    xlApp.DisplayAlerts = False ' 禁用警告對話框
    xlApp.Visible = False
    
    ' 打開Excel文件

     Set wb = xlApp.Workbooks.Open(mailnetpath, True, False)

    
    
    Set ws = wb.Sheets(1)
    
    ' 獲取最後一行的行號
    lastRow = ws.Cells(ws.rows.count, "A").End(xlUp).row
    
    ' 讀取數據
    ReDim result(1 To lastRow, 1 To 9)
    For i = 1 To lastRow
        
        result(i, 1) = ws.Cells(i, "A").value
        result(i, 2) = ws.Cells(i, "B").value
        result(i, 3) = ws.Cells(i, "C").value
        result(i, 4) = ws.Cells(i, "D").value
        result(i, 5) = ws.Cells(i, "E").value
        result(i, 6) = ws.Cells(i, "F").value
        result(i, 7) = ws.Cells(i, "G").value
        result(i, 8) = ws.Cells(i, "H").value
        result(i, 9) = ws.Cells(i, "I").value

        If i = i_line Then
            ws.Cells(i, "I").value = "True" ' 標記已讀取
        End If
 

    Next i
   
    ' 保存文件並關閉

    wb.SaveAs mailnetpath, FileFormat:=51 ' 51表示Excel 2010及以上版本的xlsx格式
    
    wb.Close
    
    ' 釋放對象引用
    Set ws = Nothing
    Set wb = Nothing
    xlApp.Quit
    Set xlApp = Nothing
    
    ' 返回讀取到的數據
    ReadDataFromFile = result
End Function
Function WriteDataToFile(str1 As String, str2 As String, str3 As String, str4 As String, Optional ByVal xst As Integer = 1, Optional ByVal xsp As Integer = 7, Optional ByVal yst As Integer = 1, Optional ByVal ysp As Integer = 20) As Boolean '4
    Dim wb As Workbook
    Dim ws As Worksheet
    Dim lastRow As Long
    
    Dim xlApp As Object
    Set xlApp = CreateObject("Excel.Application")
    xlApp.DisplayAlerts = False
    xlApp.Visible = False
   
    

    Set wb = xlApp.Workbooks.Open(mailnetpath, True, False)

    
    Set ws = wb.Sheets(1)
    ' 獲取最後一行的行號
    lastRow = ws.Cells(ws.rows.count, "A").End(xlUp).row
    
    ' 寫入數據
    ws.Cells(lastRow + 1, "A").value = str1
    ws.Cells(lastRow + 1, "B").value = str2
    ws.Cells(lastRow + 1, "C").value = str3 'subject
    ws.Cells(lastRow + 1, "D").value = str4
    ws.Cells(lastRow + 1, "E").value = xst
    ws.Cells(lastRow + 1, "F").value = xsp
    ws.Cells(lastRow + 1, "G").value = yst
    ws.Cells(lastRow + 1, "H").value = ysp
    ws.Cells(lastRow + 1, "I").value = "False"
    
    ' 保存文件並關閉

    wb.SaveAs mailnetpath, FileFormat:=51 ' 51表示Excel 2010及以上版本的xlsx格式

    
    wb.Close
    
    WriteDataToFile = True
End Function
Function CheckAndCloseWorkbook()
    Dim xlApp As Object
    Dim wb As Object
    
    ' 取得所有開啟的 Excel 應用程式
    On Error Resume Next
    Set xlApp = GetObject(, "Excel.Application")
    On Error GoTo 0
    
    If Not xlApp Is Nothing Then
        ' 檢查每個應用程式中的活頁簿
        For Each wb In xlApp.Workbooks
            If wb.name = "FT_Summary.xlsx" Then
                ' 如果找到 FT_Summary.xlsx，關閉該活頁簿
                wb.Close SaveChanges:=False
                Exit For
            End If
        Next wb
    End If
    
    ' 釋放物件
    Set wb = Nothing
    Set xlApp = Nothing
End Function
Function SendSummaryEmail() 'have use 5
 '   Dim OutlookApp As Object
  '  Dim objMail As Object
    Dim OutlookApp As Outlook.Application
    Dim objMail As Outlook.MailItem
    Dim wb As Workbook
    Dim ws As Worksheet
    Dim body As String
    Dim attachment As String
    Dim lastCol6 As Integer
    Dim j As Integer
    Dim ii As Integer
    Dim aa2 As Long
    Dim xst As Integer
    Dim xsp As Integer
    Dim yst As Integer
    Dim ysp As Integer
    Dim mail_cnt As Integer
    Dim subject As String
    Dim filepatch As String
    'Dim recipients() As String
    Dim recipients As String
    Dim cc As String
    Dim data As Variant
    nowprocess = ReadProcessFile
    If nowprocess = 1 Then
        Exit Function
    End If
    processFile 1, "SendSummaryEmail_START"
    data = ReadDataFromFile()
    
    j = 7
'    Dim olApp As Outlook.Application
    On Error Resume Next
    Set OutlookApp = GetObject(, "Outlook.Application")
    On Error GoTo 0
    If OutlookApp Is Nothing Then
        Exit Function
    End If
'    For ii = LBound(data) To UBound(data)
'        If InStr(1, UCase(data(ii, 9)), "FALSE", vbTextCompare) > 0 Then
'            'WriteNumberedTimeToFile ii + 1000
'            filepatch = data(ii, 4)
'            attachment = filepatch & "\" & "FT_Summary.xlsx"
'            If Dir(attachment) <> "" Then
'                'WriteNumberedTimeToFile ii + 2000
'                ReadDataFromFile (ii)
'            End If
'        End If
'    Next ii
        For ii = LBound(data) To UBound(data)
       ' WriteNumberedTimeToFile ii
        If InStr(1, UCase(data(ii, 9)), "FALSE", vbTextCompare) > 0 Then
        'If InStr(1, UCase(data(ii, 9)), "TRUE", vbTextCompare) > 0 Then
            recipients = data(ii, 1)
            cc = data(ii, 2)
            subject = data(ii, 3)
            filepatch = data(ii, 4)
            xst = data(ii, 5)
            xsp = data(ii, 6)
            yst = data(ii, 7)
            ysp = data(ii, 8)
            Set objMail = OutlookApp.CreateItem(olMailItem)
            CheckAndCloseWorkbook
            attachment = filepatch & "\" & "EQC_Summary.xlsx"
            If Dir(attachment) <> "" Then objMail.Attachments.Add attachment
            attachment = filepatch & "\" & "OTHER_Summary.xlsx"
            If Dir(attachment) <> "" Then objMail.Attachments.Add attachment
            attachment = filepatch & "\" & "EQCTOTALDATA_RAW.xlsx"
            If Dir(attachment) <> "" Then objMail.Attachments.Add attachment
            attachment = filepatch & "\" & "EQCTOTALDATA.xlsx"
            If Dir(attachment) <> "" Then objMail.Attachments.Add attachment
            attachment = filepatch & "\" & "FT_SUMMARY.xlsx"
            If Dir(attachment) <> "" Then objMail.Attachments.Add attachment
            CheckAndCloseWorkbook
            If Dir(attachment) <> "" Then
                CheckAndCloseWorkbook
                On Error GoTo mailleave
                CheckAndCloseWorkbook
                Set wb = Workbooks.Open(attachment)
                'Set ws = wb.Sheets("summary")
                Set ws = wb.Sheets("Sheet1")
                lastCol6 = ws.Cells(6, ws.Columns.count).End(xlToLeft).Column
                lastRow1 = ws.Cells(ws.rows.count, 1).End(xlUp).row
                For i = 7 To lastRow1
                    aa2 = ws.Cells(i, 2).value
                    If aa2 > 0 Then
                        j = j + 1
                    Else
                        Exit For
                    End If
                Next i
                'SetRangeBorders 1, 1, j - 1, lastCol6, wb
                SetRangeBorders xst, yst, ysp, xsp, wb
        
                'ws.Range("A1:G20").Copy
                'ws.Range(ws.Cells(1, 1), ws.Cells(j - 1, lastCol6)).Copy
                If xsp > 0 Then
                    ws.Range(ws.Cells(xst, yst), ws.Cells(ysp, xsp)).Copy
                Else
                    ws.Range(ws.Cells(xst, yst), ws.Cells(ysp, yst + 6)).Copy
                End If
                Application.Wait Now + TimeValue("0:00:01")
                DoEvents
                Application.Wait Now + TimeValue("0:00:01")
        
                objMail.Display
                'objMail.To = Join(recipients, ";")
                objMail.To = recipients
                If cc <> "cc" Then
                    objMail.cc = cc
                End If
                objMail.subject = subject
                '設定郵件正文
                  objMail.GetInspector.Activate
                objMail.GetInspector.WordEditor.Range.Paste
                body = "請點 <a href='file://" & filepatch & "'>" & filepatch & "</a>"
                objMail.HTMLBody = body & objMail.HTMLBody
                Application.CutCopyMode = False
            
            
                objMail.Send
                ReadDataFromFile (ii)
                wb.Close SaveChanges:=False
                Set ws = Nothing
                Set wb = Nothing
            End If
        End If
    Next ii
mailleave:
    Set objMail = Nothing
    Set OutlookApp = Nothing
   ' WriteNumberedTimeToFile 99
   processFile nowprocess, "SendSummaryEmail_End"
End Function
Function SendProcessingEmail() 'have use
    Dim OutlookApp As Outlook.Application
    Dim objMail As Outlook.MailItem
    Dim receivedDate As Date
    Dim senderAddress As String
    Dim moString As String
    Dim lotString As String
    Dim lotStringtemp As String
    Dim yieldString As String
    Dim linemessage As String
    Dim j As Integer
    Dim subject As String
    Dim Testing_House As String
    Dim cc As String
    Dim dblNum As Double
    processFile 1, "SendProcessingEmail_START"
    On Error Resume Next
    Set OutlookApp = GetObject(, "Outlook.Application")
    On Error GoTo 0
    If OutlookApp Is Nothing Then
        Exit Function
    End If
    r = Sheet1.Cells(rows.count, 1).End(xlUp).row + 1 '找到最後一行記錄
    For i = 1 To r - 1
        dblNum = 0
        If InStr(1, LCase(Sheet1.Cells(i, 7).value), "false", vbTextCompare) > 0 Then
            Testing_House = Sheet1.Cells(i, 2).value 'Testing_House
            receivemain = Sheet1.Cells(i, 3).value '主要收件者
            product = UCase(Sheet1.Cells(i, 4).value)
            moString = UCase(Sheet1.Cells(i, 5).value)
            lottemp = UCase(Sheet1.Cells(i, 6).value)
            yieldString = UCase(Sheet1.Cells(i, 9).value)
            On Error Resume Next
            If IsNumeric(yieldString) Then
                dblNum = CDbl(yieldString)
                dblNum = dblNum * 100
            End If
            On Error GoTo 0
            Set objMail = OutlookApp.CreateItem(olMailItem)
            objMail.To = receivemain
            objMail.cc = "<EMAIL>"
'            If dblNum > 0 Then
'                subject = "noreply: TEST:" & Testing_House & " Product:" & product & " MO:" & moString & " LOT:" & lottemp & " YIELD:" & dblNum & "% Processing"
'
'            Else
'                subject = "noreply: TEST:" & Testing_House & " Product:" & product & " MO:" & moString & " LOT:" & lottemp & " Processing"
'            End If
            subject = Sheet1.Cells(i, 15).value & " Processing"
            objMail.subject = subject
            objMail.body = "This is sent automatically. You don't need to reply."
            '設定郵件正文
            'objMail.GetInspector.Activate
            'Application.CutCopyMode = False
            'objMail.Display
            objMail.Send
            'linemessage = subject
            'TestSend_LINE_Notify_Message linemessage
        End If
    Next i

    Set objMail = Nothing
    Set OutlookApp = Nothing
    processFile 0, "SendProcessingEmail_END"
End Function
Function Check_download_fail_file() As Integer '2
    Dim product As String
    Dim moString As String
    Dim linemessage As String
    Dim lot As String
    Dim lottemp As String
    Dim filecnt As Integer
    Dim receivemain As String
    Dim nowprocess  As Integer
    Dim ccname_temp() As String
    Dim ccnamex As String
    Dim i As Integer
    Dim k As Integer
    nowprocess = 0
    nowprocess = ReadProcessFile
    If nowprocess = 1 Then Exit Function
    processFile 1, "Check_download_fail_file_START"
    filecnt = 0
    r = Sheet1.Cells(rows.count, 1).End(xlUp).row + 1 '找到最後一行記錄
    '20231117
    Dim wb As Workbook
    Dim ws As Worksheet
    Dim workbookName As String
    workbookName = "autodownloadLY.xlsm"  ' Replace with your actual file name
     For Each wb In Workbooks
        If wb.name = workbookName Then
            ' Workbook is open, set the worksheet reference
            Set ws = wb.Worksheets("Sheet2")
            Exit For
        End If
    Next wb
    For i = 1 To r - 1
        filedownloadok = False
        startTime = Timer
        If InStr(1, LCase(Sheet1.Cells(i, 7).value), "false", vbTextCompare) > 0 Then
            receivemain = Sheet1.Cells(i, 3).value '主要收件者
            product = UCase(Sheet1.Cells(i, 4).value)
            moString = UCase(Sheet1.Cells(i, 5).value)
            lottemp = UCase(Sheet1.Cells(i, 6).value)
            If InStr(1, lottemp, ".", vbTextCompare) > 0 Then
                lot = Left(lottemp, InStr(lottemp, ".") - 1)
            Else
                lot = lottemp
            End If
           
            Dim cellValue As String
            cellValue = ws.Range("D13").value ' 請根據實際需要更換工作表名
            If UCase(cellValue) <> "Y" Then
               CopyFolderCreateIfNotExist2 product, UCase(moString)  '20231027
            End If
            
            
            If InStr(1, UCase(Sheet1.Cells(i, 8).value), "ASK", vbTextCompare) > 0 Then
                If moString <> "" Then
                    If InStr(1, LCase(Sheet1.Cells(i, 2).value), "etd", vbTextCompare) > 0 Then 'ETD
                        filedownloadok = CopyFilesETD(product, moString, lottemp)
                    ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "gtk", vbTextCompare) > 0 Then 'GTK
                        filedownloadok = CopyFilesGTK(moString, tempPath & "\" & product & "\" & UCase(moString), product, lot)
                    End If
                Else
                    moString = lottemp
                End If
                If filedownloadok = False Then
                    If InStr(1, LCase(Sheet1.Cells(i, 2).value), "etd", vbTextCompare) > 0 Then 'ETD
                        filedownloadok = CopyFilesETD2(product, lottemp)
                    ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "gtk", vbTextCompare) > 0 Then 'GTK
                        filedownloadok = CopyFilesGTK2(product, lottemp)
                    End If
                End If
            Else
                If InStr(1, LCase(Sheet1.Cells(i, 2).value), "etd", vbTextCompare) > 0 Then 'ETD
    '                product = "G2532USKE1U-K"
    '                moString = "F2330641D"
    '                lotString = "DLF2A.1D"
    '
    '                CopyFilesETD product, moString, lotString
                    filedownloadok = CopyFilesETD(product, moString, lottemp)
                ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "gtk", vbTextCompare) > 0 Then 'GTK
                    filedownloadok = CopyFilesGTK(moString, tempPath & "\" & product & "\" & UCase(moString), product, lot)
                ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "chuzhou", vbTextCompare) > 0 Then 'Chuzhou
                    filedownloadok = CopyFilesChuzhou(moString, tempPath & "\" & product & "\" & UCase(moString), product, lottemp)
                ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "Suqian", vbTextCompare) > 0 Then 'Suqian
                     filedownloadok = CopyFilesSuqian(moString, tempPath & "\" & product & "\" & UCase(moString), product, lottemp)
                ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "lingsen", vbTextCompare) > 0 Then 'LINGSEN
                     filedownloadok = CopyFilesLINGSEN(moString, tempPath & "\" & product & "\" & UCase(moString), product, lottemp)
                ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "nanotech", vbTextCompare) > 0 Then 'Nanotech
                    filedownloadok = CopyFilesNanotech(moString, tempPath & "\" & product & "\" & UCase(moString), product, lottemp)
                ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "tsht", vbTextCompare) > 0 Then 'Nanotech
                    filedownloadok = CopyFilesTSHT(moString, tempPath & "\" & product & "\" & UCase(moString), product, lottemp)
                ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "mseczd", vbTextCompare) > 0 Then 'MSECZD
                    filedownloadok = CopyFilesMSECZD(moString, tempPath & "\" & product & "\" & UCase(moString), product, lottemp)
                ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "nfme", vbTextCompare) > 0 Then 'NFME
                    filedownloadok = CopyFilesNFME(moString, tempPath & "\" & product & "\" & UCase(moString), product, lottemp)
                ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "jcet", vbTextCompare) > 0 Then 'JCET
                    filedownloadok = CopyFilesJCET(moString, tempPath & "\" & product & "\" & UCase(moString), product, lottemp)
                ElseIf InStr(1, LCase(Sheet1.Cells(i, 2).value), "xaht", vbTextCompare) > 0 Then 'XAHT
                    filedownloadok = CopyFilesXAHT(moString, tempPath & "\" & product & "\" & UCase(moString), product, lottemp)
                End If
            End If
            If filedownloadok = True Then
                linemessage = netpath & "\" & product & "\" & UCase(moString) & "\" & " Processing"
                TestSend_LINE_Notify_Message linemessage
                FolderPicker tempPath & "\" & product & "\" & UCase(moString), netpath & "\" & product & "\" & UCase(moString), receivemain, Sheet1.Cells(i, 15).value
                If Dir(tempPath & "\" & product & "\" & UCase(moString) & "\" & "EQCTOTALDATA.xlsx") <> "" Or Dir(tempPath & "\" & product & "\" & UCase(moString) & "\" & "EQC_SUMMARY.xlsx") <> "" Or Dir(tempPath & "\" & product & "\" & UCase(moString) & "\" & "FT_SUMMARY.xlsx") <> "" Then
                    moveilfe tempPath & "\" & product & "\" & UCase(moString)
                End If
                If Dir(netpath & "\" & product & "\" & UCase(moString) & "\" & "EQCTOTALDATA.xlsx") <> "" Or Dir(netpath & "\" & product & "\" & UCase(moString) & "\" & "EQC_SUMMARY.xlsx") <> "" Or Dir(netpath & "\" & product & "\" & UCase(moString) & "\" & "FT_SUMMARY.xlsx") <> "" Then
                    Sheet1.Cells(i, 7).value = "TRUE"
                    
                     If InStr(1, LCase(Sheet1.Cells(i, 3).value), ";", vbTextCompare) > 0 Then
                         ccname_temp = Split(Sheet1.Cells(i, 3).value, ";")
                          For k = LBound(ccname_temp) To UBound(ccname_temp)
                            ccnamex = ccname_temp(k)
                            ccnamex = Replace(ccnamex, " ", "")
                            WriteFileToNet i, ccnamex
                          Next k
                         
                    Else
                        WriteFileToNet i, Sheet1.Cells(i, 3).value
                    End If
                    'ReDim Preserve folderPathnet(0 To filecnt)
                    'folderPathnet(filecnt - 1) = netpath & "\" & product & "\" & UCase(moString)
                    linemessage = netpath & "\" & product & "\" & UCase(moString) & "\" & "is OK"
                    TestSend_LINE_Notify_Message linemessage
                    
                Else
                    Sheet1.Cells(i, 7).value = "FALSE"
                    filecnt = filecnt + 1
                End If
            Else
                 linemessage = netpath & "\" & product & "\" & UCase(moString) & "\" & "Download File Fail PLZ CHECK"
                 TestSend_LINE_Notify_Message linemessage
            End If
            elapsedTime = Timer - startTime
            Sheet1.Cells(i, 10).value = elapsedTime & "Sec"
            
        End If
    Next i
    processFile 0, "Check_download_fail_file_END"
    Check_download_fail_file = filecnt

    On Error Resume Next
        Application.OnTime Now + TimeValue("00:00:10"), "Module1.ReadtemplotFile"
    On Error GoTo 0

End Function
Function CopyFilesNanotech(ByVal fileName As String, ByVal filetemp As String, Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathNanotech As String
    Dim destinationPath As String
    Dim file As String
    Dim newestFile As String
    Dim tempfilepatch As String
    Dim newestDate As Date
    Dim copyMsgBox As Object
    Dim uf As UserForm
    Dim startTime As Double
    Dim elapsedTime As Double
    Dim lotfile() As String
    Dim i  As Integer
    ReDim lotfile(0)
    i = 0
    sourcePathNanotech = sourcePath & "\Nano\temp\" & pd & "\"
    destinationPath = filetemp
    If CreateFolders(pd, UCase(fileName)) = False Then
        CopyFilesNanotech = False
        Exit Function
    End If
       
    file = Dir(sourcePathNanotech & fileName & "\*")  'mo
    Do While file <> ""
        ReDim Preserve lotfile(UBound(lotfile) + 1)
        lotfile(UBound(lotfile) - 1) = file
        file = Dir()
        i = i + 1
    Loop
    If i > 0 Then
        ReDim Preserve lotfile(UBound(lotfile) - 1)
        For i = LBound(lotfile) To UBound(lotfile)
            tempfilepatch = destinationPath & lotfile(i)
            FileCopy sourcePathNanotech & lotfile(i), destinationPath & "\" & lotfile(i)
            CopyFilesNanotech = True
        Next i
    End If

End Function
Function CopyFilesLINGSEN(ByVal fileName As String, ByVal filetemp As String, Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathLINGSEN As String
    Dim destinationPath As String
    Dim file As String
    Dim newestFile As String
    Dim tempfilepatch As String
    Dim newestDate As Date
    Dim copyMsgBox As Object
    Dim uf As UserForm
    Dim startTime As Double
    Dim elapsedTime As Double
    Dim lotfile() As String
    Dim i  As Integer
    ReDim lotfile(0)
    i = 0
    sourcePathLINGSEN = sourcePath & "\Lingsen\temp\"
    destinationPath = filetemp
    If CreateFolders(pd, UCase(fileName)) = False Then
        CopyFilesLINGSEN = False
        Exit Function
    End If
       
    file = Dir(sourcePathLINGSEN & "*" & fileName & "*") 'mo
    Do While file <> ""
        ReDim Preserve lotfile(UBound(lotfile) + 1)
        lotfile(UBound(lotfile) - 1) = file
        file = Dir()
        i = i + 1
    Loop
    If i > 0 Then
        ReDim Preserve lotfile(UBound(lotfile) - 1)
        For i = LBound(lotfile) To UBound(lotfile)
            'tempfilepatch = destinationPath & lotfile(i)
            FileCopy sourcePathLINGSEN & lotfile(i), destinationPath & "\" & lotfile(i)
            CopyFilesLINGSEN = True
        Next i
    End If

End Function
Function CopyFilesSuqian(ByVal fileName As String, ByVal filetemp As String, Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathSuqian As String
    Dim destinationPath As String
    Dim fso As Object
    Dim folder As Object
    Dim targetPath As String
    Dim copyfileok  As Boolean
    'moString, tempPath & "\" & product & "\" & UCase(moString), product, lotString
    Set fso = CreateObject("Scripting.FileSystemObject")
    sourcePathSuqian = sourcePath & "\JCET\SUQIAN\" & pd & "\" & lot & "\"
    destinationPath = tempPath & "\" & pd & "\" & UCase(fileName) & "\"
    If CreateFolders(pd, UCase(fileName)) = False Then
        Exit Function
    End If
    If CreateFolders(pd & "\" & UCase(fileName), UCase(fileName)) = False Then
        Exit Function
    End If

    For Each file In fso.GetFolder(sourcePathSuqian).files
        If InStr(1, file.name, mo, vbTextCompare) > 0 Then
            targetPath = destinationPath & UCase(fileName) & "\" & file.name
            fso.CopyFile sourcePathSuqian & file.name, targetPath
             copyfileok = True
             CopyFilesSuqian = True
        End If
    Next file
    If copyfileok = True Then Exit Function
    CopyFilesSuqian = False
    
End Function
Function CopyFilesChuzhou(ByVal fileName As String, ByVal filetemp As String, Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathChuzhou As String
    Dim destinationPath As String
    Dim file As String
    Dim newestFile As String
    Dim tempfilepatch As String
    Dim newestDate As Date
    Dim copyMsgBox As Object
    Dim uf As UserForm
    Dim startTime As Double
    Dim elapsedTime As Double
    Dim lotfile() As String
    Dim i  As Integer
    ReDim lotfile(0)
    i = 0
    sourcePathChuzhou = sourcePath & "\JCET\CHUZHOU\TO252\"
    destinationPath = filetemp
    If CreateFolders(pd, UCase(fileName)) = False Then
        CopyFilesChuzhou = False
        Exit Function
    End If
    file = Dir(sourcePathChuzhou & lot & "*") 'MO
    Do While file <> ""
        If Right(file, 4) = ".zip" Or Right(file, 4) = ".rar" Or Right(file, 3) = ".7z" Then
            If FileDateTime(sourcePathChuzhou & file) > newestDate Then
                newestDate = FileDateTime(sourcePathChuzhou & file)
                newestFile = file
            End If
        End If
        file = Dir()
    Loop
     If newestFile <> "" Then
        If Dir(destinationPath & "\" & newestFile) <> "" Then
            If FileLen(sourcePathChuzhou & newestFile) = FileLen(destinationPath & "\" & newestFile) Then
                CopyFilesChuzhou = True
                Exit Function
            End If
        End If

        FileCopy sourcePathChuzhou & newestFile, destinationPath & "\" & newestFile
        If FileLen(sourcePathChuzhou & newestFile) = FileLen(destinationPath & "\" & newestFile) Then
            CopyFilesChuzhou = True
        Else
           
            CopyFilesChuzhou = False
        End If
    End If
End Function
Function DelayOneSecond()
    Dim waitTime As Double
    waitTime = Now + TimeValue("00:00:01")
    Application.Wait waitTime
End Function
Function CopyFilesGTK(ByVal fileName As String, ByVal filetemp As String, Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathGTK As String
    Dim destinationPath As String
    Dim file As String
    Dim newestFile As String
    Dim tempfilepatch As String
    Dim newestDate As Date
    Dim copyMsgBox As Object
    Dim uf As UserForm
    Dim startTime As Double
    Dim elapsedTime As Double
    Dim lotfile() As String
    Dim i  As Integer
    ReDim lotfile(0)
    i = 0
    sourcePathGTK = sourcePath & "\GTK\temp\"
    destinationPath = filetemp
    If CreateFolders(pd, UCase(fileName)) = False Then
        CopyFilesGTK = False
        Exit Function
    End If
    
    file = Dir(sourcePathGTK & fileName & "*") 'MO
    Do While file <> ""
        If Right(file, 4) = ".zip" Or Right(file, 4) = ".rar" Or Right(file, 3) = ".7z" Then
            If FileDateTime(sourcePathGTK & file) > newestDate Then
                newestDate = FileDateTime(sourcePathGTK & file)
                newestFile = file
            End If
        End If
        file = Dir()
    Loop
    
    If newestFile <> "" Then
        If Dir(destinationPath & "\" & newestFile) <> "" Then
            If FileLen(sourcePathGTK & newestFile) = FileLen(destinationPath & "\" & newestFile) Then
                ' File already exists and is the same size, do not copy
'                Set copyMsgBox = CreateObject("WScript.Shell")
'                copyMsgBox.Popup "Copying file is ok By youself,", 3, "Copy File", vbInformation + vbSystemModal
'                Set copyMsgBox = Nothing
                CopyFilesGTK = True
                Exit Function
            End If
        End If

        'Set copyMsgBox = CreateObject("WScript.Shell")
        ' File does not exist or is a different size, copy the file
        'copyMsgBox.Popup "Copying file, please wait...", 2, "Copy File", vbInformation + vbSystemModal
        'startTime = Timer
        DelayOneSecond
        FileCopy sourcePathGTK & newestFile, destinationPath & "\" & newestFile
        'If FileLen(destinationPath & "\" & newestFile) > 0 Then
        If FileLen(sourcePathGTK & newestFile) = FileLen(destinationPath & "\" & newestFile) Then
            CopyFilesGTK = True
            'elapsedTime = Timer - startTime
           ' copyMsgBox.Popup "GTK Copy file OK. Time taken: " & elapsedTime & " seconds.", 3, "Copy File", vbInformation + vbSystemModal
        Else
           
            CopyFilesGTK = False
            'copyMsgBox.Popup "GTK Copy file Fail. Time taken: " & elapsedTime & " seconds.", 3, "Copy File", vbInformation + vbSystemModal
        End If
    Else
        file = Dir(sourcePathGTK & lot & "*") 'lot
        Do While file <> ""
            ReDim Preserve lotfile(UBound(lotfile) + 1)
            lotfile(UBound(lotfile) - 1) = file
            file = Dir()
            i = i + 1
        Loop
        If i > 0 Then
            ReDim Preserve lotfile(UBound(lotfile) - 1)
            For i = LBound(lotfile) To UBound(lotfile)
                tempfilepatch = destinationPath & lotfile(i)
                FileCopy sourcePathGTK & lotfile(i), tempfilepatch
                CopyFilesGTK = True
            Next i
        Else
            Dim fso As Object
            Set fso = CreateObject("Scripting.FileSystemObject")
            sourcePathGTK = sourcePath & "\" & "GTK\FT\" & pd & "\" & lot
            destinationPath = tempPath & "\" & pd & "\" & lot
            If Len(Dir(sourcePathGTK, vbDirectory)) > 0 Then
                fso.CopyFolder sourcePathGTK, destinationPath
                 CopyFilesGTK = True
                 Exit Function
            End If
            CopyFilesGTK = False
        End If
    End If
    'Set copyMsgBox = Nothing
End Function
Function CopyFilesGTK2(Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathGTK As String
    Dim destinationPath As String
    Dim lottemp As String

    If CreateFolders(pd, UCase(lot)) = False Then
        CopyFilesGTK2 = False
        Exit Function
    End If
    If CreateFolders(pd & "\" & UCase(lot), UCase(lot)) = False Then
        Exit Function
    End If
   
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    sourcePathGTK = sourcePath & "\" & "GTK\FT\" & pd & "\" & lot
    destinationPath = tempPath & "\" & pd & "\" & lot & "\" & lot
    If Len(Dir(sourcePathGTK, vbDirectory)) > 0 Then
        fso.CopyFolder sourcePathGTK, destinationPath
        CopyFilesGTK2 = True
        Exit Function
    End If
    CopyFilesGTK2 = False
End Function
Function CopyFilesETD2(Optional ByVal pd As String = "default", Optional ByVal lot As String = "default") As Boolean
    Dim sourcePathGTK As String
    Dim destinationPath As String
    Dim lottemp As String

    If CreateFolders(pd, UCase(lot)) = False Then
        CopyFilesETD2 = False
        Exit Function
    End If
    
     If CreateFolders(pd & "\" & UCase(lot), UCase(lot)) = False Then
        Exit Function
    End If
   
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    sourcePathETD = sourcePath & "\" & "Etrend\FT\" & pd & "\" & lot
    destinationPath = tempPath & "\" & pd & "\" & lot & "\" & lot
    If Len(Dir(sourcePathETD, vbDirectory)) > 0 Then
        fso.CopyFolder sourcePathETD, destinationPath
        CopyFilesETD2 = True
        Exit Function
    End If
    CopyFilesETD2 = False
End Function
Function CopyFilesETD(ByVal model As String, ByVal mo As String, ByVal lot As String) As Boolean
    Dim sourcePathETD As String
    Dim destinationPath As String
    Dim fso As Object
    Dim folder As Object
    Dim targetPath As String
    Dim copyfileok  As Boolean
    Set fso = CreateObject("Scripting.FileSystemObject")
    sourcePathETD = sourcePath & "\ETD\FT\" & model & "\" & lot & "\" '20240314
    
    
    destinationPath = tempPath & "\" & model & "\" & UCase(mo) & "\"
    If CreateFolders(model, UCase(mo)) = False Then
        Exit Function
    End If
    If CreateFolders(model & "\" & UCase(mo), UCase(mo)) = False Then
        Exit Function
    End If
    
'    For Each folder In fso.GetFolder(sourcePathETD).subfolders
'        If InStr(folder.Name, lot) > 0 Then
'            targetPath = destinationPath & "\" & folder.Name
'            If fso.FolderExists(targetPath) Then fso.DeleteFolder targetPath
'                fso.CopyFolder folder.path, targetPath
'            If Len(Dir(targetPath, vbDirectory)) > 0 Then
'                CopyFilesETD = True
'            Else
'                CopyFilesETD = False
'            End If
'            Exit Function
'        End If
'    Next folder
    '20231122 fix
    If Len(Dir(sourcePathETD, vbDirectory)) > 0 Then
        For Each file In fso.GetFolder(sourcePathETD).files
            If InStr(1, file.name, mo, vbTextCompare) > 0 Then
                targetPath = destinationPath & UCase(mo) & "\" & file.name
                fso.CopyFile sourcePathETD & file.name, targetPath
                 copyfileok = True
                 CopyFilesETD = True
            End If
        Next file
    End If
    copyfileok = True
      CopyFilesETD = True
    If copyfileok = True Then Exit Function
    CopyFilesETD = False
End Function
Function DeleteFiles(filePaths As Variant)
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    Dim i As Integer
    For i = LBound(filePaths) To UBound(filePaths)
        If fso.fileExists(filePaths(i)) Then
            fso.DeleteFile filePaths(i), True
        End If
    Next i
    
    Set fso = Nothing
End Function
Function ReadXlsxToStringArray(ByVal filePath As String, ByRef trueline() As Variant) As Variant
    Dim wb As Workbook
    Set wb = Workbooks.Open(filePath, ReadOnly:=True)
    Dim xlSheet As Worksheet
    Set xlSheet = wb.Sheets(1)
    
    Dim lastKRow As Long
    lastKRow = xlSheet.Cells(xlSheet.rows.count, 1).End(xlUp).row
    
    ReDim trueline(1 To lastKRow)
    
    
    Dim result() As String
    ReDim result(1 To lastKRow)
    
    k = 1
    For i = 1 To lastKRow
        result(i) = ""
        For j = 1 To 14
            result(i) = result(i) & xlSheet.Cells(i, j).value & ","
            If j = 7 Then
                rowValue = xlSheet.Cells(i, 7).value
                If LCase(rowValue) = "false" Then
                    trueline(k) = i
                    k = k + 1
                End If
            End If
        Next j
        result(i) = Left(result(i), Len(result(i)) - 1) '移[EXCEPT_CHAR]最後一個逗號
    Next i
    wb.Close SaveChanges:=False
    ReadXlsxToStringArray = result
    
End Function
Function mainprocess(ByVal folderPath As String)
    Dim allFilesthisfoldertemp As Variant
    Dim allFilesthisfolderziprar As Variant
    Dim allFilessubFoldertemp As Variant
    Dim allFilessubFoldertemp2 As Variant
    Dim allFilessubFolder As Variant
    Dim allFilessubzip As Variant
    Dim allFilessubrar As Variant
    Dim allFilessub7z As Variant
    Dim allFilessubspd As Variant
    Dim allFilessubcsv As Variant
    Dim allFile As Variant
    Dim ccdbfile As Variant
    Dim dl4file As Variant
    Dim dlxfile As Variant
    Dim mdbfile As Variant
    Dim file_cnt1  As Integer
    Dim file_cnt  As Integer
    Dim file_cntx  As Integer
    Dim first_zip_file_cnt  As Integer
    Dim first_rar_file_cnt  As Integer
    Dim first_7z_file_cnt  As Integer
    Dim first_zip_file_cnt1  As Integer
    Dim first_rar_file_cnt1  As Integer
    Dim first_7z_file_cnt1  As Integer
    Dim i  As Integer
    file_cntx = 0
    allFile = GetFilesInSubfolders(folderPath, file_cntx)
    If file_cntx > 0 Then
        ccdbfile = GetVariantfilename2(allFile, "ccdb", file_cnt)
        If file_cnt > 0 Then DeleteFiles ccdbfile
        dl4file = GetVariantfilename2(allFile, "dl4", file_cnt)
        If file_cnt > 0 Then DeleteFiles dl4file
        mdbfile = GetVariantfilename2(allFile, "mdb", file_cnt)
        If file_cnt > 0 Then DeleteFiles mdbfile
        dlxfile = GetVariantfilename2(allFile, "dlx", file_cnt)
        If file_cnt > 0 Then DeleteFiles dlxfile
        allFile = GetFilesInSubfolders(folderPath, file_cntx)
        allFilessubzip = GetVariantfilename(allFile, ".zip", first_zip_file_cnt)
        If first_zip_file_cnt > 0 Then Unzip allFilessubzip
        allFilessubrar = GetVariantfilename(allFile, ".rar", first_rar_file_cnt)
        If first_rar_file_cnt > 0 Then Unzip allFilessubrar
        allFilessub7z = GetVariantfilename(allFile, ".rar", first_7z_file_cnt)
        If first_7z_file_cnt > 0 Then Unzip allFilessub7z
        allFile = GetFilesInSubfolders(folderPath, file_cntx)
        If file_cntx > 0 Then changenamespdtocsv allFile
    End If
    file_cnt1 = 0
    file_cnt = 0
    file_cntx = 0
    allFilesthisfoldertemp = FindthisfolderALLFiles(folderPath, file_cnt1)
    If file_cnt1 > 0 Then
        allFilesthisfolderziprar = FindziprarFiles(allFilesthisfoldertemp, file_cnt)
        If file_cnt > 0 Then Unzip (allFilesthisfolderziprar)
        allFilessubFoldertemp = GetFilesInSubfolders(folderPath, file_cntx)
        allFilessubFoldertemp2 = RemoveElementsFromArray(allFilessubFoldertemp, allFilesthisfoldertemp, file_cnt1)
        allFilessubFolder = RemoveDuplicateFiles(allFilessubFoldertemp2)
        mdbfile = GetVariantfilename2(allFilessubFolder, "mdb", file_cnt)
        If file_cnt > 0 Then DeleteFiles mdbfile
        ccdbfile = GetVariantfilename2(allFilessubFolder, "ccdb", file_cnt)
        If file_cnt > 0 Then DeleteFiles ccdbfile
        ccdbfile = GetVariantfilename2(allFilessubFolder, "accdb", file_cnt)
        If file_cnt > 0 Then DeleteFiles ccdbfile
        dl4file = GetVariantfilename2(allFilessubFolder, "dl4", file_cnt)
        If file_cnt > 0 Then DeleteFiles dl4file
        dlxfile = GetVariantfilename2(allFilessubFolder, "dlx", file_cnt)
        If file_cnt > 0 Then DeleteFiles dlxfile
        allFilessubzip = GetVariantfilename(allFilessubFolder, ".zip", first_zip_file_cnt)
        If first_zip_file_cnt > 0 Then Unzip allFilessubzip
        allFilessubrar = GetVariantfilename(allFilessubFolder, ".rar", first_rar_file_cnt)
        If first_rar_file_cnt > 0 Then Unzip allFilessubrar
        allFilessub7z = GetVariantfilename(allFilessubFolder, ".7z", first_7z_file_cnt)
        If first_7z_file_cnt > 0 Then Unzip allFilessub7z
    End If
        
    allFilessubFoldertemp = GetFilesInSubfolders(folderPath, file_cntx)
    If file_cntx > 0 Then
        Dim allFilessubzip2 As Variant
        Dim allFilessubrar2 As Variant
        Dim allFilessub7z2 As Variant
        allFilessubzip2 = GetVariantfilename(allFilessubFoldertemp, ".zip", file_cnt)
        If file_cnt > 0 Then
            If first_zip_file_cnt > 0 Then
                For i = LBound(allFilessubzip2) To UBound(allFilessubzip2)
                On Error Resume Next
                    For j = LBound(allFilessubzip) To UBound(allFilessubzip)
                        If allFilessubzip2(i) <> "" And InStr(1, allFilessubzip2(i), allFilessubzip(j), vbTextCompare) = 0 Then
                            Unzip1 allFilessubzip2(i), True
                        End If
                    Next j
                On Error GoTo 0
                Next i
            Else
                Unzip allFilessubzip2, True
            End If
        End If
        allFilessubrar2 = GetVariantfilename(allFilessubFoldertemp, ".rar", file_cnt)
        If file_cnt > 0 Then
            If first_rar_file_cnt > 0 Then
                For i = LBound(allFilessubrar2) To UBound(allFilessubrar2)
                On Error Resume Next
                    For j = LBound(allFilessubrar) To UBound(allFilessubrar)
                        If allFilessubrar2(i) <> "" And InStr(1, allFilessubrar2(i), allFilessubrar(j), vbTextCompare) = 0 Then
                            Unzip1 allFilessubrar2(i), True
                        End If
                    Next j
                On Error GoTo 0
                Next i
            Else
                Unzip allFilessubrar2, True
            End If
        End If
        allFilessub7z2 = GetVariantfilename(allFilessubFoldertemp, ".7z", file_cnt)
        If file_cnt > 0 Then
            If first_7z_file_cnt > 0 Then
                For i = LBound(allFilessub7z2) To UBound(allFilessub7z2)
                On Error Resume Next
                    For j = LBound(allFilessub7z) To UBound(allFilessub7z)
                        If allFilessub7z2(i) <> "" And InStr(1, allFilessub7z2(i), allFilessub7z(j), vbTextCompare) = 0 Then
                            Unzip1 allFilessub7z2(i), True
                        End If
                    Next j
                On Error GoTo 0
                Next i
            Else
                Unzip allFilessub7z2, True
            End If
        End If
    End If
    allFilessubFoldertemp = GetFilesInSubfolders(folderPath, file_cntx)
    If file_cntx > 0 Then
        allFilessubFoldertemp2 = RemoveElementsFromArray(allFilessubFoldertemp, allFilesthisfoldertemp, file_cnt1)
        allFilessubFolder = RemoveDuplicateFiles(allFilessubFoldertemp2)
        allFilessubspd = GetVariantfilename(allFilessubFolder, ".spd", file_cnt)
        If file_cnt > 0 Then changenamespdtocsv allFilessubspd
    End If
End Function
Function changenamespdtocsv(ByVal Filestemp As Variant) As Variant
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    Dim i As Long
    
    For i = LBound(Filestemp) To UBound(Filestemp)
        Dim filePath As String
        filePath = Filestemp(i)
        
        If LCase(Right(filePath, 4)) = ".spd" Then
            Dim newFilePath As String
            newFilePath = Left(filePath, Len(filePath) - 4) & ".csv"
            If Dir(newFilePath) = "" Then
                fso.MoveFile filePath, newFilePath
            End If
            Filestemp(i) = newFilePath
        End If
    Next i
    
    changenamespdtocsv = Filestemp
End Function
Function GetVariantfilename2(ByVal Filestemp As Variant, file_extension As String, ByRef file_cnt As Integer) As Variant
    Dim result() As String
    Dim result_temp() As String
    ReDim result_temp(0 To UBound(Filestemp))
    Dim i As Integer
    file_cnt = 0
    For i = LBound(Filestemp) To UBound(Filestemp)
        Dim filePath As String
        filePath = Filestemp(i)
        
        If InStr(LCase(filePath), LCase(file_extension)) > 0 Then
            result_temp(file_cnt) = filePath
            file_cnt = file_cnt + 1
        End If
    Next i
    If file_cnt > 0 Then
        ReDim result(0 To file_cnt)
        For i = 0 To file_cnt
            result(i) = result_temp(i)
        Next i
    End If
    GetVariantfilename2 = result

End Function
Function GetVariantfilename(ByVal Filestemp As Variant, file_extension As String, ByRef file_cnt As Integer) As Variant
    Dim result() As String
    Dim result_temp() As String
    ReDim result_temp(0 To UBound(Filestemp))
    Dim i As Integer
    file_cnt = 0
    For i = LBound(Filestemp) To UBound(Filestemp)
        Dim filePath As String
        filePath = Filestemp(i)
        
        If LCase(Right(filePath, Len(file_extension))) = LCase(file_extension) Then
            result_temp(file_cnt) = filePath
            file_cnt = file_cnt + 1
        End If
    Next i
    If file_cnt > 0 Then
        ReDim result(0 To file_cnt)
        For i = 0 To file_cnt 'zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz
            On Error Resume Next
            result(i) = result_temp(i)
            On Error GoTo 0
        Next i
    End If
    GetVariantfilename = result

End Function

Function RemoveDuplicateFiles(ByVal csvFilestemp As Variant) As Variant
    Dim result() As String
    Dim dict As Object
    Set dict = CreateObject("Scripting.Dictionary")

    Dim i As Long
    For i = LBound(csvFilestemp) To UBound(csvFilestemp)
        Dim filePath As String
        filePath = csvFilestemp(i)
        Dim fileName As String
        fileName = Mid(filePath, InStrRev(filePath, "\") + 1)
        If Not dict.exists(fileName) Then
            dict.Add fileName, filePath
        End If
    Next i
    i = 0
    If dict.count > 0 Then
        ReDim result(0 To dict.count - 1)
        i = 0
        Dim key As Variant
        For Each key In dict.keys
            result(i) = dict(key)
            i = i + 1
        Next key
    End If
    
    RemoveDuplicateFiles = result
    
End Function
Function RemoveElementsFromArray(a As Variant, B As Variant, Optional cnt As Integer = 0) As Variant
    Dim j As Long
        j = 0
    If cnt > 0 Then
        Dim dict As Object
        Set dict = CreateObject("Scripting.Dictionary")
        
        Dim i As Long
        For i = LBound(B) To UBound(B)
            dict(B(i)) = True
        Next i
        
        Dim result() As Variant
        ReDim result(0 To UBound(a))
        
       
        For i = LBound(a) To UBound(a)
            If Not dict.exists(a(i)) Then
                result(j) = a(i)
                j = j + 1
            End If
        Next i
        If j > 2 Then
            ReDim Preserve result(0 To j - 1)
        End If
        RemoveElementsFromArray = result
    Else
        ReDim result(0 To UBound(a))
        For i = LBound(a) To UBound(a)
            result(j) = a(i)
            j = j + 1
        Next i
        RemoveElementsFromArray = result
    End If
End Function
Function GetFilesInSubfolders(ByVal folderPath As String, ByRef file_cnt As Integer) As Variant
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    Dim folder As Object
    Set folder = fso.GetFolder(folderPath)
    Dim result() As String
    Dim i As Integer
    i = 0
    Dim file As Object
    For Each file In folder.files
        ReDim Preserve result(i)
        result(i) = file.path
        i = i + 1
        file_cnt = file_cnt + 1
    Next file
    
    Dim subfolder As Object
    For Each subfolder In folder.Subfolders
        Dim subFolderPath As String
        subFolderPath = subfolder.path
        Dim subResult
        subResult = GetFilesInSubfolders(subFolderPath, file_cnt)
        If Not IsEmpty(subResult) Then
            Dim j As Integer
            On Error Resume Next
            For j = 0 To UBound(subResult)
                ReDim Preserve result(i)
                result(i) = subResult(j)
                i = i + 1
                file_cnt = file_cnt + 1
            Next j
            On Error GoTo GetFilesInSubfoldersNext
        End If
    Next subfolder
GetFilesInSubfoldersNext:
    GetFilesInSubfolders = result
End Function

Function FindziprarFiles(ByVal Filestemp As Variant, ByRef file_cnt As Integer) As Variant
    Dim result() As String
    Dim dict As Object
    Set dict = CreateObject("Scripting.Dictionary")
    file_cnt = 0
    Dim i As Long
    For i = LBound(Filestemp) To UBound(Filestemp)
        Dim filePath As String
        filePath = Filestemp(i)
        
        If LCase(Right(filePath, 4)) = ".zip" Or LCase(Right(filePath, 4)) = ".rar" Or LCase(Right(filePath, 3)) = ".7z" Then
            If Not dict.exists(filePath) Then
                dict.Add filePath, filePath
            End If
        End If
    Next i
    i = 0
    If dict.count > 0 Then
        ReDim result(0 To dict.count - 1)
        i = 0
        Dim key As Variant
        For Each key In dict.keys
            result(i) = dict(key)
            i = i + 1
        Next key
    End If
    
    FindziprarFiles = result
    file_cnt = i
End Function
Function FindLateCSVFiles(csvFileseqc() As Variant, csvFilesft() As Variant) As Variant
    Dim count As Integer
    count = Len(csvFilesft(1)(1)) - Len(Replace(csvFilesft(1)(1), "\", ""))
    Dim countnow As Integer
    ' 取得最晚的時間
    Dim latestTime As Date
    latestTime = DateSerial(1900, 1, 1) + TimeSerial(0, 0, 0)
    Dim i As Integer
    For i = 1 To UBound(csvFilesft)
        ' 如果是檔案而非資料夾，且路徑不是子目錄
        countnow = Len(csvFilesft(i)(1)) - Len(Replace(csvFilesft(i)(1), "\", ""))
        If countnow <= count Then
            ' 取得檔案的最後修改時間
            Dim fileTime As Date
            fileTime = FileDateTime(csvFilesft(i)(1))
            ' 如果檔案的時間比 latestTime 還晚，就更新 latestTime
            If fileTime > latestTime Then
                latestTime = fileTime
            End If
        End If
    Next i
     ' 找出比 latestTime 晚的檔案
    Dim lateCSVFiles() As Variant
    Dim tempcsv() As Variant
    ReDim tempcsv(0 To UBound(csvFileseqc))
    'ReDim lateCSVFiles(0 To UBound(csvFileseqc))
    
    Dim lateCSVFilesCount As Integer
    lateCSVFilesCount = 0
    For i = 1 To UBound(csvFileseqc)
        Dim seqcFileTime As Date
        seqcFileTime = CDate(csvFileseqc(i)(0))
        If seqcFileTime > latestTime Then
            lateCSVFilesCount = lateCSVFilesCount + 1
            tempcsv(lateCSVFilesCount) = csvFileseqc(i)
        End If
    Next i
    If lateCSVFilesCount >= 1 Then
        'ReDim Preserve lateCSVFiles(1 To lateCSVFilesCount)
         ReDim lateCSVFiles(1 To lateCSVFilesCount)
         For i = 1 To lateCSVFilesCount
            lateCSVFiles(i) = tempcsv(i)
         Next i
    End If
    
    FindLateCSVFiles = lateCSVFiles
End Function
Function findfteqctime(ByVal folderPath_eqc As String) As Integer '20240223 add
    Dim fileB As Object
    Set fileB = CreateObject("Scripting.FileSystemObject").GetFile(folderPath_eqc)
    Dim fileBContents As String
    Open fileB For Input As #2
    fileBContents = Input$(LOF(2), 2)
    Close #2
     ' 找出 B 檔中不是 1 的行的 A 欄位值，以及 A 檔中相對應的行
    Dim rowsInB As Variant
    rowsInB = Split(fileBContents, vbCrLf)
    If (InStr(1, rowsInB(1), "TX", vbTextCompare) > 0) Then  'ASL
        findfteqctime = 900
    Else
        findfteqctime = 400
    End If
End Function
Function FindmatchedCSV(csvFileseqc As Variant, csvFilesft As Variant, ByVal ctafile As Boolean) As Variant '20240223 fix
    Dim tempcsv() As Variant
    Dim i As Long, j As Long, k As Long, l As Long, m As Long
    Dim zz As Long
    Dim timeDiff As Double
    Dim matched As Boolean
    Dim timecnt As Long
    
    For Each ftFile In csvFilesft
        If (InStr(1, ftFile(1), "G5619", vbTextCompare) > 0) Then
            GoTo matchedCSVfinal
        End If
    Next ftFile
        
    For Each seqcFile In csvFileseqc
        If (InStr(1, seqcFile(1), "G5619", vbTextCompare) > 0) Then
        GoTo matchedCSVfinal
        End If
    Next seqcFile
    timecnt = 400 '400sec
    timecnt = findfteqctime(csvFileseqc(1)(1))
    If ctafile = True Then
        timecnt = 100
    End If
    
    ReDim tempcsv(0 To UBound(csvFileseqc), 0 To 1)
    i = 0
    
    For Each seqcFile In csvFileseqc
        matched = False
        For Each ftFile In csvFilesft
            timeDiff = DateDiff("s", seqcFile(0), ftFile(0))
            If Abs(timeDiff) <= timecnt Then '2-OAER×?o3!RE?!Rttimecnt?o
                For j = 0 To i
                    If tempcsv(j, 0) = ftFile(1) Then
                         GoTo nextqcx
                    End If
                Next j
                matched = True
nextqcx:
                Exit For
            End If
        Next ftFile
        
        If matched Then
            tempcsv(i, 0) = ftFile(1)
            tempcsv(i, 1) = seqcFile(1)
            i = i + 1
        End If
    Next seqcFile
    l = i
    For Each seqcFile In csvFileseqc
        matched = False
        For zz = 0 To l
            If (InStr(1, tempcsv(zz, 1), seqcFile(1), vbTextCompare) > 0) Then
                GoTo nextqcxx2
            End If
        Next zz
        For Each ftFile In csvFilesft
            If FindCSVFileByTime2x(ftFile(1), seqcFile(1)) = True Then
                For k = 0 To l
                    If tempcsv(k, 0) = ftFile(1) Then
                        GoTo nextqcx2
                    End If
                Next k
                matched = True
nextqcx2:
                Exit For
            End If
        Next ftFile
        If matched Then
            tempcsv(l, 0) = ftFile(1)
            tempcsv(l, 1) = seqcFile(1)
            l = l + 1
        End If
nextqcxx2:
     Next seqcFile
     m = l
'20240129 no use FileDateTime
'     For Each seqcFile In csvFileseqc
'        matched = False
'         For zz = 0 To m
'            If (InStr(1, tempcsv(zz, 1), seqcFile(1), vbTextCompare) > 0) Then
'                GoTo nextqcxx3
'            End If
'        Next zz
'        For Each ftFile In csvFilesft
'            If FindCSVFileByTimex(ftFile(1), seqcFile(1)) = True Then
'                For k = 0 To m
'                    If tempcsv(k, 0) = ftFile(1) Then
'                        GoTo nextqcx3
'                    End If
'                Next k
'                matched = True
'nextqcx3:
'                Exit For
'            End If
'        Next ftFile
'        If matched Then
'            tempcsv(m, 0) = ftFile(1)
'            tempcsv(m, 1) = seqcFile(1)
'            m = m + 1
'        End If
'nextqcxx3:
'     Next seqcFile
matchedCSVfinal:
    If m > 0 Then
        ReDim matchedCSV(0 To m - 1, 0 To 1)
        For j = 0 To m - 1
            matchedCSV(j, 0) = tempcsv(j, 0)
            matchedCSV(j, 1) = tempcsv(j, 1)
        Next j
    Else
        ReDim matchedCSV(0, 0)
    End If
    
    FindmatchedCSV = matchedCSV
End Function
Function FindunmatchedCSV(csvFileseqc As Variant, csvFilesmatch As Variant) As Variant
    Dim tempcsv() As Variant
    Dim unmatchedCSV() As Variant
    Dim i As Long, j As Long
    Dim matched As Boolean
    
    ReDim tempcsv(0 To UBound(csvFileseqc), 0 To 1)
    i = 0

    For Each seqcFile In csvFileseqc
        matched = False
        For Each matchFile In csvFilesmatch
            If seqcFile(1) = matchFile Then
                matched = True
                Exit For
            End If
        Next matchFile
        
        If Not matched Then
            tempcsv(i, 0) = seqcFile(0)
            tempcsv(i, 1) = seqcFile(1)
            i = i + 1
        End If
    Next seqcFile
    
     If i > 0 Then
        ReDim unmatchedCSV(0 To i - 1, 0 To 1)
        For j = 0 To i - 1
            unmatchedCSV(j, 0) = tempcsv(j, 0)
            unmatchedCSV(j, 1) = tempcsv(j, 1)
        Next j
    Else
        ReDim unmatchedCSV(0, 0)
    End If
    
    FindunmatchedCSV = unmatchedCSV
End Function
Function WaitUntilMouseMove(minutes As Long) As Boolean
    Dim timeOut As Long
    Dim lastPos As POINTAPI
    
    ' 計算等待時間（單位：毫秒）
    timeOut = MinutesToMilliseconds(minutes)
    
    ' 顯示等待視窗
    Dim myExcel As Object
    Set myExcel = CreateObject("Excel.Application")
    ' 新增活頁簿並顯示訊息
    myExcel.Workbooks.Add
    myExcel.Visible = True
    
    ' 初始顯示的訊息
    Dim message As String
    message = "正在偵測系統是否閒置 " & minutes & " 分鐘...若按ESC或關閉視窗則立即執行"
    If minutes < 1 Then
        message = "正在偵測系統是否閒置 " & timeOut / 1000 & " 秒...若按ESC或關閉視窗則立即執行"
    End If
    myExcel.Cells(1, 1).ColumnWidth = 60
    myExcel.Cells(1, 1).value = message
    myExcel.Cells(2, 1).value = "是否立即執行Y/N"
    myExcel.Cells(2, 2).value = "N"
    
     ' 紀錄滑鼠位置
    lastX = GetCursorX()
    lastY = GetCursorY()
    
    ' 等待滑鼠移動或是時間到期
    Do While timeOut > 0
        ' 當滑鼠有移動時，重新計時
        If lastX <> GetCursorX() Or lastY <> GetCursorY() Then
            lastX = GetCursorX()
            lastY = GetCursorY()
            timeOut = MinutesToMilliseconds(minutes)
        Else
            timeOut = timeOut - 1000
            ' 每 100 毫秒檢查一次滑鼠狀態
            Application.Wait (Now + TimeValue("0:00:01"))
            ' 顯示剩餘時間
            If minutes >= 1 Then
                message = "正在偵測系統是否閒置 " & timeOut / 1000 & " 秒...若按ESC或關閉視窗即不執行"
            Else
                message = "正在偵測系統是否閒置 " & timeOut & " 毫秒...若按ESC或關閉視窗則立即執行"
            End If
            On Error GoTo exit1
            myExcel.DisplayAlerts = False
            myExcel.Cells(1, 1).value = message
        End If
        
         ' 如果使用者按下 ESC 鍵，就中斷等待
        If GetAsyncKeyState(vbKeyEscape) <> 0 Then
            GoTo exit1
        End If
        If StrComp(myExcel.Cells(2, 2).value, "Y", vbTextCompare) = 0 Then
            Exit Do
        End If

    Loop
    myExcel.DisplayAlerts = False
    ' 關閉等待視窗
    myExcel.Quit
    Set myExcel = Nothing
    WaitUntilMouseMove = True
    Exit Function
exit1:
    Set myExcel = Nothing
    WaitUntilMouseMove = False
End Function
' 取得滑鼠 X 座標
Function GetCursorX() As Long
    Dim pt As POINTAPI
    GetCursorPos pt
    GetCursorX = pt.x
End Function

' 取得滑鼠 Y 座標
Function GetCursorY() As Long
    Dim pt As POINTAPI
    GetCursorPos pt
    GetCursorY = pt.y
End Function
' 將分鐘數轉換成毫秒
Function MinutesToMilliseconds(minutes As Long) As Long
    MinutesToMilliseconds = minutes * 60 * 1000
End Function
Sub DeleteDlxFiles(ByVal folderPath As String)
    ' 創建 FileSystemObject
    Dim fs As Object
    Set fs = CreateObject("Scripting.FileSystemObject")
    
    ' 取得指定資料夾物件
    Dim folder As Object
    Set folder = fs.GetFolder(folderPath)
    
    ' 刪[EXCEPT_CHAR]資料夾中的 .dlx 檔案
    Dim file As Object
    For Each file In folder.files
        If InStrRev(LCase(file.name), ".dlx") > 0 Then
            fs.DeleteFile file.path
        End If
    Next file
End Sub
Sub DeleteDl4Files(ByVal folderPath As String)
    ' 創建 FileSystemObject
    Dim fs As Object
    Set fs = CreateObject("Scripting.FileSystemObject")
    
    ' 取得指定資料夾物件
    Dim folder As Object
    Set folder = fs.GetFolder(folderPath)
    
    ' 刪[EXCEPT_CHAR]資料夾中的 .dlx 檔案
    Dim file As Object
    For Each file In folder.files
        If InStrRev(LCase(file.name), ".dl4") > 0 Then
            fs.DeleteFile file.path
        End If
    Next file
    DeletemdbFiles folderPath
    DeletexlsFiles folderPath
End Sub
Sub DeletemdbFiles(ByVal folderPath As String)
    ' 創建 FileSystemObject
    Dim fs As Object
    Set fs = CreateObject("Scripting.FileSystemObject")
    
    ' 取得指定資料夾物件
    Dim folder As Object
    Set folder = fs.GetFolder(folderPath)
    
    ' 刪[EXCEPT_CHAR]資料夾中的 .dlx 檔案
    Dim file As Object
    For Each file In folder.files
        If InStrRev(LCase(file.name), ".mdb") > 0 Then
            fs.DeleteFile file.path
        End If
    Next file
End Sub
Sub DeletexlsFiles(ByVal folderPath As String)
    ' 創建 FileSystemObject
    Dim fs As Object
    Set fs = CreateObject("Scripting.FileSystemObject")
    
    ' 取得指定資料夾物件
    Dim folder As Object
    Set folder = fs.GetFolder(folderPath)
    
    ' 刪[EXCEPT_CHAR]資料夾中的 .dlx 檔案
    Dim file As Object
    For Each file In folder.files
        If InStrRev(LCase(file.name), ".xls") > 0 Then
            fs.DeleteFile file.path
        End If
    Next file
End Sub

Public Sub UnzipSPD(ByVal folderPath As String)
    ' 設定 7-Zip 的安裝路徑
    Const SevenZipPath As String = "C:\Program Files\7-Zip\7z.exe"
    
    ' 取得 SPD 檔案的清單
    Dim fs As Object
    Set fs = CreateObject("Scripting.FileSystemObject")
    Dim folder As Object
    Set folder = fs.GetFolder(folderPath)
    Dim file As Object
    Dim spdList As New Collection
    For Each file In folder.files
        If LCase(Right(file.name, 8)) = ".spd.zip" Or LCase(Right(file.name, 8)) = ".csv.zip" Then
            spdList.Add file
        End If
    Next file
    Set wsh = CreateObject("WScript.Shell")
    ' 使用 7-Zip 解壓縮 SPD 檔案
    If spdList.count > 0 Then
        Dim shell As Object
        Set shell = CreateObject("WScript.Shell")
        Dim command As String
        Dim zipFile As Object
        For Each zipFile In spdList
            ' 組合 7-Zip 的解壓縮指令
            command = Chr(34) & SevenZipPath & Chr(34) & " x -aoa " & Chr(34) & zipFile.path & Chr(34) & " -o" & Chr(34) & folderPath & Chr(34)
            ' 執行 7-Zip 指令
            'shell.Run command, 0, True
            ' 使用wsh.Run執行命令，並等待解壓縮完成
            wsh.Run command, vbHide, True
            ' 刪[EXCEPT_CHAR]已解壓縮的 SPD 檔案
            fs.DeleteFile zipFile.path
        Next zipFile
    End If
End Sub
Function Unzip(ByVal folderPath As Variant, Optional delfile As Boolean = False)
    Const SevenZipPath As String = "C:\Program Files\7-Zip\7z.exe"
    Set wsh = CreateObject("WScript.Shell")
    
    Dim fs As Object
    Set fs = CreateObject("Scripting.FileSystemObject")
    Dim folder As Object
    Dim folderPathtemp As String
    
    Dim i As Integer
    For i = LBound(folderPath) To UBound(folderPath)
        If folderPath(i) <> "" Then
         folderPathtemp = fs.GetParentFolderName(folderPath(i))
         Set folder = fs.GetFolder(folderPathtemp)
         Dim file As Object
         'For Each file In folder.files
             If LCase(Right(folderPath(i), 4)) = ".zip" Or LCase(Right(folderPath(i), 4)) = ".rar" Or LCase(Right(folderPath(i), 3)) = ".7z" Then
                 Dim command As String
                 command = Chr(34) & SevenZipPath & Chr(34) & " x -aoa " & Chr(34) & folderPath(i) & Chr(34) & " -o" & Chr(34) & folder.path & Chr(34)
                 wsh.Run command, vbHide, True
                 If delfile = True Then fs.DeleteFile folderPath(i)
             End If
        ' Next file
        End If
    Next i
End Function
Function Unzip1(ByVal folderPath As String, Optional delfile As Boolean = False)
    Const SevenZipPath As String = "C:\Program Files\7-Zip\7z.exe"
    Set wsh = CreateObject("WScript.Shell")
    
    Dim fs As Object
    Set fs = CreateObject("Scripting.FileSystemObject")
    Dim folder As Object
    Dim folderPathtemp As String
    
   
    If folderPath <> "" Then
     folderPathtemp = fs.GetParentFolderName(folderPath)
     Set folder = fs.GetFolder(folderPathtemp)
     Dim file As Object
         If LCase(Right(folderPath, 4)) = ".zip" Or LCase(Right(folderPath, 4)) = ".rar" Or LCase(Right(folderPath, 3)) = ".7z" Then
             Dim command As String
             command = Chr(34) & SevenZipPath & Chr(34) & " x -aoa " & Chr(34) & folderPath & Chr(34) & " -o" & Chr(34) & folder.path & Chr(34)
             wsh.Run command, vbHide, True
             If delfile = True Then fs.DeleteFile folderPath
         End If

    End If

End Function



'Sub ChangeSpdToCsv(ByVal folderPath As String)
'    ' 建立一個 FileSystemObject 物件
'    Dim fso As Object
'    Set fso = CreateObject("Scripting.FileSystemObject")
'    ' 取得目標資料夾的物件
'    Dim folder As Object
'    Set folder = fso.GetFolder(folderPath)
'
'    ' 對於目錄中的每個檔案執行下列程序
'    Dim file As Object
'    For Each file In folder.files
'        ' 如果檔名的尾碼為 .spd，則進行更名
'        If LCase(Right(file.Name, 4)) = ".spd" Then
'            ' 將新檔名設為原檔名去掉尾碼 .spd，再加上 .csv
'            Dim newFileName As String
'            If InStr(1, UCase(file.Name), ".SPD") > 0 Then
'                If Not fso.fileExists(file.ParentFolder.path & "\" & Replace(file.Name, ".spd", ".csv", , , vbTextCompare)) Then
'                    newFileName = Replace(file.Name, ".spd", ".csv", , , vbTextCompare)
'                    ' 更改檔案名稱
'                    file.Name = newFileName
'                End If
'
'            End If
'        End If
'    Next file
'End Sub
Sub ChangeSpdToCsv(ByVal folderPath As String)
    ' 建立一個 FileSystemObject 物件
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    ' 取得目標資料夾的物件
    Dim folder As Object
    Set folder = fso.GetFolder(folderPath)

    '對於目錄中的每個檔案執行下列程序
    Dim file As Object
    For Each file In folder.files
        ' 如果檔名的尾碼為 .spd，則進行更名
        If LCase(Right(file.name, 4)) = ".spd" Then
            ' 將新檔名設為原檔名去掉尾碼 .spd，再加上 .csv
            Dim newFileName As String
            If InStr(1, UCase(file.name), ".SPD") > 0 Then
                If Not fso.fileExists(file.ParentFolder.path & "\" & Replace(file.name, ".spd", ".csv", , , vbTextCompare)) Then
                    newFileName = Replace(file.name, ".spd", ".csv", , , vbTextCompare)
                    ' 更改檔案名稱
                    file.name = newFileName
                End If
            End If
        End If
    Next file
    
    ' 對於每個子目錄執行下列程序
    Dim subfolder As Object
    For Each subfolder In folder.Subfolders
        ChangeSpdToCsv subfolder.path ' 遞迴呼叫
    Next subfolder
End Sub
Public Function setAppl(Optional mySpeedUp As Integer = 1, Optional myCalc As Integer = xlCalculationManual, Optional myScrUpd As Boolean = False, Optional myDispSta As Boolean = False, Optional myEnaEve As Boolean = False) '2020/04/29
    
    If mySpeedUp = 0 Then
        Application.Calculation = xlCalculationAutomatic 'make file changed
        Application.ScreenUpdating = True
        Application.DisplayStatusBar = True
        Application.EnableEvents = True
    Else
        Application.Calculation = myCalc 'make file changed
        Application.ScreenUpdating = myScrUpd
        Application.DisplayStatusBar = myDispSta
        Application.EnableEvents = myEnaEve
    End If

End Function
Function ColumnLetter(ColumnNumber As Integer) As String
    Dim n As Long
    Dim c As Byte
    Dim s As String

    n = ColumnNumber
    Do
        c = ((n - 1) Mod 26)
        s = Chr(c + 65) & s
        n = (n - c) \ 26
    Loop While n > 0

    ColumnLetter = s

End Function
Function ChangeUntestedLimitToBeNone()

    If Not checkSheetIsDatalog Then Exit Function
    
    Dim mySheetIndex As Integer
    
    If Application.Version >= 12# And Application.ActiveWorkbook.FileFormat <> 56 And Application.ActiveWorkbook.FileFormat <> 43 And Application.ActiveWorkbook.FileFormat <> 39 Then
        myMaxDeviceRangeName = "A13:A" + CStr(maxRowN)
'        myMaxItemRangeName = "C7:DZZ7"
        myMaxItemRangeName = "C7:" + ColumnLetter(maxColumnN) + "7" '2019/01/14
    Else
        myMaxDeviceRangeName = "A13:A65536"
        myMaxItemRangeName = "C7:IV7"
    End If
    myTotalDeviceNumber = Application.WorksheetFunction.CountA(Sheets(1).Range(myMaxDeviceRangeName))
    'myTotalItemNumber = Application.WorksheetFunction.CountA(ActiveSheet.Range(myMaxItemRangeName))
    
    For myPassBinN = 1 To maxPassBinN
        For myDeviceIndex = 1 To myTotalDeviceNumber
            'If ActiveSheet.Cells(myDeviceIndex + 12, 2) <= maxPassBinN Then 'pass bin
            If Sheets(1).Cells(myDeviceIndex + 12, 2) = myPassBinN Then 'pass bin
                For mySheetIndex = 1 To Sheets.count
                    If Not checkSheetIsDatalog(mySheetIndex) Then Exit For
                    myTotalItemNumber = Application.WorksheetFunction.CountA(Sheets(mySheetIndex).Range(myMaxItemRangeName))
                    For myItemIndex = 1 To myTotalItemNumber
                        With Sheets(mySheetIndex)
                            If .Cells(myDeviceIndex + 12, myItemIndex + 2).value = 0 Then
                                If .Cells(10, myItemIndex + 2).value <> "none" And .Cells(myDeviceIndex + 12, myItemIndex + 2).value >= .Cells(10, myItemIndex + 2).value Then
                                    .Cells(10, myItemIndex + 2).value = "none"
                                    .Cells(10, myItemIndex + 2).Font.Color = RGB(255, 0, 0)
'                                Else
                                End If '2018/04/10
                                    If .Cells(11, myItemIndex + 2).value <> "none" And .Cells(myDeviceIndex + 12, myItemIndex + 2).value <= .Cells(11, myItemIndex + 2).value Then
                                        .Cells(11, myItemIndex + 2).value = "none"
                                        .Cells(11, myItemIndex + 2).Font.Color = RGB(255, 0, 0)
                                    End If
'                                End If
                            End If
                        End With
                    Next myItemIndex
                Next mySheetIndex
                If Not myHideMsg Then MsgBox "Mission Complete!"
                Exit Function
            End If
        Next myDeviceIndex
    Next myPassBinN
    
'    If Not myHideMsg And myDeviceIndex > myTotalDeviceNumber Then MsgBox "There is no pass(Bin1-4)!"
    If Not myHideMsg And myDeviceIndex > myTotalDeviceNumber Then MsgBox "There is no pass(Bin1-" + CStr(maxPassBinN) + ")!" '2019/09/26
  

End Function
Sub spd2csv(Optional myCloseAICA As Boolean = True)
    '將 A 欄位的資料依照逗號分隔
    Columns("A:A").Select
    Selection.TextToColumns Destination:=Range("A1"), DataType:=xlDelimited, _
        TextQualifier:=xlDoubleQuote, ConsecutiveDelimiter:=False, Tab:=False, _
        Semicolon:=False, Comma:=True, Space:=False, Other:=False, _
        TrailingMinusNumbers:=True
End Sub
' 計算指定工作表中總共有多少個裝置
Public Function GetTotalDeviceNumber(Optional mySheetIndex As Integer = 1) As Long

    ' 如果未指定工作表索引，使用目前的活頁簿
    If mySheetIndex = 0 Then mySheetIndex = ActiveSheet.index
    
    ' 根據檔案格式設定最大裝置範圍的名稱
    If Application.Version >= 12# And Application.ActiveWorkbook.FileFormat <> 56 And Application.ActiveWorkbook.FileFormat <> 43 And Application.ActiveWorkbook.FileFormat <> 39 Then
        myMaxDeviceRangeName = "A13:A" + CStr(maxRowN)
    Else
        myMaxDeviceRangeName = "A13:A65536"
    End If
    
    ' 計算指定範圍內的非空儲存格數目，並返回結果
    GetTotalDeviceNumber = Application.WorksheetFunction.CountA(Sheets(mySheetIndex).Range(myMaxDeviceRangeName))

End Function

Function FillEmptyItemName(Optional myBookName As Variant = "", Optional mySheetN As Integer = 1)
    
    Dim TestArray() As String, curItemN As Integer, preItemN As Integer, subItemN As Integer '2019/12/24
    
    If myBookName = "" Then myBookName = ActiveWorkbook.name
    If Application.Version >= 12# And Application.ActiveWorkbook.FileFormat <> 56 And Application.ActiveWorkbook.FileFormat <> 43 And Application.ActiveWorkbook.FileFormat <> 39 Then
'        myMaxItemRangeName = "C12:DZZ12"
        myMaxItemRangeName = "C12:" + ColumnLetter(maxColumnN) + "12" '2019/01/14
    Else
        myMaxItemRangeName = "C12:IV12"
    End If
    myTotalItemNumber = Application.WorksheetFunction.CountA(Workbooks(myBookName).Sheets(mySheetN).Range(myMaxItemRangeName))
    For i = 2 + myTotalItemNumber + 1 To 2 + myTotalItemNumber + 128 '2022/10/27
        If Workbooks(myBookName).Sheets(mySheetN).Cells(8, i).value = "" Then
            myTotalItemNumber = i - 3
            Exit For
        End If
    Next i
    
    preItemN = 0
    subItemN = 0
    With Workbooks(myBookName).Sheets(mySheetN)
        For myItemIndex = 1 To myTotalItemNumber
'            If .Cells(12, myItemIndex + 2).Value <> "" Then
            If .Cells(12, myItemIndex + 2).value <> "" Or .Cells(8, myItemIndex + 2).value <> "" Then '2022/10/27
                If .Cells(7, myItemIndex + 2).value = "" Then
                    If myItemIndex < 10 Then
                        .Cells(7, myItemIndex + 2).value = "0.00.0" + CStr(myItemIndex)
                    Else
                        .Cells(7, myItemIndex + 2).value = "0.00." + CStr(myItemIndex)
                    End If
                    .Cells(7, myItemIndex + 2).Font.Color = 255
                    If .Cells(8, myItemIndex + 2).value = "" Then
                        If myItemIndex <> 1 Or InStr(.Cells(12, myItemIndex + 2), "Time") = 0 Then
                            .Cells(8, myItemIndex + 2).value = .Cells(12, myItemIndex + 2).value
                        Else
                            .Cells(8, myItemIndex + 2).value = "Test_Time"
                        End If
                        .Cells(8, myItemIndex + 2).Font.Color = 255
                    End If
'                    If .Cells(10, myItemIndex + 2).Value = "" Then
'                    If Replace(CStr(.Cells(10, myItemIndex + 2).Value), " ", "") = "" Then '20180524
'                        .Cells(10, myItemIndex + 2).Value = "none"
'                        .Cells(10, myItemIndex + 2).Font.Color = 255
'                    End If
'                    If .Cells(11, myItemIndex + 2).Value = "" Then
'                    If Replace(CStr(.Cells(11, myItemIndex + 2).Value), " ", "") = "" Then '20180524
'                        .Cells(11, myItemIndex + 2).Value = "none"
'                        .Cells(11, myItemIndex + 2).Font.Color = 255
'                    End If
                ElseIf IsNumeric(.Cells(7, myItemIndex + 2).value) Then '2019/12/24
                    TestArray() = Split(CStr(.Cells(7, myItemIndex + 2).value), ".")
                    If UBound(TestArray) = 1 Then
                        curItemN = CInt(TestArray(0))
                        If curItemN <> preItemN Then
                            subItemN = 1
                        Else
                            subItemN = subItemN + 1
                        End If
                        If curItemN < 10 Then
                            If subItemN < 10 Then
                                .Cells(7, myItemIndex + 2).value = "0.0" + CStr(curItemN) + ".0" + CStr(subItemN)
                            Else
                                .Cells(7, myItemIndex + 2).value = "0.0" + CStr(curItemN) + "." + CStr(subItemN)
                            End If
                        Else
                            If subItemN < 10 Then
                                .Cells(7, myItemIndex + 2).value = "0." + CStr(curItemN) + ".0" + CStr(subItemN)
                            Else
                                .Cells(7, myItemIndex + 2).value = "0." + CStr(curItemN) + "." + CStr(subItemN)
                            End If
                        End If
                        preItemN = CInt(TestArray(0))
                    ElseIf UBound(TestArray) = 0 Then '2020/06/01
                        curItemN = preItemN
                        If myItemIndex = 1 Then
                            subItemN = .Cells(7, myItemIndex + 2).value
                        Else
                            subItemN = subItemN + 1
                        End If
                        If curItemN < 10 Then
                            If subItemN < 10 Then
                                .Cells(7, myItemIndex + 2).value = "0.0" + CStr(curItemN) + ".0" + CStr(subItemN)
                            Else
                                .Cells(7, myItemIndex + 2).value = "0.0" + CStr(curItemN) + "." + CStr(subItemN)
                            End If
                        Else
                            If subItemN < 10 Then
                                .Cells(7, myItemIndex + 2).value = "0." + CStr(curItemN) + ".0" + CStr(subItemN)
                            Else
                                .Cells(7, myItemIndex + 2).value = "0." + CStr(curItemN) + "." + CStr(subItemN)
                            End If
                        End If
                    End If
                Else
                    Exit For
                End If
            End If
        Next myItemIndex
                
        For myItemIndex = 1 To myTotalItemNumber '2019/12/24
            If .Cells(12, myItemIndex + 2).value <> "" Then
                If Replace(CStr(.Cells(10, myItemIndex + 2).value), " ", "") = "" Then
                    .Cells(10, myItemIndex + 2).value = "none"
                    .Cells(10, myItemIndex + 2).Font.Color = 255
                End If
                If Replace(CStr(.Cells(11, myItemIndex + 2).value), " ", "") = "" Then
                    .Cells(11, myItemIndex + 2).value = "none"
                    .Cells(11, myItemIndex + 2).Font.Color = 255
                End If
            End If
        Next myItemIndex
    End With

End Function

Public Function GetTotalItemNumber(Optional mySheetIndex As Integer = 1) As Variant

    ' 若未指定工作表編號，預設為目前使用中的工作表
    If mySheetIndex = 0 Then mySheetIndex = ActiveSheet.index
    
    ' 判斷目前 Excel 版本及檔案格式，選擇對應的範圍
    If Application.Version >= 12# And Application.ActiveWorkbook.FileFormat <> 56 And Application.ActiveWorkbook.FileFormat <> 43 And Application.ActiveWorkbook.FileFormat <> 39 Then
        myMaxItemRangeName = "C8:" + ColumnLetter(maxColumnN) + "8" ' 該行是新增的，舊的註解可以刪[EXCEPT_CHAR]
    Else
        myMaxItemRangeName = "C8:IV8" '20180524
    End If
    
    ' 計算該範圍內非空白儲存格的數量
    GetTotalItemNumber = Application.WorksheetFunction.CountA(Sheets(mySheetIndex).Range(myMaxItemRangeName))

End Function
Function ConvertCsvToXlsx(ByVal folderPath As String) As Boolean
    Dim myExcel As Object
    Dim myWorkbook As Object
    Set myExcel = CreateObject("Excel.Application")

'    '創建 csv 資料夾，若不存在
'    If Dir(folderPath & "\csv", vbDirectory) = "" Then
'        MkDir folderPath & "\csv"
'    End If
      
    Dim myFileName As String
    myFileName = Dir(folderPath & "*.csv")
    
    Dim fso As Object
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Do While myFileName <> ""
        myExcel.Visible = False
        Set myActiveWorkbook = myExcel.Workbooks.Open(folderPath & myFileName, UpdateLinks:=False)
        myActiveWorkbookName = myActiveWorkbook.name
        myNewWorkbookName = Left(myActiveWorkbookName, Len(myActiveWorkbookName) - 3) & "xlsx"
        ' 若目標檔案已存在，則刪[EXCEPT_CHAR]該檔案
        Dim targetFilePath As String
        targetFilePath = folderPath & myNewWorkbookName
        If fso.fileExists(targetFilePath) Then
            fso.DeleteFile targetFilePath
        End If
        ' 另存為新檔案
        myActiveWorkbook.SaveAs targetFilePath, FileFormat:=51
        ' 關閉 workbook
        myActiveWorkbook.Close SaveChanges:=False
        '移動 csv 檔案至 csv 資料夾
        'Name folderPath & myFileName As folderPath & "csv\" & myFileName
        myFileName = Dir()
    Loop
    
    myExcel.Quit
    Set myExcel = Nothing
    Set myWorkbook = Nothing
    
    ConvertCsvToXlsx = True
End Function

Sub Device2BinControl(ByVal folderPath As String, Optional onlyonecsv As Boolean = False, Optional write_summary As Boolean = False, Optional summary_name As String = "FT", Optional summary_patch As String = "d:\temp") '2020/11/27

   'If Not checkSheetsHaveDatalog Then GoTo Final
    
    
    Dim myDeviceBinN(maxRowN) As Integer
'    Dim myBinArray(2560) As Long
    Dim myBinArray(maxColumnN) As Long '2015/02/12 by Greg
    Dim mySiteBinArray(maxSiteN, maxColumnN) As Long, myTotalSiteNo As Integer, myGoodSiteN As Boolean, mySiteTotalDeviceNo(maxSiteN) As Long '2018/10/03
    Dim myOutputSheetName, myItemName As String
    Dim everFailed As Boolean
    Dim myActiveWorkbook As Workbook
    Dim myDatalogSheet As Worksheet
    Dim mySummarySheet As Worksheet
    Dim myMax(32, maxColumnN), myMin(32, maxColumnN), myBinN(maxRowN) As Integer, failItemNo(maxColumnN) As Integer, myTotalFailItemN As Integer
    Dim TestArray() As String, myFailItemN As Integer
    Dim myItemFloat(maxColumnN) As Boolean, myValueEqualMaxLimit(maxColumnN) As Boolean, myValueEqualMinLimit(maxColumnN) As Boolean
    Dim myValueEqualLimitDeviceN(maxRowN, 1) As Long, myTotValueEqualLimitDeviceNo As Long
    Dim myOldSummarySheetIndex As Integer
    Dim mySheetIndex As Integer '2020/11/12
    
    Dim myAskRtPass As Boolean
    Dim myAskContinueOnFail As Boolean
    Dim myOptTotDeviceN As Long
    Dim myPassDeviceN As Long
    
    myMaxPassBinN = 4
    myAskRtPass = False
    myAskContinueOnFail = False
    myOptTotDeviceN = 0
    myPassDeviceN = 0
    
    Call setAppl(1, xlCalculationManual, True) '2020/06/01
    
   
    
    myBin1 = True '2017/09/21 by Greg
     
    myContinueOnFail = True '
    myRemoveUntestedValue = False
    If myAskRtPass Or myAskContinueOnFail Then myStartTimer = Timer
    myWorkbookTotalCount = Workbooks.count
    myOriginalWorkbookName = ActiveWorkbook.name
    
    If onlyonecsv = False Then
        If Dir(folderPath & "\csv", vbDirectory) = "" Then
           MkDir folderPath & "\csv"
        End If
    End If
   ' ConvertCsvToXlsx folderPath & "\"
    
    Dim myExcel As Object
    Dim myWorkbook As Object

      
    Dim myFileName As String
    If onlyonecsv = False Then
        myFileName = Dir(folderPath & "\*.csv")
    Else
        myFileName = folderPath
        'folderPath = Replace(folderPath, ".spd", ".csv", , , vbTextCompare)'20231031 fix  mark
        folderPath = Left(myFileName, Len(myFileName) - 4) & ".csv" '20231031 fix
    End If
    Dim xlsx_filename As String
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    Do While myFileName <> ""
'======================================================================================================================================================================================================================================================================================================================================================================================
        'Workbooks.Open fileName:=folderPath & myFileName
        ' Rows("12:12").Select
        'Selection.AutoFilter
        'Range(Selection, Selection.End(xlDown)).AutoFilter Field:=2
        If onlyonecsv = False Then
            Set myActiveWorkbook = Workbooks.Open(fileName:=folderPath & "\" & myFileName, UpdateLinks:=False)
            xlsx_filename = folderPath & "\" & myFileName
        Else
            Set myActiveWorkbook = Workbooks.Open(fileName:=folderPath, UpdateLinks:=False)
            xlsx_filename = folderPath
        End If
        xlsx_filename = Left(xlsx_filename, Len(xlsx_filename) - 4) & ".xlsx"
        myActiveWorkbookName = myActiveWorkbook.name
        myActiveWorkbook.Activate
        
        'del Test_Time 20231114
'        If InStr(1, Sheets(1).Cells(12, 3).value, "Time", vbTextCompare) > 0 Then
'           Sheets(1).Columns("C:C").Delete
'        End If
        
         If checkSheetsHaveDatalog = 0 Then GoTo Final '2020/08/20
        For i = 2 To Sheets.count
            If (Sheets(i).name = "QAData" Or Sheets(i).name = "QA_Data") And Sheets(i).Cells(6, 1) <> "" Then '2020/11/27
                Sheets(1).Move After:=Sheets(Sheets.count)
                Sheets(i - 1).Move Before:=Sheets(1)
            End If
        Next i
        If checkSheetsHaveDatalog = 0 Then GoTo Final '2020/08/20
        
        myBin1 = True '2017/09/21 by Greg
        myContinueOnFail = False
        myRemoveUntestedValue = False
        myOldSummarySheetIndex = 0
        
        myDefOK = False
            'For myLoop = 1 To 65535
            'For myloop = 0 To 65535
            For myloop = 0 To maxRowN - 1
                myDeviceBinN(myloop) = 1
                For i = 0 To 1
                    myValueEqualLimitDeviceN(myloop, i) = 0
                Next i
            Next
            myTotValueEqualLimitDeviceNo = 0
'            For myloop = 1 To 2560
            For myloop = 1 To maxColumnN - 1 '2017/04/07 by Greg
                myBinArray(myloop) = 0
                myItemFloat(myloop) = False
                myValueEqualMaxLimit(myloop) = False
                myValueEqualMinLimit(myloop) = False
            Next
            For i = 0 To maxSiteN '2018/10/03
                For myloop = 1 To maxColumnN - 1
                    mySiteBinArray(i, myloop) = 0
                Next
                mySiteTotalDeviceNo(i) = 0
            Next i
            
            myHideMsg = True
            Call ChangeUntestedLimitToBeNone
            myHideMsg = False
         myTotalItemNumber = 0
            myTotalDeviceNumber = 0 '2016/11/14
            If Application.Version >= 12# And Application.ActiveWorkbook.FileFormat <> 56 And Application.ActiveWorkbook.FileFormat <> 43 And Application.ActiveWorkbook.FileFormat <> 39 Then '2018/06/21
'                myMaxItemRangeName = "C7:DZZ7"
                myMaxItemRangeName = "C7:" + ColumnLetter(maxColumnN) + "7" '2019/01/14
            Else
                myMaxItemRangeName = "C7:IV7"
            End If
            myStartDatalogSheetN = 0 '2018/06/21 to reduce time
'            myEndDatalogSheetN = 0 '2018/06/21
            myEndDatalogSheetN = -1 '2018/06/25 to exit for loop
            For mySheetIndex = 1 To Sheets.count
                Set myDatalogSheet = myActiveWorkbook.Sheets(mySheetIndex)
                If CStr(myDatalogSheet.Cells(12, 1).value) = "Serial#" And CStr(myDatalogSheet.Cells(12, 2).value) = "Bin#" Then
'                    If myStartDatalogSheetN = 0 Then myStartDatalogSheetN = mySheetIndex '2018/06/21
                    If myStartDatalogSheetN = 0 Then '2019/07/18
                        myStartDatalogSheetN = mySheetIndex
                        If myDatalogSheet.Cells(9, 1).value = "CTA" Then
                            myTesterType = 1
                        ElseIf myDatalogSheet.Cells(9, 1).value = "STS" Then
                            myTesterType = 2
                        ElseIf myDatalogSheet.Cells(9, 1).value = "ETS" Then '2022/04/20
                            myTesterType = 3
                        ElseIf myDatalogSheet.Cells(9, 1).value = "YS" Then '2022/04/20
                            myTesterType = 4
                        End If
                    Else '2020/11/12
                        mySheetTotalDeviceNumber = GetTotalDeviceNumber(mySheetIndex)
                        If mySheetTotalDeviceNumber <> myTotalDeviceNumber Then Exit For
                    End If
                    myEndDatalogSheetN = mySheetIndex '2018/06/22
                    myDatalogSheet.Activate '2017/09/21 by Greg
                    ActiveWindow.FreezePanes = False '2019/04/08
                    myDatalogSheet.Cells(1, 1).Select
'                    ActiveWindow.SmallScroll Down:=5
                    ActiveWindow.SmallScroll Down:=4 '2021/01/13
                    'Rows("13:13").Select
                    Range("C13").Select '2013/09/05 by Greg
                    With ActiveWindow
                        '.SplitColumn = 0
                        '.SplitRow = 7
                        .FreezePanes = True '2013/09/05 by Greg
                        If ActiveSheet.AutoFilterMode = False Then '2018/12/18
                            rows("12:12").AutoFilter '2018/10/26
                        End If
                        .ScrollColumn = 3
                    End With
                    myInputSheetName = myActiveWorkbook.Worksheets(mySheetIndex).name
                    If myDefOK = False Then
                        'myMaxDeviceRangeName = "A13:A65535"
                        'myMaxDeviceRangeName = "A13:A65536"
                        If Application.Version >= 12# And Application.ActiveWorkbook.FileFormat <> 56 And Application.ActiveWorkbook.FileFormat <> 43 And Application.ActiveWorkbook.FileFormat <> 39 Then
                            myMaxDeviceRangeName = "A13:A" + CStr(maxRowN)
                        Else
                            myMaxDeviceRangeName = "A13:A65536"
                        End If
                        Set myMaxDeviceRange = myDatalogSheet.Range(myMaxDeviceRangeName)
                        myTotalDeviceNumber = Application.WorksheetFunction.CountA(myMaxDeviceRange)
                        myDeviceCellY = myTotalDeviceNumber + 12
                        myDefOK = True
                    End If
                    Set myMaxItemRange = myDatalogSheet.Range(myMaxItemRangeName)
                    'myTotalItemNumber = Application.WorksheetFunction.CountA(myMaxItemRange)
                    'myItemCellX = myTotalItemNumber + 2
                    myItemCellX = Application.WorksheetFunction.CountA(myMaxItemRange) + 2
                    For myLoopItem = 3 To myItemCellX
                        With myDatalogSheet
                            '.Cells(6, myLoopItem).Value = myLoopItem - 1
                            '.Cells(6, myLoopItem).Value = myLoopItem - 1 + myTotalItemNumber
'                            .Cells(6, myLoopItem).Value = myLoopItem + 2 + myTotalItemNumber '20170609
                            .Cells(6, myLoopItem).value = myLoopItem + (myMaxPassBinN - 2) + myTotalItemNumber '2018/05/24
                        End With
                    Next
                    myTotalItemNumber = myTotalItemNumber + Application.WorksheetFunction.CountA(myMaxItemRange)
                    'Range(myDatalogSheet.Cells(13, 3), myDatalogSheet.Cells(myDeviceCellY, myItemCellX)).Font.Color = RGB(0, 0, 0)
                    Range(myDatalogSheet.Cells(13, 2), myDatalogSheet.Cells(myDeviceCellY, myItemCellX)).Font.Color = RGB(0, 0, 0) '2023/05/31
                    If mySheetIndex = 1 Then '2018/10/03
                        For i = 3 To 3 + myTotalItemNumber - 1
'                            If InStr(LCase(CStr(ActiveSheet.Cells(8, i).value)), "site_no") Then '20240202 site fix for quad site name
'                                mySiteColumnN = i
'                                Exit For
'                            End If
                            'Site_Check
                            If InStr(LCase(CStr(ActiveSheet.Cells(8, i).value)), "site") And InStr(1, ActiveSheet.Cells(8, i).value, "Site_Check", vbTextCompare) = 0 Then  '20240202 site fix for quad site name
                                mySiteColumnN = i
                                Exit For
                            End If
                        Next i
                    End If
                Else
                    If myStartDatalogSheetN > 0 Then '2018/06/21
                        myEndDatalogSheetN = mySheetIndex - 1
                        Exit For
                    End If
                End If
            Next
'            For mySheetIndex = 1 To Sheets.Count
            For mySheetIndex = myStartDatalogSheetN To myEndDatalogSheetN '2018/06/21 to reduce time
                Set myDatalogSheet = myActiveWorkbook.Sheets(mySheetIndex)
'                If CStr(myDatalogSheet.Cells(12, 1).Value) = "Serial#" And CStr(myDatalogSheet.Cells(12, 2).Value) = "Bin#" Then
                    Set myMaxItemRange = myDatalogSheet.Range(myMaxItemRangeName)
                    myItemCellX = Application.WorksheetFunction.CountA(myMaxItemRange) + 2
                    For myLoopItem = 3 To myItemCellX
                        With myDatalogSheet
                            myMax(mySheetIndex, myLoopItem) = .Cells(10, myLoopItem).value
                            myMin(mySheetIndex, myLoopItem) = .Cells(11, myLoopItem).value
                        End With
                    Next myLoopItem
'                End If
            Next mySheetIndex
'            For mySheetIndex = 1 To Sheets.Count
            For mySheetIndex = myStartDatalogSheetN To myEndDatalogSheetN '2018/06/21 to reduce time
                Set myDatalogSheet = myActiveWorkbook.Sheets(mySheetIndex)
'                If CStr(myDatalogSheet.Cells(12, 1).Value) = "Serial#" And CStr(myDatalogSheet.Cells(12, 2).Value) = "Bin#" Then
                    For myLoopDevice = 13 To myDeviceCellY
                        myBinN(myLoopDevice) = myDatalogSheet.Cells(myLoopDevice, 2).value
                    Next myLoopDevice
                    Exit For
'                End If
            Next mySheetIndex
            ''' check if item value is float
            For mySheetIndex = 1 To Sheets.count
                If checkSheetIsDatalog(CInt(mySheetIndex)) Then
                    myItemCellX = GetTotalItemNumber(CInt(mySheetIndex)) + 2
                    For myLoopItem = 3 To myItemCellX
                        For myLoopDevice = 13 To myDeviceCellY
                            myVal = Sheets(mySheetIndex).Cells(myLoopDevice, myLoopItem).value
                            myBin = Sheets(mySheetIndex).Cells(6, myLoopItem).value
                            If InStr(CStr(myVal), ".") Then myItemFloat(myBin) = True
                            If myItemFloat(myBin) Or myLoopDevice > 1012 Then Exit For '2017/11/21
                        Next myLoopDevice
                    Next myLoopItem
                End If
            Next mySheetIndex
            For myLoopDevice = 13 To myDeviceCellY
                everFailed = False
'                For mySheetIndex = 1 To Sheets.Count
                For mySheetIndex = myStartDatalogSheetN To myEndDatalogSheetN '2018/06/21 to reduce time
                    Set myDatalogSheet = myActiveWorkbook.Sheets(mySheetIndex)
'                    If CStr(myDatalogSheet.Cells(12, 1).Value) = "Serial#" And CStr(myDatalogSheet.Cells(12, 2).Value) = "Bin#" Then 'take time
                        myInputSheetName = myActiveWorkbook.Worksheets(mySheetIndex).name
                        Set myMaxItemRange = myDatalogSheet.Range(myMaxItemRangeName)
                        myItemCellX = Application.WorksheetFunction.CountA(myMaxItemRange) + 2
                        myTotalFailItemN = 0
                        If Not everFailed Then
                            myColumnIndex = myItemCellX + 1 'stop setting red if not myContinueOnFail
                        Else
                            myColumnIndex = 3
                        End If
                        'For myLoopItem = 3 To myItemCellX
                            With myDatalogSheet
                                If myBinN(myLoopDevice) > myMaxPassBinN Or myBin1 Then '2018/05/24
                                For myLoopItem = 3 To myItemCellX '2018/06/21
                                    TestArray() = Split(.Cells(7, myLoopItem), ".") '2018/10/26 to reduce time
                                    If Not myContinueOnFail And everFailed And UBound(TestArray) > 1 Then '20231117 tong fix
                                        If CInt(TestArray(2)) = 1 Then
                                            Exit For '2018/10/26 to reduce time
                                        End If
                                    End If
                                    myVal = .Cells(myLoopDevice, myLoopItem).value
                                    myBin = .Cells(6, myLoopItem).value
                                    If myMax(mySheetIndex, myLoopItem) <> "none" Or myMin(mySheetIndex, myLoopItem) <> "none" Then
                                        '''check if next test item have run and result is pass after value equal limit fail
                                        myNotFailHere = False
                                        If myOldSummarySheetIndex > 0 And ((myMax(mySheetIndex, myLoopItem) <> "none" And myVal = myMax(mySheetIndex, myLoopItem)) Or (myMin(mySheetIndex, myLoopItem) <> "none" And myVal = myMin(mySheetIndex, myLoopItem))) Then
                                            TestArray() = Split(.Cells(7, myLoopItem), ".")
                                            myFailItemN = CInt(TestArray(1))
                                            For i = myLoopItem + 1 To myItemCellX
                                                TestArray() = Split(.Cells(7, i), ".")
                                                If CInt(TestArray(1)) > myFailItemN Then Exit For
                                            Next i
                                            '''check if remaining sub item is fail after value equal limit fail
                                            For j = myLoopItem + 1 To i - 1
                                                If (myMax(mySheetIndex, j) <> "none" And .Cells(myLoopDevice, j).value > myMax(mySheetIndex, j)) Or (myMin(mySheetIndex, j) <> "none" And .Cells(myLoopDevice, j).value < myMin(mySheetIndex, j)) Then
                                                    myNotFailHere = True
                                                    Exit For
                                                End If
                                            Next j
                                            If Not myNotFailHere And i < myItemCellX Then
                                                For j = i To myItemCellX
                                                    TestArray() = Split(.Cells(7, j), ".")
                                                    If CInt(TestArray(1)) > (myFailItemN + 1) Then Exit For
                                                    If .Cells(myLoopDevice, j).value <> 0 And (myMax(mySheetIndex, j) <> "none" Or myMin(mySheetIndex, j) <> "none") Then
                                                        If (myMax(mySheetIndex, j) = "none" Or .Cells(myLoopDevice, j).value < myMax(mySheetIndex, j)) And (myMin(mySheetIndex, j) = "none" Or .Cells(myLoopDevice, j).value > myMin(mySheetIndex, j)) Then
                                                            myNotFailHere = True
                                                            Exit For
                                                        End If
                                                    End If
                                                Next j
                                            End If
                                        End If
'                                        If ((myMax(mySheetIndex, myLoopItem) <> "none" And ((myTesterType = 0 And myVal >= myMax(mySheetIndex, myLoopItem)) Or (myTesterType > 0 And myVal > myMax(mySheetIndex, myLoopItem)))) Or _
'                                            (myMin(mySheetIndex, myLoopItem) <> "none" And ((myTesterType = 0 And myVal <= myMin(mySheetIndex, myLoopItem)) Or (myTesterType > 0 And myVal < myMin(mySheetIndex, myLoopItem))))) And _
'                                            myNotFailHere <> True Then '2018/05/28
                                        If (((myMax(mySheetIndex, myLoopItem) <> "none" And ((myTesterType = 0 And myVal >= myMax(mySheetIndex, myLoopItem)) Or (myTesterType > 0 And myVal > myMax(mySheetIndex, myLoopItem)))) Or _
                                            (myMin(mySheetIndex, myLoopItem) <> "none" And ((myTesterType = 0 And myVal <= myMin(mySheetIndex, myLoopItem)) Or (myTesterType > 0 And myVal < myMin(mySheetIndex, myLoopItem)))))) And myVal <> "PASS" And _
                                            myNotFailHere <> True Then '2022/10/27
'                                            myBin = .Cells(6, myLoopItem).Value
                                            If myContinueOnFail Or myLoopItem < myColumnIndex Then
                                                '.Cells(myLoopDevice, myLoopItem).Font.Color = RGB(255, 0, 0)
                                                myTotalFailItemN = myTotalFailItemN + 1
                                                failItemNo(myTotalFailItemN) = myLoopItem
                                            End If
                                            If (myBin <> "") And (everFailed <> True) Then
                                                myDeviceBinN(myLoopDevice - 1) = myBin
                                                TestArray() = Split(.Cells(7, myLoopItem), ".")
                                                If UBound(TestArray) > 0 Then '20231117
                                                myFailItemN = CInt(TestArray(1))
                                                    For myColumnIndex = myLoopItem + 1 To myItemCellX
                                                        TestArray() = Split(.Cells(7, myColumnIndex), ".")
                                                        If CInt(TestArray(1)) > myFailItemN Then Exit For
                                                    Next myColumnIndex
                                                End If
                                            End If
                                            '''check if remaining sub item is fail after value equal limit fail
                                        If Not everFailed Then '2018/10/26 to reduce time
                                            myHaveAnotherFail = False
                                            For i = myLoopItem + 1 To myColumnIndex - 1
                                                If (myMax(mySheetIndex, i) <> "none" And .Cells(myLoopDevice, i).value > myMax(mySheetIndex, i)) Or (myMin(mySheetIndex, i) <> "none" And .Cells(myLoopDevice, i).value < myMin(mySheetIndex, i)) Then
                                                    myHaveAnotherFail = True
                                                    Exit For
                                                End If
                                            Next i
                                            '''check if next test item have not run after value equal limit fail
                                            myTotNotTestedNo = 0
                                            If Not myHaveAnotherFail Then
                                                For i = myColumnIndex To myItemCellX
                                                    TestArray() = Split(.Cells(7, i), ".")
                                                    If CInt(TestArray(1)) > (myFailItemN + 1) Then Exit For
                                                    If myItemFloat(.Cells(6, i).value) And .Cells(myLoopDevice, i).value = 0 Then
                                                        myTotNotTestedNo = myTotNotTestedNo + 1
                                                        If myTotNotTestedNo > 1 Then Exit For
                                                    End If
                                                Next i
                                            End If
                                        End If
                                            If (myBin <> "") And (myLoopItem < myColumnIndex Or myVal <> 0) And Not everFailed And myTotNotTestedNo < 2 Then
                                                If (myMax(mySheetIndex, myLoopItem) <> "none" And myVal = myMax(mySheetIndex, myLoopItem)) Then
                                                    myValueEqualMaxLimit(myBin) = True
                                                    If myTotValueEqualLimitDeviceNo = 0 Then
                                                        myValueEqualLimitDeviceN(myTotValueEqualLimitDeviceNo, 0) = myLoopDevice
                                                        myValueEqualLimitDeviceN(myTotValueEqualLimitDeviceNo, 1) = myBin
                                                        myTotValueEqualLimitDeviceNo = myTotValueEqualLimitDeviceNo + 1
                                                    Else
                                                        If myValueEqualLimitDeviceN(myTotValueEqualLimitDeviceNo - 1, 0) <> myLoopDevice Then
                                                            myValueEqualLimitDeviceN(myTotValueEqualLimitDeviceNo, 0) = myLoopDevice
                                                            myValueEqualLimitDeviceN(myTotValueEqualLimitDeviceNo, 1) = myBin
                                                            myTotValueEqualLimitDeviceNo = myTotValueEqualLimitDeviceNo + 1
                                                        End If
                                                    End If
                                                End If
                                                If (myMin(mySheetIndex, myLoopItem) <> "none" And myVal = myMin(mySheetIndex, myLoopItem)) Then
                                                    myValueEqualMinLimit(myBin) = True
                                                    If myTotValueEqualLimitDeviceNo = 0 Then
                                                        myValueEqualLimitDeviceN(myTotValueEqualLimitDeviceNo, 0) = myLoopDevice
                                                        myValueEqualLimitDeviceN(myTotValueEqualLimitDeviceNo, 1) = myBin
                                                        myTotValueEqualLimitDeviceNo = myTotValueEqualLimitDeviceNo + 1
                                                    Else
                                                        If myValueEqualLimitDeviceN(myTotValueEqualLimitDeviceNo - 1, 0) <> myLoopDevice Then
                                                            myValueEqualLimitDeviceN(myTotValueEqualLimitDeviceNo, 0) = myLoopDevice
                                                            myValueEqualLimitDeviceN(myTotValueEqualLimitDeviceNo, 1) = myBin
                                                            myTotValueEqualLimitDeviceNo = myTotValueEqualLimitDeviceNo + 1
                                                        End If
                                                    End If
                                                End If
                                            End If
                                            everFailed = True
                                        End If
                                    End If
                                Next myLoopItem '2018/06/21
                                Else
                                    myDeviceBinN(myLoopDevice - 1) = myBinN(myLoopDevice) '20170609
                                End If
                            End With
                        'Next
                        'If myContinueOnFail Then
                            For i = 1 To myTotalFailItemN
                                strCells = ColumnLetter(failItemNo(i)) + CStr(myLoopDevice)
                                Do While i < myTotalFailItemN
                                    If Len(strCells + "," + ColumnLetter(failItemNo(i + 1)) + CStr(myLoopDevice)) < 256 Then
                                        i = i + 1
                                        strCells = strCells + "," + ColumnLetter(failItemNo(i)) + CStr(myLoopDevice)
                                    Else
                                        Exit Do
                                    End If
                                Loop
                                
                                Range(strCells).Font.Color = 255 'Len(strCells) can not over 255
                            Next i
                        'End If
                        If Not myContinueOnFail Then
                            If myColumnIndex <= myItemCellX Then
                                'Range(myDatalogSheet.Cells(myLoopDevice, myColumnIndex), myDatalogSheet.Cells(myLoopDevice, myItemCellX)).Font.Color = 255
                                If myRemoveUntestedValue Then '2022/08/23
                                    Range(myDatalogSheet.Cells(myLoopDevice, myColumnIndex), myDatalogSheet.Cells(myLoopDevice, myItemCellX)).ClearContents
                                Else
                                    Range(myDatalogSheet.Cells(myLoopDevice, myColumnIndex), myDatalogSheet.Cells(myLoopDevice, myItemCellX)).Font.Color = 255
                                End If
                            End If
                        End If
'                    End If
                Next
            Next
            
            If myTotalDeviceNumber Then '2016/11/14
                myTotalSiteNo = 0 '2018/10/03
                myGoodSiteN = True
                'For myLoopDevice = 13 To myDeviceCellY
                For myLoopDevice = 12 To myDeviceCellY - 1 '2018/06/21
                        myBinArray(myDeviceBinN(myLoopDevice)) = myBinArray(myDeviceBinN(myLoopDevice)) + 1
                        If mySiteColumnN > 2 Then '2018/10/03
                            If InStr(CStr(Sheets(1).Cells(myLoopDevice + 1, mySiteColumnN).value), ".") Or Sheets(1).Cells(myLoopDevice + 1, mySiteColumnN).value < 1 Then
                                myGoodSiteN = False
                            ElseIf Sheets(1).Cells(myLoopDevice + 1, mySiteColumnN).value <= maxSiteN Then
                                mySiteBinArray(Sheets(1).Cells(myLoopDevice + 1, mySiteColumnN).value, myDeviceBinN(myLoopDevice)) = mySiteBinArray(Sheets(1).Cells(myLoopDevice + 1, mySiteColumnN).value, myDeviceBinN(myLoopDevice)) + 1
                                If Sheets(1).Cells(myLoopDevice + 1, mySiteColumnN).value > myTotalSiteNo Then myTotalSiteNo = Sheets(1).Cells(myLoopDevice + 1, mySiteColumnN).value
                                mySiteTotalDeviceNo(Sheets(1).Cells(myLoopDevice + 1, mySiteColumnN).value) = mySiteTotalDeviceNo(Sheets(1).Cells(myLoopDevice + 1, mySiteColumnN).value) + 1
                            End If
                        End If
                Next myLoopDevice '2018/06/21
'                    For mySheetIndex = 2 To Sheets.Count
'                    For mySheetIndex = 1 To Sheets.Count '2017/09/22
                    For mySheetIndex = myStartDatalogSheetN To myEndDatalogSheetN '2018/06/21 to reduce time
                        'Set myDatalogSheet = myActiveWorkbook.Sheets(mySheetIndex)
'                        If CStr(Sheets(mySheetIndex).Cells(12, 1).Value) = "Serial#" And CStr(Sheets(mySheetIndex).Cells(12, 2).Value) = "Bin#" Then
                            With Sheets(mySheetIndex)
                                For myLoopDevice = 13 To myDeviceCellY '2018/06/21
                                    '.Cells(myLoopDevice, 2).Value = myDeviceBinN(myLoopDevice)
                                    .Cells(myLoopDevice, 2).value = myDeviceBinN(myLoopDevice - 1)
                                Next myLoopDevice
                            End With
'                        End If
                    Next
                    'End If
                'Next
            End If
            
            If myTotalDeviceNumber Then '2016/11/14
                myTotalItemNumber = 0
                myOutputSheetName = "Summary"
                Worksheets.Add count:=1, After:=Sheets(Sheets.count)
                Worksheets(Sheets.count).name = myOutputSheetName
                Set mySummarySheet = Worksheets(myOutputSheetName)
                If myOptTotDeviceN Then
'                    mySummarySheet.Cells(2, 2).Value = myBinArray(1) + myBinArray(2) + myBinArray(3) + myBinArray(4) + myOptTotDeviceN - myTotalDeviceNumber
                    mySummarySheet.Cells(2, 2).value = myBinArray(1) + myOptTotDeviceN - myTotalDeviceNumber
'                    For myLoopItem = 2 To maxPassBinN
                    For myLoopItem = 2 To myMaxPassBinN '2018/05/24
                        mySummarySheet.Cells(2, 2).value = mySummarySheet.Cells(2, 2).value + myBinArray(myLoopItem)
                    Next myLoopItem
                    myTotalDeviceNumber = myOptTotDeviceN
                End If
                mySummarySheet.Cells(7, 1).value = "1"
                mySummarySheet.Cells(7, 2).value = myBinArray(1)
                mySummarySheet.Cells(7, 3).value = myBinArray(1) / myTotalDeviceNumber * 100 & "%"
                mySummarySheet.Cells(7, 4).value = "All Pass"
'                For myLoopItem = 2 To maxPassBinN
                For myLoopItem = 2 To myMaxPassBinN '2018/05/24
                    With mySummarySheet
                        .Cells(8 + myLoopItem - 2, 1).value = myLoopItem
                        .Cells(8 + myLoopItem - 2, 2).value = myBinArray(myLoopItem)
                        .Cells(8 + myLoopItem - 2, 3).value = myBinArray(myLoopItem) / myTotalDeviceNumber * 100 & "%"
                        .Cells(8 + myLoopItem - 2, 4).value = "Bin " + CStr(myLoopItem)
                    End With
                Next myLoopItem
'                For mySheetIndex = 1 To Sheets.Count
                For mySheetIndex = myStartDatalogSheetN To myEndDatalogSheetN '2018/06/21 to reduce time
                    Set myDatalogSheet = myActiveWorkbook.Sheets(mySheetIndex)
'                    If CStr(myDatalogSheet.Cells(12, 1).Value) = "Serial#" And CStr(myDatalogSheet.Cells(12, 2).Value) = "Bin#" Then
                        myInputSheetName = myActiveWorkbook.Worksheets(mySheetIndex).name
                        Set myMaxItemRange = myDatalogSheet.Range(myMaxItemRangeName)
                        myItemCellX = Application.WorksheetFunction.CountA(myMaxItemRange) + 2
'                        For myLoopItem = 3 To myItemCellX + 3
                        For myLoopItem = 3 To myItemCellX
                            With myDatalogSheet
                                myBin = .Cells(6, myLoopItem).value
                                myItemName = .Cells(8, myLoopItem).value
                                With mySummarySheet
                                    .Cells(myBin + 6, 1).value = myBin
                                    .Cells(myBin + 6, 2).value = myBinArray(myBin)
                                    .Cells(myBin + 6, 3).value = myBinArray(myBin) / myTotalDeviceNumber * 100 & "%"
                                    .Cells(myBin + 6, 4).value = myItemName
                                    If myItemFloat(myBin) Then
                                        If myValueEqualMaxLimit(myBin) And myValueEqualMinLimit(myBin) Then
                                            .Cells(myBin + 6, 5).value = "Bad Max & Min Limit!"
                                            .Cells(myBin + 6, 5).Font.Color = 255
                                        Else
                                            If myValueEqualMaxLimit(myBin) Then
                                                .Cells(myBin + 6, 5).value = "Bad Max Limit!"
                                                .Cells(myBin + 6, 5).Font.Color = 255
                                            End If
                                            If myValueEqualMinLimit(myBin) Then
                                                .Cells(myBin + 6, 5).value = "Bad Min Limit!"
                                                .Cells(myBin + 6, 5).Font.Color = 255
                                            End If
                                        End If
                                    End If
                                End With
                            End With
                        Next
                        myTotalItemNumber = myTotalItemNumber + Application.WorksheetFunction.CountA(myMaxItemRange)
'                    End If
                Next
                mySummarySheet.Activate
                mySummarySheet.Cells(6, 1).value = "Bin" '20170609
                mySummarySheet.Cells(6, 2).value = "Count"
                mySummarySheet.Cells(6, 3).value = "%"
                mySummarySheet.Cells(6, 4).value = "Definition"
                mySummarySheet.Cells(6, 5).value = "Note"
                If myTotalSiteNo > 0 And myGoodSiteN Then '2018/10/03
                    For i = 1 To myTotalSiteNo
                        mySummarySheet.Cells(5, 4 + 2 * i).value = "Total"
                        mySummarySheet.Cells(5, 5 + 2 * i).value = mySiteTotalDeviceNo(i)
                        mySummarySheet.Cells(6, 4 + 2 * i).value = "Site " + CStr(i)
                        mySummarySheet.Cells(6, 5 + 2 * i).value = "%"
                        mySummarySheet.Cells(7, 4 + 2 * i).value = mySiteBinArray(i, 1)
                        If mySiteTotalDeviceNo(i) > 0 Then '2018/10/08
                            mySummarySheet.Cells(7, 5 + 2 * i).value = mySiteBinArray(i, 1) / mySiteTotalDeviceNo(i) * 100 & "%"
                            For myLoopItem = 2 To myMaxPassBinN + myTotalItemNumber
                                mySummarySheet.Cells(8 + myLoopItem - 2, 4 + 2 * i).value = mySiteBinArray(i, myLoopItem)
                                mySummarySheet.Cells(8 + myLoopItem - 2, 5 + 2 * i).value = mySiteBinArray(i, myLoopItem) / mySiteTotalDeviceNo(i) * 100 & "%"
                            Next myLoopItem
                        Else
                            mySummarySheet.Cells(7, 5 + 2 * i).value = "0%"
                            For myLoopItem = 2 To myMaxPassBinN + myTotalItemNumber
                                mySummarySheet.Cells(8 + myLoopItem - 2, 4 + 2 * i).value = mySiteBinArray(i, myLoopItem)
                                mySummarySheet.Cells(8 + myLoopItem - 2, 5 + 2 * i).value = "0%"
                            Next myLoopItem
                        End If
                    Next i
                End If
'                Range("A6").Select
'                Selection.AutoFilter
                rows("7:7").AutoFilter '2018/10/09 zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz
                mySummarySheet.Cells(1, 1).value = "Total"
                mySummarySheet.Cells(1, 2).value = myTotalDeviceNumber
                mySummarySheet.Cells(2, 1).value = "Pass"
                If myOptTotDeviceN = 0 Then
'                    mySummarySheet.Cells(2, 2).Value = myBinArray(1) + myBinArray(2) + myBinArray(3) + myBinArray(4)
                    mySummarySheet.Cells(2, 2).value = myBinArray(1)
'                    For myLoopItem = 2 To maxPassBinN
                    For myLoopItem = 2 To myMaxPassBinN '2018/05/24
                        mySummarySheet.Cells(2, 2).value = mySummarySheet.Cells(2, 2).value + myBinArray(myLoopItem)
                    Next myLoopItem
                End If
                mySummarySheet.Cells(3, 1).value = "Fail"
                mySummarySheet.Cells(3, 2).value = mySummarySheet.Cells(1, 2).value - mySummarySheet.Cells(2, 2).value
                mySummarySheet.Cells(4, 1).value = "Yield"
                mySummarySheet.Cells(4, 2).value = mySummarySheet.Cells(2, 2).value / myTotalDeviceNumber * 100 & "%"
                Cells.Select
                Selection.Columns.AutoFit
                Range("B7").Select
                ActiveWindow.FreezePanes = True
                
                If myAskRtPass Or myAskContinueOnFail Then
'                    For mySheetIndex = 1 To Sheets.Count
                    For mySheetIndex = myStartDatalogSheetN To myEndDatalogSheetN '2018/06/21 to reduce time
                        Set myDatalogSheet = myActiveWorkbook.Sheets(mySheetIndex)
'                        If CStr(myDatalogSheet.Cells(12, 1).Value) = "Serial#" And CStr(myDatalogSheet.Cells(12, 2).Value) = "Bin#" Then
                            myItemCellX = GetTotalItemNumber(CInt(mySheetIndex)) + 2
                            For myLoopItem = 3 To myItemCellX
                                myBin = myDatalogSheet.Cells(6, myLoopItem).value
                                If myItemFloat(myBin) And (myValueEqualMaxLimit(myBin) Or myValueEqualMinLimit(myBin)) Then
                                    MsgBox "BIN " + CStr(myBin) + " """ + CStr(myDatalogSheet.Cells(8, myLoopItem).value) + """" + " has equal fail, please check limit!!"
                                End If
                            Next myLoopItem
'                        End If
                    Next mySheetIndex
                Else
                    If myPassDeviceN > 1 Then
                        For mySheetIndex = 1 To Sheets.count
                            If checkSheetIsDatalog(CInt(mySheetIndex)) Then
                                Sheets(mySheetIndex).rows(CStr(12 + myPassDeviceN)).Cut
                                Sheets(mySheetIndex).rows(CStr(12 + 1)).Insert Shift:=xlDown
                            End If
                        Next mySheetIndex
                    End If
                End If
                If myTotValueEqualLimitDeviceNo > 0 Then
                    For mySheetIndex = 1 To Sheets.count
                        If checkSheetIsDatalog(CInt(mySheetIndex)) Then
                            j = 0
                            For i = 1 To myTotValueEqualLimitDeviceNo
                                If myItemFloat(myValueEqualLimitDeviceN(i - 1, 1)) Then
                                    If (myAskRtPass Or myAskContinueOnFail Or myPassDeviceN = 0) And myOldSummarySheetIndex = 0 Then
                                        Sheets(mySheetIndex).Cells(myValueEqualLimitDeviceN(i - 1, 0), 2).Font.Color = 255
                                        If myValueEqualLimitDeviceN(i - 1, 0) > (12 + i) Then
                                            j = j + 1
                                            Sheets(mySheetIndex).rows(CStr(myValueEqualLimitDeviceN(i - 1, 0))).Cut
                                            Sheets(mySheetIndex).rows(CStr(12 + j)).Insert Shift:=xlDown
'                                            Sheets(mySheetIndex).Cells(12 + j, 2).Font.Color = 255
                                        End If
                                    Else
                                        If myValueEqualLimitDeviceN(i - 1, 0) < (12 + myPassDeviceN) Then
                                            myValueEqualLimitDeviceN(i - 1, 0) = myValueEqualLimitDeviceN(i - 1, 0) + 1
                                        Else
                                            If myValueEqualLimitDeviceN(i - 1, 0) = (12 + myPassDeviceN) Then myValueEqualLimitDeviceN(i - 1, 0) = 13
                                        End If
                                        Sheets(mySheetIndex).Cells(myValueEqualLimitDeviceN(i - 1, 0), 2).Font.Color = 255
                                        If myValueEqualLimitDeviceN(i - 1, 0) > (13 + i) Then
                                            j = j + 1
                                            Sheets(mySheetIndex).rows(CStr(myValueEqualLimitDeviceN(i - 1, 0))).Cut
                                            Sheets(mySheetIndex).rows(CStr(13 + j)).Insert Shift:=xlDown
'                                            Sheets(mySheetIndex).Cells(13 + j, 2).Font.Color = 255
                                        End If
                                    End If
                                End If
                            Next i
                        End If
                    Next mySheetIndex
                End If
            End If
        
        
        If myOldSummarySheetIndex > 0 Then
            For i = 7 To mySummarySheet.UsedRange.rows.count
                mySummarySheet.Cells(i, 2).value = Sheets(myOldSummarySheetIndex).Cells(i, 2).value + mySummarySheet.Cells(i, 2).value
            Next i
            For i = 13 To Sheets(1).UsedRange.rows.count
                mySummarySheet.Cells(Sheets(1).Cells(i, 2).value + 6, 2).value = mySummarySheet.Cells(Sheets(1).Cells(i, 2).value + 6, 2).value - 1
            Next i
            myTotPassNo = Sheets(myOldSummarySheetIndex).Cells(1 + 6, 2).value
'            For myLoopItem = 2 To maxPassBinN
            For myLoopItem = 2 To myMaxPassBinN '2018/05/24
                myTotPassNo = myTotPassNo + Sheets(myOldSummarySheetIndex).Cells(myLoopItem + 6, 2).value
            Next myLoopItem
            mySummarySheet.Cells(1, 2).value = Sheets(myOldSummarySheetIndex).Cells(1, 2).value
            mySummarySheet.Cells(2, 2).value = mySummarySheet.Cells(2, 2).value + Sheets(myOldSummarySheetIndex).Cells(2, 2).value - myTotPassNo
            mySummarySheet.Cells(3, 2).value = mySummarySheet.Cells(1, 2).value - mySummarySheet.Cells(2, 2).value
            mySummarySheet.Cells(4, 2).value = mySummarySheet.Cells(2, 2).value / mySummarySheet.Cells(1, 2).value * 100 & "%"
            If mySummarySheet.Cells(2, 2) > 0 Then mySummarySheet.Cells(4, 2).NumberFormatLocal = "0.00%"
            For i = 7 To mySummarySheet.UsedRange.rows.count
                mySummarySheet.Cells(i, 3).value = mySummarySheet.Cells(i, 2).value / mySummarySheet.Cells(1, 2).value * 100 & "%"
                If mySummarySheet.Cells(i, 3) > 0.001 Then mySummarySheet.Cells(i, 3).NumberFormatLocal = "0.00%"
            Next i
            mySummarySheet.Cells.Columns.AutoFit
            mySummarySheet.Move After:=Sheets(myOldSummarySheetIndex)
            Application.DisplayAlerts = False
            Sheets(myOldSummarySheetIndex).Delete
            Application.DisplayAlerts = True
        End If
'======================================================================================================================================================================================================================================================================================================================================================================================
'        ' 存檔案
'        ActiveWorkbook.Save
'        ' 關閉 workbook
'        ActiveWorkbook.Close SaveChanges:=False
        On Error Resume Next
        Dim xlSheet As Object
        Set xlSheet = ActiveWorkbook.Sheets("Summary")
         On Error GoTo Final_nosave
        ' 篩選資料
        xlSheet.AutoFilter.Sort.SortFields.Clear
        xlSheet.AutoFilter.Sort.SortFields.Add key:= _
        xlSheet.Range("B7"), SortOn:=xlSortOnValues, Order:=xlDescending, DataOption:= _
        xlSortNormal
        With xlSheet.AutoFilter.Sort
            .Header = xlYes
            .MatchCase = False
            .Orientation = xlTopToBottom
            .SortMethod = xlPinYin
            .Apply
        End With
        xlSheet.AutoFilterMode = False
        Dim ws1 As Worksheet
        Dim wsSummary As Worksheet
        Set ws1 = ActiveWorkbook.Sheets(1)
        Set wsSummary = ActiveWorkbook.Sheets("Summary")
        
        ws1.Range("A6:B6").Copy wsSummary.Range("C1:D1")
        ws1.Range("A5:B5").Copy wsSummary.Range("C2:D2")
        ws1.Range("A3").Copy wsSummary.Range("C3")
        
        Dim notzeroline As Long
        notzeroline = 0
        
        Dim fileNamehyplink As String
        If write_summary = True Then
             Dim hylinename As String
            ' hylinename = folderPath
            hylinename = Left(folderPath, Len(folderPath) - 4) & ".xlsx"
            hylinename = ReplacePath(hylinename)
            wsSummary.Range("D3").value = hylinename
            Set cell = xlSheet.Range("D3") '路徑到時要改成 在網路上
            fileNamehyplink = GetFileName(cell.value)
            cell.Hyperlinks.Add Anchor:=cell, Address:=cell.value, SubAddress:="", TextToDisplay:=fileNamehyplink
            hylinename = Left(folderPath, Len(folderPath) - 4) & "_EQCFAILDATA.xlsx"
            If Dir(hylinename) <> "" Then
                hylinename = ReplacePath(hylinename)
                wsSummary.Range("D4").value = hylinename
                Set cell2 = xlSheet.Range("D4") '路徑到時要改成 在網路上
                fileNamehyplink = GetFileName(cell2.value)
                cell2.Hyperlinks.Add Anchor:=cell2, Address:=cell2.value, SubAddress:="", TextToDisplay:=fileNamehyplink
            End If
            lastRow = xlSheet.Cells(xlSheet.rows.count, "B").End(xlUp).row
            lastCol = xlSheet.Cells(6, xlSheet.Columns.count).End(xlToLeft).Column '總欄位
            For i = 8 To lastRow '從8行 20230808
                If xlSheet.Cells(i, "B").value = 0 Then
                    notzeroline = i
                    Exit For
                End If
            Next i
            For i = 7 To lastRow
                If xlSheet.Cells(i, "D").value = "All Pass" Then
                    xlSheet.Cells(i, "D").Interior.Color = RGB(0, 255, 0) '綠色
                    Exit For
                End If
             Next i
            xlSheet.Range(xlSheet.Cells(1, 1), xlSheet.Cells(notzeroline - 1, lastCol)).Copy
            Application.Wait Now + TimeValue("0:00:05") ' 等待5秒鐘
            DoEvents ' 等待剪貼簿內容被更新
            xlSheet.Range(xlSheet.Cells(1, 1), xlSheet.Cells(notzeroline - 1, lastCol)).Copy
            Application.Wait Now + TimeValue("0:00:05") ' 等待5秒鐘
            DoEvents ' 等待剪貼簿內容被更新
            Dim index As Integer
'            index = InStrRev(folderPath, "\")
'            If index > 0 Then
'                Dim folderPath_1 As String
'                folderPath_1 = Left(folderPath, index - 1)
'            End If
            'If wsSummary.Range("B4").value < 1 Then'20230808
            If InStr(1, summary_name, "EQC", vbTextCompare) > 0 Then
                If Len(wsSummary.Range("D4").value) > 0 Then
                    OpenFileAndPasteData summary_patch & "\" & summary_name & "Summary" & ".xlsx"
                Else
                    OpenFileAndPasteData summary_patch & "\" & summary_name & "Summary_NO_ONLINE" & ".xlsx" '20241210
                End If
            Else
                OpenFileAndPasteData summary_patch & "\" & summary_name & "Summary" & ".xlsx"
            End If
        End If
        
       
     ' 另存為新檔案
        Dim myNewWorkbookName As String
        Dim xlxsfile As String
        
        myNewWorkbookName = Left(myFileName, Len(myFileName) - 4)
        xlxsfile = folderPath & "\" & myNewWorkbookName & ".xlsx"
        If onlyonecsv = False Then
            If Dir(xlxsfile) <> "" Then
                Kill xlxsfile
            End If
            ActiveWorkbook.SaveAs folderPath & "\" & myNewWorkbookName & ".xlsx", FileFormat:=51
        Else
            folderPath = Left(folderPath, Len(folderPath) - 4) & ".xlsx"
            If Dir(folderPath) <> "" Then
                 Kill folderPath
            End If
            ActiveWorkbook.SaveAs folderPath, FileFormat:=51
        End If
Final_nosave:
        ' 關閉 workbook
        ActiveWorkbook.Close SaveChanges:=False
        'SortSummarySheetDescending xlsx_filename nouse
        
        If onlyonecsv = False Then
            xlxsfile = folderPath & "\" & "csv\" & myFileName
            If Dir(xlxsfile) <> "" Then
                Kill xlxsfile
            End If
        End If
'
        If onlyonecsv = False Then
            '移動 csv 檔案至 csv 資料夾
            Name folderPath & "\" & myFileName As folderPath & "\" & "csv\" & myFileName
        End If
        
        If onlyonecsv = True Then
            Exit Do
        End If
        myFileName = Dir(folderPath & "\*.csv")
    Loop
    
'    Workbooks(1).Activate
Final:
    Set myWorkbook = Nothing
    Set myActiveWorkbook = Nothing
    Set myDatalogSheet = Nothing
    Set mySummarySheet = Nothing
    Call setAppl(0) '2020/06/01
End Sub
Function SortSummarySheetDescending(filePath As String) '將summary 由大到最小FAIL 排序
    Dim xlApp As Object
    Set xlApp = CreateObject("Excel.Application")
    Dim xlBook As Object
    
    On Error GoTo exitSortSummarySheetDescending
    Set xlBook = xlApp.Workbooks.Open(filePath)

    Dim xlSheet As Object
    Set xlSheet = xlBook.Sheets("Summary")
    
    ' 篩選資料
    xlSheet.AutoFilter.Sort.SortFields.Clear
    xlSheet.AutoFilter.Sort.SortFields.Add key:= _
        xlSheet.Range("B7"), SortOn:=xlSortOnValues, Order:=xlDescending, DataOption:= _
        xlSortNormal
    With xlSheet.AutoFilter.Sort
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With
    xlSheet.AutoFilterMode = False
    ' 儲存並關閉檔案
    xlBook.Save
    xlBook.Close SaveChanges:=False
    xlApp.Quit
    Set xlSheet = Nothing
    Set xlBook = Nothing
exitSortSummarySheetDescending:
    Set xlApp = Nothing
End Function
Public Function checkSheetsHaveDatalog(Optional myBookName As Variant = "") As Integer '2020/08/20

    Dim i As Integer
    
'    Call convertCta8280 '2018/05/23
    Call convertOtherDatalog '2022/04/20
    For i = 1 To Sheets.count
        If checkSheetIsDatalog(CInt(i), myBookName) Then
            If i > 1 Then Call FillEmptyItemName(myBookName, i)
            Exit For
        End If
    Next i
    If i > Sheets.count Then
'        checkSheetsHaveDatalog = False
        checkSheetsHaveDatalog = 0 '2020/08/20
    Else
'        checkSheetsHaveDatalog = True
        checkSheetsHaveDatalog = i '2020/08/20
    End If

End Function
Public Function convertOtherDatalog() As Boolean '2022/04/20

    Dim TestArray() As String, myColumnN As Integer, myColumnN1 As Integer, myColumnN2 As Integer
    
    convertOtherDatalog = False
    
    myBinN = GetMyMaxPassBinN
    
    myTesterType = 0
    For i = 1 To Sheets.count
'        If InStr(Sheets(1).Cells(1, 2).Value, "ETS ") = 1 Then '2020/04/14
        If InStr(Sheets(1).Cells(1, 2).value, "ETS ") = 1 Or InStr(Sheets(1).Cells(39, 2).value, "ETS Site") > 0 Then '2022/10/27
            myTesterType = 3 'EAGLE
            Exit For
        End If
        If InStr(Sheets(1).Cells(6, 1).value, ".PRG Line#    :") = 1 Then '2022/04/20
            myTesterType = 4 'YS(S100、S50、V50)
            Exit For
        End If
'        If Sheets(i).Name = "Data11" Or Sheets(i).Name = "DUT_DATA" Then
'            If Sheets(i).Name = "Data11" Then
'        If Sheets(i).Name = "Data11" Or Sheets(i).Name = "QAData" Or Sheets(i).Name = "DUT_DATA" Then '2018/05/25
'        If Sheets(i).Name = "Data11" Or Sheets(i).Name = "QAData" Or Sheets(i).Name = "DUT_DATA" Or Sheets(i).Name = "QA_Data" Then '2018/05/29
'            If Sheets(i).Name = "Data11" Or Sheets(i).Name = "QAData" Then '2018/05/25
        If Sheets(i).name = "Data11" Or Sheets(i).name = "QAData" Or Sheets(i).name = "DataInfo" Or Sheets(i).name = "DUT_DATA" Or Sheets(i).name = "QA_Data" Then '2021/08/06
            If Sheets(i).name = "Data11" Or Sheets(i).name = "QAData" Or Sheets(i).name = "DataInfo" Then '2021/08/06
                myTesterType = 1 'CTA8280
            Else
                myTesterType = 2 'STS8200
            End If
            If i > 1 Then
                Sheets(i).Move Before:=Sheets(1)
            End If
            Exit For
        End If
    Next i
    Dim myTotalDeviceNumber As Long
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    If i > Sheets.count Then
        myTotalDeviceNumber = GetTotalDeviceNumber
        myRowN = 12
        myStartRowN = 13
        mySourceSheetIndex = 1
        myFindedDataN = 0
        For j = 1 To myTotalDeviceNumber
            'WriteNumberedTimeToFile j 'tongtongtong
            myRowN = myRowN + 1
            myStr = CStr(Sheets(mySourceSheetIndex).Cells(myRowN, 1).value)
            If j > 97100 Then
            j = j
            ElseIf j > 96500 Then
            j = j
            ElseIf j > 96180 Then
            j = j
            End If
            
            If myStr = "" Then
                j = j - 1
            Else
                If myStr = "[Data]" Then
                    myTesterType = 1
                    myStartRowN = myRowN + 1
                    myFindedDataN = 1
                Else '20231026 greg fix start
                    If myStr = "[QAData]" Then
                        If myFindedDataN = 1 Then
                            myStopRowN = myRowN - 1
                            Sheets.Add Before:=Sheets(mySourceSheetIndex)
                            mySourceSheetIndex = mySourceSheetIndex + 1
'                            ActiveSheet.Name = "Data11"
'                            Sheets(mySourceSheetIndex).Name = "sum"
''                            Sheets(mySourceSheetIndex).Rows(CStr(myStartRowN) + ":" + CStr(myStopRowN)).Copy
'                            Sheets(mySourceSheetIndex).Rows(CStr(myStartRowN - 1) + ":" + CStr(myStopRowN)).Copy '2019/12/11
''                            Sheets(mySourceSheetIndex - 1).Rows("1:1").Insert Shift:=xlDown '資料量太多時會彈出警告，excel無法以可用的資源完成工作
'                            Sheets(mySourceSheetIndex - 1).Paste '2023/10/24
'                            Sheets(mySourceSheetIndex - 1).Cells(1, 1) = "" '2019/12/11
                            ActiveSheet.name = "sum" '2023/10/26
                            Sheets(mySourceSheetIndex).name = "Data11"
                            mySumEndRowN = myStartRowN - 2
                            Sheets(mySourceSheetIndex).rows("1:" + CStr(mySumEndRowN)).Copy
                            Sheets(mySourceSheetIndex - 1).Paste
                        End If
                        myStartRowN = myRowN + 1
                        myFindedDataN = 2
                    Else
                        If myStr = "SITE_NUM" Then
                            myTesterType = 2
                            myStartRowN = myRowN
                            myFindedDataN = 1
                        Else
                            If j = myTotalDeviceNumber Then
                                myStopRowN = myRowN
                                If myFindedDataN = 1 Then
                                    Sheets.Add Before:=Sheets(mySourceSheetIndex)
                                    mySourceSheetIndex = mySourceSheetIndex + 1
                                    If myTesterType = 1 Then
'                                        ActiveSheet.Name = "Data11"
'                                        Sheets(mySourceSheetIndex).Name = "sum"
'                                        Sheets(mySourceSheetIndex).Rows(CStr(myStartRowN - 1) + ":" + CStr(myStopRowN)).Copy '2019/12/24
''                                        Sheets(mySourceSheetIndex - 1).Rows("1:1").Insert Shift:=xlDown '資料量太多時會彈出警告，excel無法以可用的資源完成工作
'                                        Sheets(mySourceSheetIndex - 1).Paste '2023/10/24
'                                        Sheets(mySourceSheetIndex - 1).Cells(1, 1) = ""
                                        ActiveSheet.name = "sum" '2023/10/26
                                        Sheets(mySourceSheetIndex).name = "Data11"
                                        mySumEndRowN = myStartRowN - 2
                                        Sheets(mySourceSheetIndex).rows("1:" + CStr(mySumEndRowN)).Copy
                                        Sheets(mySourceSheetIndex - 1).Paste
                                        Sheets("Data11").Move Before:=Sheets(1)
                                        Sheets("Data11").rows("1:" + CStr(mySumEndRowN)).Delete Shift:=xlUp
                                        Sheets("Data11").Cells(1, 1) = ""
                                    Else
                                        ActiveSheet.name = "DUT_DATA"
                                        Sheets(mySourceSheetIndex).name = "Summary information"
                                        Sheets(mySourceSheetIndex).rows(CStr(myStartRowN) + ":" + CStr(myStopRowN)).Copy '2019/12/24
'                                        Sheets(mySourceSheetIndex - 1).Rows("1:1").Insert Shift:=xlDown '資料量太多時會彈出警告，excel無法以可用的資源完成工作
                                        Sheets(mySourceSheetIndex - 1).Paste '2023/10/24
                                    End If
'                                    Sheets(mySourceSheetIndex).Rows(CStr(myStartRowN) + ":" + CStr(myStopRowN)).Copy
'                                    Sheets(mySourceSheetIndex - 1).Rows("1:1").Insert Shift:=xlDown
                                Else
                                    If myFindedDataN = 2 Then
                                        Sheets.Add After:=Sheets(mySourceSheetIndex)
                                        mySourceSheetIndex = mySourceSheetIndex
                                        ActiveSheet.name = "QAData"
'                                        Sheets(mySourceSheetIndex).Rows(CStr(myStartRowN) + ":" + CStr(myStopRowN)).Copy
                                        Sheets(mySourceSheetIndex).rows(CStr(myStartRowN - 1) + ":" + CStr(myStopRowN)).Copy '2019/12/24
'                                        Sheets(mySourceSheetIndex + 1).Rows("1:1").Insert Shift:=xlDown '資料量太多時會彈出警告，excel無法以可用的資源完成工作
                                        Sheets(mySourceSheetIndex + 1).Paste '2023/10/24
                                        Sheets(mySourceSheetIndex + 1).Cells(1, 1) = "" '2019/12/24
                                        
                                        myColumnN = 0 '2022/02/17
                                        For k = 1 To 128
                                            If Sheets(mySourceSheetIndex + 1).Cells(2, k).value = "Data_Cnt" Or Sheets(mySourceSheetIndex + 1).Cells(2, k).value = "TEST_NUM" Then
                                                myColumnN = k
                                                Exit For
                                            End If
                                        Next k
                                        mySerial_NoColumnN = 0
'                                        myPart_NoColumnN = 0
                                        myDut_NoColumnN = 0
                                        mySite_NoColumnN = 0
                                        If (myColumnN > 0 And myColumnN < 129) Then
                                            For k = 1 To 128
                                                If Sheets(mySourceSheetIndex + 1).Cells(2, k).value = "Serial_No" Then
                                                    mySerial_NoColumnN = k
                                                    Exit For
                                                End If
                                            Next k
                                            For k = 1 To 128
                                                If Sheets(mySourceSheetIndex + 1).Cells(2, k).value = "Dut_No" Then
                                                    myDut_NoColumnN = k
                                                    Exit For
                                                End If
                                            Next k
                                            For k = 1 To 128
                                                If Sheets(mySourceSheetIndex + 1).Cells(2, k).value = "Site_No" Then
                                                    mySite_NoColumnN = k
                                                    Exit For
                                                End If
                                            Next k
                                        End If
                                        If (mySerial_NoColumnN > 0 And mySerial_NoColumnN < 129) And (myDut_NoColumnN > 0 And myDut_NoColumnN < 129) And (mySite_NoColumnN > 0 And mySite_NoColumnN < 129) Then
                                            If Application.Version >= 12# And Application.ActiveWorkbook.FileFormat <> 56 And Application.ActiveWorkbook.FileFormat <> 43 And Application.ActiveWorkbook.FileFormat <> 39 Then
                                                myMaxDeviceRangeName = "A6:A" + CStr(maxRowN)
                                            Else
                                                myMaxDeviceRangeName = "A6:A65536"
                                            End If
                                            myData11TotalDeviceNumber = Application.WorksheetFunction.CountA(Sheets(mySourceSheetIndex - 1).Range(myMaxDeviceRangeName))
                                            myStartSearchRowN = 5 + myData11TotalDeviceNumber
                                            Call setAppl '2023/10/24
                                            For k = 5 + myStopRowN - myStartRowN - 3 To 6 Step -1
                                                If Sheets(mySourceSheetIndex + 1).Cells(k, myColumnN + 1).value = "" Then
                                                    rows(k).Delete Shift:=xlUp 'remove untested online EQC data
                                                Else
                                                    For n = myStartSearchRowN To 6 Step -1
                                                        With Sheets(mySourceSheetIndex - 1)
                                                            If .Cells(n, myDut_NoColumnN).value = Cells(k, myDut_NoColumnN).value And .Cells(n, mySite_NoColumnN).value = Cells(k, mySite_NoColumnN).value Then
                                                                Cells(k, mySerial_NoColumnN).value = .Cells(n, mySerial_NoColumnN).value 'replace online EQC serial number with FT serial number
                                                                myStartSearchRowN = n - 1
                                                                Exit For '2023/10/24 to reduce time
                                                            End If
                                                        End With
                                                    Next n
                                                End If
                                            Next k
                                            Call setAppl(0) '2023/10/24
                                        End If
                                        
                                        Sheets(mySourceSheetIndex).rows(CStr(myStartRowN - 1) + ":" + CStr(myStopRowN)).Clear '2023/10/26
                                        Sheets("Data11").Move Before:=Sheets(1)
                                        Sheets("Data11").rows("1:" + CStr(mySumEndRowN)).Delete Shift:=xlUp
                                        Sheets("Data11").Cells(1, 1) = ""
                                        
                                    End If
                                End If
                            End If
                        End If
                    End If '20231026 greg fix end
                End If
            End If
        Next j
    End If
    Application.ScreenUpdating = True
Application.Calculation = xlCalculationAutomatic

    myColumnN = 0
    myFindedRowN = 1 '2019/12/24
    For j = 1 To 16
'        If Sheets(1).Cells(1, j).Value = "Data_Num" Or Sheets(1).Cells(1, j).Value = "TEST_NUM" Then
        If Sheets(1).Cells(1, j).value = "Data_Num" Or Sheets(1).Cells(1, j).value = "Data_Cnt" Or Sheets(1).Cells(1, j).value = "TEST_NUM" Then '2018/08/08
'        If Sheets(1).Cells(2, j).Value = "Data_Num" Or Sheets(1).Cells(2, j).Value = "Data_Cnt" Or Sheets(1).Cells(2, j).Value = "TEST_NUM" Then '2019/12/11
            myColumnN = j
            If Sheets(1).Cells(1, j).value = "Data_Num" Or Sheets(1).Cells(1, j).value = "Data_Cnt" Then myTesterType = 1 '2018/12/18
'            If Sheets(1).Cells(2, j).Value = "Data_Num" Or Sheets(1).Cells(2, j).Value = "Data_Cnt" Then myTesterType = 1 '2019/12/11
            Exit For
        End If
    Next j
    If myColumnN = 0 Then '2019/12/24
        myFindedRowN = 2
        For j = 1 To 16
            If Sheets(1).Cells(2, j).value = "Data_Num" Or Sheets(1).Cells(2, j).value = "Data_Cnt" Or Sheets(1).Cells(2, j).value = "TEST_NUM" Then
                myColumnN = j
                If Sheets(1).Cells(2, j).value = "Data_Num" Or Sheets(1).Cells(2, j).value = "Data_Cnt" Then myTesterType = 1
                Exit For
            End If
        Next j
    End If
    
    If myColumnN > 0 Then
        convertOtherDatalog = True
        Sheets(1).Activate
        
        ActiveWorkbook.Styles("Normal").Font.name = "新細明體"
        ActiveWorkbook.Styles("Normal").Font.Size = 12
        
        If myTesterType = 2 Then '2018/06/20
            myExtraRowN = 4
            For i = myExtraRowN + 1 To 128
                If Replace(CStr(Cells(i, 1).value), " ", "") <> "" Then
                    If Replace(CStr(Cells(i, 2).value), " ", "") = "" Then
                        myExtraRowN = i
                    End If
                Else
                    Exit For
                End If
            Next i
            If myExtraRowN > 4 Then
                rows("5:" + CStr(myExtraRowN)).Delete Shift:=xlUp
            End If
            
            myStartRowN = 6
            myColumnN2 = 0
            For k = 1 To 16
                If Sheets(1).Cells(1, k).value = "TEST_NUM" Then
                    myColumnN2 = k
                    Exit For
                End If
            Next k
            If myColumnN2 > 0 Then
                For i = 1 To myColumnN2
                    myStr = ColumnLetter(CInt(i)) + CStr(myStartRowN) + ":" + ColumnLetter(CInt(i)) + CStr(Sheets(1).UsedRange.rows.count)
                    Range(myStr).TextToColumns 'Destination:=Range("C18") , DataType:=xlDelimited, _
'                        TextQualifier:=xlDoubleQuote, ConsecutiveDelimiter:=False, Tab:=True, _
'                        Semicolon:=False, Comma:=False, Space:=False, Other:=False, FieldInfo _
'                        :=Array(1, 1), TrailingMinusNumbers:=True
                Next i
            End If
        End If
        
        If myTesterType = 2 And Sheets(1).name <> "QA_Data" Then '2018/05/29
            myDataHaveQA = False
            For i = 2 To Sheets.count '2018//06/22
                If Sheets(i).name = "Summary information" Then
                    For j = 1 To 64
                        If InStr(CStr(Sheets(i).Cells(j, 1).value), "QATotal:") Then
                            myDataHaveQA = True
                            Exit For
                        End If
                    Next j
                    Exit For
                End If
            Next i
            myColumnN1 = 0
            For k = 1 To 16
                If Sheets(1).Cells(1, k).value = "PASSFG" Then
                    myColumnN1 = k
                    Exit For
                End If
            Next k
            If Not myDataHaveQA Then
                If myColumnN1 > 0 And myColumnN2 > 0 Then
                    With Sheets(1)
                        myTotalSubTestNo = .UsedRange.Columns.count - myColumnN2 '2018/06/21
                        For k = myStartRowN To .UsedRange.rows.count
                            If UCase(CStr(.Cells(k, myColumnN1).value)) = "TRUE" Then
'                                If (myColumnN2 + .Cells(k, myColumnN2).Value) < .UsedRange.Columns.Count Then 'value operation take time
                                If .Cells(k, myColumnN2).value < myTotalSubTestNo Then '2018/06/21
                                    myDataHaveQA = True
                                    Exit For
                                End If
                            End If
                        Next k
                    End With
                End If
            End If
            
            'For i = 2 To Sheets.Count
            '    If Sheets(i).Name = "Summary information" Then
            '        For j = 1 To 64
'                        If InStr(CStr(Sheets(i).Cells(j, 1).Value), "QATotal:") Then
            '            If InStr(CStr(Sheets(i).Cells(j, 1).Value), "QATotal:") Or myDataHaveQA Then '2018/06/20
                        If myDataHaveQA Then '2018/06/22
                            Sheets.Add After:=Sheets(i)
                            ActiveSheet.name = "QA_Data"
                            Sheets(1).Activate
            '                myColumnN1 = 0
            '                myColumnN2 = 0
            '                For k = 1 To 16
            '                    If Sheets(1).Cells(1, k).Value = "PASSFG" Then
            '                        myColumnN1 = k
            '                        Exit For
            '                    End If
            '                Next k
            '                For k = 1 To 16
            '                    If Sheets(1).Cells(1, k).Value = "TEST_NUM" Then
            '                        myColumnN2 = k
            '                        Exit For
            '                    End If
            '                Next k
                            If myColumnN1 > 0 And myColumnN2 > 0 Then
'                                myStartRowN = 6
                                With Sheets(1)
                                    myFtItemN = 0
                                    myTotalSubTestNo = .UsedRange.Columns.count - myColumnN2 '2018/06/21
                                    For k = myStartRowN To .UsedRange.rows.count
                                        If UCase(CStr(.Cells(k, myColumnN1).value)) = "TRUE" Then
'                                            If (myColumnN2 + .Cells(k, myColumnN2).Value) < .UsedRange.Columns.Count Then 'value operation take time
                                            If .Cells(k, myColumnN2).value < myTotalSubTestNo Then '2018/06/21
                                                myFtItemN = .Cells(k, myColumnN2).value
                                                .Range(.Columns(myColumnN2 + .Cells(k, myColumnN2).value + 1), .Columns(.UsedRange.Columns.count)).Cut
                                                Sheets("QA_Data").rows("1:1").Insert Shift:=xlDown
                                                .Range(.Columns(1), .Columns(myColumnN2)).Copy
                                                Sheets("QA_Data").Columns(1).Insert Shift:=xlToRight
                                                Exit For
                                            End If
                                        End If
                                    Next k
                                End With
                                With Sheets("QA_Data")
'                                    For k = .UsedRange.Rows.Count To myStartRowN Step -1
'                                        If .Cells(k, myColumnN2).Value <= myFtItemN Then
'                                            .Rows(k).Delete Shift:=xlUp
'                                        End If
'                                    Next k
                                    myQaDeviceN = 0
'                                    .Activate '2018/06/21
'                                    Call DeviceSortControl(False, .Cells(5, myColumnN2)) '2018/06/21
                                    For k = myStartRowN To .UsedRange.rows.count
                                        If .Cells(k, myColumnN2).value > myFtItemN Then
'                                            .Rows(k).Cut
'                                            .Rows(myStartRowN + myQaDeviceN).Insert Shift:=xlDown
                                            .rows(myStartRowN + myQaDeviceN).value = .rows(k).value '2018/06/21 less time
'                                            .Rows(k).Copy
'                                            .Rows(myStartRowN + myQaDeviceN).Select
'                                            .Paste
                                            myQaDeviceN = myQaDeviceN + 1
'                                        Else '2018/06/21
'                                            Exit For
                                        End If
                                    Next k
                                    If myQaDeviceN > 0 Then .Range(.rows(myStartRowN + myQaDeviceN), .rows(.UsedRange.rows.count)).Clear
'                                    Sheets(1).Activate '2018/06/21
                                End With
                            End If
            '                Exit For
                        End If
            '        Next j
            '        Exit For
            '    End If
            'Next i
        End If
        
'        Range("A3:" + ColumnLetter(myColumnN) + "4").ClearContents
'        Rows("4:4").Cut
'        Rows("3:3").Insert Shift:=xlDown
'        Rows("2:2").Cut
'        Rows("5:5").Insert Shift:=xlDown
'        If myTesterType = 2 Then
'            Rows("5:5").Delete Shift:=xlUp
'        End If
'        Rows("2:2").Insert Shift:=xlDown, CopyOrigin:=xlFormatFromLeftOrAbove
'        For j = 1 To 7
'        Range("A4:" + ColumnLetter(myColumnN) + "5").ClearContents '2019/12/11
'        Rows("5:5").Cut
'        Rows("4:4").Insert Shift:=xlDown
'        Rows("3:3").Cut
'        Rows("6:6").Insert Shift:=xlDown
'        If myTesterType = 2 Then
'            Rows("6:6").Delete Shift:=xlUp
'        End If
'        Rows("3:3").Insert Shift:=xlDown, CopyOrigin:=xlFormatFromLeftOrAbove
'        For j = 1 To 6
        Range("A" + CStr(myFindedRowN + 2) + ":" + ColumnLetter(myColumnN) + CStr(myFindedRowN + 3)).ClearContents '2019/12/24
        rows(myFindedRowN + 3).Cut
        rows(myFindedRowN + 2).Insert Shift:=xlDown
        rows(myFindedRowN + 1).Cut
        rows(myFindedRowN + 4).Insert Shift:=xlDown
        If myTesterType = 2 Then
            rows(myFindedRowN + 4).Delete Shift:=xlUp
        End If
        rows(myFindedRowN + 1).Insert Shift:=xlDown, CopyOrigin:=xlFormatFromLeftOrAbove
        For j = myFindedRowN To 7
            rows("1:1").Insert Shift:=xlDown, CopyOrigin:=xlFormatFromLeftOrAbove
        Next j

        myTotalDeviceNumber = GetTotalDeviceNumber
        Columns("A:A").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
        Columns("A:A").Insert Shift:=xlToRight, CopyOrigin:=xlFormatFromLeftOrAbove
        Cells(12, 1).value = "Serial#"
        Cells(12, 2).value = "Bin#"
        myColumnN = 0
        myColumnN1 = 0
        myColumnN2 = 0
        For j = 3 To 18
            If Sheets(1).Cells(8, j).value = "SW_Bin" Or Sheets(1).Cells(8, j).value = "SOFT_BIN" Then
                myColumnN = j
                Exit For
            End If
        Next j
        For j = 3 To 18
            If Sheets(1).Cells(8, j).value = "Dut_Pass" Or Sheets(1).Cells(8, j).value = "PASSFG" Then
                myColumnN1 = j
                Exit For
            End If
        Next j
'        If myTesterType = 2 Then
'            For j = 3 To 18
'                If Sheets(1).Cells(8, j).Value = "DUT_NO" Then
'                    myColumnN2 = j
'                    Exit For
'                End If
'            Next j
'        End If
        For j = 1 To myTotalDeviceNumber
            Cells(12 + j, 1).value = j
            If myColumnN > 0 Then
                Cells(12 + j, 2).value = Cells(12 + j, myColumnN).value
            Else
                Cells(12 + j, 2).value = 0
            End If
            If myColumnN1 > 0 Then
                If UCase(CStr(Cells(12 + j, myColumnN1).value)) = "TRUE" Then
                    Cells(12 + j, myColumnN1).value = 1
                Else
                    Cells(12 + j, myColumnN1).value = 0
                End If
            End If
'            If myColumnN2 > 0 Then
'                Cells(12 + j, myColumnN2).Value = Cells(12 + j, myColumnN2).Value
'            End If
        Next j
        
        myTotalItemNumber = GetTotalItemNumber
        For j = 3 To 3 + myTotalItemNumber - 1
            If Replace(CStr(Cells(12, j).value), " ", "") = "" Then Cells(12, j).value = "Unit"
            If myTesterType = 2 Then
                Cells(6, j).NumberFormatLocal = "G/通用格式"
                Cells(10, j).value = Cells(10, j).value '2018/05/28
                Cells(11, j).value = Cells(11, j).value '2018/05/28
            End If
        Next j
        
        Cells(1, 1).value = "Spreadsheet"
        Cells(1, 2).value = "Format"
        Cells(2, 1).value = "Test Program:"
        Cells(3, 1).value = "Lot ID:"
        Cells(4, 1).value = "Operator:"
        Cells(5, 1).value = "Computer:"
        Cells(6, 1).value = "Date:"
        
        If myTesterType = 1 Then
            Cells(9, 1).value = "CTA" '2018/05/28
            For j = 2 To Sheets.count
                If Sheets(j).name = "sum" Then
                    For k = 1 To 64
                        If Sheets(j).Cells(k, 1).value = "LotID" Then
                            Cells(3, 2).value = Sheets(j).Cells(k, 2).value
                            Exit For
                        End If
                    Next k
                    For k = 1 To 64
                        If Sheets(j).Cells(k, 1).value = "Operator" Then
                            Cells(4, 2).value = Sheets(j).Cells(k, 2).value
                            Exit For
                        End If
                    Next k
                    For k = 1 To 64
                        If Sheets(j).Cells(k, 1).value = "EndTime" Then
                            Cells(6, 2).value = Sheets(j).Cells(k, 2).value
                            Exit For
                        End If
                    Next k
                    For k = 1 To 64
'                        If Sheets(j).Cells(k, 1).Value = "ProgramName" Then
                        If Sheets(j).Cells(k, 1).value = "ProgramName" Or Sheets(j).Cells(k, 1).value = "Program" Then '2018/08/08
                            TestArray() = Split(Sheets(j).Cells(k, 2).value, "\")
                            If UBound(TestArray) > 0 Then
                                Cells(2, 2).value = TestArray(UBound(TestArray) - 1)
                            End If
                            Exit For
                        End If
                    Next k
                    Exit For
                End If
            Next j
'        Else
        ElseIf myTesterType = 2 Then '2022/04/20
            Cells(9, 1).value = "STS" '2018/05/28
            For j = 2 To Sheets.count
                If Sheets(j).name = "Summary information" Then
                    For k = 1 To 64
                        myStr = CStr(Sheets(j).Cells(k, 1).value)
                        myTarStr = "Lot Id:"
                        If InStr(myStr, myTarStr) = 1 Then
                            If Len(myStr) - Len(myTarStr) > 0 Then
                                Cells(3, 2).value = Mid(myStr, Len(myTarStr) + 1, Len(myStr) - Len(myTarStr))
                            Else
                                If Sheets(j).Cells(k, 2).value <> "" Then Cells(3, 2).value = Sheets(j).Cells(k, 2).value '2018/06/21
                            End If
                            Exit For
                        End If
                    Next k
                    For k = 1 To 64
                        myStr = CStr(Sheets(j).Cells(k, 1).value)
                        myTarStr = "User:"
                        If InStr(myStr, myTarStr) = 1 Then
                            If Len(myStr) - Len(myTarStr) > 0 Then
                                Cells(4, 2).value = Mid(myStr, Len(myTarStr) + 1, Len(myStr) - Len(myTarStr))
                            Else
                                If Sheets(j).Cells(k, 2).value <> "" Then Cells(4, 2).value = Sheets(j).Cells(k, 2).value '2018/06/21
                            End If
                            Exit For
                        End If
                    Next k
                    For k = 1 To 64
                        myStr = CStr(Sheets(j).Cells(k, 1).value)
                        myTarStr = "Tester ID:"
                        If InStr(myStr, myTarStr) = 1 Then
                            If Len(myStr) - Len(myTarStr) > 0 Then
                                Cells(5, 2).value = Mid(myStr, Len(myTarStr) + 1, Len(myStr) - Len(myTarStr))
                            Else
                                If Sheets(j).Cells(k, 2).value <> "" Then Cells(5, 2).value = Sheets(j).Cells(k, 2).value '2018/06/21
                            End If
                            Exit For
                        End If
                    Next k
                    For k = 1 To 64
                        myStr = CStr(Sheets(j).Cells(k, 1).value)
                        myTarStr = "Ending Time:"
                        If InStr(myStr, myTarStr) = 1 Then
                            If Len(myStr) - Len(myTarStr) > 0 Then
                                Cells(6, 2).value = Mid(myStr, Len(myTarStr) + 1, Len(myStr) - Len(myTarStr))
                            Else
                                If Sheets(j).Cells(k, 2).value <> "" Then Cells(6, 2).value = Sheets(j).Cells(k, 2).value '2018/06/21
                            End If
                            Exit For
                        End If
                    Next k
                    For k = 1 To 64
                        myStr = CStr(Sheets(j).Cells(k, 1).value)
                        myTarStr = "Program:"
                        If InStr(myStr, myTarStr) = 1 Then
                            If Len(myStr) - Len(myTarStr) > 0 Then
                                TestArray() = Split(Mid(myStr, Len(myTarStr) + 1, Len(myStr) - Len(myTarStr)), "\")
                                If UBound(TestArray) > 0 Then
                                    Cells(2, 2).value = TestArray(UBound(TestArray))
                                End If
                            Else
                                If Sheets(j).Cells(k, 2).value <> "" Then '2018/06/21
                                    TestArray() = Split(CStr(Sheets(j).Cells(k, 2).value), "\")
                                    If UBound(TestArray) > 0 Then
                                        Cells(2, 2).value = TestArray(UBound(TestArray))
                                    End If
                                End If
                            End If
                            Exit For
                        End If
                    Next k
                    Exit For
                End If
            Next j
        End If
        Cells(1, 1).Select
'    Else
'        If Sheets(1).Cells(9, 1).Value = "CTA" Then '2018/05/28
'            myTesterType = 1
'        Else
'            If Sheets(1).Cells(9, 1).Value = "STS" Then myTesterType = 2
'        End If
    End If
    
    If myTesterType = 3 Then '2020/04/14
        convertOtherDatalog = True
        Sheets(1).Activate
        
        ActiveWorkbook.Styles("Normal").Font.name = "新細明體"
        ActiveWorkbook.Styles("Normal").Font.Size = 12
        
        rows("1:6").Copy
        rows("1:1").Insert Shift:=xlDown
        rows("1:6").ClearContents
        Cells(1, 2).value = "Spreadsheet"
        Cells(1, 3).value = "Format"
        Cells(2, 2).value = "Test Program:"
        Cells(3, 2).value = "Lot ID:"
        Cells(4, 2).value = "Operator:"
        Cells(5, 2).value = "Computer:"
        Cells(6, 2).value = "Date:"
        For k = 7 To 64
            If InStr(Sheets(1).Cells(k, 1).value, "Datalog for Lot Number") Then
                Cells(3, 3).value = Sheets(1).Cells(k, 2).value
                Exit For
            End If
        Next k
        For k = 7 To 64
            If InStr(Sheets(1).Cells(k, 1).value, "Data collected by operator") Then
                Cells(4, 3).value = Sheets(1).Cells(k, 2).value
                Exit For
            End If
        Next k
        For k = 7 To 64
            If InStr(Sheets(1).Cells(k, 1).value, "Data collected on station") Then
                Cells(5, 3).value = Sheets(1).Cells(k, 2).value
                Exit For
            End If
        Next k
        For k = 7 To 64
            If InStr(Sheets(1).Cells(k, 1).value, "On") Then
                Cells(6, 3).value = Sheets(1).Cells(k, 2).value
                Exit For
            End If
        Next k
        For k = 7 To 64
            If InStr(Sheets(1).Cells(k, 1).value, "Test program") Then
                TestArray() = Split(Sheets(1).Cells(k, 2).value, "\")
                If UBound(TestArray) > 0 Then
                    Cells(2, 3).value = TestArray(UBound(TestArray))
                End If
                Exit For
            End If
        Next k
        
'        For k = 7 To 64
        For k = 7 To 68 '2020/12/29
            If InStr(Sheets(1).Cells(k, 1).value, "Test Number") Then
                rows("7:" + CStr(k - 2)).Delete Shift:=xlUp
                Range(Cells(7, 1), Cells(11, 1)).ClearContents
                Columns("A:A").Cut
                Columns("D:D").Insert Shift:=xlToRight
                For j = 3 To 64
                    If Cells(12, j).value = "" Then
                        If j > 3 Then
                            Range(Cells(12, 3), Cells(12, j - 1)).Cut Cells(7, 3)
                            For i = 3 To j - 1 '2020/04/15
                                Cells(11, i).value = "UNIT"
                            Next i
                        End If
                        Exit For
                    End If
                Next j
                rows("8:8").Cut
                rows("7:7").Insert Shift:=xlDown
                rows("10:10").Cut
                rows("9:9").Insert Shift:=xlDown
                Range(Cells(12, 1), Cells(12, 2)).ClearContents
                rows("12:12").Cut
                rows("9:9").Insert Shift:=xlDown
                Cells(12, 1).value = "Serial#"
                Cells(12, 2).value = "Bin#"
                Cells(9, 1).value = "ETS"
                Exit For
            End If
        Next k
        
        myTotalDeviceNumber = GetTotalDeviceNumber '2020/04/15
        For myRowIndex = myTotalDeviceNumber + 12 To 13 Step -1
            If Cells(myRowIndex, 1).value = "" Then rows(CStr(myRowIndex)).Delete Shift:=xlUp
        Next myRowIndex
    End If
    
    If myTesterType = 4 Then '2022/04/20
        myBinNoColumnN = 0
        For i = 1 To 32
            If Left(CStr(Cells(10, i)), 4) = "Bin#" Then
                myBinNoColumnN = i
                Exit For
            End If
        Next i
        If myBinNoColumnN > 0 And Left(CStr(Cells(10, 1)), 7) = "Serial#" Then
            convertOtherDatalog = True
            Range("B1:B5").Copy Cells(1, myBinNoColumnN)
            Range("B1:B5").ClearContents
            Columns("E:E").Cut
            Columns("B:B").Insert Shift:=xlToRight
            rows("1:1").Insert Shift:=xlDown, CopyOrigin:=xlFormatFromLeftOrAbove
            rows("11:11").Cut
            rows("7:7").Insert Shift:=xlDown
            rows("7:7").Insert Shift:=xlDown, CopyOrigin:=xlFormatFromLeftOrAbove
            Cells(1, 1).value = "Spreadsheet"
            Cells(1, 2).value = "Format"
            Cells(6, 3).NumberFormatLocal = "G/通用格式"
            Range("A8:B8").ClearContents
            Range("A10:A11").ClearContents
            Cells(12, 1).value = "Serial#"
            Cells(12, 2).value = "Bin#"
            Cells(9, 1).value = "YS"
            myTotalItemNumber = GetTotalItemNumber
            For j = 3 To 3 + myTotalItemNumber - 1
                If Replace(CStr(Cells(12, j).value), " ", "") = "" Then Cells(12, j).value = "Unit"
                If Replace(CStr(Cells(10, j).value), " ", "") = "NA" Then Cells(10, j).value = "none"
                If Replace(CStr(Cells(11, j).value), " ", "") = "NA" Then Cells(11, j).value = "none"
            Next j
        End If
    End If

End Function
Public Function checkSheetIsDatalog(Optional mySheetN As Integer = 1, Optional myBookName As Variant = "") As Boolean

    If myBookName = "" Then myBookName = ActiveWorkbook.name
    If mySheetN > 0 Then
        If InStr(CStr(Workbooks(myBookName).Sheets(mySheetN).Cells(12, 1).value), "Serial#,Bin#") = 1 Then Call spd2csv(False) '20171103
        If CStr(Workbooks(myBookName).Sheets(mySheetN).Cells(12, 1).value) = "Serial#" And CStr(Workbooks(myBookName).Sheets(mySheetN).Cells(12, 2).value) = "Bin#" Then
            checkSheetIsDatalog = True
            If mySheetN = 1 Then Call FillEmptyItemName
        Else
            checkSheetIsDatalog = False
        End If
    Else
        checkSheetIsDatalog = False
    End If

End Function
Function ConfirmDelete() As Boolean
    Dim result As VbMsgBoxResult
    result = MsgBox("確定要刪[EXCEPT_CHAR]暫存資料嗎？", vbYesNo, "刪[EXCEPT_CHAR]暫存資料")
    If result = vbYes Then
        ConfirmDelete = True
    Else
        ConfirmDelete = False
    End If
End Function
Function DeleteOldFilesOrFolders(path As String, daysToKeep As Integer) As Boolean
' 這個 Function 的目的是刪[EXCEPT_CHAR]指定資料夾中比特定天數還早的檔案和資料夾
' 參數 path 為要刪[EXCEPT_CHAR]的資料夾的路徑
' 參數 daysToKeep 為要保留的天數，比這個日期還早的檔案和資料夾都會被刪[EXCEPT_CHAR]
' 這個 Function 回傳值為 Boolean 型態，代表是否有執行刪[EXCEPT_CHAR]動作
Dim objFSO As Object ' 宣告 FileSystemObject 物件，用來存取檔案系統
Dim objFolder As Object ' 宣告 Folder 物件，用來表示要刪[EXCEPT_CHAR]的資料夾
Dim objFile As Object ' 宣告 File 物件，用來表示要刪[EXCEPT_CHAR]的檔案
Dim subfolder As Object ' 宣告 SubFolder 物件，用來表示要刪[EXCEPT_CHAR]的子資料夾
Dim ageLimit As Date ' 宣告 Date 型態的變數，用來儲存保留的日期
Dim shouldDelete As Boolean ' 宣告 Boolean 型態的變數，用來表示是否有執行刪[EXCEPT_CHAR]動作
Set objFSO = CreateObject("Scripting.FileSystemObject") ' 建立 FileSystemObject 物件
Set objFolder = objFSO.GetFolder(path) ' 取得要刪[EXCEPT_CHAR]的資料夾
ageLimit = Now() - daysToKeep ' 計算保留的日期
shouldDelete = False ' 初始值設定為 False，代表沒有執行刪[EXCEPT_CHAR]動作

If daysToKeep > 0 Then ' 如果保留天數大於 0
    ' 刪[EXCEPT_CHAR]檔案
    For Each objFile In objFolder.files
        If objFile.DateLastModified < ageLimit Then ' 如果檔案日期比保留日期還早
            objFile.Delete True ' 刪[EXCEPT_CHAR]檔案
            shouldDelete = True ' 設定為 True，代表有執行刪[EXCEPT_CHAR]動作
        End If
    Next
    
    ' 刪[EXCEPT_CHAR]資料夾
    For Each subfolder In objFolder.Subfolders
        If subfolder.DateLastModified < ageLimit Then ' 如果資料夾日期比保留日期還早
            subfolder.Delete True ' 刪[EXCEPT_CHAR]資料夾
            shouldDelete = True ' 設定為 True，代表有執行刪[EXCEPT_CHAR]動作
        Else
            shouldDelete = DeleteOldFilesOrFolders(subfolder.path, daysToKeep) ' 遞迴呼叫自己，刪[EXCEPT_CHAR]子資料夾
        End If
    Next
End If
Set objFile = Nothing
Set subfolder = Nothing
Set objFolder = Nothing
Set objFSO = Nothing
DeleteOldFilesOrFolders = shouldDelete
End Function
Function CheckZipContents(filePath As String)
    Dim zipExePath As String
    Dim unzipCmd As String
    Dim fso As Object
    Dim folderPath As String
    Dim folder As Object
    Dim ii As Integer
    Dim wsh As Object

    Set fso = CreateObject("Scripting.FileSystemObject")
    Set wsh = CreateObject("WScript.Shell")

    zipExePath = "C:\Program Files\7-Zip\7z.exe"
    If InStr(1, filePath, ".7z") = 0 Then
        folderPath = Left(filePath, Len(filePath) - 4)
    Else
        folderPath = Left(filePath, Len(filePath) - 3)
    End If
    unzipCmd = Chr(34) & zipExePath & Chr(34) & " x " & Chr(34) & filePath & Chr(34) & " -o.\\"

    ' 使用wsh.Run執行命令，並等待解壓縮完成
    wsh.Run unzipCmd, vbHide, True

    ' 解壓縮後執行的代碼
   ' DeleteFile filePath

    ' 檢查解壓縮後的檔案是否包含資料夾
    If fso.FolderExists(folderPath) Then
         Set folder = fso.GetFolder(folderPath)
         If folder.Attributes And 16 Then ' 資料夾的屬性值為 16
        ' MsgBox "ZIP 檔案包含資料夾。"
         ' 刪[EXCEPT_CHAR]解壓縮後的暫存資料夾
        If folder.path = folderPath Then '確認資料夾存在
            DeleteDlxFiles folderPath
            DeleteDl4Files folderPath
            UnzipSPD folderPath
            ChangeSpdToCsv folderPath
            Device2BinControl (folderPath)
        End If

        End If
    Else
        ChangeSpdToCsv1 folderPath
        Device2BinControl folderPath, True
        folderPath = Left(folderPath, Len(folderPath) - 4) & ".csv"
        DeleteFile (folderPath)
    End If
End Function
Function DeleteFile(ByVal filePath As String)
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    If fso.fileExists(filePath) Then
        fso.DeleteFile filePath
    End If
    Set fso = Nothing
End Function
Function ChangeSpdToCsv1(ByVal filePath As String) ' 定義Function
    
   Dim fso As Object ' 宣告Scripting.FileSystemObject物件
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    Dim file As Object ' 宣告檔案物件
    Set file = fso.GetFile(filePath) ' 取得檔案物件
    
    If LCase(Right(file.name, 4)) = ".spd" Then ' 如果檔案是spd檔案
          
        Dim newFileName As String ' 宣告新檔案名稱
        If InStr(1, UCase(file.name), ".SPD") > 0 Then ' 如果檔案名稱中包含".SPD"
            newFileName = Replace(file.name, ".spd", ".csv", , , vbTextCompare) ' 將".spd"替換為".csv"
        Else ' 否則
            newFileName = file.name ' 新檔案名稱與原檔案名稱相同
        End If
        
        If Not fso.fileExists(file.ParentFolder.path & "\" & newFileName) Then ' 如果新檔案不存在
            file.name = newFileName ' 將檔案名稱改為新檔案名稱
        End If
    End If
End Function
Function ProcessExcelFile(fileName As String) As Boolean
    Dim xlApp As Object
    Dim xlBook As Object
    Dim xlSheet As Object
    Dim lastRow As Long
    Dim i As Long
    Dim val As Variant
    val = 0
    ' 建立 Excel Application 物件
    Set xlApp = CreateObject("Excel.Application")
    
    ' 開啟 Excel 檔案
    Set xlBook = xlApp.Workbooks.Open(fileName)
    
    ' 讀取第一個 Sheet
    Set xlSheet = xlBook.Sheets(1)
    
    ' 取得最後一列資料的列數
    lastRow = xlSheet.Cells(xlSheet.rows.count, "B").End(-4162).row
    
    ' 檢查每一列，如果不是 1，就取出該欄位的值
    For i = 13 To lastRow
        If xlSheet.Cells(i, "B").value <> 1 Then
           
            val = xlSheet.Cells(i, "B").value
        End If
    Next i
    ' 選擇最左邊的工作表
   xlBook.Sheets(1).Activate
   Dim value As Variant
   value = xlBook.Sheets(1).Range("C13").value
   If val > 3 Then
    xlBook.Sheets(1).Cells(14, val - 2).Select
    xlBook.Sheets(1).Cells(5, val - 2).value = value
   End If
   
   
    
    ' 儲存並關閉檔案
    xlBook.Save
    xlBook.Close SaveChanges:=False
    
    ' 釋放物件所佔用的資源
    Set xlSheet = Nothing
    Set xlBook = Nothing
    Set xlApp = Nothing
    
    ' 執行完畢
    ProcessExcelFile = True
End Function
Function FindEQCFAILDATALOG(csvFilesonlineeqcfail() As Variant) As String
    ' 設定要尋找的檔案名稱關鍵字
    Dim findfilename As String
    Dim error_show As String
    Dim new_file_path As String
    Dim eqc_fail_cnt As Integer
    Dim newFileContents As String
    Dim eqcfailcsv As String
    
    Dim i As Integer
    Dim j As Integer
    
    For j = 0 To UBound(csvFilesonlineeqcfail)
        ' 開啟檔案 B
        eqcfailcsv = Left(csvFilesonlineeqcfail(j), Len(csvFilesonlineeqcfail(j)) - 4) & "_EQCFAILDATA.csv"
        If Len(Dir(eqcfailcsv)) > 0 Then
            Dim fileB As Object
            Set fileB = CreateObject("Scripting.FileSystemObject").GetFile(eqcfailcsv)
            Dim fileBContents As String
            Open fileB For Input As #2
            fileBContents = Input$(LOF(2), 2)
            Close #2
             Dim rowsInB As Variant
             rowsInB = Split(fileBContents, vbCrLf)
             For i = 12 To UBound(rowsInB)
                If (rowsInB(i) = "") Then
                    Exit For
                End If
                newFileContents = newFileContents & Split(fileBContents, vbCrLf)(i) & vbCrLf
             Next i
             Kill eqcfailcsv
        End If
    Next j
    FindEQCFAILDATALOG = newFileContents
End Function
Function GetFileName(fullPath As String) As String
    Dim parts() As String
    parts = Split(fullPath, "\")
    GetFileName = parts(UBound(parts))
End Function

Function ConvertToHyperlinks(ByVal filePath As String) As String
    ' 創建一個 Excel 應用程式物件
    Dim fileName As String

    Dim xlApp As Object
    Set xlApp = CreateObject("Excel.Application")
    
    ' 打開指定的檔案
    Dim xlBook As Object
    Set xlBook = xlApp.Workbooks.Open(filePath)
    
    ' 獲取 Sheet 1 的物件
    Dim xlSheet As Object
    Set xlSheet = xlBook.Sheets(1)
    
    ' 計算資料區間範圍
    Dim lastRow As Integer
    lastRow = xlSheet.Range("C" & xlSheet.rows.count).End(-4162).row ' 取得最後一行
    
    ' 將 C14 至最後一行的儲存格轉換為超連結
    For i = 14 To lastRow
        Dim cell As Object
        Set cell = xlSheet.Range("C" & i)
        On Error Resume Next
            fileName = GetFileName(cell.value)
            cell.Hyperlinks.Add Anchor:=cell, Address:=cell.value, SubAddress:="", TextToDisplay:=fileName
       On Error GoTo 0
    Next i
    
    xlBook.Sheets(1).Activate
    ' 保存變更並關閉 Excel 檔案
    xlBook.Save
    xlBook.Close
    xlApp.Quit
    
    ' 釋放物件
    Set xlSheet = Nothing
    Set xlBook = Nothing
    Set xlApp = Nothing
    
    ' 返回檔案路徑
    ConvertToHyperlinks = filePath
End Function
Function FindEQCRTCSVFiles(ByVal folderPath As String) As Variant
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    Dim folder As Object
    Set folder = fso.GetFolder(folderPath)
    Dim result() As String
    Dim i As Integer
    i = 0
    Dim file As Object
    For Each file In folder.files
        Dim fileName As String
        fileName = file.name
        If Right(fileName, 4) = ".csv" Then
            If InStr(fileName, "r1") = 0 And _
               InStr(LCase(fileName), "onlieeqc") = 0 And _
               InStr(fileName, "qa") = 0 And _
               InStr(LCase(fileName), "eqcfaildata") = 0 And _
               InStr(LCase(fileName), "eqctotaldata") = 0 Then
                ReDim Preserve result(i)
                result(i) = file.path
                i = i + 1
            End If
        End If
    Next file
    Dim subfolder As Object
    For Each subfolder In folder.Subfolders
        Dim subFolderPath As String
        subFolderPath = subfolder.path
        If InStr(1, LCase(subFolderPath), "ctacsv", vbTextCompare) = 0 Then
            Dim subResult
            subResult = FindEQCRTCSVFiles(subFolderPath)
            If Not IsEmpty(subResult) Then
                Dim j As Integer
                On Error Resume Next
                For j = 0 To UBound(subResult)
                    ReDim Preserve result(i)
                    result(i) = subResult(j)
                    i = i + 1
                Next j
                On Error GoTo FindEQCRTCSVFilesnext
            End If
        End If
    Next subfolder
FindEQCRTCSVFilesnext:
    FindEQCRTCSVFiles = result
End Function
Sub WriteCSVFilesToExcel(ByVal folderPath As String, ByVal csvFileseqcrt As Variant)
    Dim j As Integer
    Dim i As Integer
    Dim fileList() As Variant
    ReDim fileB(1 To 1) As String
    Dim tempeqcrtcsvname As String

    ReDim fileList(1 To 1)
    For i = 0 To UBound(csvFileseqcrt)
        tempeqcrtcsvname = csvFileseqcrt(i, 1)
        If CheckEQCRTCSVFile(tempeqcrtcsvname) = True Then
            If j = 0 Then
                fileList(1) = Array(csvFileseqcrt(i, 1))
            Else
                ReDim Preserve fileList(1 To UBound(fileList) + 1)
                fileList(j + 1) = Array(csvFileseqcrt(i, 1))
            End If
            j = j + 1
        End If

    Next i
    If j > 0 Then
    '沒有eqc fail時要處理 zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz
        For i = 1 To UBound(fileList)
              ReDim Preserve fileB(1 To UBound(fileList)) As String
               fileB(i) = fileList(i)(0)
        Next i
    End If
    If j > 0 Then
        MergeCSVFiles folderPath & "\" & "EQCTOTALDATA.csv", fileB
    End If
End Sub
Function CheckEQCCSVFile(ByVal filePath As String) As Boolean
    Dim fileContent As String
     Dim rows As Variant
     Dim elements As Variant
     Dim secondLine As String
     Dim secondElement As String
    If filePath <> "" Then
        If InStr(1, LCase(filePath), "eqctotaldata", vbTextCompare) = 0 And InStr(1, LCase(filePath), "eqcfaildata", vbTextCompare) = 0 Then
            Dim fileA As Object
            Set fileA = CreateObject("Scripting.FileSystemObject").GetFile(filePath)
            Open fileA For Input As #1
             For i = 0 To 1
                Line Input #1, fileLine
                If InStr(1, LCase(fileLine), "(qc)", vbTextCompare) > 0 Or InStr(1, filePath, ".qa", vbTextCompare) > 0 Then
                    CheckEQCCSVFile = True
                    Close #1
                    Exit Function
                End If
            Next i
            Line Input #1, fileLine
            If InStr(1, fileLine, "qa", vbTextCompare) > 0 Then
                CheckEQCCSVFile = True
            End If
            Close #1
        End If
    End If
    CheckEQCCSVFile = False
End Function
Function CheckEQCRTCSVFile(ByVal filePath As String) As Boolean
    Dim fileContent As String
     Dim rows As Variant
     Dim elements As Variant
     Dim secondLine As String
     Dim secondElement As String
    If filePath <> "" Then
        Dim fileA As Object
        Set fileA = CreateObject("Scripting.FileSystemObject").GetFile(filePath)
        Open fileA For Input As #1
         For i = 0 To 1
            Line Input #1, fileLine
            If InStr(1, LCase(fileLine), "(qc)", vbTextCompare) > 0 Then
                CheckEQCRTCSVFile = True
                Close #1
                Exit Function
            End If
        Next i
        Close #1
    End If
    CheckEQCRTCSVFile = False
End Function
Function CheckEQCBIN1CSVFile(ByVal filePath As String) As Boolean
    Dim fileContent As String
     Dim rows As Variant
     Dim elements As Variant
     Dim secondLine As String
     Dim secondElement As String
    If filePath <> "" Then
        If InStr(1, LCase(filePath), "eqctotaldata", vbTextCompare) = 0 And InStr(1, LCase(filePath), "eqcfaildata", vbTextCompare) = 0 Then
            Dim fileNumber As Integer
            Dim fileLine As String
            ' 取得未使用的檔案號
            fileNumber = FreeFile
            ' 開啟檔案
            Open filePath For Input As #fileNumber
            ' 逐行讀取檔案內容
            Do While Not EOF(fileNumber)
                Line Input #fileNumber, fileLine
                ' 在這裡處理每行的內容，例如可以將每行的內容加入陣列或處理其他操作
                If InStr(1, LCase(fileLine), "(qc)", vbTextCompare) > 0 Or InStr(1, LCase(filePath), "onlineeqc", vbTextCompare) > 0 Then
                    CheckEQCBIN1CSVFile = True
                    Close #fileNumber
                    Exit Function
                End If
            Loop
        
            ' 關閉檔案
            Close #fileNumber
        End If
    End If
    CheckEQCBIN1CSVFile = False
End Function
Function CheckDataTime(ByVal filePath As String) As Date
    Dim fileContent As String
     Dim rows As Variant
    If filePath <> "" Then
        Dim fileA As Object
        Set fileA = CreateObject("Scripting.FileSystemObject").GetFile(filePath)
        Open fileA For Input As #1
         For i = 0 To 6
            Line Input #1, fileLine
            If i = 5 Then
              lineElementsA = Split(fileLine, ",")
              rows = lineElementsA(1) ' 格式=02/17/22 01:34:07
             Close #1
             Exit For
            End If
        Next i
    End If
    Dim datePart As String
    Dim timePart As String
    Dim datatemp As Date
    If rows <> "" Then
        datePart = Split(rows, " ")(0)
        timePart = Split(rows, " ")(1)
        datatemp = CDate(datePart & " " & timePart)
        If datatemp < DateSerial(2020, 1, 1) Then
        ' dateValue 小於 2020 年
            'datatemp = DateSerial(2000 + Right(datePart, 2), Mid(datePart, 4, 2), Left(datePart, 2)) + TimeSerial(Left(timePart, 2), Mid(timePart, 4, 2), Right(timePart, 2))
             'datatemp = DateSerial(2000 + Right(dateString, 2), Mid(dateString, 1, 2), Left(dateString, 2)) + TimeSerial(Mid(dateString, 10, 2), Mid(dateString, 13, 2), Mid(dateString, 16, 2))
             datatemp = DateSerial(2000 + Right(datePart, 2), Left(datePart, 2), Mid(datePart, 4, 2)) + TimeSerial(Left(timePart, 2), Mid(timePart, 4, 2), Right(timePart, 2))
        End If
    End If
     CheckDataTime = datatemp
End Function
Function AddFileToList(dateStr As Date, fileName As String, ByRef fileList As Variant) As Variant
    Dim newFile As Variant
    newFile = Array(dateStr, fileName)
    
    If IsEmpty(fileList) Then
        ReDim fileList(1 To 1)
        fileList(1) = newFile
    Else
        Dim i As Integer
        Dim inserted As Boolean
        inserted = False
        For i = LBound(fileList) To UBound(fileList)
            Dim compareDate As Date
            compareDate = fileList(i)(0)
            If dateStr < compareDate Then
                ' Insert new file before current file
                ReDim Preserve fileList(1 To UBound(fileList) + 1)
                Dim j As Integer
                For j = UBound(fileList) - 1 To i Step -1
                    fileList(j + 1) = fileList(j)
                Next j
                fileList(i) = newFile
                inserted = True
                Exit For
            End If
        Next i
        
        If Not inserted Then
            ' Insert new file at end
            ReDim Preserve fileList(1 To UBound(fileList) + 1)
            fileList(UBound(fileList)) = newFile
        End If
    End If
    
    ' Sort files by date in ascending order
    Dim sortedList As Variant
    sortedList = fileList
    Dim n As Integer
    n = UBound(sortedList)

    For i = 1 To n - 1

        For j = i + 1 To n
            If sortedList(i)(0) > sortedList(j)(0) Then
                Dim temp As Variant
                temp = sortedList(i)
                sortedList(i) = sortedList(j)
                sortedList(j) = temp
            End If
        Next j
    Next i
    
    AddFileToList = sortedList
End Function

Sub MergeCSVFiles(fileA As String, fileB() As String) 'fileA=EQCTOTALDATA 20240131 fix
    Dim fileAContents As String
    Dim fileBContents As String
    Dim temp_str As String
    Dim hyplink_temp As String
    Dim i As Integer
    Dim havetesttime_eqcdata As Boolean
    Dim havetesttime As Boolean
    Dim have_X_COORD_qc As Boolean
    Dim have_Y_COORD_qc As Boolean
    Dim have_Alarm_qc As Boolean
    Dim cta_file8280 As Boolean
    Dim bypass1, bypass2, bypass3 As Integer
    Dim lineElementsB As Variant
    Dim targetRowInC As String
    
    have_X_COORD_qc = False
    have_Y_COORD_qc = False
    have_Alarm_qc = False
    cta_file8280 = False
    cta_file = 0
    bypass1 = -1
    bypass2 = -1
    bypass3 = -1
    
    ' 讀取檔案A
    Open fileA For Input As #1
    fileAContents = Input$(LOF(1), 1)
    Close #1
    havetesttime_eqcdata = False
    Dim rowsInAA As Variant
    Dim fileAContentsA As String
    rowsInAA = Split(fileAContents, vbCrLf)
    lineElementsA11 = Split(rowsInAA(11), ",")
    If (InStr(1, lineElementsA11(2), "Time", vbTextCompare) > 0) Then 'eqcdata have test time
       havetesttime_eqcdata = True
    End If
    ' 將檔案A和檔案B的內容寫入abc.csv
    Dim outFile As Object
    If Len(Dir(fileA)) > 0 Then
        Kill fileA
    End If
   ' outFile.Write fileAContents
    Dim rowsInA As Variant
    rowsInA = Split(fileAContents, vbCrLf)
    Set outFile = CreateObject("Scripting.FileSystemObject").CreateTextFile(fileA)
    For j = 0 To UBound(rowsInA)
        If (rowsInA(j) <> "") Then
            outFile.Write rowsInA(j) & vbCrLf
        End If
    Next j
 
    ' 逐一讀取檔案B
    For i = LBound(fileB) To UBound(fileB)
        havetesttime = False
        Open fileB(i) For Input As #2
        fileBContents = Input$(LOF(2), 2)
        Close #2
        Dim rowsInB As Variant
        rowsInB = Split(fileBContents, vbCrLf)
        
        lineElementsB = Split(rowsInB(11), ",")
        If (InStr(1, lineElementsB(2), "Time", vbTextCompare) > 0) Then 'have test time
            havetesttime = True
        End If
        lineElementsB = Split(rowsInB(7), ",")
        
        If (InStr(1, lineElementsB(2), "Index_No", vbTextCompare) > 0) Then
            cta_file8280 = True
        End If
        If (InStr(1, lineElementsB(8), "X_COORD", vbTextCompare) > 0 And cta_file8280 = True) Then
            have_X_COORD_qc = True
            bypass1 = 8
        End If
        If (InStr(1, lineElementsB(9), "Y_COORD", vbTextCompare) > 0 And cta_file8280 = True) Then
            have_Y_COORD_qc = True
            bypass2 = 9
        End If
        If (InStr(1, lineElementsB(14), "Alarm", vbTextCompare) > 0 And cta_file8280 = True) Then
            have_Alarm_qc = True
            bypass3 = 14
        End If
    
        For j = 12 To UBound(rowsInB)
            temp_str = rowsInB(j)
            If temp_str <> "" Then
                If (have_X_COORD_qc = True Or have_Y_COORD_qc = True Or have_Alarm_qc = True) Then '20240131 fix
                    lineElementsB = Split(temp_str, ",")
                    For k = 0 To UBound(lineElementsB)
                        If k <> bypass1 And k <> bypass2 And k <> bypass3 Then
                            targetRowInC = targetRowInC & lineElementsB(k) & ","
                        End If
                    Next k
                    temp_str = targetRowInC
                    targetRowInC = ""
                ElseIf havetesttime = True And havetesttime_eqcdata = False Then 'eqcdata no test time but qc have test time
                    lineElementsB = Split(temp_str, ",")
                    For k = 0 To UBound(lineElementsB)
                        If (k <> 2) Then
                            targetRowInC = targetRowInC & lineElementsB(k) & ","
                        End If
                    Next k
                    temp_str = targetRowInC
                    targetRowInC = ""
                End If
                hyplink_temp = fileB(i)
                hyplink_temp = ReplacePath(fileB(i))
                temp_str = AddFTToRowA(temp_str, 2, hyplink_temp & ",")
                outFile.Write temp_str & vbCrLf
            End If
        Next j
        
    Next i
    outFile.Close
End Sub
Function FindEqcDataSame(fileName As String, start_ As Integer, end_ As Integer) As String '不使用
    ' 定義 Excel 相關對象
    Dim xlApp As Object
    Set xlApp = CreateObject("Excel.Application")
    Dim xlBook As Object
    Set xlBook = xlApp.Workbooks.Open(fileName)
    Dim xlSheet As Object
    Set xlSheet = xlBook.Sheets(1)
    
    ' 讀取第 13 行所有欄位的數據
    Dim data() As Variant
    Dim lastCol As Integer
    lastCol = xlSheet.Cells(13, xlSheet.Columns.count).End(xlToLeft).Column
    data = xlSheet.Range("D13:" & xlSheet.Cells(13, lastCol).Address).value
      
    ' 依次查找連續出現次數最多的整數
    Dim cnt As Integer
    Dim maxCnt As Integer
    Dim i As Integer
    For i = 4 To lastCol - 4 'D開始 所以少3
        'If IsNumeric(data(1, i)) And Int(data(1, i)) = data(1, i) Then
         If IsNumeric(data(1, i)) And Int(data(1, i)) = data(1, i) Then
            cnt = cnt + 1
            If cnt > maxCnt Then
                maxCnt = cnt
                start_ = i - maxCnt + 1
                end_ = i
            End If
        Else
            cnt = 0
        End If
    Next i
    
    ' 關閉 Excel 相關對象
    xlBook.Close False
    xlApp.Quit
    Set xlSheet = Nothing
    Set xlBook = Nothing
    Set xlApp = Nothing
    
    ' 返回結果
    start_ = start_ + 3
    end_ = end_ + 3
    FindEqcDataSame = "Start column: " & Chr(start_ + 64) & ", end column: " & Chr(end_ + 64)
End Function
Function IsAllZeros(data As Variant, colIndex As Integer, count As Integer) As Boolean
    Dim i As Integer
    If colIndex > count Then
        For i = colIndex - count + 1 To colIndex
            If data(1, i) <> 0 Then
                IsAllZeros = False
                Exit Function
            End If
        Next i
    End If
    IsAllZeros = True
End Function
Function FindALLCSVFiles(ByVal folderPath As String) As Variant
    Dim fso As Object
    Set fso = CreateObject("Scripting.FileSystemObject")
    Dim folder As Object
    Set folder = fso.GetFolder(folderPath)
    Dim result() As String
    Dim i As Integer
    i = 0
    Dim file As Object
    For Each file In folder.files
        Dim fileName As String
        fileName = file.name
        If InStr(1, Right(fileName, 4), ".csv", vbTextCompare) > 0 And InStr(1, LCase(fileName), "eqcfaildata", vbTextCompare) = 0 And InStr(1, LCase(fileName), "eqctotaldata", vbTextCompare) = 0 Then   '20240202
            ReDim Preserve result(i)
            result(i) = file.path
            i = i + 1
        End If
    Next file
    
    Dim subfolder As Object
    For Each subfolder In folder.Subfolders
        Dim subFolderPath As String
        subFolderPath = subfolder.path
        If InStr(1, LCase(subFolderPath), "ctacsv", vbTextCompare) = 0 And InStr(1, LCase(subFolderPath), "Correlation", vbTextCompare) = 0 And InStr(1, LCase(subFolderPath), "生產資料", vbTextCompare) = 0 Then  '20230511
        'If InStr(1, LCase(subFolderPath), "ctacsv", vbTextCompare) = 0 And InStr(1, LCase(subFolderPath), "Correlation", vbTextCompare) = 0 And InStr(1, LCase(subFolderPath), "產線驗證", vbTextCompare) = 0 And InStr(1, LCase(subFolderPath), "生產資料", vbTextCompare) = 0 Then
        'If InStr(1, LCase(subFolderPath), "ctacsv", vbTextCompare) = 0 Then
            Dim subResult
            subResult = FindALLCSVFiles(subFolderPath)
            If Not IsEmpty(subResult) Then
                Dim j As Integer
                On Error Resume Next
                For j = 0 To UBound(subResult)
                    ReDim Preserve result(i)
                    result(i) = subResult(j)
                    i = i + 1
                Next j
                On Error GoTo FindALLCSVFilesnext
            End If
        End If
    Next subfolder
FindALLCSVFilesnext:
    FindALLCSVFiles = result
End Function
Function FindCSVWith2Ones(ByVal filePath As String) As Boolean
    Dim fileContent As String
    Dim lineElements As Variant
    Dim onesCount As Integer
    onesCount = 0
    If filePath <> "" Then
        Dim fileA As Object
        Set fileA = CreateObject("Scripting.FileSystemObject").GetFile(filePath)
        ' 檢查檔案是否已經開啟
        On Error Resume Next
        fileNum = FreeFile()
        Open filePath For Input Lock Read Write As #fileNum
        If Err.Number <> 0 Then
            ' 關閉檔案後再嘗試開啟
            Close #fileNum
            fileNum = FreeFile()
            Open filePath For Input Lock Read Write As #fileNum
        End If
        On Error GoTo 0
        Do Until EOF(1)
            Line Input #fileNum, fileLine
            If i > 12 Then
                lineElements = Split(fileLine, ",")
               
                If lineElements(1) = 1 Then
                    onesCount = onesCount + 1
                End If
                If onesCount > 2 Then
                    FindCSVWith2Ones = True
                    Close #fileNum
                    Exit Function
                End If
            End If
            i = i + 1
        Loop
        Close #fileNum
    End If
    FindCSVWith2Ones = False
End Function
Sub DelFilesOrFolders()
    Dim flag As Boolean
    destinationPath = Sheet2.Range("D2").value ' 設定預設路徑為Sheet2的D2儲存格的值
    flag = ConfirmDelete
    Dim val As Variant
    val = Sheet2.Range("C4").value ' 天數
    Dim val_int As Integer
    val_int = val
    If flag = True And val_int <> 0 Then
        DeleteOldFilesOrFolders ConfirmDelete, val_int
    End If
End Sub

Function SetRangeBorders(rowStart As Integer, colStart As Integer, rowEnd As Integer, colEnd As Integer, wb As Workbook)
    'Set ws = wb.Sheets("summary")
    Set ws = wb.Sheets(1)
    Dim rangeStr1 As String
    Dim rangeStr2 As String
    rangeStr1 = Cells(rowStart, colStart).Address(False, False)
    If colEnd > 0 Then
        rangeStr2 = Cells(rowEnd, colEnd).Address(False, False)
    Else
        rangeStr2 = Cells(rowEnd, colStart + 6).Address(False, False)
    End If

    With ws.Range(rangeStr1, rangeStr2).Borders
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlThin
    End With
    
    With ws.Range(rangeStr1, rangeStr2).Borders(xlEdgeLeft)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlMedium
    End With
    With ws.Range(rangeStr1, rangeStr2).Borders(xlEdgeTop)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlMedium
    End With
    With ws.Range(rangeStr1, rangeStr2).Borders(xlEdgeBottom)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlMedium
    End With
    With ws.Range(rangeStr1, rangeStr2).Borders(xlEdgeRight)
        .LineStyle = xlContinuous
        .ColorIndex = 0
        .TintAndShade = 0
        .Weight = xlMedium
    End With
End Function

Function ChangeCtaToAsl(ByVal folderPath As String) '宣告字串變數，用來儲存選擇的資料夾路徑

    Dim wb As Workbook
    Set wb = Workbooks.Open(folderPath)
    
    If checkSheetsHaveDatalog = 0 Then GoTo Finalx '2020/08/20
    For i = 2 To Sheets.count
        If (Sheets(i).name = "QAData" Or Sheets(i).name = "QA_Data") And Sheets(i).Cells(6, 1) <> "" Then '2020/11/27
            Sheets(1).Move After:=Sheets(Sheets.count)
            Sheets(i - 1).Move Before:=Sheets(1)
        End If
    Next i
    If checkSheetsHaveDatalog = 0 Then GoTo Finalx '2020/08/20
    ' 找到包含 "QAData" 的 sheet
    For Each ws In wb.Sheets
        If InStr(ws.name, "QAData") > 0 Then
            ' 計算 C 欄總共有幾行
            lastRow = ws.Cells(ws.rows.count, "C").End(xlUp).row
            ' 複製 C13 至最後一行且貼上到 A13 至最後一行
            ws.Range("C13:C" & lastRow).Copy Destination:=ws.Range("A13")
            Exit For ' 找到符合條件的 sheet 後就可以離開 For 迴圈了
        End If
    Next ws
    Dim onlyfilename As String '宣告字串變數，用來儲存選擇的資料夾路徑
    Dim onlyfilenameft As String '宣告字串變數，用來儲存選擇的資料夾路徑
    Dim onlyfilenameqc As String '宣告字串變數，用來儲存選擇的資料夾路徑
    onlyfilename = Left(folderPath, Len(folderPath) - 4)
    Call setAppl(1, xlCalculationManual, True) '2020/06/01
    For Each ws In wb.Worksheets
        If InStr(1, LCase(ws.name), "qadata", vbTextCompare) > 0 Or InStr(1, LCase(folderPath), "r1qc", vbTextCompare) Or InStr(1, LCase(folderPath), "r2qc", vbTextCompare) Or InStr(1, LCase(folderPath), "r3qc", vbTextCompare) Or InStr(1, LCase(folderPath), "eqc", vbTextCompare) Or InStr(1, LCase(folderPath), "qc", vbTextCompare) Then  '20241223 cta qc name check zzzzz
            ws.Range("B2").value = ws.Range("B2").value & " (qc)"
            ws.Move Before:=wb.Sheets(1)
        ElseIf InStr(1, LCase(ws.name), "data11", vbTextCompare) > 0 Then
            ws.Range("B2").value = ws.Range("B2").value & " (auto_qc)"
        ElseIf InStr(1, LCase(ws.name), "sum", vbTextCompare) > 0 Then
            ws.Move Before:=wb.Sheets(2)
        End If
    Next ws
    For Each ws In wb.Worksheets
        If InStr(1, LCase(ws.name), "sum", vbTextCompare) > 0 Then
            Application.DisplayAlerts = False
            ws.Delete
            Application.DisplayAlerts = True
        End If
    Next ws
    Dim aa2 As String
    
    For Each ws In wb.Worksheets
        aa2 = ws.Range("a2").value
        lastRow = ws.Cells(ws.rows.count, "A").End(xlUp).row
        If InStr(1, LCase(ws.name), "qadata", vbTextCompare) > 0 Then
            If InStr(1, LCase(aa2), "serial_no", vbTextCompare) = 0 Then
                ' 找到第二個 "_" 的位置
                'pos = InStr(InStr(1, onlyfilename, "_") + 1, onlyfilename, "_")
                '從後面往前找第2個_
                pos = InStrRev(onlyfilename, "_", InStrRev(onlyfilename, "_") - 1)
                If pos = 0 Then '20241203
                    pos = InStrRev(onlyfilename, "=", InStrRev(onlyfilename, "=") - 1)
                End If
                ' 將第二個 "_" 後面的字串替換為 "r1"
                onlyfilenameqc = Left(onlyfilename, pos - 1) & "_onlieEQC_" & Mid(onlyfilename, pos + 1) & ".csv"
                Application.DisplayAlerts = False
                ws.Copy
                Application.CutCopyMode = False
                Application.DisplayAlerts = True
                If Dir(onlyfilenameqc) <> "" Then
                    Kill onlyfilenameqc
                End If
                If lastRow > 12 Then
                    ActiveWorkbook.SaveAs fileName:=onlyfilenameqc, FileFormat:=xlCSV, CreateBackup:=False
                End If
                ActiveWorkbook.Close SaveChanges:=False
            Else
                 Application.DisplayAlerts = False
                ws.Delete
                Application.DisplayAlerts = True
                GoTo nextws
            End If
        End If
        
        If InStr(1, LCase(ws.name), "qadata", vbTextCompare) > 0 Then
            Application.DisplayAlerts = False
            ws.Delete
            Application.DisplayAlerts = True
            GoTo nextws
        End If
        If InStr(1, LCase(ws.name), "data11", vbTextCompare) > 0 Then
            ' 找到第二個 "_" 的位置
            'pos = InStr(InStr(1, onlyfilename, "_") + 1, onlyfilename, "_")
            '從後面往前找第2個_
             pos = InStrRev(onlyfilename, "_", InStrRev(onlyfilename, "_") - 1)
             If pos = 0 Then '20241203
                  pos = InStrRev(onlyfilename, "=", InStrRev(onlyfilename, "=") - 1)
              End If
            ' 將第二個 "_" 後面的字串替換為 "r1"
            'If InStr(1, ws.Range("B2").value, "qc", vbTextCompare) > 0 Or InStr(1, LCase(ws.Range("B2").value), "(_qc)", vbTextCompare) > 0 Then
            If InStr(1, LCase(ws.Range("B2").value), "(qc)", vbTextCompare) > 0 Or InStr(1, ws.Range("B2").value, "_QC") > 0 Then
                onlyfilenameft = Left(onlyfilename, pos - 1) & "_eqc_" & Mid(onlyfilename, pos + 1) & ".csv"
            Else
                onlyfilenameft = Left(onlyfilename, pos - 1) & "_ft_" & Mid(onlyfilename, pos + 1) & ".csv"
            End If
             If Dir(onlyfilenameft) <> "" Then
                Kill onlyfilenameft
            End If
            'ActiveWorkbook.SaveAs fileName:=onlyfilenameft, FileFormat:=xlCSV, CreateBackup:=False
            If lastRow > 12 Then
                ws.SaveAs fileName:=onlyfilenameft, FileFormat:=xlCSV, Local:=True
            End If
            ActiveWorkbook.Close SaveChanges:=False
        End If
nextws:
    Next ws
Finalx:
    On Error Resume Next
    If Not wb Is Nothing Then
        wb.Close SaveChanges:=False
    End If
     Call setAppl(0) '2020/06/01
    Set ws = Nothing
    Set wb = Nothing
End Function

Function InsEqcRtData(fileName As String, start_ As Integer, end_ As Integer)
    ' 使用 Excel 物件打開檔案
    Dim xlApp As Object
    Set xlApp = CreateObject("Excel.Application")
    Dim xlBook As Object
    Set xlBook = xlApp.Workbooks.Open(fileName)
    Dim xlSheet As Object
    Set xlSheet = xlBook.Sheets(1)

    
    ' 讀取 B14 到 BMax_Line 行的內容
    Dim lastRow As Integer
    lastRow = xlSheet.Range("B" & xlSheet.rows.count).End(xlUp).row '總行數
    Dim data() As Variant
    data = xlSheet.Range("B14:B" & lastRow).value
    
    
     Dim lastCol As Integer
    lastCol = xlSheet.Cells(13, xlSheet.Columns.count).End(xlToLeft).Column '總欄位
    
     ' 找到不等於 1 的行
    Dim i As Integer
    Dim j As Integer
    Dim zz As Integer
    Dim yy As Integer
    Dim foundMatch As Boolean
    Dim allzero As Boolean
    foundMatch = True
    zz = 6
    For i = LBound(data) To UBound(data) - 1
        foundMatch = True
        allzero = True
        Dim k As Integer
        For j = i + 1 To UBound(data)
            For k = start_ To end_
                If xlSheet.Cells(i + 13, k).value <> xlSheet.Cells(j + 13, k).value Then
                    foundMatc = False
                    GoTo nnn
                End If
            Next k
            For k = start_ To end_
                If xlSheet.Cells(i + 13, k).value <> 0 Then
                   allzero = False
                   Exit For
                End If
            Next k
            If allzero = False Then
                xlSheet.Range(xlSheet.Cells(i + 13, 1), xlSheet.Cells(j + 13, end_)).Interior.ColorIndex = zz
                'xlSheet.Range(xlSheet.Cells(i + 13, 2), xlSheet.Cells(j + 13, 3)).Interior.ColorIndex = zz
                xlSheet.Range(xlSheet.Cells(i + 13, start_), xlSheet.Cells(j + 13, end_)).Font.Bold = True
            End If
nnn:
            If foundMatc = False Then '20241210 mark
                Exit For
            End If
        Next j
    Next i
     '保存檔案並關閉 Excel 物件
    If Not xlBook Is Nothing Then
        xlBook.Save
        xlBook.Close
        xlApp.Quit
    End If
InsEqcRtData_next:
End Function
Function InsEqcRtData2(fileName As String, start_ As Integer, end_ As Integer, Min_Line As Integer, Max_Line As Integer, ByRef rows As Variant)
    
    
    Dim lineElementsA As Variant
    Dim lineElementsB As Variant
    'Dim rows As Variant
    Dim foundMatch As Boolean
    Dim allzero As Boolean
    
    foundMatch = True
    zz = 6
    
    Dim numRows As Integer

    numRows = UBound(rows) - 12
    For i = Min_Line To Max_Line - 13
        Min_Line = i
        lineElementsA = Split(rows(i + 12), ",")
        If lineElementsA(1) <> 1 Then '找到不為1的
            'For j = numRows To Max_Line + 1 Step -1
             For j = Max_Line - 12 To (UBound(rows) - 12)
                allzero = True
                foundMatch = True
                Dim k As Integer
                If rows(j + 12) <> "" Then
                    lineElementsB = Split(rows(j + 12), ",")
                    For k = start_ To end_
                        If lineElementsB(k) <> lineElementsA(k) Then
                            foundMatch = False
                            Exit For
                        End If
                    Next k
                    For k = start_ To end_
                        If lineElementsA(k) <> 0 Then
                           allzero = False
                           Exit For
                        End If
                    Next k
                    If foundMatch And allzero = False Then
                        Min_Line = Min_Line + 1
                        Max_Line = Max_Line + 1
                        Dim newRow As String
                        newRow = Join(lineElementsB, ",")
                        If rows(j + 12) <> "" Then
                            rows(j + 12) = ""
                            For k = j + 12 To numRows + 12 - 1
                                rows(k) = rows(k + 1)
                            Next k
                        End If
                        
                        If rows(i + 12) <> "" Then
                            numRows = numRows + 1
                            ReDim Preserve rows(numRows + 12)
                            For k = numRows + 12 - 1 To i + 12 + 1 Step -1
                                rows(k + 1) = rows(k)
                            Next k
                            rows(i + 12 + 1) = ""
                        End If
                        rows(i + 12 + 1) = newRow
                       
                        ReDim Preserve rows(numRows + 12 - 1)
                        

                        InsEqcRtData2 fileName, start_, end_, Min_Line, Max_Line, rows
'                        zz = zz + 1
                        GoTo InsEqcRtData2_next
                        Exit For
                    End If
                End If
            Next j
        End If
    Next i
InsEqcRtData2_next:
    ' 將更新後的內容重新存回檔案
    'fileName = "E:\temp\WNTM292330168_770682\EQCTOTALDATA" & Min_Line & ".csv"
    Dim outFile As Object
    Set outFile = CreateObject("Scripting.FileSystemObject").CreateTextFile(fileName)
    outFile.Write Join(rows, vbCrLf)
    outFile.Close
    Set outFile = Nothing
End Function
Function FindALLEQCFILE(csvfile As Variant) As Variant
    Dim i As Integer
    Dim csvFilesx() As String
    Dim fileList() As Variant
    csvFilesx = csvfile
    ReDim fileList(1 To 1)
    For i = 0 To UBound(csvFilesx)
        If CheckEQCCSVFile(csvFilesx(i)) = True Then
            If j = 0 Then
                fileList(1) = Array(CheckDataTime(csvFilesx(i)), csvFilesx(i))
            Else
                AddFileToList CheckDataTime(csvFilesx(i)), csvFilesx(i), fileList
            End If
            j = j + 1
        End If
    Next i
    FindALLEQCFILE = fileList
End Function
Function FindALLFTFILE(csvfile As Variant) As Variant
    Dim i As Integer
    Dim csvFilesx() As String
    Dim fileList() As Variant
    csvFilesx = csvfile
    ReDim fileList(1 To 1)
    For i = 0 To UBound(csvFilesx)
        If CheckFTCSVFile(csvFilesx(i)) = True Then
            If j = 0 Then
                fileList(1) = Array(CheckDataTime(csvFilesx(i)), csvFilesx(i))
            Else
                AddFileToList CheckDataTime(csvFilesx(i)), csvFilesx(i), fileList
            End If
            j = j + 1
        End If
    Next i
    FindALLFTFILE = fileList
End Function
Function CheckFTCSVFile(ByVal filePath As String) As Boolean
    Dim fileContent As String
     Dim rows As Variant
     Dim elements As Variant
     Dim secondLine As String
     Dim secondElement As String
    If filePath <> "" Then
        If InStr(1, LCase(filePath), "eqctotaldata", vbTextCompare) = 0 And InStr(1, LCase(filePath), "eqcfaildata", vbTextCompare) = 0 And InStr(1, filePath, "qa", vbTextCompare) = 0 Or InStr(1, filePath, "1621qa", vbTextCompare) > 0 Then
            Dim fileA As Object
            Set fileA = CreateObject("Scripting.FileSystemObject").GetFile(filePath)
            Open fileA For Input As #1
             For i = 0 To 1
                Line Input #1, fileLine
                If InStr(1, LCase(fileLine), "(ft)", vbTextCompare) > 0 Or InStr(1, LCase(fileLine), "(auto_qc)", vbTextCompare) > 0 Then 'Or InStr(1, LCase(fileLine), "(qc)", vbTextCompare) > 0
                    If i = 1 Then
                        Line Input #1, fileLine
                        If InStr(1, fileLine, "qa", vbTextCompare) = 0 Then
                            CheckFTCSVFile = True
                        End If
                    End If
                    Close #1
                    Exit Function
                End If
            Next i
            Close #1
        End If
    End If
    CheckFTCSVFile = False
End Function
Sub Send_LINE_Notify_Message(ByVal message As String, ByVal personalAccessToken As String)
'    Dim xmlHttp As WinHttp.WinHttpRequest
'    Set xmlHttp = New WinHttp.WinHttpRequest
'
'    Dim apiUrl As String
'    apiUrl = "https://notify-api.line.me/api/notify"
'
'    xmlHttp.Open "POST", apiUrl, False
'    xmlHttp.setRequestHeader "Content-Type", "application/x-www-form-urlencoded"
'    xmlHttp.setRequestHeader "Authorization", "Bearer " & personalAccessToken
'
'    Dim postData As String
'    postData = "message=" & message
'
'    xmlHttp.Send postData
End Sub
Sub TestSend_LINE_Notify_Message(ByVal message As String)
    Dim personalAccessToken As String

    personalAccessToken = "ODMnfwSfWqg0HuSLejn7owWJhIvOkHBsfQeRbEqRmnK"
    
    Send_LINE_Notify_Message message, personalAccessToken
End Sub

