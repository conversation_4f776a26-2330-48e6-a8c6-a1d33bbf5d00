#!/usr/bin/env python3
"""
Fix email_download_status enum value case mismatch
Updates lowercase values to match SQLAlchemy model expectations
"""

import sqlite3
import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/enum_fix.log', mode='a'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EmailDownloadStatusFixer:
    """Fix enum values in email_download_status table"""
    
    def __init__(self, db_path="email_inbox.db"):
        self.db_path = Path(db_path)
        
    def check_database_exists(self):
        """Check if database file exists"""
        if not self.db_path.exists():
            logger.error(f"Database file not found: {self.db_path}")
            return False
        return True
    
    def check_table_exists(self, conn):
        """Check if email_download_status table exists"""
        cursor = conn.cursor()
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='email_download_status'
        """)
        result = cursor.fetchone()
        return result is not None
    
    def analyze_current_status(self):
        """Analyze current enum values in the database"""
        try:
            if not self.check_database_exists():
                return None
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if table exists
            if not self.check_table_exists(conn):
                logger.warning("email_download_status table does not exist")
                conn.close()
                return None
            
            # Get current status values and counts
            cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM email_download_status
                GROUP BY status
                ORDER BY count DESC
            """)
            
            status_counts = cursor.fetchall()
            
            # Get total record count
            cursor.execute("SELECT COUNT(*) FROM email_download_status")
            total_records = cursor.fetchone()[0]
            
            # Get sample records for verification
            cursor.execute("""
                SELECT id, email_id, status, created_at 
                FROM email_download_status 
                LIMIT 5
            """)
            sample_records = cursor.fetchall()
            
            conn.close()
            
            analysis = {
                'total_records': total_records,
                'status_distribution': status_counts,
                'sample_records': sample_records,
                'needs_fix': False,
                'fix_mapping': {}
            }
            
            # Check if any values need to be fixed
            lowercase_values = {'completed', 'pending', 'downloading', 'failed', 'retry_scheduled'}
            uppercase_values = {'COMPLETED', 'PENDING', 'DOWNLOADING', 'FAILED', 'RETRY_SCHEDULED'}
            
            for status_value, count in status_counts:
                if status_value in lowercase_values:
                    analysis['needs_fix'] = True
                    analysis['fix_mapping'][status_value] = status_value.upper()
            
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze database: {str(e)}")
            return None
    
    def create_backup_before_fix(self):
        """Create a backup before applying fixes"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = Path(f"backups/email_inbox_pre_enum_fix_{timestamp}.db")
            
            # Ensure backup directory exists
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create backup
            import shutil
            shutil.copy2(self.db_path, backup_path)
            
            # Verify backup
            conn = sqlite3.connect(backup_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM email_download_status")
            backup_count = cursor.fetchone()[0]
            conn.close()
            
            logger.info(f"Backup created: {backup_path} ({backup_count} records)")
            return backup_path
            
        except Exception as e:
            logger.error(f"Failed to create backup: {str(e)}")
            return None
    
    def apply_enum_fixes(self, analysis, create_backup=True):
        """Apply the enum value fixes"""
        try:
            if not analysis or not analysis['needs_fix']:
                logger.info("No enum fixes needed")
                return True
            
            # Create backup if requested
            if create_backup:
                backup_path = self.create_backup_before_fix()
                if not backup_path:
                    logger.error("Cannot proceed without backup")
                    return False
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Begin transaction
            cursor.execute("BEGIN TRANSACTION")
            
            updated_records = 0
            
            # Apply each fix
            for old_value, new_value in analysis['fix_mapping'].items():
                cursor.execute("""
                    UPDATE email_download_status 
                    SET status = ? 
                    WHERE status = ?
                """, (new_value, old_value))
                
                affected_rows = cursor.rowcount
                updated_records += affected_rows
                logger.info(f"Updated {affected_rows} records: '{old_value}' -> '{new_value}'")
            
            # Commit changes
            cursor.execute("COMMIT")
            
            logger.info(f"Successfully updated {updated_records} records")
            
            # Verify the changes
            cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM email_download_status
                GROUP BY status
                ORDER BY count DESC
            """)
            
            new_status_counts = cursor.fetchall()
            logger.info("Updated status distribution:")
            for status, count in new_status_counts:
                logger.info(f"  {status}: {count} records")
            
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Failed to apply enum fixes: {str(e)}")
            try:
                cursor.execute("ROLLBACK")
                conn.close()
            except:
                pass
            return False
    
    def verify_fixes(self):
        """Verify that the fixes were applied correctly"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check for any remaining lowercase values
            cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM email_download_status
                WHERE status IN ('completed', 'pending', 'downloading', 'failed', 'retry_scheduled')
                GROUP BY status
            """)
            
            remaining_lowercase = cursor.fetchall()
            
            # Get current distribution
            cursor.execute("""
                SELECT status, COUNT(*) as count
                FROM email_download_status
                GROUP BY status
                ORDER BY count DESC
            """)
            
            current_distribution = cursor.fetchall()
            
            conn.close()
            
            verification = {
                'remaining_lowercase_count': len(remaining_lowercase),
                'remaining_lowercase_values': remaining_lowercase,
                'current_distribution': current_distribution,
                'fix_successful': len(remaining_lowercase) == 0
            }
            
            return verification
            
        except Exception as e:
            logger.error(f"Failed to verify fixes: {str(e)}")
            return None

def main():
    """Main function"""
    print("Email Download Status Enum Fixer")
    print("=" * 50)
    
    fixer = EmailDownloadStatusFixer()
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Fix email_download_status enum values")
    parser.add_argument("--analyze-only", action="store_true", help="Only analyze current status")
    parser.add_argument("--no-backup", action="store_true", help="Skip backup creation")
    parser.add_argument("--verify-only", action="store_true", help="Only verify current status")
    
    args = parser.parse_args()
    
    if args.verify_only:
        # Just verify current status
        verification = fixer.verify_fixes()
        if verification:
            if verification['fix_successful']:
                print("SUCCESS: All enum values are correct")
            else:
                print("WARNING: Found remaining lowercase values:")
                for status, count in verification['remaining_lowercase_values']:
                    print(f"  {status}: {count} records")
        return
    
    # Step 1: Analyze current status
    print("Step 1: Analyzing current database status...")
    analysis = fixer.analyze_current_status()
    
    if not analysis:
        print("ERROR: Could not analyze database")
        return
    
    print(f"Found {analysis['total_records']} total records")
    print("Current status distribution:")
    for status, count in analysis['status_distribution']:
        print(f"  {status}: {count} records")
    
    if not analysis['needs_fix']:
        print("SUCCESS: No enum fixes needed - all values are already correct")
        return
    
    print("\nFixes needed:")
    for old_value, new_value in analysis['fix_mapping'].items():
        old_count = next((count for status, count in analysis['status_distribution'] if status == old_value), 0)
        print(f"  '{old_value}' -> '{new_value}' ({old_count} records)")
    
    if args.analyze_only:
        print("\n(Analysis only - no changes made)")
        return
    
    # Step 2: Apply fixes
    print(f"\nStep 2: Applying enum fixes...")
    success = fixer.apply_enum_fixes(analysis, create_backup=not args.no_backup)
    
    if not success:
        print("ERROR: Failed to apply fixes")
        return
    
    # Step 3: Verify fixes
    print("\nStep 3: Verifying fixes...")
    verification = fixer.verify_fixes()
    
    if verification and verification['fix_successful']:
        print("SUCCESS: All enum values have been successfully updated!")
        print("Final status distribution:")
        for status, count in verification['current_distribution']:
            print(f"  {status}: {count} records")
    else:
        print("ERROR: Verification failed - some issues remain")
        if verification and verification['remaining_lowercase_values']:
            print("Remaining lowercase values:")
            for status, count in verification['remaining_lowercase_values']:
                print(f"  {status}: {count} records")

if __name__ == "__main__":
    # Ensure logs directory exists
    Path("logs").mkdir(exist_ok=True)
    main()