"""
Email Status Management Service
Handles business logic for email status tracking fields with validation
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.exc import InvalidRequestError

from backend.shared.infrastructure.adapters.database.models import EmailDB


class EmailStatusValidationError(ValueError):
    """Custom exception for email status validation errors"""
    pass


class EmailStatusService:
    """Service class for managing email status with business rule validation"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def validate_download_status(self, success: bool, completed_at: Optional[datetime], 
                                created_at: datetime) -> None:
        """
        Validate download status business rules
        
        Args:
            success: Download success flag
            completed_at: Download completion timestamp
            created_at: Email creation timestamp
            
        Raises:
            EmailStatusValidationError: If validation fails
        """
        if success and completed_at is None:
            raise EmailStatusValidationError(
                "download_success=True requires download_completed_at to be set"
            )
        
        if completed_at and completed_at < created_at:
            raise EmailStatusValidationError(
                "download_completed_at must be after created_at"
            )
    
    def validate_processing_status(self, success: bool, completed_at: Optional[datetime],
                                 created_at: datetime) -> None:
        """
        Validate processing status business rules
        
        Args:
            success: Processing success flag
            completed_at: Processing completion timestamp
            created_at: Email creation timestamp
            
        Raises:
            EmailStatusValidationError: If validation fails
        """
        if success and completed_at is None:
            raise EmailStatusValidationError(
                "processing_success=True requires processing_completed_at to be set"
            )
        
        if completed_at and completed_at < created_at:
            raise EmailStatusValidationError(
                "processing_completed_at must be after created_at"
            )
    
    def update_download_success(self, email_id: int, success: bool, 
                              completed_at: Optional[datetime] = None) -> bool:
        """
        Update download success status with validation
        
        Args:
            email_id: Email record ID
            success: Download success flag
            completed_at: Optional completion timestamp (defaults to now if success=True)
            
        Returns:
            bool: True if update successful
            
        Raises:
            EmailStatusValidationError: If validation fails
        """
        email = self.session.query(EmailDB).get(email_id)
        if not email:
            return False
        
        # Set default timestamp if success=True and no timestamp provided
        if success and completed_at is None:
            completed_at = datetime.utcnow()
        
        # Validate business rules
        self.validate_download_status(success, completed_at, email.created_at)
        
        # Update the record
        email.download_success = success
        email.download_completed_at = completed_at
        self.session.commit()
        
        return True
    
    def update_processing_success(self, email_id: int, success: bool,
                                completed_at: Optional[datetime] = None) -> bool:
        """
        Update processing success status with validation
        
        Args:
            email_id: Email record ID
            success: Processing success flag
            completed_at: Optional completion timestamp (defaults to now if success=True)
            
        Returns:
            bool: True if update successful
            
        Raises:
            EmailStatusValidationError: If validation fails
        """
        email = self.session.query(EmailDB).get(email_id)
        if not email:
            return False
        
        # Set default timestamp if success=True and no timestamp provided
        if success and completed_at is None:
            completed_at = datetime.utcnow()
        
        # Validate business rules
        self.validate_processing_status(success, completed_at, email.created_at)
        
        # Update the record
        email.processing_success = success
        email.processing_completed_at = completed_at
        self.session.commit()
        
        return True
    
    def get_status_summary(self, email_id: int) -> Optional[dict]:
        """
        Get comprehensive status summary for an email
        
        Args:
            email_id: Email record ID
            
        Returns:
            dict: Status summary or None if email not found
        """
        email = self.session.query(EmailDB).get(email_id)
        if not email:
            return None
        
        return {
            'email_id': email.id,
            'message_id': email.message_id,
            'download_success': email.download_success,
            'processing_success': email.processing_success,
            'download_completed_at': email.download_completed_at,
            'processing_completed_at': email.processing_completed_at,
            'created_at': email.created_at,
            'is_fully_processed': email.download_success and email.processing_success
        }
    
    def bulk_update_download_status(self, email_ids: List[int], success: bool) -> int:
        """
        Bulk update download status for multiple emails
        
        Args:
            email_ids: List of email IDs to update
            success: Download success flag
            
        Returns:
            int: Number of emails updated successfully
        """
        updated_count = 0
        now = datetime.utcnow() if success else None
        
        for email_id in email_ids:
            try:
                if self.update_download_success(email_id, success, now):
                    updated_count += 1
            except EmailStatusValidationError:
                continue  # Skip invalid updates
        
        return updated_count
    
    def bulk_update_processing_status(self, email_ids: List[int], success: bool) -> int:
        """
        Bulk update processing status for multiple emails
        
        Args:
            email_ids: List of email IDs to update
            success: Processing success flag
            
        Returns:
            int: Number of emails updated successfully
        """
        updated_count = 0
        now = datetime.utcnow() if success else None
        
        for email_id in email_ids:
            try:
                if self.update_processing_success(email_id, success, now):
                    updated_count += 1
            except EmailStatusValidationError:
                continue  # Skip invalid updates
        
        return updated_count
    
    def get_emails_by_status(self, download_success: Optional[bool] = None,
                           processing_success: Optional[bool] = None,
                           limit: int = 100) -> List[EmailDB]:
        """
        Query emails by status fields
        
        Args:
            download_success: Filter by download success status
            processing_success: Filter by processing success status
            limit: Maximum number of results
            
        Returns:
            List[EmailDB]: List of matching email records
        """
        query = self.session.query(EmailDB)
        
        if download_success is not None:
            query = query.filter(EmailDB.download_success == download_success)
        
        if processing_success is not None:
            query = query.filter(EmailDB.processing_success == processing_success)
        
        return query.limit(limit).all()
    
    def get_status_statistics(self) -> dict:
        """
        Get comprehensive status statistics
        
        Returns:
            dict: Statistics summary
        """
        total_emails = self.session.query(EmailDB).count()
        
        download_success_count = self.session.query(EmailDB).filter(
            EmailDB.download_success == True
        ).count()
        
        processing_success_count = self.session.query(EmailDB).filter(
            EmailDB.processing_success == True
        ).count()
        
        fully_processed_count = self.session.query(EmailDB).filter(
            EmailDB.download_success == True,
            EmailDB.processing_success == True
        ).count()
        
        return {
            'total_emails': total_emails,
            'download_success_count': download_success_count,
            'processing_success_count': processing_success_count,
            'fully_processed_count': fully_processed_count,
            'download_success_rate': download_success_count / total_emails if total_emails > 0 else 0,
            'processing_success_rate': processing_success_count / total_emails if total_emails > 0 else 0,
            'fully_processed_rate': fully_processed_count / total_emails if total_emails > 0 else 0
        }
    
    def reset_email_status(self, email_id: int) -> bool:
        """
        Reset email status to initial state (all False/None)
        
        Args:
            email_id: Email record ID
            
        Returns:
            bool: True if reset successful
        """
        email = self.session.query(EmailDB).get(email_id)
        if not email:
            return False
        
        email.download_success = False
        email.processing_success = False
        email.download_completed_at = None
        email.processing_completed_at = None
        
        self.session.commit()
        return True
    
    @staticmethod
    def validate_email_status_consistency(email: EmailDB) -> None:
        """
        Validate email status consistency before commit
        
        Args:
            email: EmailDB instance to validate
            
        Raises:
            EmailStatusValidationError: If validation fails
        """
        # Validate download status
        if email.download_success and email.download_completed_at is None:
            raise EmailStatusValidationError(
                "download_success=True requires download_completed_at to be set"
            )
        
        if (email.download_completed_at and 
            email.created_at and 
            email.download_completed_at < email.created_at):
            raise EmailStatusValidationError(
                "download_completed_at must be after created_at"
            )
        
        # Validate processing status
        if email.processing_success and email.processing_completed_at is None:
            raise EmailStatusValidationError(
                "processing_success=True requires processing_completed_at to be set"
            )
        
        if (email.processing_completed_at and 
            email.created_at and 
            email.processing_completed_at < email.created_at):
            raise EmailStatusValidationError(
                "processing_completed_at must be after created_at"
            )