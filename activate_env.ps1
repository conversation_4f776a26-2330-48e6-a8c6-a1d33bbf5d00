# Outlook Summary Email Processing System - Development Environment Setup Script
# Support for new modular Flask frontend architecture
# Usage: . .\dev_env.ps1

# Set console output encoding to UTF-8 for Windows
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "[START] Outlook Email Processing System - Development Environment Setup" -ForegroundColor Magenta
Write-Host "=" * 70 -ForegroundColor Gray

# Set encoding environment variables
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONUTF8 = "1"
$env:LANG = "en_US.UTF-8"
Write-Host "[OK] Set encoding environment variables" -ForegroundColor Green

# Check virtual environment
if (Test-Path ".\venv_win_3_11_9\Scripts\Activate.ps1") {
    Write-Host "[OK] Activating virtual environment (venv_win_3_11_9)" -ForegroundColor Green
    . ".\venv_win_3_11_9\Scripts\Activate.ps1"
    
    # Set Flask environment variables - Support for new modular architecture
    $env:FLASK_APP = "frontend.app:create_app"
    $env:FLASK_ENV = "development"
    $env:FLASK_DEBUG = "True"
    $env:FLASK_RUN_HOST = "0.0.0.0"
    $env:FLASK_RUN_PORT = "5000"
    Write-Host "[OK] Set Flask environment variables (modular architecture)" -ForegroundColor Green
    
    # Check frontend directory structure
    if (Test-Path ".\frontend\app.py") {
        Write-Host "[OK] Detected new modular frontend structure" -ForegroundColor Green
    } else {
        Write-Host "[WARNING] Modular frontend structure not detected" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "[READY] Development environment is ready! Available commands:" -ForegroundColor Yellow
    Write-Host "=" * 60 -ForegroundColor Gray
    
    Write-Host "[FRONTEND] Frontend Application:" -ForegroundColor Cyan
    Write-Host "  python frontend/app.py                    # Direct frontend application startup" -ForegroundColor White
    Write-Host "  flask run                                 # Start using Flask CLI" -ForegroundColor White
    Write-Host "  make run-frontend                         # Start frontend using Makefile" -ForegroundColor White
    
    Write-Host ""
    Write-Host "[SERVICES] Integrated Services:" -ForegroundColor Cyan
    Write-Host "  python start_integrated_services.py      # Start all backend services" -ForegroundColor White
    Write-Host "  make run-services                         # Start services using Makefile" -ForegroundColor White
    
    Write-Host ""
    Write-Host "[TESTING] Testing and Quality:" -ForegroundColor Cyan
    Write-Host "  pytest                                    # Run all tests" -ForegroundColor White
    Write-Host "  make test                                 # Run tests using Makefile" -ForegroundColor White
    Write-Host "  make quality-check                        # Code quality check" -ForegroundColor White
    
    Write-Host ""
    Write-Host "[TOOLS] Development Tools:" -ForegroundColor Cyan
    Write-Host "  make help                                 # View all available commands" -ForegroundColor White
    Write-Host "  make dev-setup                            # Reset development environment" -ForegroundColor White
    
    Write-Host ""
    Write-Host "[CONFIG] Current Configuration:" -ForegroundColor Yellow
    Write-Host "  Application: $env:FLASK_APP" -ForegroundColor White
    Write-Host "  Environment: $env:FLASK_ENV" -ForegroundColor White
    Write-Host "  Debug Mode:  $env:FLASK_DEBUG" -ForegroundColor White
    Write-Host "  Listen Host: $env:FLASK_RUN_HOST" -ForegroundColor White
    Write-Host "  Listen Port: $env:FLASK_RUN_PORT" -ForegroundColor White
    Write-Host "  Encoding:    UTF-8 [OK]" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "[MODULES] Modular Frontend Architecture:" -ForegroundColor Yellow
    Write-Host "  [EMAIL]    Email Processing  - /email/*" -ForegroundColor White
    Write-Host "  [ANALYTICS] Data Analytics   - /analytics/*" -ForegroundColor White
    Write-Host "  [FILES]    File Management   - /files/*" -ForegroundColor White
    Write-Host "  [EQC]      Quality Control   - /eqc/*" -ForegroundColor White
    Write-Host "  [TASKS]    Task Processing   - /tasks/*" -ForegroundColor White
    Write-Host "  [MONITOR]  System Monitoring - /monitoring/*" -ForegroundColor White
    
    Write-Host ""
    Write-Host "[QUICK START] Run 'python frontend/app.py' to start the application" -ForegroundColor Green
    
} else {
    Write-Host "[ERROR] Virtual environment not found!" -ForegroundColor Red
    Write-Host "Please run the following commands to create virtual environment:" -ForegroundColor Yellow
    Write-Host "  python -m venv venv_win_3_11_9" -ForegroundColor White
    Write-Host "  make dev-setup" -ForegroundColor White
}