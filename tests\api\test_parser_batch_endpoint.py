#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接測試 API 端點
模擬前端的實際請求
"""

import requests
import json
import time
import sys

def test_api_endpoint():
    """測試批次解析 API 端點"""
    print("直接測試 API 端點")
    print("=" * 50)
    
    # API 端點配置
    base_url = "http://localhost:5000"
    endpoint = "/email/api/parser/emails/batch-parse"
    full_url = f"{base_url}{endpoint}"
    
    # 請求 headers（模擬前端）
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': 'dev-parser-key-12345'
    }
    
    # 請求 payload
    payload = {
        'failed_only': False,
        'limit': 10
    }
    
    print(f"測試 URL: {full_url}")
    print(f"Headers: {headers}")
    print(f"Payload: {payload}")
    print("-" * 50)
    
    try:
        # 發送 POST 請求
        print("發送 POST 請求...")
        response = requests.post(
            full_url,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"狀態碼: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 請求成功!")
            try:
                data = response.json()
                print(f"回應數據: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except:
                print(f"回應內容: {response.text}")
        elif response.status_code == 401:
            print("❌ 401 UNAUTHORIZED - API 認證失敗")
            print(f"錯誤內容: {response.text}")
        else:
            print(f"❌ 請求失敗: {response.status_code}")
            print(f"錯誤內容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 連接失敗 - 服務可能未啟動")
        print("請確認服務在 http://localhost:5000 運行")
    except requests.exceptions.Timeout:
        print("❌ 請求超時")
    except Exception as e:
        print(f"❌ 請求異常: {e}")

def test_health_check():
    """測試健康檢查端點"""
    print("\n測試健康檢查")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        print(f"健康檢查狀態: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"服務狀態: {data.get('status')}")
            print(f"模組狀態: {data.get('modules', {})}")
        else:
            print(f"健康檢查失敗: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 服務未運行")
        return False
    except Exception as e:
        print(f"❌ 健康檢查異常: {e}")
        return False
    
    return True

def test_auth_debug():
    """測試認證調試端點"""
    print("\n測試認證調試")
    print("=" * 50)
    
    url = "http://localhost:5000/email/api/parser/debug/auth"
    headers = {'X-API-Key': 'dev-parser-key-12345'}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"認證調試狀態: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 認證調試端點正常")
            
            auth_config = data.get('auth_config', {})
            print(f"SKIP_API_AUTH: {auth_config.get('SKIP_API_AUTH_env')}")
            print(f"認證跳過: {auth_config.get('skip_auth_parsed')}")
            print(f"允許的 keys: {auth_config.get('allowed_keys_count')} 個")
            
            request_info = data.get('request_info', {})
            print(f"提供的 key: {request_info.get('api_key_provided')}")
            print(f"Key 有效: {request_info.get('api_key_valid')}")
            
        else:
            print(f"❌ 認證調試失敗: {response.text}")
            
    except Exception as e:
        print(f"❌ 認證調試異常: {e}")


def test_parser_statistics():
    """測試解析統計端點"""
    print("\n測試解析統計")
    print("=" * 50)
    
    url = "http://localhost:5000/email/api/parser/statistics"
    headers = {'X-API-Key': 'dev-parser-key-12345'}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"統計端點狀態: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 統計端點正常")
            if data.get('success'):
                db_stats = data.get('data', {}).get('database_stats', {})
                print(f"總郵件數: {db_stats.get('total_emails', 0)}")
                print(f"待解析: {db_stats.get('pending_emails', 0)}")
                print(f"已解析: {db_stats.get('parsed_emails', 0)}")
                print(f"失敗: {db_stats.get('failed_emails', 0)}")
        else:
            print(f"❌ 統計端點失敗: {response.text}")
            
    except Exception as e:
        print(f"❌ 統計端點異常: {e}")

def wait_for_service():
    """等待服務啟動"""
    print("等待服務啟動...")
    for i in range(30):  # 等待最多 30 秒
        try:
            response = requests.get("http://localhost:5000/health", timeout=2)
            if response.status_code == 200:
                print(f"✅ 服務已啟動 (耗時 {i+1} 秒)")
                return True
        except:
            pass
        
        print(f"等待中... ({i+1}/30)")
        time.sleep(1)
    
    print("❌ 服務啟動超時")
    return False

def main():
    """主測試流程"""
    print("API 端點直接測試")
    print("用於診斷批次解析端點的問題")
    print("=" * 50)
    
    # 1. 檢查服務是否運行
    if not test_health_check():
        print("\n服務未運行，嘗試等待...")
        if not wait_for_service():
            print("無法連接到服務，請手動啟動:")
            print("python start_integrated_services.py")
            return
    
    # 2. 測試認證調試端點
    test_auth_debug()
    
    # 3. 測試解析統計端點
    test_parser_statistics()
    
    # 4. 測試批次解析端點
    test_api_endpoint()
    
    print("\n測試完成")

if __name__ == '__main__':
    main()