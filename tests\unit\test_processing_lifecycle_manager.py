"""
測試 Epic-03 Story 3.2: ProcessingLifecycleManager
處理狀態生命週期管理的完整測試
"""

import pytest
from datetime import datetime, timedelta
from typing import Dict, Any

from backend.shared.services.processing_lifecycle_manager import (
    ProcessingLifecycleManager,
    StatusTransition,
    LifecycleEvent
)
from backend.shared.services.process_status_enums import ProcessStatus


class TestProcessingLifecycleManager:
    """ProcessingLifecycleManager 完整測試"""
    
    @pytest.fixture
    def lifecycle_manager(self):
        """創建生命週期管理器實例"""
        return ProcessingLifecycleManager()
    
    # ===========================================
    # 狀態轉換驗證測試
    # ===========================================
    
    def test_valid_status_transitions(self, lifecycle_manager):
        """測試有效的狀態轉換"""
        # PENDING -> PARSING
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.PENDING.value,
            ProcessStatus.PARSING.value
        ) == True
        
        # PARSING -> PROCESSING
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.PARSING.value,
            ProcessStatus.PROCESSING.value,
            metadata={'parsing_completed': True, 'parsed_data': 'test'}
        ) == True
        
        # PROCESSING -> VALIDATION
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.PROCESSING.value,
            ProcessStatus.VALIDATION.value,
            metadata={'processing_completed': True, 'processed_data': 'test'}
        ) == True
        
        # VALIDATION -> COMPLETED
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.VALIDATION.value,
            ProcessStatus.COMPLETED.value,
            metadata={'validation_passed': True, 'final_result': 'test'}
        ) == True
    
    def test_invalid_status_transitions(self, lifecycle_manager):
        """測試無效的狀態轉換"""
        # PENDING -> COMPLETED (跳過中間步驟)
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.PENDING.value,
            ProcessStatus.COMPLETED.value
        ) == False
        
        # COMPLETED -> PARSING (終止狀態不能轉換)
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.COMPLETED.value,
            ProcessStatus.PARSING.value
        ) == False
        
        # FAILED -> PROCESSING (終止狀態不能轉換)
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.FAILED.value,
            ProcessStatus.PROCESSING.value
        ) == False
    
    def test_conditional_transitions(self, lifecycle_manager):
        """測試條件性轉換"""
        # PARSING -> PROCESSING 需要 parsing_completed 條件
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.PARSING.value,
            ProcessStatus.PROCESSING.value,
            metadata={'parsing_completed': False, 'parsed_data': 'test'}
        ) == False
        
        # PARSING -> FAILED 需要 parsing_error 條件
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.PARSING.value,
            ProcessStatus.FAILED.value,
            metadata={'parsing_error': 'Test error'}
        ) == True
        
        # VALIDATION -> FAILED 需要 validation_failed 條件
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.VALIDATION.value,
            ProcessStatus.FAILED.value,
            metadata={'validation_failed': True}
        ) == True
    
    def test_metadata_requirements(self, lifecycle_manager):
        """測試元數據要求"""
        # PARSING -> PROCESSING 需要 parsed_data
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.PARSING.value,
            ProcessStatus.PROCESSING.value,
            metadata={'parsing_completed': True}  # 缺少 parsed_data
        ) == False
        
        # PROCESSING -> VALIDATION 需要 processed_data
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.PROCESSING.value,
            ProcessStatus.VALIDATION.value,
            metadata={'processing_completed': True}  # 缺少 processed_data
        ) == False
        
        # VALIDATION -> COMPLETED 需要 final_result
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.VALIDATION.value,
            ProcessStatus.COMPLETED.value,
            metadata={'validation_passed': True}  # 缺少 final_result
        ) == False
    
    # ===========================================
    # 狀態變更處理測試
    # ===========================================
    
    def test_handle_status_change(self, lifecycle_manager):
        """測試狀態變更處理"""
        status_id = 123
        email_id = 456
        old_status = ProcessStatus.PENDING.value
        new_status = ProcessStatus.PARSING.value
        metadata = {'start_time': datetime.utcnow()}
        
        # 處理狀態變更
        lifecycle_manager.handle_status_change(
            status_id=status_id,
            email_id=email_id,
            old_status=old_status,
            new_status=new_status,
            metadata=metadata
        )
        
        # 驗證事件已記錄
        events = lifecycle_manager.get_lifecycle_events(status_id=status_id)
        assert len(events) == 1
        
        event = events[0]
        assert event.status_id == status_id
        assert event.email_id == email_id
        assert event.old_status == old_status
        assert event.new_status == new_status
        assert event.event_type == "status_change"
        assert event.metadata == metadata
    
    def test_multiple_status_changes(self, lifecycle_manager):
        """測試多個狀態變更"""
        status_id = 789
        email_id = 101112
        
        # 模擬完整的狀態變更流程
        status_flow = [
            (ProcessStatus.PENDING.value, ProcessStatus.PARSING.value),
            (ProcessStatus.PARSING.value, ProcessStatus.PROCESSING.value),
            (ProcessStatus.PROCESSING.value, ProcessStatus.VALIDATION.value),
            (ProcessStatus.VALIDATION.value, ProcessStatus.COMPLETED.value)
        ]
        
        for old_status, new_status in status_flow:
            lifecycle_manager.handle_status_change(
                status_id=status_id,
                email_id=email_id,
                old_status=old_status,
                new_status=new_status
            )
        
        # 驗證所有事件都已記錄
        events = lifecycle_manager.get_lifecycle_events(status_id=status_id)
        assert len(events) == 4
        
        # 驗證事件順序 (最新的在前)
        assert events[0].new_status == ProcessStatus.COMPLETED.value
        assert events[1].new_status == ProcessStatus.VALIDATION.value
        assert events[2].new_status == ProcessStatus.PROCESSING.value
        assert events[3].new_status == ProcessStatus.PARSING.value
    
    # ===========================================
    # 超時檢測測試
    # ===========================================
    
    def test_timeout_detection(self, lifecycle_manager):
        """測試超時檢測"""
        current_time = datetime.utcnow()
        
        # 模擬活躍狀態數據
        active_statuses = [
            {
                'id': 1,
                'email_id': 100,
                'status': ProcessStatus.PENDING.value,
                'started_at': current_time - timedelta(seconds=400)  # 超過 300 秒
            },
            {
                'id': 2,
                'email_id': 200,
                'status': ProcessStatus.PARSING.value,
                'started_at': current_time - timedelta(seconds=700)  # 超過 600 秒
            },
            {
                'id': 3,
                'email_id': 300,
                'status': ProcessStatus.PROCESSING.value,
                'started_at': current_time - timedelta(seconds=100)  # 未超時
            }
        ]
        
        # 檢查超時
        timeout_statuses = lifecycle_manager.check_timeouts(active_statuses)
        
        # 驗證檢測結果
        assert len(timeout_statuses) == 2
        
        # 驗證超時狀態信息
        timeout_ids = [status['status_id'] for status in timeout_statuses]
        assert 1 in timeout_ids  # PENDING 超時
        assert 2 in timeout_ids  # PARSING 超時
        assert 3 not in [status['status_id'] for status in timeout_statuses]  # PROCESSING 未超時
    
    def test_handle_timeout(self, lifecycle_manager):
        """測試超時處理"""
        status_id = 456
        email_id = 789
        current_status = ProcessStatus.PARSING.value
        elapsed_seconds = 700.5
        
        # 處理超時
        lifecycle_manager.handle_timeout(
            status_id=status_id,
            email_id=email_id,
            current_status=current_status,
            elapsed_seconds=elapsed_seconds
        )
        
        # 驗證超時事件已記錄
        events = lifecycle_manager.get_lifecycle_events(status_id=status_id)
        assert len(events) == 1
        
        event = events[0]
        assert event.event_type == "timeout"
        assert event.status_id == status_id
        assert event.email_id == email_id
        assert event.old_status == current_status
        assert event.new_status == ProcessStatus.TIMEOUT.value
        assert event.duration == elapsed_seconds
        assert 'elapsed_seconds' in event.metadata
    
    # ===========================================
    # 狀態建議測試
    # ===========================================
    
    def test_get_status_suggestions(self, lifecycle_manager):
        """測試狀態建議"""
        # PENDING 狀態的建議
        suggestions = lifecycle_manager.get_status_suggestions(
            ProcessStatus.PENDING.value
        )
        assert ProcessStatus.PARSING.value in suggestions
        assert ProcessStatus.FAILED.value in suggestions
        assert ProcessStatus.TIMEOUT.value in suggestions
        
        # PARSING 狀態的建議 (有條件)
        suggestions_with_parsing_completed = lifecycle_manager.get_status_suggestions(
            ProcessStatus.PARSING.value,
            metadata={'parsing_completed': True, 'parsed_data': 'test'}
        )
        assert ProcessStatus.PROCESSING.value in suggestions_with_parsing_completed
        
        # PARSING 狀態的建議 (無條件 - 失敗和超時)
        suggestions_without_condition = lifecycle_manager.get_status_suggestions(
            ProcessStatus.PARSING.value,
            metadata={'parsing_error': 'Some error'}
        )
        assert ProcessStatus.FAILED.value in suggestions_without_condition
        
        # 終止狀態應該沒有建議
        completed_suggestions = lifecycle_manager.get_status_suggestions(
            ProcessStatus.COMPLETED.value
        )
        assert len(completed_suggestions) == 0
    
    # ===========================================
    # 事件管理測試
    # ===========================================
    
    def test_get_lifecycle_events_filtering(self, lifecycle_manager):
        """測試生命週期事件過濾"""
        # 創建測試事件
        for i in range(5):
            lifecycle_manager.handle_status_change(
                status_id=i + 1,
                email_id=100 + i,
                old_status=ProcessStatus.PENDING.value,
                new_status=ProcessStatus.PARSING.value
            )
        
        # 測試按 status_id 過濾
        events_by_status = lifecycle_manager.get_lifecycle_events(status_id=3)
        assert len(events_by_status) == 1
        assert events_by_status[0].status_id == 3
        
        # 測試按 email_id 過濾
        events_by_email = lifecycle_manager.get_lifecycle_events(email_id=102)
        assert len(events_by_email) == 1
        assert events_by_email[0].email_id == 102
        
        # 測試限制數量
        limited_events = lifecycle_manager.get_lifecycle_events(limit=3)
        assert len(limited_events) <= 3
    
    def test_clear_old_events(self, lifecycle_manager):
        """測試清理舊事件"""
        # 創建一些事件
        for i in range(3):
            lifecycle_manager.handle_status_change(
                status_id=i + 1,
                email_id=200 + i,
                old_status=ProcessStatus.PENDING.value,
                new_status=ProcessStatus.PARSING.value
            )
        
        # 手動設置一些事件為舊事件
        old_time = datetime.utcnow() - timedelta(days=10)
        lifecycle_manager.lifecycle_events[0].timestamp = old_time
        lifecycle_manager.lifecycle_events[1].timestamp = old_time
        
        # 清理 7 天前的事件
        lifecycle_manager.clear_old_events(days=7)
        
        # 驗證舊事件已被清理
        assert len(lifecycle_manager.lifecycle_events) == 1
    
    # ===========================================
    # 統計信息測試
    # ===========================================
    
    def test_get_status_statistics(self, lifecycle_manager):
        """測試狀態統計"""
        # 創建不同類型的事件
        lifecycle_manager.handle_status_change(
            status_id=1,
            email_id=301,
            old_status=ProcessStatus.PENDING.value,
            new_status=ProcessStatus.PARSING.value
        )
        
        lifecycle_manager.handle_timeout(
            status_id=2,
            email_id=302,
            current_status=ProcessStatus.PARSING.value,
            elapsed_seconds=600
        )
        
        # 獲取統計
        stats = lifecycle_manager.get_status_statistics()
        
        # 驗證統計結構
        assert 'total_events' in stats
        assert 'event_types' in stats
        assert 'status_changes' in stats
        assert 'timeouts' in stats
        assert 'average_durations' in stats
        assert 'unique_emails' in stats
        assert 'unique_statuses' in stats
        
        # 驗證統計數據
        assert stats['total_events'] >= 2
        assert stats['status_changes'] >= 1
        assert stats['timeouts'] >= 1
        assert stats['unique_emails'] >= 2
    
    def test_empty_statistics(self, lifecycle_manager):
        """測試空統計"""
        # 清空事件
        lifecycle_manager.lifecycle_events.clear()
        
        # 獲取統計
        stats = lifecycle_manager.get_status_statistics()
        
        # 驗證空統計
        assert stats['total_events'] == 0
        assert stats['status_changes'] == 0
        assert stats['timeouts'] == 0
        assert len(stats['average_durations']) == 0
    
    # ===========================================
    # 錯誤處理測試
    # ===========================================
    
    def test_invalid_status_handling(self, lifecycle_manager):
        """測試無效狀態處理"""
        # 無效狀態值應該返回 False
        assert lifecycle_manager.validate_status_transition(
            "invalid_status",
            ProcessStatus.PARSING.value
        ) == False
        
        assert lifecycle_manager.validate_status_transition(
            ProcessStatus.PENDING.value,
            "another_invalid_status"
        ) == False
    
    def test_error_resilience(self, lifecycle_manager):
        """測試錯誤恢復能力"""
        # 這些調用應該不會拋出異常
        lifecycle_manager.handle_status_change(
            status_id=None,
            email_id=None,
            old_status="invalid",
            new_status="also_invalid"
        )
        
        lifecycle_manager.handle_timeout(
            status_id=None,
            email_id=None,
            current_status="invalid",
            elapsed_seconds=None
        )
        
        # 獲取統計和建議應該仍然工作
        stats = lifecycle_manager.get_status_statistics()
        assert isinstance(stats, dict)
        
        suggestions = lifecycle_manager.get_status_suggestions("invalid_status")
        assert isinstance(suggestions, list)


class TestStatusTransition:
    """StatusTransition 數據類測試"""
    
    def test_status_transition_creation(self):
        """測試狀態轉換創建"""
        transition = StatusTransition(
            from_status=ProcessStatus.PENDING,
            to_status=ProcessStatus.PARSING,
            condition=lambda meta: meta.get('ready') is True,
            timeout_seconds=300,
            auto_trigger=True,
            metadata_requirements=['data']
        )
        
        assert transition.from_status == ProcessStatus.PENDING
        assert transition.to_status == ProcessStatus.PARSING
        assert transition.timeout_seconds == 300
        assert transition.auto_trigger == True
        assert 'data' in transition.metadata_requirements
        
        # 測試條件函數
        assert transition.condition({'ready': True}) == True
        assert transition.condition({'ready': False}) == False


class TestLifecycleEvent:
    """LifecycleEvent 數據類測試"""
    
    def test_lifecycle_event_creation(self):
        """測試生命週期事件創建"""
        timestamp = datetime.utcnow()
        metadata = {'test': 'data'}
        
        event = LifecycleEvent(
            event_type="status_change",
            status_id=123,
            email_id=456,
            old_status=ProcessStatus.PENDING.value,
            new_status=ProcessStatus.PARSING.value,
            timestamp=timestamp,
            metadata=metadata,
            duration=10.5
        )
        
        assert event.event_type == "status_change"
        assert event.status_id == 123
        assert event.email_id == 456
        assert event.old_status == ProcessStatus.PENDING.value
        assert event.new_status == ProcessStatus.PARSING.value
        assert event.timestamp == timestamp
        assert event.metadata == metadata
        assert event.duration == 10.5


if __name__ == "__main__":
    # 運行生命週期管理器測試
    pytest.main([__file__, "-v", "--tb=short"])