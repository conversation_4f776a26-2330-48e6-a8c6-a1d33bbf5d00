# PTS Renamer 檔案上傳重複選擇問題修復

## 問題描述
用戶報告：http://localhost:5000/pts-renamer/ 按了上傳壓縮檔後會出現兩次檔案選擇對話框

## 根本原因分析
對比 localhost:8010/ui 的成功實現與 PTS Renamer：

### 成功系統 (localhost:8010/ui)
- `frontend/shared/static/js/components/file-upload.js` 使用 `isUploading` 狀態鎖
- 防止重複處理機制：
```javascript
if (this.isUploading) {
    StatusManager.showToast('已有檔案正在上傳中，請等待完成後再試！', 'warning');
    return;
}
```

### 原 PTS Renamer 問題
- 缺乏狀態鎖機制
- 允許並發檔案處理
- 導致重複觸發檔案選擇對話框

## 修復實施

### 1. 修改 `frontend/pts_renamer/static/js/pts_renamer.js`
```javascript
constructor() {
    // ...
    this.isUploading = false;  // 添加上傳狀態鎖
}

handleFileSelect(event) {
    if (this.isUploading) {
        this.showNotification('檔案正在處理中，請稍等...', 'warning');
        return;
    }
    
    this.isUploading = true;
    // ... 處理邏輯
    
    // 處理完成後重置狀態
    setTimeout(() => {
        this.isUploading = false;
    }, 500);
}
```

### 2. 修改 `frontend/pts_renamer/templates/pts_rename_main.html`
```javascript
document.getElementById('fileInput').addEventListener('change', function(event) {
    if (window.ptsRenamerUI.isUploading) {
        console.log('檔案正在處理中，跳過重複處理');
        fileInput.value = '';
        return;
    }
    // ... 處理邏輯
});
```

## 修復策略
1. 參考成功系統的狀態管理模式
2. 添加 `isUploading` 狀態鎖
3. 在處理開始時設定鎖定，完成後解鎖
4. 並發請求時顯示警告訊息並中止處理

## 驗證需求
- 使用 Playwright 測試修復後的系統
- 確認檔案上傳只觸發一次選擇對話框
- 驗證狀態鎖機制正常運作